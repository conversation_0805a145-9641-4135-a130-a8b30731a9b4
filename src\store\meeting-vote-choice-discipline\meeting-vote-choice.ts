import { defineStore } from 'pinia'
import type {
  BranchPartyMeetingList,
  BranchVoteScoreList,
} from '@/services/affairs/party-building-list-branch/types'

export const useMeetingVoteChoicesStore = defineStore('meetingVoteAll', () => {
  const allMeetingList = ref<BranchPartyMeetingList[]>([]) // 会议列表
  const checkMeeting = ref<BranchPartyMeetingList[]>([]) // 会议列表

  function setAllMeetingList(data: BranchPartyMeetingList[]) {
    allMeetingList.value = data
  }

  function setCheckMeeting(data: BranchPartyMeetingList[]) {
    checkMeeting.value = data
  }

  const allVoteList = ref<BranchVoteScoreList[]>([]) // 会议列表
  const checkVote = ref<BranchVoteScoreList[]>([]) // 会议列表

  function setAllVoteList(data: BranchVoteScoreList[]) {
    allVoteList.value = data
  }

  function setCheckVote(data: BranchVoteScoreList[]) {
    checkVote.value = data
  }

  function clearAll() {
    allMeetingList.value = []
    checkMeeting.value = []
    allVoteList.value = []
    checkVote.value = []
  }

  return {
    allMeetingList,
    checkMeeting,
    allVoteList,
    checkVote,

    setAllMeetingList,
    setCheckMeeting,
    setAllVoteList,
    setCheckVote,
    clearAll,
  }
})
