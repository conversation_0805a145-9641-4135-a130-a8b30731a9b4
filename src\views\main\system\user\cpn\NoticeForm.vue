<script setup lang="ts">
import { NForm } from 'naive-ui'
import { formRules } from './config'
import {
  addUserItem,
  editorUserItem,
  getUserInfo,
} from '@/services/system/User'
import type { uploadFileItem } from '@/services/affairs/party-building-list/exam-indicators/types'
import { uploadImg } from '@/services'
import type { UserDetailItem } from '@/services/system/User/types'
import {
  useCurrentOrganizationListOptions,
  useFetchEnumerationOptions,
  useFetchOriginOptions,
} from '@/hooks/use-select-options'
import { POLITICAL_STATUS } from '@/store/dict'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const sexType = ref(useFetchEnumerationOptions('sex'))
const eduType = ref(useFetchEnumerationOptions('edu'))
const ethnicType = ref(useFetchEnumerationOptions('ethnic'))
const memberStatusType = ref(useFetchEnumerationOptions('member_status'))
const orgType = ref(useCurrentOrganizationListOptions())
const partyIdentityType = ref(useFetchEnumerationOptions('party_identity'))
const originType = ref(useFetchOriginOptions())

const formDataReactive = reactive<UserDetailItem>({
  userId: null,
  username: '',
  trueName: '',
  sex: null,
  ethnic: null,
  // customEthnic: null,
  edu: null,
  jobTime: null,
  origin: null,
  departmentId: '',
  address: '',
  memberStatus: '0',
  phone: '',
  identityId: '',
  partyIdentity: '7',
  joinTime: null,
  regularTime: null,
  deptId: null,
  avatarId: '',
  avatarUrl: '',
  // partyMember: null,
  political: null,
})

const formRef = ref<InstanceType<typeof NForm>>()

onBeforeMount(() => {
  if (props.type === 'modify' && props.id) {
    getUserInfo(props.id).then((res) => {
      formDataReactive.userId = res.userId
      formDataReactive.username = res.username
      formDataReactive.trueName = res.trueName
      formDataReactive.sex = res.sex
      formDataReactive.ethnic = res.ethnic ? res.ethnic : null
      // formDataReactive.customEthnic = String(res.ethnic)
      //   ? String(res.ethnic)
      //   : null
      formDataReactive.edu = res.edu
      formDataReactive.jobTime = res.jobTime ? res.jobTime.slice(0, 10) : null
      formDataReactive.origin = res.origin
      formDataReactive.departmentId = res.departmentId
      formDataReactive.address = res.address
      formDataReactive.memberStatus = res.memberStatus
      formDataReactive.phone = res.phone
      formDataReactive.identityId = res.identityId
      formDataReactive.partyIdentity = res.partyIdentity
      formDataReactive.joinTime = res.joinTime?.slice(0, 10) || null
      formDataReactive.regularTime = res.regularTime
        ? res.regularTime.slice(0, 10)
        : null
      formDataReactive.deptId = res.deptId
      formDataReactive.avatarId = res.avatarId
      formDataReactive.avatarUrl = res.avatarUrl
      // === '中共党员' ? '中共党员' : res.political === '预备党员' ? '预备党员' : '群众'
      formDataReactive.political = res.political
      // formDataReactive.partyMember
      //   = res.partyIdentity !== '6' ? '中共党员' : '预备党员' // 6 为群众
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (formDataReactive.political === '3') {
        formDataReactive.partyIdentity = null
        formDataReactive.deptId = null
        formDataReactive.joinTime = null
        formDataReactive.regularTime = null
      }
      if (formDataReactive.userId) {
        // 编辑为非党员情况下 入党时间、转正时间、所属组织重置为null
        editorUserItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
      else {
        addUserItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
// 照片上传
const handleAppCoverDone = async(file: File) => {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formDataReactive.avatarId) {
      return
    }
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      formDataReactive.avatarId = data.fileId
    }
  }
  catch (error) {}
}
// 照片删除
const handleAppCoverDelete = () => {
  formDataReactive.avatarId = ''
}

/** 获取党组织id */
const handleUpdateValue = (v: string) => {
  formDataReactive.deptId = v
}

/** 获取籍贯 */
const handleUpdateOriginValue = (v: string) => {
  formDataReactive.origin = v
}

/** 获取民族枚举值 转为 number类型 */
// const handleUpdateEthnicValue = (v: string) => {
//   formDataReactive.customEthnic = v
//   formDataReactive.ethnic = Number(v)
// }

/** 禁用时间 */
const handleDateDisabled = (s: number) => {
  return s >= new Date().getTime()
}

defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <div
      v-if="type === 'add'"
      class="w-full flex flex-row justify-center items-center"
    >
      <span class="text-[#F5A53C]">新用户初始密码为身份证后六位！请提醒相关人员及时修改密码。</span>
    </div>
    <div><span>个人信息</span></div>
    <n-grid :cols="24">
      <!-- 一排 -->
      <n-form-item-gi :span="12" label="用户名：" path="username">
        <n-input
          v-model:value="formDataReactive.username"
          placeholder="请输入用户名"
          maxlength="20"
          :disabled="type === 'modify'"
          show-count
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="姓名：" path="trueName">
        <n-input
          v-model:value="formDataReactive.trueName"
          placeholder="请输入姓名"
          maxlength="20"
          :disabled="type === 'modify'"
          show-count
          clearable
        />
      </n-form-item-gi>
      <!-- 二排 -->
      <n-form-item-gi :span="12" label="手机号：" path="phone">
        <n-input
          v-model:value="formDataReactive.phone"
          placeholder="请输入手机号"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="身份证：" path="identityId">
        <n-input
          v-model:value="formDataReactive.identityId"
          :disabled="type === 'modify'"
          placeholder="请输入身份证"
        />
      </n-form-item-gi>
      <!-- 三排 -->
      <n-form-item-gi :span="12" label="性别：" path="sex">
        <n-select
          v-model:value="formDataReactive.sex"
          placeholder="请选择性别"
          :options="sexType.enumerationList"
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="民族：">
        <n-select
          v-model:value="formDataReactive.ethnic"
          :options="ethnicType.enumerationList"
          placeholder="请选择民族"
          clearable
        />
      </n-form-item-gi>
      <!-- 四排 -->
      <n-form-item-gi :span="12" label="学历：">
        <n-select
          v-model:value="formDataReactive.edu"
          :options="eduType.enumerationList"
          placeholder="请选择学历"
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="参加工作时间：" path="jobTime">
        <n-date-picker
          v-model:formatted-value="formDataReactive.jobTime"
          value-format="yyyy-MM-dd"
          placeholder="请选择参加工作时间"
          type="date"
          class="w-full"
          size="medium"
          clearable
        />
      </n-form-item-gi>
      <!-- 五排 -->
      <n-form-item-gi :span="12" label="工作部门：" path="departmentId">
        <n-input
          v-model:value="formDataReactive.departmentId"
          placeholder="请输入工作部门"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="政治面貌：" path="political">
        <n-select
          v-model:value="formDataReactive.political"
          placeholder="请选择政治面貌"
          :options="POLITICAL_STATUS"
          clearable
        />

        <!-- <n-radio-group v-model:value="formDataReactive.partyMember">
          <n-radio label="中共党员" value="1" />
          <n-radio label="预备党员" value="0" />
        </n-radio-group> -->
      </n-form-item-gi>

      <!-- 六排 -->
      <n-form-item-gi :span="24" label="家庭住址：" path="address">
        <n-input
          v-model:value="formDataReactive.address"
          placeholder="请输入家庭住址"
          maxlength="50"
          show-count
          clearable
        />
      </n-form-item-gi>
      <!-- 七排 -->
      <n-form-item-gi :span="12" label="状态：" path="memberStatus">
        <n-select
          v-model:value="formDataReactive.memberStatus"
          :options="memberStatusType.enumerationList"
          placeholder="请选择状态"
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="籍贯：" path="origin">
        <n-cascader
          v-model:value="formDataReactive.origin"
          :options="originType.OriginOptions"
          value-field="id"
          label-field="name"
          children-field="children"
          check-strategy="child"
          :show-path="true"
          clearable
          placeholder="请选择籍贯"
          @update:value="handleUpdateOriginValue"
        />
      </n-form-item-gi>

      <n-form-item-gi :span="12" label="照片：" path="avatarId">
        <ImgUploader
          v-model:oldImgUrl="formDataReactive.avatarUrl"
          :width="295"
          :height="431"
          :need-cropper="false"
          @done="handleAppCoverDone"
          @delete="handleAppCoverDelete"
        />
      </n-form-item-gi>
    </n-grid>
    <div v-if="formDataReactive.political !== '3'">
      <span>党员信息</span>
    </div>
    <n-grid v-if="formDataReactive.political !== '3'" :cols="24">
      <n-form-item-gi :span="12" label="所属党组织：" path="deptId">
        <n-cascader
          v-model:value="formDataReactive.deptId"
          :options="orgType.organizationCurrentListTree"
          value-field="id"
          label-field="name"
          children-field="children"
          check-strategy="all"
          :show-path="false"
          clearable
          placeholder="请选择所属党组织"
          @update:value="handleUpdateValue"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="党内职务：" path="partyIdentity">
        <n-select
          v-model:value="formDataReactive.partyIdentity"
          :options="partyIdentityType.enumerationList"
          placeholder="请选择党内职务"
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="入党时间：" path="joinTime">
        <n-date-picker
          v-model:formatted-value="formDataReactive.joinTime"
          :is-date-disabled="handleDateDisabled"
          value-format="yyyy-MM-dd"
          placeholder="请选择入党时间"
          type="date"
          class="w-full"
          size="medium"
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="转正时间：">
        <n-date-picker
          v-model:formatted-value="formDataReactive.regularTime"
          :is-date-disabled="handleDateDisabled"
          value-format="yyyy-MM-dd"
          placeholder="请选择转正时间"
          type="date"
          class="w-full"
          size="medium"
          clearable
        />
      </n-form-item-gi>
    </n-grid>
  </n-form>
</template>
<style lang="scss" scoped></style>
