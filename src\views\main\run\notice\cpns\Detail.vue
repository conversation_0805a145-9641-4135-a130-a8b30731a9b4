<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui'
import dayjs from 'dayjs'
import RichEditor from '@/components/RichEditor.vue'
import DetailHeader from '@/components/DetailHeader.vue'
import ItemWrapper from '@/components/ItemWrapper.vue'
import {
  addCategory,
  editorCategory,
  fetchCategoryClassification,
  getNewsDetail,
  uploadImg,
} from '@/services'

const formData = reactive<Record<string, any>>({
  id: '',
  author: '苏银凯基党委',
  title: '',
  publishTime: null,
  content: '',
  coverId: '',
  coverUrl: '',
  categoryId: sessionStorage.getItem('selectedIDForNews') || '',
  isTop: '0',
  isRecommend: '0',
  readTimeConfig: 10,
  newsCategoryVO: {
    id: '',
    name: '',
    sort: null,
  },
})
const options = ref()
async function loadClassification() {
  const res = await fetchCategoryClassification()
  options.value = res.map((item: any) => ({ label: item.name, value: item.id }))
}
const route = useRoute()
const newID = (route.query.id as string) || null
const getNewsDetailFn = async() => {
  const data = await getNewsDetail(newID)
  formData.id = data.id
  formData.author = data.author
  formData.title = data.title
  formData.publishTime = new Date(data.publishTime).getTime()
  formData.content = data.content
  formData.coverId = data.coverId
  formData.coverUrl = data.coverUrl
  formData.categoryId = data.newsCategoryVO.id
  formData.isTop = data.isTop
  formData.isRecommend = data.isRecommend
  formData.readTimeConfig = data.readTimeConfig
  formData.newsCategoryVO = data.newsCategoryVO
}
onMounted(() => {
  loadClassification()
  if (newID) {
    getNewsDetailFn()
  }
})
// 图片上传
// const imgId = ref(0)
// async function updateImage(fileList: UploadFileInfo[]) {
//   const data = new FormData()
//   if (fileList.length > 0) {
//     data.append('file', fileList[0].file!)
//     const res = await uploadImg(data)
//     formData.coverId = res.fileId
//   } else {
//     formData.coverId = '0'
//   }
// }
// function getRichText(richText: string) {
//   formData.content = richText
// }
const requiredItemsList = [
  'title',
  'author',
  'coverId',
  'content',
  'categoryId',
]

const handleDateDisabled = (s: number) => {
  return s < new Date().getTime() - 86400000
}

const currentPublishTime = ref()
const formatValue = (s: string) => {
  currentPublishTime.value = s
}
const router = useRouter()
async function publishConfirm() {
  let flag = false
  requiredItemsList.forEach((item) => {
    if (!formData[item]) {
      flag = true
    }
  })
  if (flag) {
    window.$message.error('请完成必填项')
    return
  }

  if (
    currentPublishTime.value
    && dayjs(currentPublishTime.value).valueOf() < dayjs().valueOf()
  ) {
    window.$message.error('发布时间不能早于当前时间')
    return
  }
  const payload = {
    title: formData.title,
    author: formData.author,
    publishTime: currentPublishTime.value,
    content: formData.content,
    coverId: formData.coverId,
    categoryId: formData.categoryId,
    isTop: formData.isTop,
    isRecommend: formData.isRecommend,
    readTimeConfig: formData.readTimeConfig,
  }
  if (formData.id) {
    const obj: object = { ...formData, publishTime: currentPublishTime.value }
    await editorCategory(obj)
  }
  else {
    await addCategory(payload)
  }
  window.$message.success('已发布')
  router.push({
    name: 'noticeList',
  })
}

// PC封面图片上传
const handleCoverDone = async(file: UploadFileInfo[]) => {
  const data = new FormData()
  if (file) {
    data.append('file', file as unknown as Blob)
    const res = await uploadImg(data)
    formData.coverId = res.fileId
  }
  else {
    formData.coverId = '0'
  }
}
// PC封面图片删除操作
const handleCoverDelete = () => {
  formData.coverId = '0'
}
</script>
<template>
  <div>
    <DetailHeader
      back-name="noticeList"
      :release="publishConfirm"
      :need-show-dialog="true"
    />
    <div class="pl-[200px]">
      <ItemWrapper item-name="标题：" :required="true">
        <n-input
          v-model:value="formData.title"
          style="width: 580px"
          maxlength="80"
          type="textarea"
          rows="2"
          show-count
          clearable
        />
      </ItemWrapper>
      <ItemWrapper item-name="作者：" :required="true">
        <n-input v-model:value="formData.author" style="width: 580px" />
      </ItemWrapper>
      <ItemWrapper item-name="封面图片：" :required="true">
        <!-- <n-upload
          :max="1"
          style="width: 300px"
          list-type="image-card"
          @update:file-list="updateImage"
        >
          点击上传
        </n-upload> -->
        <ImgUploader
          v-model:oldImgUrl="formData.coverUrl"
          :width="220"
          :height="150"
          :need-cropper="false"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </ItemWrapper>
      <!-- <div class="ml-[140px]">
        推荐尺寸220*150；支出.jpg，.jpeg，.png类型文件，5M以内
      </div> -->
      <ItemWrapper item-name="正文：" :required="true">
        <RichEditor v-model:value="formData.content" />
      </ItemWrapper>
      <ItemWrapper item-name="选择分类：" :required="true">
        <n-select
          v-model:value="formData.categoryId"
          disabled
          style="width: 500px"
          :options="options"
        />
      </ItemWrapper>
      <ItemWrapper item-name="发布时间：">
        <n-date-picker
          v-model:value="formData.publishTime"
          format="yyyy-MM-dd HH:mm:ss"
          type="datetime"
          style="width: 500px"
          clearable
          :is-date-disabled="handleDateDisabled"
          @update:formatted-value="formatValue"
          @clear="() => (formData.publishTime = null)"
        />
      </ItemWrapper>
      <ItemWrapper item-name="置顶和推荐：">
        <n-switch
          v-model:value="formData.isTop"
          checked-value="1"
          unchecked-value="0"
        />
        &nbsp;置顶
        <n-switch
          v-model:value="formData.isRecommend"
          checked-value="1"
          unchecked-value="0"
          class="ml-[80px]"
        />
        &nbsp;推荐
      </ItemWrapper>
      <!-- <ItemWrapper item-name="学习时长：" :required="true">
        <div class="flex items-center">
          <n-input-number
            v-model:value="formData.readTimeConfig"
            style="width: 80px"
            :min="5"
            :max="60"
          /><span class="ml-[10px]">秒</span>
        </div>
      </ItemWrapper> -->
    </div>
  </div>
</template>

<style scoped lang="scss">
::v-deep(.n-upload-trigger) {
  width: 220px;
  height: 150px;
}
</style>
