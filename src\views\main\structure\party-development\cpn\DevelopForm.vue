<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { formRules } from './config'
import {
  useCurrentOrganizationListOptions,
  useDevMemberChoseList,
} from '@/hooks/use-select-options'
import type { PartyDevelopmentMemberAdd } from '@/services/structure/party-development/types'
import { postPartyDevelopmentList } from '@/services/structure/party-development'
import type {
  uploadFileItem,
  uploadFileItemNew,
} from '@/services/affairs/party-building-list/exam-indicators/types'
import { uploadImg } from '@/services'
interface Props {
  type?: string
  id?: string
}
withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

// const { organizationListTree } = useOrganizationListOptions() // 获取组织列表
const { organizationCurrentListTree } = useCurrentOrganizationListOptions() // 获取当前用户管辖组织列表

const { userList } = useDevMemberChoseList() // 获取用户列表

const referenceFile = [
  { no: '1', title: '1.1 入党申请书模板', link: '' },
  { no: '2', title: '1.2 入党申请书撰写说明', link: '' },
  {
    no: '3',
    title: '2.1 同入党申请人XX同志的谈话记录模板1_谈话人填写',
    link: '',
  },
  {
    no: '4',
    title: '2.2 同入党申请人XX同志的谈话记录模板2_谈话人填写',
    link: '',
  },
]

// const userRef = ref([])
// function handelUserList(deptId: string) {
//   if (deptId) {
//     getUserList().then((res: any) => {
//       userRef.value = res
//     })
//   }
// }

// const userList = computed(() => contactList.value.map((item: ContactListItem) => ({
//   value: item.userId,
//   label: item.trueName,
// })))

const formDataReactive = reactive<PartyDevelopmentMemberAdd>({
  userId: '',
  organizationId: '', // 接收组织
  applyTime: null, // 申请时间
  fileIds: [],
  fileList: [],
})
const loading = ref(false)
const formRef = ref<InstanceType<typeof NForm>>()
// onMounted(async() => {
//   loading.value = true
//   if ((props.type === 'modify' || props.type === 'view') && props.id) {
//     try {
//       const res = await getSinglePartyListDetail(props.id)
//       formDataReactive.title = res.title
//       formDataReactive.evaluationYearAndMonth = res.evaluationYearAndMonth
//       formDataReactive.organizationList
//         = res.organizationVoList?.map(item => item.id) ?? null
//       formDataReactive.id = props.id
//       loading.value = false
//     } catch (error) {}
//   }
// })
// 文件相关
async function handleFileChange(
  fileInfoList: uploadFileItemNew[],
  isDelIDs: string,
) {
  try {
    const fileData = new FormData()
    const fileIdArr: Array<string> = []
    // 获取所有FileID
    formDataReactive.fileList.forEach(item => fileIdArr.push(item.id))
    // 删除动作
    if (isDelIDs) {
      fileIdArr.forEach((item, index) => {
        if (item === isDelIDs) {
          fileIdArr.splice(index, 1)
        }
      })
      formDataReactive.fileIds = [...fileIdArr]
    } else {
      // 新增动作
      const lastFileItem = fileInfoList[fileInfoList.length - 1]?.file
      fileData.append('file', lastFileItem as Blob)
      const data: uploadFileItem = await uploadImg(fileData)
      formDataReactive.fileList.push({
        original: lastFileItem?.name as string,
        fileName: data.url || '',
        id: data.fileId,
      })
      if (data) {
        fileIdArr.push(data.fileId)
        if (formDataReactive.fileList.length) {
          formDataReactive.fileIds = formDataReactive.fileIds!.concat(fileIdArr)
        } else {
          formDataReactive.fileIds = [...fileIdArr]
        }
      }
    }
  } catch (error) {}
}

// 验证表单,调用接口
async function validateAndSave() {
  loading.value = true
  const errors = await new Promise((resolve) => {
    formRef.value?.validate((errors: any) => {
      resolve(errors)
    })
  })

  if (!errors) {
    // const saveFunction
    //   = props.type === 'modify' && props.id
    //     ? putPartyBuildList
    //     : postPartyBuildList
    const saveData = { ...formDataReactive }
    try {
      const res = await postPartyDevelopmentList(saveData)
      if (res) {
        window.$message.success('保存成功')
        emits('saved')
      }
      loading.value = false
    } catch (error) {}
  }
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="90"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <SectionTitle text="基本信息" />
    <n-grid :cols="24" x-gap="12" y-gap="8">
      <n-form-item-gi :span="12" label="用户：" path="userId">
        <n-select
          v-model:value="formDataReactive.userId"
          placeholder="请选择用户"
          value-field="userId"
          label-field="trueName"
          :options="userList"
          filterable
          clearable
        />
      </n-form-item-gi>
    </n-grid>
    <SectionTitle text="入党信息" />
    <n-grid :cols="24" :x-gap="24" :y-gap="8">
      <n-form-item-gi :span="12" label="接收组织：" path="organizationId">
        <!-- <n-cascader
          v-model:value="formDataReactive.organizationId"
          placeholder="请选择目标组织"
          :options="(organizationCurrentListTree as any)"
          value-field="id"
          label-field="name"
          children-field="children"
          check-strategy="child"
          :show-path="false"
          clearable
          @update:value="(v:any) => (formDataReactive.organizationId = v)"
        /> -->
        <n-tree-select
          v-model:value="formDataReactive.organizationId"
          :options="organizationCurrentListTree"
          value-field="id"
          label-field="name"
          key-field="id"
          children-field="children"
          check-strategy="all"
          placeholder="请选择接收组织"
          :show-path="false"
          clearable
          filterable
          @update:value="(v:any) => (formDataReactive.organizationId = v)"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="申请时间：" path="applyTime">
        <n-date-picker
          v-model:formatted-value="formDataReactive.applyTime"
          style="width: 100%"
          placeholder="请选择申请时间"
          clearable
          type="datetime"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="24" label="附件：">
        <file-uploader
          :max="4"
          accept=".doc, .docx, .pdf"
          :size-limit="200"
          :original-file-list="(formDataReactive.fileList as any)"
          @file-list-change="handleFileChange"
        >
          <template #tips>
            <span class="tips">
              最多可上传4个文件，支持扩展名：.doc，docx，.pdf，大小200M以内
            </span>
          </template>
        </file-uploader>
      </n-form-item-gi>
    </n-grid>
    <SectionTitle text="参考文档" />
    <div
      class="grid grid-cols-2 gap-x-[40px] gap-y-[10px] flex-wrap justify-between"
    >
      <div v-for="item in referenceFile" :key="item.no">
        <a class="text-[#006FFF] cursor-pointer underline" :href="item.link">{{
          item.title
        }}</a>
      </div>
    </div>
  </n-form>
</template>
<style lang="scss" scoped></style>
