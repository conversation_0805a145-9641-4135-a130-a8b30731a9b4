<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui'
import DetailHeader from '@/components/DetailHeader.vue'
import ItemWrapper from '@/components/ItemWrapper.vue'

import { uploadImg } from '@/services'
import {
  getStudyContentDetail,
  postStudyContent,
  putStudyContent,
} from '@/services/study/study-manage'
import type {
  uploadFileItem,
  uploadFileItemNew,
} from '@/services/affairs/party-building-list/exam-indicators/types'
import type { File } from '@/services/study/study-manage/types'

const route = useRoute()
const enterType = computed(() => route.query.enterType ?? null)
const title = ref()
if (enterType.value === 'edit') {
  title.value = '编辑课程'
}
else if (enterType.value === 'add') {
  title.value = '发布课程'
}
else {
  title.value = '课程详情'
}
const types = [
  { label: '视频', value: '0' },
  { label: '图片', value: '1' },
  { label: '文档', value: '2' },
]
const uploadConfig = ref({
  max: 0,
  accept: '',
  sizeLimit: 0,
  tips: '',
})

const formData = reactive({
  id: '',
  title: '',
  isTop: '0',
  cover: {
    id: '',
    url: '',
    original: '',
  },
  type: '0',
  isDownload: '0',
  content: '',
  categoryId: sessionStorage.getItem('studyCategoryId') || '',
  // studyTimeConfig: 80,
  files: [] as any[],
})

const originType = ref()
const originalStudyTimeConfig = ref()
const originFiles = ref()

const contentId = (route.query.id as string) || null
const getNewsDetailFn = async() => {
  const data = await getStudyContentDetail(contentId)

  formData.id = data.id
  // 标题
  formData.title = data.title
  // 是否置顶
  formData.isTop = data.isTop

  // 封面
  formData.cover.id = data.cover.id
  formData.cover.url = data.cover.url
  formData.cover.original = data.cover.original
  // 课程类型
  formData.type = data.type
  originType.value = data.type
  // 是否允许下载
  formData.isDownload = data.isDownload
  // 课程内容
  formData.files = data.files.map((file: File) => {
    return {
      id: file.id,
      url: file.url,
      original: file.original,
    }
  })
  originFiles.value = data.files.map((file: File) => {
    return {
      id: file.id,
      url: file.url,
      original: file.original,
    }
  })
  // 内容介绍
  formData.content = data.content
  // 规则设置
  // formData.studyTimeConfig = data.studyTimeConfig
  originalStudyTimeConfig.value = data.studyTimeConfig
}
const setDefaultStudyTimeConfig = (type: string) => {
  // formData.studyTimeConfig = getStudyTimeConfigDefault(type)
}

watch(
  () => formData.type,
  (newValue) => {
    if (newValue !== originType.value) {
      setDefaultStudyTimeConfig(newValue)
      formData.files = []
    }
    else {
      // formData.studyTimeConfig = originalStudyTimeConfig.value
      formData.files = originFiles.value
    }
    switch (newValue) {
    case '0':
      uploadConfig.value = {
        max: 1,
        accept: '.mp4',
        sizeLimit: 10240,
        tips: '仅可以上传一个视频，支持 .mp4 类型文件，1GB以内',
      }
      break
    case '1':
      uploadConfig.value = {
        max: 100,
        accept: '.jpg, .png, .jpeg',
        sizeLimit: 200,
        tips: '可以上传多张图片，支持扩展名：.jpg .jpeg .png，单个大小20M以内',
      }
      break
    case '2':
      uploadConfig.value = {
        max: 1,
        accept: '.pdf',
        sizeLimit: 500,
        tips: '仅可以上传一个文档，支持扩展名：.pdf 大小50M以内',
      }
      break
    default:
      uploadConfig.value = {
        max: 0,
        accept: '',
        sizeLimit: 0,
        tips: '',
      }
      break
    }
  },
  { immediate: true },
)
// watch(
//   () => formData.studyTimeConfig,
//   () => {
//     isStudyTimeConfigModified.value
//     = formData.studyTimeConfig !== originalStudyTimeConfig.value
//   },
// )
// function getStudyTimeConfigDefault(type: string) {
//   switch (type) {
//     case '0':
//       return 80
//     case '1':
//     case '2':
//       return 100
//     default:
//       return 0
//   }
// }
onMounted(() => {
  if (contentId) {
    getNewsDetailFn()
  }
})

// 文件上传
async function handleFileChange(
  fileInfoList: uploadFileItemNew[],
  isDelIDs: string,
) {
  try {
    const fileData = new FormData()
    // 删除动作
    if (isDelIDs || isDelIDs === null) {
      if (isDelIDs === null && formData.files.length) {
        formData.files.pop()
      }
      else {
        formData.files = formData.files.filter(item => item.id !== isDelIDs)
      }
    }
    else {
      // 新增动作
      const lastFileItem = fileInfoList[fileInfoList.length - 1]?.file
      fileData.append('file', lastFileItem as Blob)
      const data: uploadFileItem = await uploadImg(fileData)
      formData.files.push({
        original: lastFileItem?.name as string,
        url: data.url || '',
        id: data.fileId,
      })
    }
  }
  catch (error) {}
}

const router = useRouter()
const requiredItemsList = [
  'title',
  'isTop',
  'type',
  'isDownload',
  // 'studyTimeConfig',
  'categoryId',
] as const
async function publishConfirm() {
  const isCoverEmpty = !formData.cover.id
  const isFilesEmpty = formData.files.length === 0

  if (isCoverEmpty || isFilesEmpty) {
    window.$message.error('请完成必填项!')
    return
  }

  let flag = false
  requiredItemsList.forEach((item) => {
    if (!formData[item]) {
      flag = true
    }
  })
  if (flag) {
    window.$message.error('请完成必填项!')
    return
  }

  if (formData.id) {
    await putStudyContent(formData)
  }
  else {
    await postStudyContent(formData)
  }
  window.$message.success('已保存')
  router.push({
    name: 'studyManageList',
  })
}

// PC封面图片上传
const handleCoverDone = async(file: UploadFileInfo[]) => {
  const data = new FormData()
  if (file) {
    data.append('file', file as unknown as Blob)
    const res = await uploadImg(data)
    formData.cover.id = res.fileId
  }
  else {
    formData.cover.id = '0'
  }
}
// PC封面图片删除操作
const handleCoverDelete = () => {
  formData.cover.id = '0'
}
</script>
<template>
  <div>
    <DetailHeader
      back-name="studyManageList"
      :header-title="title"
      right-btn-text="保存"
      :is-show-confirm-btn="enterType === 'view' ? false : true"
      :release="publishConfirm"
      :need-show-dialog="enterType === 'view' ? false : true"
    />
    <div class="pl-[200px]">
      <ItemWrapper item-name="标题：" :required="true">
        <n-input
          v-model:value="formData.title"
          :disabled="enterType === 'view'"
          style="width: 800px"
          clearable
        />
      </ItemWrapper>
      <ItemWrapper item-name="是否置顶：" :required="true">
        <n-switch
          v-model:value="formData.isTop"
          :disabled="enterType === 'view'"
          checked-value="1"
          unchecked-value="0"
        />
      </ItemWrapper>
      <ItemWrapper item-name="封面图：" :required="true">
        <ImgUploader
          v-model:oldImgUrl="formData.cover.url"
          :width="375"
          :height="190"
          :need-cropper="false"
          :is-readonly="enterType === 'view' ? true : false"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </ItemWrapper>
      <ItemWrapper item-name="课程类型：" :required="true">
        <n-radio-group
          v-model:value="formData.type"
          name="radiogroup"
          :disabled="enterType === 'view'"
        >
          <n-space>
            <n-radio
              v-for="item in types"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </n-radio>
          </n-space>
        </n-radio-group>
      </ItemWrapper>
      <ItemWrapper item-name="是否允许下载：" :required="true">
        <n-switch
          v-model:value="formData.isDownload"
          :disabled="enterType === 'view'"
          checked-value="1"
          unchecked-value="0"
        />
      </ItemWrapper>
      <div class="w-[calc(100%-558px)]">
        <ItemWrapper item-name="课程内容：" :required="true">
          <file-uploader
            style="padding-left: 10px"
            :is-readonly="enterType === 'view' ? true : false"
            :max="uploadConfig.max"
            :accept="uploadConfig.accept"
            :size-limit="uploadConfig.sizeLimit"
            :original-file-list="(formData.files as any)"
            @file-list-change="handleFileChange"
          >
            <template #tips>
              <span class="tips">{{ uploadConfig.tips }}</span>
            </template>
          </file-uploader>
        </ItemWrapper>
      </div>
      <ItemWrapper item-name="内容介绍：">
        <n-input
          v-model:value="formData.content"
          style="width: 800px"
          type="textarea"
          :disabled="enterType === 'view' ? true : false"
          placeholder="请输入内容介绍"
          clearable
          round
          maxlength="200"
          show-count
          :autosize="{ minRows: 5, maxRows: 8 }"
        />
      </ItemWrapper>

      <!-- <ItemWrapper item-name="规则设置：" :required="true">
        <div class="flex items-center">
          <span v-if="formData.type === '0'">播放进度为视频总进度的</span>
          <span v-else-if="formData.type === '1'">图片打开后</span>
          <span v-else>文档打开后</span>
          <n-input-number
            v-model:value="formData.studyTimeConfig"
            :disabled="enterType === 'view'"
            style="width: 80px"
            :min="1"
            :precision="0"
          />
          <span v-if="formData.type === '0'">%</span>
          <span v-else class="ml-[10px]">秒</span>
        </div>
      </ItemWrapper> -->
    </div>
  </div>
</template>

<style scoped lang="scss">
::v-deep(.n-upload-trigger) {
  width: 220px;
  height: 150px;
}
</style>
