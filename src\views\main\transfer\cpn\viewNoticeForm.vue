<script lang="ts" setup>
import type { SelectOption, TreeSelectOption } from 'naive-ui'
import { NForm } from 'naive-ui'
import { approvalColumn } from '../config'
import { formRules } from './config'
import type { uploadFileItem } from '@/services/affairs/party-building-list/exam-indicators/types'
import { uploadImg } from '@/services'
import { getOrganizationTree } from '@/services/system/Admin'
import type { AddTransferItemType } from '@/services/transfer/types'
import {
  addPartyMemberTransfer,
  viewPartyMemberTransfer,
} from '@/services/transfer'
import { putPartyUserList } from '@/services/structure/organization-change-over'
import { getUserInfo } from '@/services/system/User'

interface Props {
  transferTypeProp?: Array<{ label: string; value: number }>
  type?: string
  id?: string
  parentId?: string | null
  parentName?: string | null
  level: number | null
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
  parentId: null,
  parentName: null,
  level: null,
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const baseUrl = import.meta.env.VITE_API_BASE

const tableColumns = computed(() =>
  approvalColumn((row) => {
    return row.approvalUserNameNodes.map((node) => {
      return h(
        'div',
        {
          class: 'h-[20px]',
        },
        {
          default: () => node.username,
        },
      )
    })
  }),
)

const formDataReactive = reactive<{ value: AddTransferItemType }>({
  value: {
    id: null,
    userId: null,
    transferType: null,
    oldDeptId: null,
    newDeptId: null,
    newDeptName: null,
    reason: null,
    phaseStatus: null,
    remark: null,
    paymentTime: null,
    letterId: null,
    proveId: null,
    transferTime: null,
    joinTime: null,
    userName: null,
    fileList: [],
    approvalNodes: [],
  },
})

const isViewModel = computed(() => props.type === 'view')
const formRef = ref<InstanceType<typeof NForm>>()

onBeforeMount(() => {
  if (props.type === 'view' && props.id) {
    viewPartyMemberTransfer(props.id).then((res) => {
      formDataReactive.value = res
      if (!formDataReactive.value.fileList) {
        formDataReactive.value.fileList = []
      }
      // 从详情接口取出的附件需要设置它的百分比为100
      if (formDataReactive.value.fileList.length) {
        formDataReactive.value.fileList.forEach((item) => {
          item.percentage = 100
          item.url = item.fileName
          item.name = item.original
        })
      }
    })
  }
})

// 树结构枚举
const treeData = ref<any[]>([])
// 获取组织结构
const getOrganizationTreeData = () => {
  return getOrganizationTree().then((res: any) => {
    treeData.value = res
  })
}
getOrganizationTreeData()
const calcTreeDataFn: any = (arr: any[]) => {
  return arr.map((item: any) => {
    return {
      ...item,
      children:
        item.children && item.children.length
          ? calcTreeDataFn(item.children)
          : undefined,
    }
  })
}
// 计算属性重置树结构的children
const calcTreeData = computed(() => {
  return calcTreeDataFn(treeData.value)
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (formDataReactive.value.id) {
        // 更新
        emits('saved')
      }
      else {
        // 新增操作
        // 介绍信id
        formDataReactive.value.letterId = formDataReactive.value.fileList
          ?.map(file => file.id)
          .join(',') as string
        addPartyMemberTransfer(formDataReactive.value).then(() => {
          window.$message.success('添加成功')
          emits('saved')
        })
      }
    }
  })
}

/** 获取组织ID */
const partyPersonObj = reactive<{
  partyPersonList: Array<{ label: string; value: string }>
}>({ partyPersonList: [] })

/** 返回党员列表 */
const PartyPersonListOptions = computed(() => partyPersonObj.partyPersonList)

function handleUpdateValue(v: string) {
  formDataReactive.value.userId = null
  formDataReactive.value.oldDeptId = v
  if (v === null) {
    return
  }
  putPartyUserList({
    id: v,
    pageNum: 1,
    pageSize: 99999999,
    username: '',
  }).then((res) => {
    partyPersonObj.partyPersonList = res.records.map((item: any) => {
      return {
        label: item.trueName,
        value: item.userId,
      }
    })
  })
}

/** 选择党员id */
const handleUpdatePartyPersonValue = (v: string, option: SelectOption) => {
  formDataReactive.value.userId = v
  formDataReactive.value.userName = option.label as string
  getUserInfo(v).then((res) => {
    formDataReactive.value.joinTime = res.joinTime
  })
}

// 当前组织操作
function getCheckedDataOfCurrentOrganization(
  value: string | number,
  option: TreeSelectOption | null,
) {
  handleUpdateValue(value as string)
  // formDataReactive.value.organizationName = option?.label as string
}

// 获取组织名称
function getCheckedData(
  value: string | number,
  option: TreeSelectOption | null,
) {
  // formDataReactive.value.organizationName = option?.label as string
}

// 转化data为树状结构
function transformTreeData(data: any) {
  if (Array.isArray(data)) {
    data.forEach((item) => {
      item.key = item.id
      item.label = item.name
      item.value = item.id

      if (item.children) {
        item.children.forEach((child: any) => {
          transformTreeData(child)
        })
      }
    })
  }
  else {
    data.key = data.id
    data.label = data.name
    data.value = data.id

    if (data.children) {
      data.children.forEach((child: any) => {
        transformTreeData(child)
      })
    }
  }
  return data
}

/**
 * 上传介绍信
 */
async function handleUpload(
  file: File,
  options: any,
  callBack: (data: any) => void,
) {
  const fileData = new FormData()
  const fileItem = options.file.file as Blob
  fileData.append('file', fileItem)
  const data: uploadFileItem = await uploadImg(fileData)
  callBack(data)
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    :disabled="true"
    :model="formDataReactive.value"
    :rules="formRules"
    label-align="right"
    label-placement="left"
    label-width="120"
    require-mark-placement="left"
    size="small"
  >
    <div class="h-[40px] border-b-[1px]">
      <span class="font-bold text-[16px]">基本信息</span>
    </div>
    <n-grid class="mt-[20px]">
      <n-form-item-gi label="调动类型：" path="transferType" span="12">
        <n-select
          v-model:value="formDataReactive.value.transferType"
          :options="transferTypeProp"
          clearable
          placeholder="请选择调动类型"
          size="medium"
        />
      </n-form-item-gi>
      <n-form-item-gi label="调动时间：" path="transferTime" span="12">
        <n-input
          v-if="isViewModel"
          v-model:value="formDataReactive.value.transferTime"
          placeholder="请选择调动时间"
        />
        <n-date-picker
          v-else
          v-model:formatted-value="formDataReactive.value.transferTime"
          clearable
          placeholder="请选择调动时间"
          style="width: 100%"
          type="datetime"
          @update:formatted-value="
            (v:any) => (formDataReactive.value.transferTime = v)
          "
        />
      </n-form-item-gi>

      <n-form-item-gi label="调动人选择：" path="userId" span="12">
        <n-input
          v-if="isViewModel"
          v-model:value="formDataReactive.value.userName"
          placeholder="请选择调动人员"
        />
        <n-select
          v-else
          v-model:value="formDataReactive.value.userId"
          :options="PartyPersonListOptions"
          placeholder="选择调动人员"
          @update:value="handleUpdatePartyPersonValue"
        />
      </n-form-item-gi>

      <n-form-item-gi label="入党时间：" path="joinTime" span="12">
        <n-input
          v-if="isViewModel"
          v-model:value="formDataReactive.value.joinTime"
          placeholder="请选择入党时间"
        />
        <n-date-picker
          v-else
          v-model:formatted-value="formDataReactive.value.joinTime"
          clearable
          disabled
          placeholder="请选择入党时间"
          style="width: 100%"
          type="datetime"
          @update:formatted-value="
            (v:any) => (formDataReactive.value.joinTime = v)
          "
        />
      </n-form-item-gi>
      <n-form-item-gi label="党费缴纳时间：" path="paymentTime" span="24">
        <n-input
          v-if="isViewModel"
          v-model:value="formDataReactive.value.paymentTime"
          placeholder="请选择党费缴纳时间"
        />
        <n-date-picker
          v-else
          v-model:formatted-value="formDataReactive.value.paymentTime"
          clearable
          placeholder="请选择党费缴纳时间"
          style="width: 100%"
          type="month"
          value-format="yyyy-MM"
          @update:formatted-value="
            (v:any) => (formDataReactive.value.paymentTime = v)
          "
        />
      </n-form-item-gi>
      <n-form-item-gi label="当前组织：" path="oldDeptId" span="24">
        <n-input
          v-if="isViewModel"
          v-model:value="formDataReactive.value.oldDeptName"
          placeholder="请选择当前组织"
        />
        <n-tree-select
          v-else
          v-model:value="formDataReactive.value.oldDeptId"
          :options="transformTreeData(calcTreeData)"
          clearable
          placeholder="请选择当前组织"
          size="small"
          @update:value="getCheckedDataOfCurrentOrganization"
        />
      </n-form-item-gi>

      <n-form-item-gi
        v-if="
          !(isViewModel && String(formDataReactive.value.transferType) === '调出系统外')
        "
        label="目标组织："
        path="newDeptId"
        span="24"
      >
        <n-input
          v-if="isViewModel"
          v-model:value="formDataReactive.value.newDeptName"
          placeholder="请选择目标组织"
        />
        <n-tree-select
          v-else
          v-model:value="formDataReactive.value.newDeptId"
          :options="transformTreeData(calcTreeData)"
          clearable
          placeholder="请选择目标组织"
          size="small"
          @update:value="getCheckedData"
        />
      </n-form-item-gi>
      <n-form-item-gi label="调动原因：" path="reason" span="24">
        <n-input
          v-model:value="formDataReactive.value.reason"
          maxlength="200"
          placeholder="请填写调动原因"
          rows="5"
          show-count
          type="textarea"
        />
      </n-form-item-gi>
      <n-form-item-gi label="介绍信：" path="letterId" span="24">
        <file-uploader-new
          v-if="formDataReactive.value.fileList?.length"
          v-model:original-file-list="formDataReactive.value.fileList"
          :is-readonly="true"
          :max="1"
          :size-limit="200"
          :upload-method="handleUpload"
          accept=".doc, .docx, .pdf"
          need-progress
        >
          <template #tips>
            <span class="tips">
              只可上传一个文件，支持扩展名：.doc，docx，.pdf，大小200M以内
            </span>
          </template>
        </file-uploader-new>
        <span v-else class="text-[12px]">暂无</span>
      </n-form-item-gi>
      <n-form-item-gi label="党费缴纳证明：" path="proveId" span="24">
        <n-image v-if="formDataReactive.value.proveId" :src="baseUrl + formDataReactive.value.proveId" />
        <span v-else class="text-[12px]">暂无</span>
      </n-form-item-gi>
    </n-grid>
    <div class="h-[40px] border-b-[1px]">
      <span class="font-bold text-[16px]">审批流程</span>
    </div>
    <div class="mt-[20px]">
      <n-data-table
        :bordered="false"
        :columns="tableColumns"
        :data="formDataReactive.value.approvalNodes"
        :pagination="false"
      />
    </div>
  </n-form>
</template>
<style lang="scss" scoped></style>
