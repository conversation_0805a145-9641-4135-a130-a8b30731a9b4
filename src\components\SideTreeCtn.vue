<!--
 * @Description: 侧边树容器
 * @Author: 朱备 <<EMAIL>>
 * @Date: 2021-11-19 11:24:45
 * @LastEditTime: 2022-01-10 17:09:52
 * @LastEditors: 朱备 <zhu<PERSON>@easestrategy.com>
-->
<template>
  <div class="side-tree">
    <div class="top">
      <span class="title">{{ treeTitle }}</span>
      <slot name="other" />
      <n-icon v-if="showRootBtn" size="20" @click="emits('add-root')">
        <plus-round />
      </n-icon>
    </div>

    <slot name="tree" />
  </div>
</template>

<script setup lang="ts">
import { NIcon } from 'naive-ui'
import { PlusRound } from '@vicons/material'

defineProps({
  treeTitle: {
    type: String,
    required: true,
  },
  showRootBtn: {
    type: Boolean,
    default: true,
  },
})

const emits = defineEmits(['add-root'])
</script>

<style lang="scss" scoped>
.side-tree {
  > .top {
    height: 20px;
    padding: 0 10px;
    margin-bottom: 21px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    > span:first-child {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
  }
}

.n-icon {
  cursor: pointer;
}
</style>
