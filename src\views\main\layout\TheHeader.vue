<script setup lang="ts">
import { ArrowDropDownFilled } from '@vicons/material'
import type { FormItemRule, FormRules } from 'naive-ui'
import { useAuthStore } from '@/store/auth/auth'
import { updatePassword } from '@/services/auth/auth'
import { encrypt } from '@/utils/jsencrypt'

const APP_NAME = import.meta.env.VITE_APP_NAME
const APP_LOGO = import.meta.env.VITE_APP_LOGO
const store = useAuthStore()
const userName = computed(() =>
  store.wholeUserInfo ? store.wholeUserInfo.username : '',
)

const passwordForm = ref<any>({
  oldPassWord: null,
  password: null,
  reenteredPassword: null,
})

function validatePasswordStartWith(rule: FormItemRule, value: string): boolean {
  return (
    !!passwordForm.value.password
    && passwordForm.value.password.startsWith(value)
    && passwordForm.value.password.length >= value.length
  )
}
function validatePasswordSame(rule: FormItemRule, value: string): boolean {
  return value === passwordForm.value.password
}

const passwordFormRules: FormRules = {
  username: {
    required: true,
    message: '请输入账号',
    trigger: 'blur',
  },
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    {
      min: 8,
      max: 20,
      message: '用户密码长度必须介于 8 和 20 之间',
      trigger: 'blur',
    },
  ],
  reenteredPassword: [
    {
      required: true,
      message: '请再次输入密码',
      trigger: ['input', 'blur'],
    },
    {
      validator: validatePasswordStartWith,
      message: '两次密码输入不一致',
      trigger: 'input',
    },
    {
      validator: validatePasswordSame,
      message: '两次密码输入不一致',
      trigger: ['blur', 'password-input'],
    },
  ],
}

// 修改密码弹框
const updatePasswordVisible = ref(false)

const passwordFormRef = ref()

// import { Alert16Regular } from '@vicons/fluent'
function logout() {
  store.handleUserLogout()
}

// 显示修改密码弹框
function showUpdatePassword() {
  updatePasswordVisible.value = true
}

// 关闭修改密码弹框
function handleCancelPassword() {
  updatePasswordVisible.value = true
}

// 确认修改密码
function handleConfirmPassword() {
  passwordFormRef.value?.validate((errors: any) => {
    if (!errors) {
      updatePassword({
        password: encrypt(passwordForm.value.oldPassWord),
        newPassword: encrypt(passwordForm.value.password),
      }).then(() => {
        window.$message.success('操作成功，请重新登录')
        setTimeout(() => {
          logout()
        }, 1500)
      })
    }
  })
}
</script>
<template>
  <div
    class="header-box flex justify-between items-center h-[60px] relative z-20 bg-[#fff] pl-[25px] pr-[24px]"
  >
    <div class="flex items-center">
      <img
        :src="APP_LOGO"
        alt="logo"
        class="w-[26px] h-[26px] mr-[13px] rounded-[50%]"
      />
      <span class="text-[16px] font-[500] text-[#333] leading-[22px]">{{
        APP_NAME
      }}</span>
    </div>
    <div class="flex items-center">
      <!-- <n-badge :value="6" :max="15" class="mr-[36px]">
        <n-icon size="25" color="#A0A9B4">
          <alert16-regular />
        </n-icon>
      </n-badge> -->

      <n-popover placement="bottom" trigger="click">
        <template #trigger>
          <div class="flex items-center cursor-pointer">
            <img
              :src="APP_LOGO"
              alt="头像"
              class="w-[26px] h-[26px] rounded-[50%] mr-[10px]"
            />
            <span class="text-box font-[400] text-[#333]">{{ userName }}</span>
            <n-icon color="#7E7E7E" size="16">
              <arrow-drop-down-filled />
            </n-icon>
          </div>
        </template>
        <div>
          <n-button text @click="logout">
            退出
          </n-button>
        </div>
        <template #footer>
          <div>
            <n-button text @click="showUpdatePassword">
              修改密码
            </n-button>
          </div>
        </template>
      </n-popover>
    </div>
  </div>

  <!-- 修改密码 -->
  <CustomDialog
    v-model:show="updatePasswordVisible"
    title="修改密码"
    @cancel="handleCancelPassword"
    @confirm="handleConfirmPassword"
  >
    <div class="password-container">
      <n-form
        ref="passwordFormRef"
        :label-width="100"
        require-mark-placement="left"
        label-placement="left"
        :model="passwordForm"
        :rules="passwordFormRules"
      >
        <n-form-item label="旧密码:" path="oldPassWord">
          <n-input
            v-model:value="passwordForm.oldPassWord"
            placeholder="请输入旧密码"
            type="password"
            style="width: 300px"
          />
        </n-form-item>
        <n-form-item label="新密码:" path="password">
          <n-input
            v-model:value="passwordForm.password"
            placeholder="请输入新密码"
            type="password"
            style="width: 300px"
          />
        </n-form-item>
        <n-form-item
          ref="rPasswordFormItemRef"
          label="再次确认密码:"
          path="reenteredPassword"
          first
        >
          <n-input
            v-model:value="passwordForm.reenteredPassword"
            placeholder="请再次确认密码"
            type="password"
            style="width: 300px"
          />
        </n-form-item>
      </n-form>
    </div>
  </CustomDialog>
</template>
<style lang="scss" scoped>
.header-box {
  box-shadow: 0px 3px 5px 0px rgba(182, 191, 218, 0.2),
    0px 2px 18px 0px rgba(135, 151, 173, 0.1);
}
.text-box {
  text-shadow: 0px 3px 5px rgba(182, 191, 218, 0.25);
}
.password-container {
  padding: 20px;
}
</style>
