<!-- 缴纳记录 -->
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="缴费记录"
    :show-other="showDetail"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
  >
    <template #btns>
      <button class="export-btn" @click="handleExport">
        <n-icon>
          <Export class="mt-[1px]" />
        </n-icon>
        导出
      </button>
    </template>
    <template #filters>
      <div class="filter-operate">
        <n-date-picker
          v-model:value="monthDate"
          class="custom-date"
          type="month"
          clearable
          placeholder="请选择月份"
          @change="handleMonthChange"
        />
        <n-tree-select
          v-model:value="filterReactive.unitId"
          :options="transformTreeData(organizeData)"
          placeholder="请选择所在组织"
          value-field="deptId"
          key-field="deptId"
          clearable
        />
        <n-input
          v-model:value="filterReactive.orderCode"
          class="custom-input"
          size="small"
          placeholder="请输入订单号"
          clearable
        />
        <n-select
          v-model:value="filterReactive.payStatus"
          class="custom-select"
          :options="paymentStatusOption"
          size="small"
          placeholder="请选择缴纳状态"
          clearable
        />
        <n-input
          v-model:value="filterReactive.userName"
          class="custom-input"
          size="small"
          placeholder="请输入姓名"
          clearable
        />
      </div>
    </template>
    <template #others>
      <div class="import-tips">
        <div class="import-label ml-[13px]">
          <div class="ml-[4px] mt-[2px]">
            i
          </div>
        </div>
        <div class="ml-[6px]">
          当前已选数据：订单号
          {{ detailData.orderCode }}，姓名：{{
            detailData.userName
          }}，手机号码：{{ detailData.userPhone }}，所在组织：{{
            detailData.unitName
          }}，月份：{{ detailData?.returnTime.slice(0, 7) }}，应缴金额：{{
            detailData.totalAmount
          }}元，缴纳状态：{{ transformPayStatus(detailData) }}，支付方式：
          {{ transformPayment(detailData) }}
        </div>
      </div>
    </template>
  </table-container>
</template>

<script setup lang="ts">
import { h, onMounted, ref } from 'vue'
import { NButton, NIcon } from 'naive-ui'
import { Export } from '@vicons/carbon'
import { PAYSTATUS, PAYTYPESTATUS, getPayRecordColumns } from '../config'
import { exportRecordList, getRecordList } from '@/services/pay-manage/index'
import { transformTreeData } from '@/utils/transform'
import { getCurrentPartyOrganizationByUser } from '@/services/data-permission'
import { useMyTable } from '@/hooks'
import { downloadArrayBuffer } from '@/utils/downloader'

const monthDate = ref(null)

// 查询条件
const filterReactive = ref({
  monthDate: null,
  unitId: '',
  orderCode: '',
  payStatus: null,
  userName: '',
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getRecordList, filterReactive, {
  // batchDeleteTable: true,
  // delApi: delTalkAboutTableListItem,
})

const showDetail = ref(false)

// 组织数据
const organizeData = ref([])

// 缴纳状态
// 支付状态（1-未支付 2-已支付）
const paymentStatusOption = ref([
  {
    label: '未支付',
    value: 1,
  },
  {
    label: '已支付',
    value: 2,
  },
])

/** 获取列表 */
const getOrganizeList = () => {
  getCurrentPartyOrganizationByUser().then((res: any) => {
    organizeData.value = res || []
  })
}

const detailData = ref({
  orderCode: '',
  userName: '',
  unitName: '',
  returnTime: '',
  userPhone: '',
  totalAmount: '',
})

// 修改和删除按钮渲染
const tableColumns = getPayRecordColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          detailData.value = row
          showDetail.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '详情',
      },
    ),
  ]
})

function handleExport() {
  loading.value = true
  exportRecordList({ ...filterReactive.value })
    .then((res: any) => {
      downloadArrayBuffer(
        res,
        '缴费记录导出.xlsx',
        'application/octet-stream; charset=UTF-8',
      )
    })
    .finally(() => {
      loading.value = false
    })
}

function handleMonthChange(value: string | number) {
  if (value === null) {
    filterReactive.value.monthDate = null
    return
  }
  const date = new Date(value)
  const year = date.getFullYear()
  const month = `0${date.getMonth() + 1}`.slice(-2) // 月份需要加1，且保证两位数显示
  const day = `0${date.getDate()}`.slice(-2) // 保证日期显示两位数
  filterReactive.value.monthDate = `${year}-${month}-${day} 00:00:00`
}

// 支付方式转换
function transformPayment(row: any) {
  if (row.paymentType === PAYTYPESTATUS.WECHAT) {
    return '微信'
  } else if (row.paymentType === PAYTYPESTATUS.ALI) {
    return '支付宝'
  } else if (row.paymentType === PAYTYPESTATUS.BANK_WECHAT) {
    return '工商银行（微信）'
  } else if (row.paymentType === PAYTYPESTATUS.BANK_ALI) {
    return '工商银行（支付宝）'
  } else {
    return ''
  }
}

// 缴纳状态转换
function transformPayStatus(row: any) {
  if (row.payStatus === PAYSTATUS.NOT) {
    return '未支付'
  } else if (row.payStatus === PAYSTATUS.PASS) {
    return '已支付'
  } else {
    return ''
  }
}

watch(filterReactive.value, () => {
  loadData()
})

onMounted(() => {
  loadData()
  getOrganizeList()
})
</script>

<style lang="scss" scoped>
.export-btn {
  width: 62px;
  height: 28px;
  background: #ffffff;
  border-radius: 3px;
  border: 1px solid #d8d9da;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
}

:deep(.n-date-picker) {
  width: 115px !important;
  height: 28px;
}

:deep(.n-input) {
  // width: 184px !important;
  height: 28px;
}

.import-tips {
  width: 100%;
  height: 36px;
  background: #fffae1;
  border-radius: 3px;
  border: 1px solid #f5dd8c;
  font-size: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #7c5125;
  display: flex;
  align-items: center;
  margin-top: 28px;
  margin-bottom: 20px;
}

.import-label {
  width: 12px;
  height: 12px;
  background: #fdae35;
  border-radius: 50%;
  color: #fff;
  display: flex;
  align-items: center;
  font-style: oblique;
}

:deep(.n-date-picker) {
  width: 115px !important;
  height: 28px;
}

.filter-operate {
  @apply flex gap-x-1;
  width: 930px;
}
</style>
