import { type VNodeChild } from 'vue'
import { NCollapse, NCollapseItem, NEllipsis, NIcon, NImage } from 'naive-ui'
import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import { KeyboardArrowDownSharp } from '@vicons/material'
import type { PublicListItem } from '@/services/party-public/types'
import { downloadFile } from '@/utils/downloader'

function truncatedText(str: string) {
  const cleanedText = str.replace(/<.*?>/g, '')
  return cleanedText.slice(0, 20)
}
export function getTableColumns(
  operationRender: (row: PublicListItem) => VNodeChild,
): TableColumns<PublicListItem> {
  return [
    {
      type: 'selection',
      align: 'center',
    },
    {
      title: '附件名称',
      key: 'annexName',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      title: '封面图',
      key: 'pictureUrl',
      render: row =>
        row.pictureUrl
          ? h(NImage, {
            src: import.meta.env.VITE_API_BASE + row.pictureUrl,
            width: '62',
            style: { height: '40px' },
          })
          : '--',
    },
    {
      title: '附件描述',
      key: 'annexDescribe',
      render: (row) => {
        return h(NEllipsis, {
          style: {
            maxWidth: '200px',
          },
          tooltip: true,
        }, {
          default: () => {
            return h('span', {
              innerHTML: `${truncatedText(row.annexDescribe)}`,
            })
          },
          tooltip: () => {
            return h('div', {
              style: {
                width: '400px',
              },
              innerHTML: row.annexDescribe,
            })
          },
        })
      },
    },
    {
      key: 'fileList',
      title: '附件',
      width: '19%',
      render: (row) => {
        return row.fileList?.length
          ? h(
            NCollapse,
            {
              arrowPlacement: 'right',
            },
            [
              h(
                NCollapseItem,
                {
                  disabled: row.fileList.length === 1,
                },
                {
                  header: () =>
                    h(
                      'div',
                      {
                        style: {
                          marginBottom: '2px',
                          cursor: 'pointer',
                          color: '#3f7ee8',
                        },
                      },
                      h(
                        'span',
                        {
                          onClick: (e: Event) => {
                            downloadFile(
                              row.fileList?.[0].fileName,
                              row.fileList?.[0].original,
                            )
                            e.stopPropagation()
                          },
                        },
                        row.fileList?.[0].original,
                      ),
                    ),
                  arrow: () =>
                    h(
                      NIcon,
                      row.fileList?.length === 1
                        ? ''
                        : () => h(KeyboardArrowDownSharp),
                    ),
                  default: () =>
                    row.fileList?.slice(1)
                      && row.fileList?.slice(1).map((item) => {
                        return h(
                          'div',
                          {
                            style: {
                              marginBottom: '2px',
                              cursor: 'pointer',
                              color: '#3f7ee8',
                            },
                          },
                          h(
                            'span',
                            {
                              onClick: (e: Event) => {
                                downloadFile(item.fileName, item.original)
                                e.stopPropagation()
                              },
                            },
                            item?.original,
                          ),
                        )
                      }),
                },
              ),
            ],
          )
          : h('span', {}, { default: () => '--' })
      },
    },
    {
      title: '最后更新时间',
      key: 'updateTime',
      render: row => row.updateTime ?? '-',
    },
    {
      title: '操作',
      key: 'operation',
      width: '15%',
      render: operationRender,
    },
  ]
}
