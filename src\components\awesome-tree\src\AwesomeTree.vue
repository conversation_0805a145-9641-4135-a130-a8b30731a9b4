<!--
 * @Description: 树组件
 * @Author: 朱备 <zhu<PERSON>@easestrategy.com>
 * @Date: 2021-11-18 17:37:41
 * @LastEditTime: 2021-12-10 17:37:06
 * @LastEditors: 朱备 <<EMAIL>>
-->
<template>
  <div
    v-if="showFilter"
    :class="{ pr: maxHeight.length > 0 }"
    style="margin-bottom: 16px"
  >
    <n-input
      v-model:value="patternRef"
      size="small"
      :placeholder="props.filterPlaceholder"
    >
      <template #prefix>
        <n-icon>
          <search-round />
        </n-icon>
      </template>
    </n-input>
  </div>

  <n-scrollbar
    :style="`max-height: ${maxHeight}; ${
      maxHeight.length > 0 ? 'box-sizing: border-box;padding-right: 20px;' : ''
    }`"
    :class="{ pr: maxHeight.length > 0 }"
  >
    <n-tree
      default-expand-all
      :selectable="selectable"
      :data="treeOptionsRef"
      :pattern="patternRef"
      :render-suffix="suffixRenderer"
      :render-label="labelRenderer"
      :expanded-keys="expandedKeysRef"
      :selected-keys="selectedKeysRef"
      @update-expanded-keys="handleExpandedKeysChange"
      @update-selected-keys="handleSelectedKeysChange"
    />
  </n-scrollbar>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { h, nextTick, ref, watch } from 'vue'
import type { TreeOption } from 'naive-ui'
import {
  NButton,
  NEllipsis,
  NIcon,
  NInput,
  NPopselect,
  NScrollbar,
  NTree,
} from 'naive-ui'
import {
  CheckRound,
  CloseRound,
  MoreVertRound,
  SearchRound,
} from '@vicons/material'
import type { ITreeNode } from '..'
import { transformToTreeOptions } from './utils'
import uuid from './uuid'

const props = defineProps({
  maxLevel: {
    type: [Number, String] as PropType<number | 'infinite'>,
    required: true,
  },
  treeData: {
    type: Array as PropType<ITreeNode[]>,
    default: () => [],
  },
  customAdd: {
    type: Boolean,
    default: false,
  },
  canMove: {
    type: Boolean,
    default: false,
  },
  selectable: {
    type: Boolean,
    default: true,
  },
  showFilter: {
    type: Boolean,
    default: true,
  },
  filterPlaceholder: {
    type: String,
    default: '搜索',
  },
  leafOnly: {
    type: Boolean,
    default: false,
  },
  maxHeight: {
    type: String,
    default: '',
  },
  canModify: {
    type: Boolean,
    default: true,
  },
})
const emits = defineEmits([
  'select-node',
  'save-node',
  'delete-node',
  'clear-current',
  'custom-add',
  'custom-modify',
  'up',
  'down',
])

const editingValueRef = ref('')
const labelInputRef = ref<InstanceType<typeof NInput>>()
const treeOptionsRef = ref<TreeOption[]>([])
const expandedKeysRef = ref<Array<string | number>>([])
const renderIdRef = ref('')
const addDisabledRef = ref(false)
const patternRef = ref('')
const selectedKeysRef = ref()

// 获取后缀下拉选项
function getSuffixOptions(option: TreeOption) {
  let options = [
    { label: '上移', value: 'up', disabled: !option.canUp },
    { label: '下移', value: 'down', disabled: !option.canDown },
    // { label: '移到顶部', value: 'top' },
    // { label: '移到底部', value: 'bottom' },
    { label: props.customAdd ? '编辑' : '重命名', value: 'edit' },
    { label: '删除', value: 'delete' },
    { label: '添加子级', value: 'add-child' },
  ]
  if (!props.canMove) {
    options = options.slice(2, 5)
  }
  if (
    props.maxLevel !== 'infinite'
    && (option.level as number) >= props.maxLevel
  ) {
    options.pop()
  }
  return options
}
// 取消新增，根据renderId删除刚添加的节点
function cancelAdd(renderId: string, treeOptions: TreeOption[]) {
  const index = treeOptions.findIndex(item => item.renderId === renderId)
  if (index < 0) {
    treeOptions.forEach((sub) => {
      if (sub.children) {
        cancelAdd(renderId, sub.children)
      }
    })
  } else {
    treeOptions.splice(index, 1)
  }
}

// 判断一个节点是否是当前选中节点的父辈
function isParent(node: TreeOption) {
  if (node.children?.length) {
    const targetIndex = node.children?.findIndex(
      // eslint-disable-next-line eqeqeq
      child => child.key == selectedKeysRef.value[0],
    )
    if (targetIndex > -1) {
      return true
    } else {
      for (const child of node.children) {
        isParent(child)
      }
    }
  }
}

// 树节点内容渲染函数
function labelRenderer(info: {
  option: TreeOption
  checked: boolean
  selected: boolean
}) {
  if (info.option.editing) {
    return h(
      'span',
      {
        style: 'display: flex; align-items: center',
      },
      [
        h(NInput, {
          ref: labelInputRef,
          size: 'small',
          value: editingValueRef.value,
          onUpdateValue: v => (editingValueRef.value = v),
          onKeyup: (e) => {
            if (e.key === 'Enter') {
              const label = editingValueRef.value.trim()
              if (label) {
                saveTreeNode({
                  key: info.option.key,
                  label,
                  pid: info.option.pid,
                })
              } else {
                window.$message.warning('请输入名称')
                return false
              }
            }
          },
        }),
        h(
          NButton,
          {
            text: true,
            type: 'primary',
            style: 'margin-left: 8px',
            onClick: () => {
              const label = editingValueRef.value.trim()
              if (label) {
                saveTreeNode({
                  key: info.option.key,
                  label,
                  pid: info.option.pid,
                })
              } else {
                window.$message.warning('请输入名称')
                return false
              }
            },
          },
          {
            default: () =>
              h(
                NIcon,
                {
                  size: 22,
                },
                { default: () => h(CheckRound) },
              ),
          },
        ),
        h(
          NButton,
          {
            text: true,
            type: 'primary',
            style: 'margin-left: 8px',
            onClick: () => {
              info.option.editing = false
              // 如果是新增的，取消新增
              if (info.option.renderId) {
                cancelAdd(info.option.renderId as string, treeOptionsRef.value)
              }
              addDisabledRef.value = false
            },
          },
          {
            default: () =>
              h(
                NIcon,
                {
                  size: 22,
                },
                { default: () => h(CloseRound) },
              ),
          },
        ),
      ],
    )
  } else {
    return h(
      NEllipsis,
      { style: 'max-width: 130px' },
      { default: () => info.option.label },
    )
  }
}
// 节点后缀渲染函数
function suffixRenderer(info: {
  option: TreeOption
  checked: boolean
  selected: boolean
}) {
  if (info.option.editing) {
    return null
  }
  return props.canModify
    ? h(
      'div',
      { onClick: handleClickSuffix },
      h(
        NPopselect,
        {
          trigger: 'click',
          placement: 'bottom-start',
          options: getSuffixOptions(info.option),
          'onUpdate:value': v => handleSelectValueChange(v, info.option),
        },
        {
          default: () =>
            h(
              NIcon,
              { size: 20, color: '#999' },
              { default: () => h(MoreVertRound) },
            ),
        },
      ),
    )
    : null
}
// 点击节点后缀图标
function handleClickSuffix(e: Event) {
  e.stopPropagation()
}
// 下拉选择选中
function handleSelectValueChange(
  value: 'up' | 'down' | 'top' | 'bottom' | 'edit' | 'delete' | 'add-child',
  node: TreeOption,
) {
  switch (value) {
    case 'up':
      emits('up', node.key)
      break
    case 'down':
      emits('down', node.key)
      break
    case 'edit':
      // 重命名
      if (props.customAdd) {
        emits('custom-modify', node)
      } else {
        node.editing = true
        editingValueRef.value = node.label ?? ''
        nextTick(() => {
          labelInputRef.value?.focus()
        })
      }

      break
    case 'delete':
      // 删除
      window.$dialog.warning({
        title: '提示',
        content: '确定删除吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          emits('delete-node', node.key)
          // 判断是否需要清空当前选中节点：
          if (selectedKeysRef.value) {
            // 1.删除的节点是选中节点
            if (selectedKeysRef.value[0] === node.key) {
              emits('clear-current')
            } else {
              // 2.删除的节点是选中节点的祖先节点
              if (isParent(node)) {
                emits('clear-current')
              }
            }
          }
        },
      })
      break
    case 'add-child':
      if (addDisabledRef.value) {
        window.$message.warning('存在未保存的项')
        return
      }
      addDisabledRef.value = true
      if (!node.children) {
        node.children = []
      }
      expandedKeysRef.value.push(node.key!)
      renderIdRef.value = uuid()
      editingValueRef.value = ''
      node.children.unshift({
        editing: true,
        pid: node.key,
        renderId: renderIdRef.value,
      })
      nextTick(() => {
        labelInputRef.value?.focus()
      })
      break
    default:
  }
}
// 保存树节点
function saveTreeNode(node: TreeOption) {
  node.editing = false
  addDisabledRef.value = false
  emits('save-node', node)
}

// 添加根节点
function addRoot() {
  if (props.customAdd) {
    emits('custom-add')
    return
  }
  if (addDisabledRef.value) {
    window.$message.warning('存在未保存的项')
    return
  }
  addDisabledRef.value = true
  renderIdRef.value = uuid()
  editingValueRef.value = ''
  treeOptionsRef.value.unshift({
    editing: true,
    pid: 0,
    renderId: renderIdRef.value,
  })
  nextTick(() => {
    labelInputRef.value?.focus()
  })
}

// 树展开节点改变
function handleExpandedKeysChange(keys: Array<string | number>) {
  expandedKeysRef.value = keys
}

// 树节点选中
function handleSelectedKeysChange(
  keys: Array<string | number>,
  option: Array<TreeOption | null>,
) {
  if (option[0] && !option[0].editing) {
    selectedKeysRef.value = keys
    emits('select-node', option)
  }
}

// 设置选中的节点
function setSelectedKeys(keys: number[]) {
  selectedKeysRef.value = keys
}

// 设置展开的节点
function setExpandedKeys(keys: number[]) {
  expandedKeysRef.value = keys
}

watch(props, (newV) => {
  if (newV.treeData) {
    treeOptionsRef.value = transformToTreeOptions(
      newV.treeData,
      1,
      props.leafOnly,
      props.maxLevel,
    )
  }
})

defineExpose({
  addRoot,
  setSelectedKeys,
  setExpandedKeys,
})
</script>

<style lang="scss" scoped>
:deep(.n-tree-node-wrapper) {
  border-radius: 3px;

  .n-tree-node {
    display: flex;
    align-items: center;
    transition: none;

    &.n-tree-node--selected {
      background-color: #e4e8f0;
    }

    .n-tree-node-content {
      width: 100%;
      height: 40px;
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      cursor: pointer;
      transition: none;

      .n-tree-node-content__suffix {
        opacity: 0;
        height: 20px;
      }

      &:hover {
        .n-tree-node-content__suffix {
          opacity: 1;
        }
      }
    }
  }
}

.pr {
  box-sizing: border-box;
  padding-right: 20px;
}
</style>
