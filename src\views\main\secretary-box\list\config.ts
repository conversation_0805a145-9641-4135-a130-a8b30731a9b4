import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'

export function getTableColumns(
  optionColumnRenderer: (row: any) => VNodeChild,
): DataTableColumns<any> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '50',
      render: (_, i) => i + 1,
    },
    {
      key: 'deptName',
      title: '所属组织',
      render: row => row.deptName ?? '--',
    },
    {
      key: 'suggesterName',
      title: '姓名',
    },

    {
      key: 'suggest',
      title: '提问内容',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'suggestTime',
      title: '提问时间',
    },
    {
      key: 'replied',
      title: '是否已回复',
      width: '150',
      render: row => (row.replied ? '是' : '否'),
    },
    {
      key: 'reply',
      title: '书记回复内容',
      render: row => row.reply ?? '--',
    },
    {
      key: 'replyTime',
      title: '回复时间',
    },

    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '116',
      render: row => optionColumnRenderer(row),
    },
  ]
}
