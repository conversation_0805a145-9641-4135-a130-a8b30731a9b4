<script setup lang="ts">
import TheHeader from './layout/TheHeader.vue'
import TheTabs from './layout/TheTabs.vue'
import { emitter } from '@/utils/event-bus'

const myScrollTop = ref(0)
const handleScroll = (e: any) => {
  myScrollTop.value = e.target.scrollTop
}
watch(
  () => myScrollTop.value,
  (newV) => {
    emitter.emit('my-scroller', newV)
  },
)
</script>
<template>
  <div>
    <n-layout class="layout">
      <n-layout-header class="layout-header">
        <the-header />
        <the-tabs />
      </n-layout-header>
      <n-layout-content class="layout-content" @scroll="handleScroll">
        <router-view />
        <n-back-top :right="50" :visibility-height="150" :bottom="50" />
      </n-layout-content>
    </n-layout>
  </div>
</template>
<style lang="scss" scoped>
.layout {
  height: 100%;
}
.layout-header {
  user-select: none;
  position: relative;
  z-index: 10;
}

.layout-content {
  height: calc(100vh - 114px);
  background: #ffffff;
}
</style>
