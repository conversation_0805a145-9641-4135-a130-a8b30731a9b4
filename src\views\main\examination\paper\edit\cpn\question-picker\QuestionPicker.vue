<!--
 * @Description: 选题组件
-->
<template>
  <custom-dialog
    title="从题库选题"
    :show="show"
    width="80vw"
    @update:show="(v: any) => emits('update:show', v)"
  >
    <n-spin :show="loadingRef">
      <div class="filter">
        <!-- 题型 -->
        <div class="item">
          <span class="label">题型：</span>
          <n-checkbox-group
            v-model:value="filterReactive.typeList"
            size="small"
          >
            <n-checkbox
              v-for="item in questionTypes"
              :key="item.label"
              style="width: 95px"
              :label="item.label"
              :value="item.value"
            />
          </n-checkbox-group>
        </div>
        <!-- 难度 -->
        <div class="item">
          <span class="label">难度：</span>
          <n-checkbox-group
            v-model:value="filterReactive.levelList"
            size="small"
          >
            <n-checkbox
              v-for="item in difficultyOptions"
              :key="item.label"
              style="width: 95px"
              :label="item.label"
              :value="item.value"
            />
          </n-checkbox-group>
        </div>

        <div class="divider" />

        <!-- 所属题库 -->
        <div class="item">
          <span class="label">所属题库：</span>
          <n-radio-group v-model:value="filterReactive.libraryId" size="small">
            <n-radio
              v-for="item in libraryOptions"
              :key="item.label"
              style="margin-right: 40px"
              :value="item.value"
            >
              {{ item.label }}
            </n-radio>
          </n-radio-group>
        </div>
        <!-- 题目标签 -->
        <div class="item mt-[30px]">
          <span class="label">题目标签：</span>
          <n-checkbox-group
            v-model:value="filterReactive.labelList"
            size="small"
          >
            <n-checkbox
              v-for="item in labelOptions"
              :key="item.label"
              style="margin-right: 30px"
              :value="item.value"
              :label="item.label"
            />
          </n-checkbox-group>
        </div>

        <div class="divider" />

        <!-- 题干搜索 -->
        <div class="item">
          <span class="label" style="line-height: 28px">题干搜索：</span>
          <n-input
            v-model:value="questionNameRef"
            style="height: 28px; width: 240px"
            size="small"
            clearable
            @input="handleQuestionNameChange"
            @clear="handleClearQuestionName"
          >
            <template #prefix>
              <n-icon style="cursor: pointer" @click="handleQuestionNameChange">
                <search />
              </n-icon>
            </template>
          </n-input>
        </div>
      </div>

      <n-data-table
        class="table"
        size="small"
        :bordered="false"
        :row-key="(row: any) => row.id"
        :columns="tableColumns"
        :data="filteredQuestionList"
        :default-checked-row-keys="checkedList.map((item) => item.id)"
        @update-checked-row-keys="handleCheckedChange"
      />

      <n-pagination
        v-if="totalRef > 0"
        v-model:page="pageNoRef"
        style="margin-top: 15px; font-size: 12px; justify-content: flex-end"
        :item-count="totalRef"
      />
    </n-spin>

    <template #action>
      <div class="bottom">
        <div class="left">
          已选择 <span style="color: #006fff">{{ checkedList.length }}</span> 题
        </div>
        <div class="right">
          <n-button type="primary" @click="emits('update:show', false)">
            确定
          </n-button>
        </div>
      </div>
    </template>
  </custom-dialog>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { computed, reactive, ref, watch } from 'vue'
import {
  NButton,
  NCheckbox,
  NCheckboxGroup,
  NDataTable,
  NIcon,
  NInput,
  NPagination,
  NRadio,
  NRadioGroup,
  NSpin,
} from 'naive-ui'
import { Search } from '@vicons/carbon'
import { tableColumns } from './config'
import {
  difficultyOptions,
  questionTypes,
} from '@/views/main/examination/config'
import useExaminationStore from '@/store/examination/index'
import type { IFilteredExaminationQuestion } from '@/services/examination/index'

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  checkedList: {
    type: Array as PropType<IFilteredExaminationQuestion[]>,
    required: true,
  },
})
const emits = defineEmits(['update:show', 'update:checkedList'])

const examinationStore = useExaminationStore()

const libraryOptions = computed(() =>
  examinationStore.examinationLibraryList.map(item => ({
    value: item.libraryId,
    label: item.libraryName,
  })),
)
const labelOptions = computed(() =>
  examinationStore.examinationLibraryLabelList
    .filter(item => item?.name)
    .map(item => ({
      value: item.id,
      label: item.name,
    })),
)
const filteredQuestionList = computed(
  () => examinationStore.filteredExaminationQuestionList,
)
const questionNameRef = ref('')
const filterReactive = reactive({
  labelList: [],
  levelList: [],
  libraryId: 0,
  typeList: [],
  name: '',
  pageSize: 10,
})
const pageNoRef = ref(0)
const loadingRef = ref(false)
const totalRef = ref(0)

examinationStore.getExaminationLibraryLabelListAction()
examinationStore
  .getExaminationLibraryListAction({
    pageNo: 1,
    pageSize: 99999,
  })
  .then((res) => {
    filterReactive.libraryId = res.firstLibraryId
  })

// 加载题目列表
function loadFilteredQuestionList() {
  if (!loadingRef.value) {
    loadingRef.value = true
    examinationStore
      .getFilteredExaminationQuestionListAction({
        ...filterReactive,
        pageNo: pageNoRef.value,
      })
      .then((res) => {
        totalRef.value = Number(res)
        loadingRef.value = false
      })
  }
}
// 题干搜索
function handleQuestionNameChange() {
  filterReactive.name = questionNameRef.value
}
// 题干重置
function handleClearQuestionName() {
  questionNameRef.value = ''
  handleQuestionNameChange()
}

// 选中改变
function handleCheckedChange(keys: Array<string | number>) {
  const questionMap = new Map<number, IFilteredExaminationQuestion>()
  props.checkedList.forEach((item) => {
    questionMap.set(item.id, item)
  })
  filteredQuestionList.value
    .filter(item => keys.includes(item.id))
    .forEach((item) => {
      questionMap.set(item.id, item)
    })

  // 删除取消选中的题目
  const allKeys = [...questionMap.keys()]
  const allQuestions = [...questionMap.values()]
  const indexesToDelete: number[] = [] // 需要删除的索引
  allKeys.forEach((item, index) => {
    if (!keys.includes(item)) {
      indexesToDelete.push(index)
    }
  })

  const resultArr: IFilteredExaminationQuestion[] = []
  allQuestions.forEach((item, index) => {
    if (!indexesToDelete.includes(index)) {
      resultArr.push(item)
    }
  })

  emits('update:checkedList', resultArr)
}

watch(filterReactive, () => {
  pageNoRef.value = 1
  loadFilteredQuestionList()
})

watch(pageNoRef, () => {
  loadFilteredQuestionList()
})
</script>

<style lang="scss" scoped>
.filter {
  padding: 13px 19px;
  background: #f5f6f8;

  > .item {
    height: 16px;
    line-height: 16px;
    margin-bottom: 14px;
    display: flex;

    > .label {
      width: 115px;
      font-size: 12px;
      font-weight: 400;
      color: #999999;
    }
  }

  > .divider {
    height: 0px;
    border: 1px dashed #e7e8e9;
    margin-bottom: 12px;
  }
}

.table {
  margin-top: 13px;
}

.bottom {
  width: 100%;
  height: 60px;
  background: #fcfdfe;
  display: flex;
  justify-content: space-between !important;
  align-items: center;
  padding: 0 29px;
  border-top: 1px solid #f2f3f6;

  > .right {
    > .n-button {
      width: 64px;
      height: 30px;
      border-radius: 15px;

      &:nth-child(n + 2) {
        margin-left: 8px;
      }
    }
  }
}

:deep(.n-spin-content) {
  width: 94%;
  margin-left: 3%;
}
</style>
