<script setup lang="ts">
import ChangeOrganize from '../cpn/ChangeOrganize.vue'
import { formRules } from './config'
import type { OrganizeBrandFormParams } from '@/services/organize-garden/types'
import { uploadImg } from '@/services'
import type { uploadFileItem } from '@/services/types'
import { useOrganizeGardenStore } from '@/store/organize-garden'
import {
  getOrganizeBrand,
  postOrganizeBrand,
  putOrganizeBrand,
} from '@/services/organize-garden'

const formDataReactive = reactive<OrganizeBrandFormParams>({
  id: '',
  orgId: '',
  brandName: '',
  coverUrl: '',
  brandSummary: '',
  existedData: '',
  logoDefinition: '',
  brandConnotation: '',
  featuresAchievements: '',
})

const currentOrgId = ref('')
const currentId = ref('')

const { getDeptId } = useOrganizeGardenStore()
watch(
  getDeptId,
  async(value) => {
    if (value) {
      currentOrgId.value = value
      await handleOrgSummaryByDeptId(value)
      formDataReactive.orgId = value
    }
  },
  {
    immediate: true,
  },
)

/**
 * 根据部门id获取党建品牌内容
 * @param {string} deptId
 */
async function handleOrgSummaryByDeptId(deptId: string) {
  const res = await getOrganizeBrand(deptId)
  for (const key in formDataReactive) {
    formDataReactive[key] = res[key]
    if (key === 'id') {
      currentId.value = (res.id as string) || ''
    }
  }
  // formDataReactive.summary = res.summary
}

/**
 * 上传图片
 * @param {any} file:File
 */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (
      formDataReactive.coverUrl === ''
      || formDataReactive.coverUrl === null
    ) {
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formDataReactive.coverUrl = data.url
      }
    }
  }
  catch (error) {}
}
/**
 * 删除图片
 */
function handleCoverDelete() {
  formDataReactive.coverUrl = ''
}

const brandFormRef = ref()
// 重置-使用 keyof 获取对象的键类型并迭代
function handleReset() {
  for (const key of Object.keys(
    formDataReactive,
  ) as (keyof OrganizeBrandFormParams)[]) {
    if (key === 'id') {
      formDataReactive[key] = currentId.value
    }
    else if (key === 'orgId') {
      formDataReactive[key] = currentOrgId.value
    }
    else {
      formDataReactive[key] = ''
    }
  }
  brandFormRef.value?.restoreValidation()
}

/** 保存 */
function handleConfirm() {
  brandFormRef.value?.validate((errors: any) => {
    if (!errors) {
      const data = {
        ...formDataReactive,
      }
      const useApi = formDataReactive.id ? putOrganizeBrand : postOrganizeBrand
      useApi(data).then(async(res) => {
        window.$message.success('保存成功')
        await handleOrgSummaryByDeptId(String(formDataReactive.orgId))
      })
    }
  })
}

// 重置校验状态
function changeOrg(orgId: string) {
  brandFormRef.value.restoreValidation()
}
</script>
<template>
  <div class="w-full px-[20px] py-[20px]">
    <ChangeOrganize @change-org="changeOrg" />
    <div class="py-[25px]">
      <span class="text-[14px] font-[600] text-[#333] leading-[20px] mb-[26px]">党建品牌</span>
    </div>
    <n-form
      ref="brandFormRef"
      :model="formDataReactive"
      :rules="formRules"
      require-mark-placement="left"
      label-placement="left"
      label-width="160px"
    >
      <n-form-item label="品牌名称：" path="brandName">
        <n-input
          v-model:value="formDataReactive.brandName"
          style="width: 580px"
          maxlength="20"
          show-count
          clearable
          rows="1"
          placeholder="请输入品牌名称"
        />
      </n-form-item>

      <n-form-item label="品牌图片" path="coverUrl">
        <ImgUploader
          v-model:oldImgUrl="formDataReactive.coverUrl"
          :width="192"
          :height="192"
          :need-cropper="false"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </n-form-item>
      <n-form-item label="品牌简介：" path="brandSummary">
        <n-input
          v-model:value="formDataReactive.brandSummary"
          maxlength="500"
          type="textarea"
          show-count
          clearable
          :autosize="{ minRows: 2, maxRows: 5 }"
          placeholder="请输入品牌简介"
        />
      </n-form-item>
      <n-form-item label="LOGO释义：" path="logoDefinition">
        <n-input
          v-model:value="formDataReactive.logoDefinition"
          maxlength="500"
          type="textarea"
          show-count
          clearable
          placeholder="请输入LOGO释义"
          :autosize="{ minRows: 2, maxRows: 5 }"
        />
      </n-form-item>
      <n-form-item label="品牌内涵：" path="brandConnotation">
        <n-input
          v-model:value="formDataReactive.brandConnotation"
          maxlength="500"
          type="textarea"
          :autosize="{ minRows: 5, maxRows: 10 }"
          show-count
          clearable
          placeholder="请输入品牌内涵"
        />
      </n-form-item>
      <n-form-item label="主要特色和取得成就：" path="featuresAchievements">
        <RichEditor
          v-model:value="formDataReactive.featuresAchievements"
          style="width: 100%"
          :rich-height="350"
        />
        <!-- <n-input
          v-model:value="formDataReactive.featuresAchievements"
          maxlength="500"
          type="textarea"
          :autosize="{ minRows: 5, maxRows: 10 }"
          show-count
          clearable
          placeholder="请输入创建措施"
        /> -->
      </n-form-item>
    </n-form>
    <div class="flex justify-center items-center gap-[20px]">
      <n-button size="large" @click="handleReset">
        重置
      </n-button>
      <n-button size="large" type="primary" @click="handleConfirm">
        确定
      </n-button>
    </div>
  </div>
</template>
<style lang="scss" scoped>
:deep(.n-upload-file-list .n-upload-file.n-upload-file--image-card-type) {
  width: 100px;
  height: 100px;
}
</style>
