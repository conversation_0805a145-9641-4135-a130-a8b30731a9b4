import type { ICarousel } from '@/services/run/carousel/types'
import { commonReq } from '@/services/request'
/** 展示启动页 */
export function getStartup() {
  return commonReq.get<ICarousel>({
    url: '/sys-operation/backend/v1/resource/start',
  })
}

/** 新增、修改启动页 */
export function postStartup(imgId: string) {
  return commonReq.post<string>({
    url: '/sys-operation/backend/v1/resource/start',
    params: {
      imgId,
    },
  })
}
