<script setup lang="ts">
import { NForm } from 'naive-ui'
import type { CascaderOption } from 'naive-ui'
import { reactive } from 'vue'
import { formRules } from './cpn/config'
import type { uploadFileItem } from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
import RichEditor from '@/components/RichEditor.vue'
import { uploadImg } from '@/services'
import type { BranchGardenDetailItem } from '@/services/structure/branch-garden/types'
import {
  getBranchGardenDetail,
  postBranchGardenItem,
  putBranchGardenItem,
} from '@/services/structure/branch-garden'
import { useOrganizationListOptions } from '@/hooks/use-select-options'

const route = useRoute()
const router = useRouter()

const formDataReactive = reactive<BranchGardenDetailItem>({
  id: '',
  orgId: null,
  orgName: '',
  content: '',
  coverId: '',
  coverUrl: '',
  moduleList: [
    {
      id: '',
      name: '',
      content: '',
    },
  ],
})

const formRef = ref<InstanceType<typeof NForm>>()
const queryDetail = (id: string) => {
  getBranchGardenDetail(id).then((res) => {
    formDataReactive.id = res.id
    formDataReactive.orgId = res.orgId
    formDataReactive.orgName = res.orgName
    formDataReactive.content = res.content
    formDataReactive.coverId = res.coverId
    formDataReactive.coverUrl = res.coverUrl
    formDataReactive.moduleList = res.moduleList || []
  })
}

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (formDataReactive.id) {
        putBranchGardenItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            router.back()
          }
        })
      }
      else {
        postBranchGardenItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            router.back()
          }
        })
      }
    }
  })
}

// PC封面图片上传
const handleCoverDone = async(file: File) => {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formDataReactive.coverId) {
      return
    }
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      formDataReactive.coverId = data.fileId || ''
    }
  }
  catch (error) {}
}
// PC封面图片删除操作
const handleCoverDelete = () => {
  formDataReactive.coverId = ''
  formDataReactive.coverUrl = ''
}

const addModule = () => {
  formDataReactive.moduleList.push({ id: '', name: '', content: '' })
}
const delModuleItem = (index: number) => {
  formDataReactive.moduleList.splice(index, 1)
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

const goBack = () => {
  window.$dialog.warning({
    title: '提示',
    content: '当前存在未保存数据，确认关闭弹窗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      router.back()
    },
  })
}
const { organizationListTree } = useOrganizationListOptions() // 获取支部列表
const handleUpdateValue = (v: string, option: CascaderOption) => {
  formDataReactive.orgId = v
  formDataReactive.orgName = option.name as string
}
const editorFlag = ref(false)
onMounted(() => {
  const { id } = route.query
  if (id && id !== '-1') {
    editorFlag.value = true
    queryDetail(id as string)
  }
  else {
    editorFlag.value = false
  }
})
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-card>
      <n-form-item label="">
        <div class="w-[100%] flex flex-row justify-between items-center">
          <NButton @click="goBack">
            返回
          </NButton>
          <NButton type="primary" @click="validateAndSave">
            发布
          </NButton>
        </div>
      </n-form-item>
      <div class="flex flex-row">
        <div class="flex-1 flex flex-col w-[100%] pl-[20px] pr-[20px]">
          <n-grid :cols="12" :x-gap="12">
            <n-form-item-gi label="选择支部" :span="12" path="orgId">
              <!-- <n-cascader
                v-model:value="formDataReactive.orgId"
                :options="organizationListTree"
                placeholder="请选择支部"
                label-field="name"
                value-field="id"
                children-field="children"
                check-strategy="child"
                :show-path="false"
                clearable
                :disabled="editorFlag"
                @update:value="handleUpdateValue"
              /> -->
              <n-tree-select
                v-model:value="formDataReactive.orgId"
                :options="organizationListTree"
                value-field="id"
                label-field="name"
                key-field="id"
                children-field="children"
                check-strategy="all"
                placeholder="请选择所属党组织"
                :show-path="false"
                clearable
                filterable
                :disabled="editorFlag"
                @update:value="handleUpdateValue"
              />
            </n-form-item-gi>
            <n-form-item-gi label="简介" :span="12" path="content">
              <n-input
                v-model:value="formDataReactive.content"
                type="textarea"
                rows="5"
                placeholder="请输入简介"
                maxlength="200"
                show-count
                clearable
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="封面图：" path="coverId">
              <ImgUploader
                v-model:oldImgUrl="formDataReactive.coverUrl"
                :width="380"
                :height="300"
                :need-cropper="false"
                @done="handleCoverDone"
                @delete="handleCoverDelete"
              />
            </n-form-item-gi>
            <n-form-item-gi
              v-for="(item, index) in formDataReactive.moduleList"
              :key="index"
              :span="12"
              :label="'展示模块的标题'"
              path="moduleList[0].name"
            >
              <n-input
                v-model:value="item.name"
                placeholder="请输入标题"
                clearable
              />
              <n-button v-show="index" @click="delModuleItem(index)">
                删除
              </n-button>
            </n-form-item-gi>
            <n-form-item-gi>
              <n-button @click="addModule">
                添加展示模块
              </n-button>
            </n-form-item-gi>
          </n-grid>
        </div>
        <div class="flex-1 flex flex-col w-[100%] pl-[20px] pr-[20px]">
          <n-scrollbar style="max-height: 700px">
            <n-grid :cols="12" :x-gap="12">
              <n-form-item-gi
                v-for="(item, index) in formDataReactive.moduleList"
                :key="index"
                :label="`${item.name}` + '介绍：'"
                :span="12"
                path="moduleList[0].content"
              >
                <RichEditor
                  v-model:value="item.content"
                  style="width: 100%"
                  :rich-height="350"
                />
              </n-form-item-gi>
            </n-grid>
          </n-scrollbar>
        </div>
      </div>
    </n-card>
  </n-form>
</template>
<style lang="scss" scoped></style>
