<script setup lang="ts">
import { emitter } from '@/utils/event-bus'
interface Props {
  sideWidth?: number
  showCollapseTrigger?: boolean | 'bar' | 'arrow-circle'
}
const props = withDefaults(defineProps<Props>(), {
  sideWidth: 259,
  showCollapseTrigger: false,
})

const myScrollTop = ref(0)
const handleScroll = (e: any) => {
  myScrollTop.value = e.target.scrollTop
}
watch(
  () => myScrollTop.value,
  (newV) => {
    emitter.emit('my-scroller', newV)
  },
)
</script>
<template>
  <n-layout class="h-full" :has-sider="true">
    <n-layout-sider
      ref="sideRef"
      :width="props.sideWidth"
      :collapsed-width="0"
      :show-trigger="showCollapseTrigger"
      :native-scrollbar="false"
    >
      <div class="box-border py-[25px] px-[13px]">
        <slot name="side" />
      </div>
    </n-layout-sider>
    <n-layout-content :native-scrollbar="false" @scroll="handleScroll">
      <slot name="main" />
      <n-back-top :right="100" :visibility-height="150" />
    </n-layout-content>
  </n-layout>
</template>
<style lang="scss" scoped></style>
