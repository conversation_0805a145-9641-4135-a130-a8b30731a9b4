import { defineStore } from 'pinia'
import type { ICarousel, ICarouselReq } from '@/services/run/carousel/types'
import { getCarouselList } from '@/services/run/carousel/carousel'
const useCarouselStore = defineStore('carousel', () => {
  const carouselList = ref<ICarousel[]>([]) // 轮播图列表
  async function getCarouselListAction(params: ICarouselReq) {
    const res = await getCarouselList(params)
    if (res) {
      carouselList.value = res.records ?? []
    }
    return Promise.resolve(res.total)
  }

  return {
    carouselList,
    getCarouselListAction,
  }
})

export default useCarouselStore
