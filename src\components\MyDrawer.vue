<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    show: boolean
    confirmFn: () => void
    title: string
    width?: number
    showBtn?: boolean
  }>(),
  {
    width: 502,
    showBtn: true,
  },
)
const emit = defineEmits<{ (e: 'changeVisible', isShow: boolean): void }>()
const newVisible = ref(false)
watch(newVisible, () => {
  emit('changeVisible', newVisible.value)
})
watch(
  () => props.show,
  () => (newVisible.value = props.show),
)
</script>
<template>
  <div>
    <n-drawer
      v-model:show="newVisible"
      :mask-closable="false"
      :default-width="width"
      placement="right"
      resizable
    >
      <n-drawer-content :title="title" closable>
        <slot />
        <template #footer>
          <div v-if="showBtn" class="flex justify-around px-[130px] w-full">
            <n-button type="primary" style="width: 80px" @click="confirmFn">
              保存
            </n-button>
            <n-button style="width: 80px" @click="() => (newVisible = false)">
              取消
            </n-button>
          </div>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<style scoped lang="scss"></style>
