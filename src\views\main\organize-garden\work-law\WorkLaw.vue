<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui'
import ChangeOrganize from '../cpn/ChangeOrganize.vue'
import { formRules } from './config'
import type { OrganizeWorkLawFormParams } from '@/services/organize-garden/types'
import { uploadImg } from '@/services'
import type { uploadFileItem } from '@/services/types'
import { useOrganizeGardenStore } from '@/store/organize-garden'
import {
  getOrganizeMethod,
  postOrganizeMethod,
  putOrganizeMethod,
} from '@/services/organize-garden'

const { getDeptId } = useOrganizeGardenStore()
const formDataReactive = reactive<OrganizeWorkLawFormParams>({
  id: '',
  orgId: '',
  title: '',
  coverUrl: '',
  description: '',
  existedData: '',
  contentUrl: '',
  contentUrlList: [],
  fileList: [],
})

watch(
  getDeptId,
  async(value) => {
    if (value) {
      formDataReactive.description = ''
      await handleOrgSummaryByDeptId(value)
      formDataReactive.orgId = value
    }
  },
  {
    immediate: true,
  },
)
async function handleOrgSummaryByDeptId(deptId: string) {
  const res = await getOrganizeMethod(deptId)

  for (const key in formDataReactive) {
    if (key !== 'contentUrlList' && key !== 'fileList') {
      formDataReactive[key] = res[key]
    }
    else {
      formDataReactive[key] = res[key] || ([] as any)
      if (res.contentUrlList && res.contentUrlList?.length > 0) {
        formDataReactive.fileList = res.contentUrlList.map(
          (url: string, index: number) => {
            return {
              id: String(index),
              status: 'finished',
              name: url,
              url: import.meta.env.VITE_API_BASE + url,
            }
          },
        )
      }
    }
  }
}

/** 上传图片 */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (
      formDataReactive.coverUrl === ''
      || formDataReactive.coverUrl === null
    ) {
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formDataReactive.coverUrl = data.url
      }
    }
  }
  catch (error) {}
}
/** 删除图片 */
function handleCoverDelete() {
  formDataReactive.coverUrl = ''
}

/** 上传多张图片 */
async function handleUpload(file: File, index: number) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      formDataReactive.contentUrlList!.push(data.url)
      formDataReactive.fileList[index].name = data.url
    }
  }
  catch (error) {}
}

async function handleContentCoverDone(options: {
  file: UploadFileInfo
  fileList: Array<UploadFileInfo>
  event?: Event
}) {
  const { fileList } = options
  formDataReactive.fileList = fileList

  // handleUpload(fileList[fileList.length - 1].file as File)
}

const workLawFormRef = ref()
// 重置-使用 keyof 获取对象的键类型并迭代
function handleReset() {
  for (const key of Object.keys(
    formDataReactive,
  ) as (keyof OrganizeWorkLawFormParams)[]) {
    formDataReactive[key] = ''
  }
  workLawFormRef.value?.restoreValidation()
}

/** 保存 */
function handleConfirm() {
  workLawFormRef.value?.validate(async(errors: any) => {
    if (!errors) {
      const newFileList = formDataReactive.fileList
        .filter(item => !item.batchId)
        .map(item => item.name)

      if (formDataReactive.fileList.length > 0) {
        for (let i = 0; i < formDataReactive.fileList.length; i++) {
          if (!newFileList.includes(formDataReactive.fileList[i].name)) {
            await handleUpload(formDataReactive.fileList[i].file as File, i)
          }
        }
      }

      const data = {
        ...formDataReactive,
        contentUrlList: formDataReactive.fileList.map(item => item.name),
      }

      const useApi
        = formDataReactive.existedData === '1'
          ? putOrganizeMethod
          : postOrganizeMethod
      useApi(data).then(async(res) => {
        window.$message.success('保存成功')
        await handleOrgSummaryByDeptId(String(formDataReactive.orgId))
      })
    }
  })
}
</script>
<template>
  <div class="w-full px-[20px] py-[20px]">
    <ChangeOrganize />
    <div class="py-[25px]">
      <span class="text-[14px] font-[600] text-[#333] leading-[20px] mb-[26px]">党组织工作法</span>
    </div>
    <n-form
      ref="workLawFormRef"
      :model="formDataReactive"
      :rules="formRules"
      require-mark-placement="left"
      label-placement="left"
      label-width="160px"
    >
      <n-form-item label="工作法标题：" path="title">
        <n-input
          v-model:value="formDataReactive.title"
          style="width: 80%"
          placeholder="请输入工作法标题"
          clearable
          maxlength="50"
          show-count
        />
      </n-form-item>
      <n-form-item label="封面图片：" path="coverUrl">
        <ImgUploader
          v-model:old-img-url="formDataReactive.coverUrl"
          :width="192"
          :height="192"
          :need-cropper="false"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </n-form-item>
      <n-form-item label="内容图片：" path="fileList">
        <div class="w-full">
          <n-upload
            v-model:file-list="formDataReactive.fileList"
            list-type="image-card"
            accept=".jpg,.png,.gif"
            @change="handleContentCoverDone"
          />
          <p class="text-[12px] font-[400] text-[#999] mt-[20px]">
            推荐尺寸 4142×1701，支持 .jpg，.png，.gif
          </p>
        </div>
      </n-form-item>

      <n-form-item label="组织工作法说明：" path="description">
        <RichEditor v-model:value="formDataReactive.description" />
      </n-form-item>
    </n-form>
    <div class="flex justify-center items-center gap-[20px]">
      <n-button size="large" @click="handleReset">
        重置
      </n-button>
      <n-button size="large" type="primary" @click="handleConfirm">
        确定
      </n-button>
    </div>
  </div>
</template>
<style lang="scss" scoped></style>
