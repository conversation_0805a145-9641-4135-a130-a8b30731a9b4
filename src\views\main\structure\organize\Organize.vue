<!-- 党组织信息管理 -->
<template>
  <table-container
    ref="tableContainerRef"
    :loading="loading"
    :page="1"
    :page-size="10"
    :show-delete="false"
    :show-pagination="false"
    :show-toolbar="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    custom-toolbar
    default-expand-all
    title="组织管理"
    @click-add="handleClickAddRootNode"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAddRootNode">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        新增
      </n-button>
    </template>
    <template #filters>
      <n-input
        v-model:value="filterReactive.orgName"
        clearable
        placeholder="请输入组织名称"
        size="small"
      />
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :mask-closable="false" :width="700">
    <n-drawer-content :title="drawerTitle" closable>
      <notice-form
        :id="idEditRef"
        ref="addNoticeFormRef"
        :data="tableData"
        :level="idEditLevel"
        :parent-id="idEditParentID"
        :parent-name="idEditParentName"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div
          v-if="editTypeRef !== 'view'"
          class="flex justify-center w-full gap-[12px]"
        >
          <n-button
            style="width: 80px"
            type="primary"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>

  <!-- 升降级弹框 -->
  <n-modal
    :show="relegationVisible"
    :show-icon="false"
    class="relegation-dialog"
    preset="dialog"
    style="width: 592px"
    @close="handleRelegationClose"
  >
    <template #header>
      <slot name="header">
        <div>组织变动管理-升/降级</div>
      </slot>
    </template>

    <div class="relegation-container">
      <n-form
        ref="relegationRef"
        :model="relegationModel"
        :rules="relegationRules"
        class="w-[100%]"
        label-placement="left"
        label-width="auto"
        require-mark-placement="left"
      >
        <n-form-item label="现组织名称：" path="name">
          <n-input
            v-model:value="relegationModel.name"
            disabled
            @keydown.enter.prevent
          />
        </n-form-item>
        <n-form-item
          class="mt-[-10px]"
          label="现组织上级组织："
          path="parentName"
        >
          <n-input
            v-model:value="relegationModel.parentName"
            disabled
            placeholder=""
            @keydown.enter.prevent
          />
        </n-form-item>
        <n-form-item
          class="mt-[-10px]"
          label="升降级后上级组织："
          path="afterParentDeptId"
        >
          <n-tree-select
            v-model:value="relegationModel.afterParentDeptId"
            :options="transformTreeData(tableData)"
          />
        </n-form-item>
        <n-form-item class="w-[100%]" label="当前等级：" path="orgType">
          <n-select
            v-model:value="relegationModel.orgType"
            :options="enumerationList"
            class="relegation-select"
            disabled
          />
        </n-form-item>
        <n-form-item class="" label="调整后等级：" path="afterOrgType">
          <n-select
            v-model:value="relegationModel.afterOrgType"
            :options="enumerationList"
            class="relegation-select"
          />
        </n-form-item>
        <n-form-item
          class="mt-[-10px]"
          label="升/降级类型："
          path="relegationType"
        >
          <n-radio-group v-model:value="relegationModel.relegationType">
            <n-radio :value="RELEGATIONTYPE.UPDOWN" name="relegationType">
              直接升/降级
            </n-radio>
            <n-radio :value="RELEGATIONTYPE.REPLACE" name="brelegationType">
              接替
            </n-radio>
          </n-radio-group>
        </n-form-item>
        <n-form-item
          v-if="relegationModel.relegationType === RELEGATIONTYPE.REPLACE"
          class="mt-[-10px]"
          label="接替组织："
          path="replaceDeptId"
        >
          <n-tree-select
            v-model:value="relegationModel.replaceDeptId"
            :options="transformTreeData(tableData)"
          />
        </n-form-item>
      </n-form>

      <div class="ml-[78px]">
        说明：<span class="ml-[10px]">接替组织必须是末级组织，否则无法执行升降级操作。</span>
      </div>
    </div>

    <template #action>
      <slot name="action">
        <div class="dialog-btn">
          <button class="sure-btn" size="small" @click="handleRelegationSure">
            确定
          </button>
          <button
            class="cancel-btn"
            size="small"
            @click="handleRelegationClose"
          >
            取消
          </button>
        </div>
      </slot>
    </template>
  </n-modal>

  <!-- 拆分弹框 -->
  <n-modal
    :show="splitVisible"
    :show-icon="false"
    preset="dialog"
    style="width: 592px"
    @close="handleSplitClose"
  >
    <template #header>
      <slot name="header">
        <div>组织拆分</div>
      </slot>
    </template>

    <div class="split-tips">
      <span class="warning"><span class="ml-[6px] mt-[-10px]">i</span> </span>
      说明:拆分前组织的业务数据自动继承至拆分后组织1中，
      拆分前后人员的各端账户的权限职务<br />均做初始化处理，需要到权限和用户管理中单独配置。
    </div>

    <div class="mt-[26px] h-[500px]" style="overflow: auto">
      <n-form
        ref="splitRef"
        :model="splitModel"
        :rules="splitRules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="left"
      >
        <n-form-item class="split-dialog" label="拆分前组织：" path="id">
          <n-tree-select
            v-model:value="splitModel.id"
            :options="transformTreeData(tableData)"
            disabled
          />
        </n-form-item>
        <n-form-item
          class="split-dialog"
          label="拆分后组织1："
          path="afterMainDeptName"
        >
          <n-input
            v-model:value="splitModel.afterMainDeptName"
            class=""
            placeholder="请输入组织1名称"
            @keydown.enter.prevent
          />
        </n-form-item>
        <n-form-item
          class="split-dialog ml-[100px] mt-[-10px]"
          label=""
          path="afterMainUserList"
        >
          <n-select
            v-model:value="splitModel.afterMainUserList"
            :options="departmentUser"
            multiple
            placeholder="请选择组织人员"
            @click="choosePeopleOne"
            @update:value="handleUpdateChoosePeopleOne"
          >
            <template #arrow>
              <PersonAddAltOutlined />
            </template>
          </n-select>
        </n-form-item>

        <n-form-item
          class="split-dialog"
          label="拆分后组织2："
          path="afterSlaveDeptName"
        >
          <n-input
            v-model:value="splitModel.afterSlaveDeptName"
            class=""
            placeholder="请输入组织2名称"
            @keydown.enter.prevent
          />
        </n-form-item>
        <n-form-item
          class="split-dialog ml-[100px] mt-[-10px]"
          label=""
          path="afterSlaveUserList"
        >
          <n-select
            v-model:value="splitModel.afterSlaveUserList"
            :options="departmentUser"
            multiple
            placeholder="请选择组织人员"
            @click="choosePeopleTwo"
            @update:value="handleUpdateChoosePeopleTwo"
          >
            <template #arrow>
              <PersonAddAltOutlined />
            </template>
          </n-select>
        </n-form-item>
      </n-form>
    </div>

    <template #action>
      <slot name="action">
        <div class="dialog-btn">
          <button class="sure-btn" size="small" @click="handleSplitSure">
            确定
          </button>
          <button class="cancel-btn" size="small" @click="handleSplitClose">
            取消
          </button>
        </div>
      </slot>
    </template>
  </n-modal>

  <!-- 拆分选择人员弹框 -->
  <custom-dialog
    :show="splitPeopleVisible"
    :show-icon="false"
    class="choose-dialog"
    preset="dialog"
    style="width: 721px; padding: 0 0 31px"
    @cancel="handleSplitPersonClose"
  >
    <template #header>
      <slot name="header">
        <div class="custom-header">
          选择人员
        </div>
      </slot>
    </template>

    <div class="ml-[16px] mb-[10px] mt-[16px]">
      <span> 组织人员: ({{ departmentUser.length }})</span>
      <span class="ml-[266px]"> 已选择: ({{ choosePersonList.length }})</span>
    </div>

    <div class="select-person flex justify-center items-center">
      <n-transfer
        ref="transfer"
        v-model:value="choosePersonList"
        :options="departmentUser"
        :render-source-label="renderSourceLabel"
        :render-target-label="renderSourceLabel"
        source-filterable
        style="width: 100%"
        virtual-scroll
      />
    </div>

    <template #action>
      <slot name="action">
        <div class="dialog-btn">
          <button
            class="sure-btn ml-[512px]"
            size="small"
            @click="handleChoosePeople"
          >
            确定
          </button>
          <button
            class="cancel-btn"
            size="small"
            @click="handleSplitPersonClose"
          >
            取消
          </button>
        </div>
      </slot>
    </template>
  </custom-dialog>

  <!-- 合并弹框 -->
  <n-modal
    :show="mergeVisible"
    :show-icon="false"
    preset="dialog"
    style="width: 592px"
    @close="handleMergeClose"
  >
    <template #header>
      <slot name="header">
        <div>组织变动管理-合并</div>
      </slot>
    </template>

    <div class="mt-[26px] h-[400px]" style="overflow: auto">
      <n-form
        ref="mergeRef"
        :model="mergeModel"
        :rules="mergeRules"
        label-placement="left"
        require-mark-placement="left"
      >
        <n-form-item
          class="split-dialog"
          label="合并前主组织名称："
          path="name"
        >
          <n-input v-model:value="mergeModel.name" class="ml-[-3px]" disabled />
        </n-form-item>
        <n-form-item
          class="split-dialog"
          label="被合并组织名称："
          path="mergedDeptId"
        >
          <n-tree-select
            v-model:value="mergeModel.mergedDeptId"
            :options="transformTreeDataWithChildren(tableData)"
          />
        </n-form-item>
        <n-form-item
          class="split-dialog"
          label="合并后组织名称："
          path="afterMergeDeptName"
        >
          <n-input
            v-model:value="mergeModel.afterMergeDeptName"
            @keydown.enter.prevent
          />
        </n-form-item>
      </n-form>
      <div>
        说明：<br />1、被合并组织在合并后将不存在，合并前主组织将变更名称（合并后组织名称）后得以保留，同时迁移的功能数据是将"被合并组织"的数据迁移到合并后的组织下。
        <br />
        <div class="mt-[10px]">
          2、被合并组织的党员账户，直接调整至合并后组织下，人员的职务统一调整为普通党员，如果需要变更，请到用户管理和权限管理中调整。
        </div>
      </div>
    </div>

    <template #action>
      <slot name="action">
        <div class="dialog-btn">
          <button class="sure-btn" size="small" @click="handleMergeSure">
            确定
          </button>
          <button class="cancel-btn" size="small" @click="handleMergeClose">
            取消
          </button>
        </div>
      </slot>
    </template>
  </n-modal>

  <!-- 管理领导班子弹框 -->
  <n-drawer
    v-model:show="showManageLeaderDrawer"
    :mask-closable="false"
    :width="800"
    placement="right"
    display-directive="if"
  >
    <n-drawer-content closable title="管理领导班子">
      <div
        class="w-[100%] flex flex-col justify-start items-start gap-y-[40px]"
      >
        <ManageLeaderTable
          ref="manageLeaderTableRef"
          :current-org-id="currentOrganizeId"
          :org-list="organizationListTree"
          :table-data="manageLeaderList"
          :user-list="userOptions"
          title="书记"
        />
        <ManageLeaderTable
          ref="viceManageLeaderTableRef"
          :current-org-id="currentOrganizeId"
          :org-list="organizationListTree"
          :table-data="viceManageLeaderList"
          :user-list="userOptions"
          title="副书记"
        />
        <ManageLeaderTable
          ref="committeeTableRef"
          :current-org-id="currentOrganizeId"
          :org-list="organizationListTree"
          :table-data="committeeList"
          :user-list="userOptions"
          title="委员"
        />
        <ManageLeaderTable
          ref="partyAffairsWorkerTableRef"
          :current-org-id="currentOrganizeId"
          :org-list="organizationListTree"
          :table-data="partyAffairsWorkerList"
          :user-list="userOptions"
          title="党务工作者"
        />
      </div>

      <template #footer>
        <div
          class="w-[100%] flex flex-row justify-center items-center gap-[10px]"
        >
          <n-button @click="handleCancelManageLeaderDrawer">
            取消
          </n-button>
          <n-button type="primary" @click="handleSubmitManageLeaderDrawer">
            确定
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<script lang="ts" setup>
// import Sortable from 'sortablejs' // 引入 SortableJS
import { NButton, NIcon, useDialog, useMessage } from 'naive-ui'
import { PersonAddAltOutlined, PlusRound } from '@vicons/material'
import { User } from '@vicons/carbon'
import { IosArrowDropdown, IosArrowDropup } from '@vicons/ionicons4'

import { getTableColumns } from './config'
import NoticeForm from './cpn/NoticeForm.vue'
import ManageLeaderTable from './cpn/ManageLeaderTable.vue'
import {
  useFetchEnumerationOptions,
  useOrganizationListOptionsNew,
} from '@/hooks/use-select-options'
import { useDrawerEditOrganize } from '@/hooks'
import {
  addOrEditLeader,
  deleteOrganizationItem,
  getCurrentUserOrganizationList,
  getLeaderList,
  getLeaderUserList,
  getOrganizationTableList,
  getVagueOrganizationTableList,
  mergeOrganizationItem,
  moveOrganizationItem,
  splitOrganizationItem,
  upDownOrganizationItem,
} from '@/services/organization'
import type {
  ManageLeaderDetailTypes,
  ManageLeaderUsersTypes,
  OrganizationItemTableItem,
  OrganizationUsersTypes,
} from '@/services/organization/types'
import { useOrganizationStore } from '@/store/organization'

const { organizationListTree } = useOrganizationListOptionsNew()
const partyIdentityType = useFetchEnumerationOptions('party_identity')

const message = useMessage()
const dialog = useDialog()

// 展示管理领导班子抽屉
const showManageLeaderDrawer = ref<boolean>(false)
// 书记领导班子
const manageLeaderList = ref<ManageLeaderUsersTypes[]>([])
// 副书记领导班子
const viceManageLeaderList = ref<ManageLeaderUsersTypes[]>([])
// 委员
const committeeList = ref<ManageLeaderUsersTypes[]>([])
// 党务工作者
const partyAffairsWorkerList = ref<ManageLeaderUsersTypes[]>([])
// 书记tableRef
const manageLeaderTableRef = ref<any>(null)
// 副书记tableRef
const viceManageLeaderTableRef = ref<any>(null)
// 委员tableRef
const committeeTableRef = ref<any>(null)
// 党务工作者tableRef
const partyAffairsWorkerTableRef = ref<any>(null)
// 表格
const tableContainerRef = ref<any>(null)

// 管理领导班子详情表单
const loadLeaderManageDetailList = ref<ManageLeaderDetailTypes[]>([])
const userOptions = ref<any>([])

// 组织store
const organizationStore = useOrganizationStore()

const filterReactive = ref<{ orgName: string }>({
  orgName: '',
})
const { enumerationList } = useFetchEnumerationOptions('org_type')

// 选择的人员列表
const choosePersonList = ref<string[]>([])

// 区分选择的是部门1还是部门2
const chooseDepartment = ref(1)
// 拆分组织的用户列表
// const departmentUser = ref<any[]>([])
const departmentUser = computed(() => {
  let arr = []
  if (chooseDepartment.value === 1) {
    arr = organizationStore.getAllPeople.map((personal) => {
      return {
        ...personal,
        disabled: organizationStore.getOrgPeople2.includes(personal.value),
      }
    })
  }
  else {
    arr = organizationStore.getAllPeople.map((personal) => {
      return {
        ...personal,
        disabled: organizationStore.getOrgPeople1.includes(personal.value),
      }
    })
  }
  return arr
})

// 升降级和平替字典
const RELEGATIONTYPE = {
  UPDOWN: '1', // 升/降级别
  REPLACE: '2', // 接替
}

const loading = ref(false)
const tableData = ref<any[]>([])
const userManagedOrgIds = ref<string[]>([])

/** 获取用户管辖范围内的组织ID列表 */
const getUserManagedOrgIds = async() => {
  try {
    const res = await getCurrentUserOrganizationList()
    const orgIds: string[] = []

    // 递归提取所有组织ID
    const extractOrgIds = (orgs: any[]) => {
      orgs.forEach((org) => {
        if (org.id || org.deptId) {
          orgIds.push(org.id || org.deptId)
        }
        if (org.children && org.children.length > 0) {
          extractOrgIds(org.children)
        }
      })
    }

    if (Array.isArray(res)) {
      extractOrgIds(res)
    }

    userManagedOrgIds.value = orgIds
  }
  catch (error) {
    console.error('获取用户管辖组织列表失败:', error)
  }
}

/** 过滤组织数据，只保留用户管辖范围内的组织 */
const filterOrganizationData = (data: any[], managedIds: string[]) => {
  if (!Array.isArray(data) || managedIds.length === 0) {
    return data
  }

  const filtered: any[] = []

  const processOrg = (org: any) => {
    // 检查当前组织是否在管辖范围内
    const isManaged
      = managedIds.includes(org.id) || managedIds.includes(org.deptId)

    if (isManaged) {
      // 如果有子组织，递归处理
      if (org.children && org.children.length > 0) {
        org.children = filterOrganizationData(org.children, managedIds)
      }
      filtered.push(org)
    }
    else if (org.children && org.children.length > 0) {
      // 如果当前组织不在管辖范围内，但子组织可能在范围内，递归处理
      const filteredChildren = filterOrganizationData(org.children, managedIds)
      if (filteredChildren.length > 0) {
        filtered.push({
          ...org,
          children: filteredChildren,
          // 标记这个节点不在管辖范围内，用于禁用操作
          _isOutOfScope: true,
        })
      }
    }
  }

  data.forEach(processOrg)
  return filtered
}

/** 获取组织列表 */
const getTableList = async() => {
  loading.value = true
  try {
    // 先获取用户管辖范围内的组织ID
    await getUserManagedOrgIds()

    // 获取完整的组织信息
    const res = await getOrganizationTableList()

    // 处理分页数据结构
    const orgData = Array.isArray(res) ? res : []

    // 过滤出用户管辖范围内的组织
    const filteredData = filterOrganizationData(
      orgData,
      userManagedOrgIds.value,
    )

    tableData.value = filteredData as any
  }
  catch (error) {
    console.error('获取组织列表失败:', error)
    tableData.value = []
  }
  finally {
    loading.value = false
  }
}

/** 获取过滤组织列表 */
const filterTableList = async() => {
  try {
    // 确保有用户管辖范围的组织ID
    if (userManagedOrgIds.value.length === 0) {
      await getUserManagedOrgIds()
    }

    // 获取模糊搜索的组织信息
    const res = await getVagueOrganizationTableList(filterReactive.value)

    // 处理分页数据结构
    const orgData = Array.isArray(res) ? res : []

    // 过滤出用户管辖范围内的组织
    const filteredData = filterOrganizationData(
      orgData,
      userManagedOrgIds.value,
    )

    tableData.value = filteredData as any
  }
  catch (error) {
    console.error('获取过滤组织列表失败:', error)
    tableData.value = []
  }
}

const filterVal = computed(() => {
  if (filterReactive.value.orgName && filterReactive.value.orgName !== '') {
    return filterReactive.value.orgName
  }
  else {
    return null
  }
})

const deleteOrganization = (id: string) => {
  deleteOrganizationItem(id).then((res) => {
    window.$message.success('删除成功')
    if (filterVal.value) {
      filterTableList()
    }
    else {
      getTableList()
    }
  })
}

watch(
  () => filterVal.value,
  (newVal) => {
    if (newVal && newVal !== '') {
      filterTableList()
    }
    else {
      getTableList()
    }
  },
)

interface rowInstance {
  name?: string
}

// 新增/编辑党建清单抽屉
const idEditRef = ref()
const idEditParentID = ref()
const idEditParentName = ref()
const idEditLevel = ref()
const addNoticeFormRef = ref()

const relegationRef = ref()
const mergeRef = ref()
const splitRef = ref()

// 升降级数据
const relegationModel: any = ref({
  name: '',
  parentId: '',
  afterParentDeptId: '',
  orgType: '',
  afterOrgType: '',
  relegationType: '',
  replaceDeptId: '',
})

// 拆分数据
const splitModel: any = ref({
  name: '',
  parentId: '',
  afterParentDeptId: '',
  orgType: '',
  afterOrgType: '',
  relegationType: '',
  replaceDeptId: '',
  afterMainUserList: [],
})

// 合并数据
const mergeModel: any = ref({
  name: '',
  parentId: '',
  afterParentDeptId: '',
  orgType: '',
  afterOrgType: '',
  relegationType: '',
  replaceDeptId: '',
  afterMainUserList: [],
  mergedDeptId: '',
})

const relegationRules = ref({
  afterParentDeptId: {
    required: true,
    message: '请输入升降级后上级组织',
    trigger: ['input', 'blur'],
  },
  afterOrgType: {
    required: true,
    message: '请选择调整后等级',
    trigger: ['blur', 'change'],
  },
  relegationType: {
    required: true,
    message: '请选择升/降级类型',
    trigger: ['blur', 'change'],
  },
  replaceDeptId: {
    required: true,
    message: '请选择接替组织',
    trigger: ['blur', 'change'],
  },
})

const splitRules = ref({
  afterMainDeptName: {
    required: true,
    message: '请输入组织1名称',
    trigger: ['input', 'blur'],
  },
  afterMainUserList: [
    {
      validator: validateUserList,
      message: '请选择组织人员',
      trigger: ['input', 'blur'],
    },
  ],
  afterSlaveDeptName: {
    required: true,
    message: '请输入组织2名称',
    trigger: ['input', 'blur'],
  },
  afterSlaveUserList: [
    {
      validator: validateUserList,
      message: '请选择组织人员',
      trigger: ['input', 'blur'],
    },
  ],
})

const mergeRules = ref({
  mergedDeptId: {
    required: true,
    message: '请选择被合并组织名称',
    trigger: ['input', 'change'],
  },
  afterMergeDeptName: {
    required: true,
    message: '请输入合并后组织名称',
    trigger: ['input', 'blur'],
  },
})

// 升降级弹框
const relegationVisible = ref(false)

// 拆分弹框
const splitVisible = ref(false)

// 拆分选择人员弹框
const splitPeopleVisible = ref(false)

// 合并弹框
const mergeVisible = ref(false)

// 当前操作的row
const currentRow = ref<rowInstance>({})

// 当前选中的组织id
const currentOrganizeId = ref('')

const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  editTitle,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEditOrganize({ name: '根节点', confirmFn: handelConfirmEdit })

/** 添加根节点 */
const handleClickAddRootNode = () => {
  idEditRef.value = '0'
  idEditParentID.value = '0'
  editTitle.value = ''
  editTypeRef.value = 'add'
  showEditRef.value = true
}

/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}

watch(showEditRef, (newV) => {
  if (!newV) {
    addNoticeFormRef.value?.resetForm()
  }
})

/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  getTableList()
}

// 关闭弹框重置拆分数据
watch(
  () => splitVisible.value,
  () => {
    if (splitVisible.value) {
      organizationStore.resetOrgPeople()
      choosePersonList.value = []
      splitModel.value.afterMainDeptName = ''
      splitModel.value.afterSlaveDeptName = ''
      splitModel.value.afterMainUserList = []
      splitModel.value.afterSlaveUserList = []
    }
  },
)

// 确定升降级
function handleRelegationSure() {
  relegationRef.value?.validate((errors: any) => {
    if (!errors) {
      const data: any = {}
      data.afterParentDeptId = relegationModel.value.afterParentDeptId
      data.afterOrgType = relegationModel.value.afterOrgType
      data.relegationType = relegationModel.value.relegationType
      data.currentParentDeptId = relegationModel.value.parentId
      if (data.relegationType === RELEGATIONTYPE.REPLACE) {
        data.replaceDeptId = relegationModel.value.replaceDeptId
      }
      data.currentDeptId = relegationModel.value.id

      relegationVisible.value = false
      upDownOrganizationItem(data).then((res) => {
        window.$message.success('操作成功')
        if (filterVal.value) {
          filterTableList()
        }
        else {
          getTableList()
        }
      })
    }
  })
}

function choosePeopleOne() {
  choosePersonList.value = organizationStore.getOrgPeople1
  chooseDepartment.value = 1
  splitPeopleVisible.value = true
}

function handleUpdateChoosePeopleOne(value: any) {
  organizationStore.setOrgPeople1(value)
}

function handleUpdateChoosePeopleTwo(value: any) {
  organizationStore.setOrgPeople1(value)
}

function choosePeopleTwo() {
  choosePersonList.value = organizationStore.getOrgPeople2
  chooseDepartment.value = 2
  splitPeopleVisible.value = true
}

const renderSourceLabel = function({ option }: any) {
  return h(
    'div',
    {
      style: {
        display: 'flex',
        'align-items': 'center',
      },
    },
    [
      h(
        NIcon,
        {
          style: {
            'margin-left': '18px',
          },
        },
        () => h(User),
      ),
      h(
        'span',
        {
          style: {
            'margin-top': '3px',
            'margin-left': '6px',
          },
        },
        // option.username,
        option.trueName,
      ),
    ],
  )
}

// 校验用户组数量
function validateUserList(rule: any, value: any) {
  if (Array.isArray(value) && value.length === 0) {
    return false
  }
  else {
    return true
  }
}

// 确定拆分
function handleSplitSure() {
  splitModel.value.afterMainUserList = organizationStore.getOrgPeople1
  splitModel.value.afterSlaveUserList = organizationStore.getOrgPeople2
  splitRef.value?.validate((errors: any) => {
    if (!errors) {
      const data: any = {}
      data.beforeMainDeptId = splitModel.value.id
      data.afterMainDeptName = splitModel.value.afterMainDeptName
      data.afterSlaveDeptName = splitModel.value.afterSlaveDeptName
      data.afterMainUserList = splitModel.value.afterMainUserList
      data.afterSlaveUserList = splitModel.value.afterSlaveUserList
      // 优化提示 若afterMainUserList.length  + afterSlaveUserList.length 不等于 departMentUser.value.length

      if (
        data.afterMainUserList.length + data.afterSlaveUserList.length
        !== departmentUser.value.length
      ) {
        dialog.warning({
          title: '警告',
          content: '还存在有未分配到新组织的人员，是否继续？',
          positiveText: '是',
          negativeText: '否',
          onPositiveClick: () => {
            splitOrganizationItem(data).then((res) => {
              window.$message.success('操作成功')
              if (filterVal.value) {
                filterTableList()
              }
              else {
                getTableList()
              }
            })
            splitVisible.value = false
          },
          onNegativeClick: () => {
            message.error('请重新检查')
          },
        })
      }
      else {
        splitOrganizationItem(data).then((res) => {
          window.$message.success('操作成功')
          if (filterVal.value) {
            filterTableList()
          }
          else {
            getTableList()
          }
        })
        splitVisible.value = false
      }
    }
  })
}

// 确定合并
function handleMergeSure() {
  mergeRef.value?.validate((errors: any) => {
    if (!errors) {
      const data: any = {}
      data.beforeMergeDeptId = mergeModel.value.id
      data.mergedDeptId = mergeModel.value.mergedDeptId
      data.afterMergeDeptName = mergeModel.value.afterMergeDeptName
      mergeOrganizationItem(data).then((res) => {
        window.$message.success('操作成功')
        if (filterVal.value) {
          filterTableList()
        }
        else {
          getTableList()
        }
      })
      mergeVisible.value = false
    }
  })
}

// 转化data为树状结构
function transformTreeData(data: any) {
  if (Array.isArray(data)) {
    data.forEach((item) => {
      item.key = item.id
      item.label = item.name
      item.value = item.id

      if (item.children) {
        item.children.forEach((child: any) => {
          transformTreeData(child)
        })
      }
    })
  }
  else {
    data.key = data.id
    data.label = data.name
    data.value = data.id

    if (data.children) {
      data.children.forEach((child: any) => {
        transformTreeData(child)
      })
    }
  }
  return data
}

// 转化data为树状结构,并且该数据没有chilren的节点，设为不可选
function transformTreeDataWithChildren(data: any) {
  if (Array.isArray(data)) {
    data.forEach((item) => {
      item.key = item.id
      item.label = item.name
      item.value = item.id

      if (item.children) {
        item.children.forEach((child: any) => {
          transformTreeData(child)
        })
        if (item.children.length === 0) {
          item.disabled = true
        }
      }
      else {
        item.disabled = true
      }
    })
  }
  else {
    data.key = data.id
    data.label = data.name
    data.value = data.id

    if (data.children) {
      data.children.forEach((child: any) => {
        transformTreeData(child)
      })
    }
  }
  return data
}

// 递归根据parentId获取父级节点
function getParentNode(treeData: any[], parentId: string): any {
  const stack = [...treeData] // 使用栈模拟递归
  while (stack.length) {
    const node = stack.pop()
    if (node.id === parentId) {
      return node
    }
    if (node.children) {
      stack.push(...node.children)
    }
  }
  return null
}

// 判断是否是disabled属性
function nodeIsDisabled(type: 'down' | 'up', row: any) {
  const parentId = row.parentId
  // const rowId = row.id

  const parentNode = getParentNode(tableData.value, parentId)

  if (parentNode) {
    if (!parentNode?.children?.length) {
      return true
    }
    if (type === 'down') {
      return parentNode.children[parentNode.children.length - 1].id === row.id
    }

    if (type === 'up') {
      return parentNode.children[0].id === row.id
    }
  }
  else {
    return true
  }
}

function handelMoveRow(type: 'down' | 'up', row: any) {
  const parentNode = getParentNode(tableData.value, row.parentId)
  if (!parentNode) {
    return
  }

  const children = parentNode.children
  const currentIndex = children.findIndex((item: any) => item.id === row.id)

  if (type === 'down' && currentIndex < children.length - 1) {
    const moveParams = {
      id: row.id,
      weight: row.weight,
    }
    const beMoveParams = {
      id: children[currentIndex + 1].id,
      weight: children[currentIndex + 1].weight,
    }

    moveOrganizationItem([moveParams, beMoveParams]).then(() => {
      window.$message.success('操作成功')
      getTableList()
    })
  }
  else if (type === 'up' && currentIndex > 0) {
    const moveParams = {
      id: row.id,
      weight: row.weight,
    }
    const beMoveParams = {
      id: children[currentIndex - 1].id,
      weight: children[currentIndex - 1].weight,
    }

    moveOrganizationItem([moveParams, beMoveParams]).then(() => {
      window.$message.success('操作成功')
      getTableList()
    })
  }
}

// 检查当前组织是否在用户管辖范围内
const isInManagedScope = (row: any) => {
  // 如果节点被标记为超出范围，则不在管辖范围内
  if (row._isOutOfScope) {
    return false
  }
  return userManagedOrgIds.value.includes(row.id)
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  const isManaged = isInManagedScope(row)

  return h(
    'div',
    { class: 'flex flex-row items-center' },
    {
      default: () => [
        h(
          NButton,
          {
            onClick: () => {
              idEditRef.value = row.id
              editTitle.value = row.parentId === '0' ? '根节点' : '子级'
              editTypeRef.value = 'view'
              showEditRef.value = true
              idEditLevel.value = row.level
            },
            type: 'primary',
            text: true,
            style: {
              marginRight: '10px',
            },
          },
          {
            default: () => '查看',
          },
        ),
        h(
          NButton,
          {
            onClick: () => {
              if (!isManaged) {
                window.$message.warning('您没有权限操作此组织')
                return
              }
              idEditRef.value = row.id
              editTitle.value = row.parentId === '0' ? '根节点' : '子级'
              editTypeRef.value = 'modify'
              showEditRef.value = true
              idEditLevel.value = row.level
            },
            type: 'primary',
            text: true,
            disabled: !isManaged,
            style: {
              marginRight: '10px',
              color: !isManaged ? '#ccc' : undefined,
            },
          },
          {
            default: () => '编辑',
          },
        ),
        h(
          NButton,
          {
            onClick: () => {
              if (!isManaged) {
                window.$message.warning('您没有权限操作此组织')
                return
              }
              window.$dialog.warning({
                title: '提示',
                content: '撤销后无法恢复，确认删除？',
                positiveText: '确认',
                negativeText: '取消',
                onPositiveClick: () => {
                  handleDelete(row)
                },
              })
            },
            type: 'primary',
            text: true,
            disabled: !isManaged,
            style: {
              marginRight: '10px',
              color: !isManaged ? '#ccc' : undefined,
            },
          },
          {
            default: () => '撤销组织',
          },
        ),
        h(
          NButton,
          {
            onClick: () => {
              if (!isManaged) {
                window.$message.warning('您没有权限操作此组织')
                return
              }
              currentRow.value = row
              relegationModel.value = row
              relegationModel.value.orgType = String(row.orgType)
              if (
                row.children
                && Array.isArray(row.children)
                && row.children.length > 0
              ) {
                // 替换
                relegationModel.value.relegationType = RELEGATIONTYPE.REPLACE
                // relegationRules.value.replaceDeptId.required = true
              }
              else {
                // 升降级
                relegationModel.value.relegationType = RELEGATIONTYPE.UPDOWN
              }
              relegationVisible.value = true
            },
            type: 'primary',
            text: true,
            disabled: !isManaged,
            style: {
              marginRight: '10px',
              color: !isManaged ? '#ccc' : undefined,
            },
          },
          {
            default: () => '升降级',
          },
        ),
        h(
          NButton,
          {
            onClick: () => {
              if (!isManaged) {
                window.$message.warning('您没有权限操作此组织')
                return
              }
              currentRow.value = row
              mergeModel.value = row
              mergeVisible.value = true
            },
            type: 'primary',
            text: true,
            disabled: !isManaged,
            style: {
              marginRight: '10px',
              color: !isManaged ? '#ccc' : undefined,
            },
          },
          {
            default: () => '合并',
          },
        ),
        h(
          NButton,
          {
            onClick: () => {
              if (!isManaged) {
                window.$message.warning('您没有权限操作此组织')
                return
              }
              organizationStore.resetOrgPeople()
              currentRow.value = row
              splitModel.value = row
              splitVisible.value = true
              organizationStore.setAllPeople(row.id)
            },
            type: 'primary',
            text: true,
            disabled: !isManaged,
            style: {
              marginRight: '10px',
              color: !isManaged ? '#ccc' : undefined,
            },
          },
          {
            default: () => '拆分',
          },
        ),
        h(
          NButton,
          {
            onClick: () => {
              if (!isManaged) {
                window.$message.warning('您没有权限操作此组织')
                return
              }
              currentOrganizeId.value = row.id
              loadLeaderManageList(row.id)
              getUserList()
              showManageLeaderDrawer.value = true
            },
            type: 'primary',
            text: true,
            disabled: !isManaged,
            style: {
              marginRight: '10px',
              color: !isManaged ? '#ccc' : undefined,
            },
          },
          {
            default: () => '管理领导班子',
          },
        ),
        h(
          NIcon,
          {
            size: 20,
            color:
              nodeIsDisabled('down', row) || !isManaged ? '#ccc' : '#AC241D',
            onClick: () => {
              if (!isManaged) {
                window.$message.warning('您没有权限操作此组织')
                return
              }
              handelMoveRow('down', row)
            },
            class:
              nodeIsDisabled('down', row) || !isManaged
                ? 'cursor-not-allowed'
                : 'cursor-pointer',
          },
          { default: () => h(IosArrowDropdown) },
        ),
        h(
          NIcon,
          {
            size: 20,
            color: nodeIsDisabled('up', row) || !isManaged ? '#ccc' : '#AC241D',
            class:
              nodeIsDisabled('up', row) || !isManaged
                ? 'cursor-not-allowed ml-[10px]'
                : 'cursor-pointer ml-[10px]',
            onClick: () => {
              if (nodeIsDisabled('up', row) || !isManaged) {
                if (!isManaged) {
                  window.$message.warning('您没有权限操作此组织')
                }
                return
              }
              handelMoveRow('up', row)
            },
          },
          { default: () => h(IosArrowDropup) },
        ),
      ],
    },
  )
})

// 关闭升降级弹窗
function handleRelegationClose() {
  relegationVisible.value = false
}

// 关闭拆分弹窗
function handleSplitClose() {
  splitVisible.value = false
}

// 关闭合并弹窗
function handleMergeClose() {
  mergeVisible.value = false
}

// 关闭拆分人员弹窗
function handleSplitPersonClose() {
  splitPeopleVisible.value = false
  // choosePersonList.value = []
  // departmentUser.value = []
}

function handleChoosePeople() {
  if (chooseDepartment.value === 1) {
    organizationStore.setOrgPeople1(choosePersonList.value)
    // 选择的是部门1
    splitModel.value.afterMainUserList = choosePersonList.value
  }
  else {
    organizationStore.setOrgPeople2(choosePersonList.value)
    // 选择的是部门2
    splitModel.value.afterSlaveUserList = choosePersonList.value
  }
  splitPeopleVisible.value = false
  choosePersonList.value = []
}

// 删除
function handleDelete(row: OrganizationItemTableItem) {
  if (row.children && Array.isArray(row.children) && row.children.length > 0) {
    window.$dialog.error({
      title: '提示',
      content: () => {
        return [
          h(
            'div',
            {
              style: {
                margin: 'auto',
              },
            },
            {
              default: () => `组织名称:  ${row.name}`,
            },
          ),
          h(
            'div',
            {
              style: {
                margin: 'auto',
                marginTop: '10px',
              },
            },
            {
              default: () =>
                '请先将该组织下的组织删除,否则无法删除当前所选组织',
            },
          ),
        ]
      },
      positiveText: '确定',
      negativeText: '取消',
    })
  }
  else {
    deleteOrganization(String(row.id))
  }
}

function loadLeaderManageList(deptId: string) {
  getLeaderList(deptId).then((res: any) => {
    if (res && res.length) {
      loadLeaderManageDetailList.value = res || []
      res.forEach((item: any) => {
        // 处理书记
        if (item.roleCode === 'ROLE_SECRETARY') {
          manageLeaderList.value = item.users
        }
        // 处理副书记
        if (item.roleCode === 'ROLE_DEPUTY_SECRETARY') {
          viceManageLeaderList.value = item.users
        }
        // 处理委员
        if (item.roleCode === 'ROLE_MEMBER') {
          committeeList.value = item.users
        }
        // 处理党务工作者
        if (item.roleCode === 'ROLE_WORKER') {
          partyAffairsWorkerList.value = item.users
        }
      })
    }
  })
}

// 获取用户list
function getUserList() {
  getLeaderUserList({
    deptId: currentOrganizeId.value,
    trueName: '',
  }).then((res) => {
    if (res && res.length) {
      userOptions.value = res.map((item: OrganizationUsersTypes) => {
        return {
          ...item,
          partyIdentityLabel: filterPartyIdentityType(item.partyIdentity),
          label: `${item.trueName} ${item.deptName} ${filterPartyIdentityType(
            item.partyIdentity,
          )}`,
          value: item.userId,
        }
      })
    }
  })
}

// 过滤支部身份
function filterPartyIdentityType(val: string) {
  if (val) {
    return partyIdentityType.enumerationList.value.find((item: any) => {
      return item.value === val
    })?.label
  }
  else {
    return partyIdentityType.enumerationList.value
  }
}

// 保存领导班子
function handleSubmitManageLeaderDrawer() {
  // 角色代码常量
  const ROLE_CODES = {
    SECRETARY: 'ROLE_SECRETARY',
    DEPUTY_SECRETARY: 'ROLE_DEPUTY_SECRETARY',
    MEMBER: 'ROLE_MEMBER',
    WORKER: 'ROLE_WORKER',
  } as const

  // 获取各角色用户列表
  const roleUserLists = {
    [ROLE_CODES.SECRETARY]: manageLeaderTableRef.value!.getManageLeaderUserList(),
    [ROLE_CODES.DEPUTY_SECRETARY]: viceManageLeaderTableRef.value!.getManageLeaderUserList(),
    [ROLE_CODES.MEMBER]: committeeTableRef.value!.getManageLeaderUserList(),
    [ROLE_CODES.WORKER]: partyAffairsWorkerTableRef.value!.getManageLeaderUserList(),
  }

  // 构建更新参数列表
  const updateManageLeaderFormList = loadLeaderManageDetailList.value.map(item => ({
    id: item.id,
    orgId: item.orgId,
    roleCode: item.roleCode,
    addOrEditParamList: roleUserLists[item.roleCode as keyof typeof roleUserLists] || [],
  }))

  // 提交更新
  addOrEditLeader(updateManageLeaderFormList)
    .then(() => {
      window.$message.success('设置成功')
      showManageLeaderDrawer.value = false
    })
    .catch((error) => {
      console.error('保存领导班子失败:', error)
      window.$message.error('保存失败，请重试')
    })
}

// 取消管理领导班子抽屉
function handleCancelManageLeaderDrawer() {
  // 重新加载数据以确保状态正确
  if (currentOrganizeId.value) {
    loadLeaderManageList(currentOrganizeId.value)
  }
  showManageLeaderDrawer.value = false
}

// function initSortable() {
//   const tbody = tableContainerRef.value?.$el?.querySelector(
//     '.n-data-table-tbody'
//   )
//   if (!tbody) return
//   new Sortable(tbody, {
//     animation: 150,
//     group: 'table-group', // 父子同组
//     // handle: '.drag-handle', // 指定拖拽手柄的类名（可选）
//     // filter: '.disabled-row', // 排除不可拖拽的行
//     draggable: 'tr', // 明确指定可拖拽的元素
//     preventOnFilter: false, // 允许默认事件
//     onStart: (evt: any) => {
//       evt.item.style.opacity = '0.8' // 拖拽时透明效果
//       const newData = [...tableData.value]
//       // const [movedItem] = newData.splice(evt.oldIndex, 1)
//     },
//     onEnd: (evt: any) => {
//       evt.item.style.opacity = '1'
//       const newData = [...tableData.value]
//       const [movedItem] = newData.splice(evt.oldIndex, 1)
//       newData.splice(evt.newIndex, 0, movedItem)
//       tableData.value = newData
//       // nextTick(() => {
//       //   tableData.value = newData
//       // })
//     },
//   })
// }

onMounted(() => {
  // setTimeout(() => {
  //   initSortable()
  // }, 3000)
  getTableList()
})
onActivated(() => {
  filterVal.value ? filterTableList() : getTableList()
})
</script>

<style lang="scss" scoped>
.current-level-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}

.relegation-container {
  display: flex;
  align-items: flex-start;
  // justify-content: center;
  flex-direction: column;
  margin-top: 30px;
}

.relegation-cancel-btn {
  margin-left: 10px;
}

.split-tips {
  width: 540px;
  height: 52px;
  background: #fffae1;
  border-radius: 3px;
  border: 1px solid #f5dd8c;
  font-size: 12px;
  font-weight: 500;
  color: #7c5125;
  line-height: 17px;
  display: flex;
  // align-items: center;
  justify-content: center;
  margin-top: 10px;
  padding-top: 8px;

  .warning {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #fdae35;
    color: #fff;
    font-style: italic;
    font-weight: bold;
    margin-left: 3px;
    font-size: 12px;
    margin-right: 6px;
  }
}

:deep(.split-dialog .n-input) {
  width: 80% !important;
}

:deep(.split-dialog .n-select) {
  width: 80% !important;
}

:deep(.split-dialog .n-tree-select) {
  width: 80% !important;
}

:deep(.relegation-dialog .n-select) {
  width: 80% !important;
}

:deep(.n-dialog__action) {
  display: flex !important;
  justify-content: center !important;
}

.dialog-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;

  .sure-btn {
    width: 80px;
    height: 30px;
    background: #cb0000;
    border-radius: 3px;
    font-size: 14px;
    font-weight: 400;
    color: #ffffff;
  }

  .cancel-btn {
    width: 80px;
    height: 30px;
    background: #ffffff;
    border-radius: 3px;
    border: 1px solid #d8d9da;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    margin-left: 12px;
  }
}

.custom-header {
  width: 100% !important;
  height: 50px;
  background: #f2f4f8;
  font-size: 14px;
  font-family: PingFangSC, PingFang SC, serif;
  font-weight: 500;
  color: #333333;
  padding: 15px 0 0 16px;
}

.select-person {
  width: 690px;
  background: #f7f9fc;
  margin-left: 16px;
}

:deep(.n-transfer-filter) {
  margin-top: 16px;
}

:deep(.n-transfer .n-transfer-list.n-transfer-list--source) {
  background: #f7f9fc;
}

:deep(.n-transfer-list-flex-container) {
  background: #f7f9fc;
}

:deep(.n-transfer-list--target .n-transfer-list-content) {
  padding-top: 20px !important;
}

/* 在全局或组件样式表中添加 */
// :deep(.n-data-table-tbody) {
//   tr {
//     cursor: move !important;
//     user-select: auto !important;
//   }
// }
</style>
