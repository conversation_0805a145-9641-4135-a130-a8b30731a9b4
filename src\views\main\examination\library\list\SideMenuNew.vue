<script lang="ts" setup>
import type { TreeOption } from 'naive-ui'
import { NEllipsis, NIcon, NInput, NPopselect } from 'naive-ui'
import { MoreVertRound, SearchRound } from '@vicons/material'
import { ref, watch } from 'vue'
import SideTreeCtn from '@/components/SideTreeCtn.vue'

interface Props {
  treeData: any[]
  defaultSelectedKeys: string[]
  title?: string
  showRootBtn?: boolean
  canUp?: boolean
  canDown?: boolean
  canEdit?: boolean
  canDelete?: boolean
  canAddChild?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '标题',
  showRootBtn: true,
  canUp: true,
  canDown: true,
  canEdit: true,
  canDelete: true,
  canAddChild: true,
})

const emits = defineEmits<{
  (e: 'move', data: any): void // 上下移动
  (e: 'saveTreeNode', data: any): void // 保存树节点
  (e: 'delNode', data: any): void // 删除节点
  (e: 'addChildNode', data: any): void // 添加子级
  (e: 'selectNodeKey', data: any): void // 选中的节点
}>()

const treeKey = ref(uuid())
const patternRef = ref()
const selectedKeys = ref<Array<string>>([])

watch(
  () => props.defaultSelectedKeys,
  () => {
    selectedKeys.value = props.defaultSelectedKeys
  },
  { deep: true, immediate: true },
)

/** 树组件右边标签 */
const maxHeight = ref('')
function uuid() {
  const s: string[] = []
  const hexDigits = '0123456789abcdef'
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substring(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4'
  s[19] = hexDigits.substring((Number(s[19]) & 0x3) | 0x8, 1)
  s[8] = s[13] = s[18] = s[23] = '-'

  return s.join('')
}
// 刷新 DOM，正确展示类别
const refreshKey = ref(uuid())
watch(
  () => props.treeData,
  () => {
    refreshKey.value = uuid()
  },
  { immediate: true, deep: true },
)
/** 上移 */
const upFn = (id: string, isChild: boolean, parentID: string) => {
  emits('move', { id, isChild, parentID, type: 'up' })
}
/** 下移 */
const downFn = (id: string, isChild: boolean, parentID: string) => {
  emits('move', { id, isChild, parentID, type: 'down' })
}
/** 编辑节点 */
const editFn = (node: TreeOption) => {
  emits('addChildNode', {
    ...node,
    model: 'modify',
    type: node.isChild ? 'sub' : 'root',
  })
}
/** 删除节点 */
const delFn = (id: string, isChild: boolean) => {
  emits('delNode', { id, isChild })
}
const addChildFn = (node: TreeOption) => {
  emits('addChildNode', { ...node, type: 'sub' })
  treeKey.value = uuid()
}
// 下拉选择选中
function handleSelectValueChange(
  value: 'up' | 'down' | 'top' | 'bottom' | 'edit' | 'delete' | 'add-child',
  node: TreeOption & { originData: any },
) {
  switch (value) {
  case 'up':
    upFn(
      node.originData.id as string,
      node.isChild as boolean,
      node.parentID as string,
    )
    break
  case 'down':
    downFn(
      node.originData.id as string,
      node.isChild as boolean,
      node.parentID as string,
    )
    break
  case 'edit':
    editFn(node)
    break
  case 'delete':
    // 删除
    window.$dialog.warning({
      title: '提示',
      content: '确定删除吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        delFn(node.originData.id as string, node.isChild as boolean)
      },
    })
    break
  case 'add-child':
    addChildFn(node)
    break
  default:
  }
}
// 点击节点后缀图标
function handleClickSuffix(e: Event) {
  e.stopPropagation()
}
// 获取后缀下拉选项
function getSuffixOptions(option: TreeOption) {
  const up = { label: '上移', value: 'up', disabled: !option.canUp }
  const down = { label: '下移', value: 'down', disabled: !option.canDown }
  const edit = { label: !option.editing ? '编辑' : '重命名', value: 'edit' }
  const deleteItem = { label: '删除', value: 'delete' }
  const addChild = {
    label: '添加子级',
    value: 'add-child',
    disabled: option.isChild,
  }
  const options = []

  props.canUp && !option.hiddleUp && options.push(up)
  props.canDown && !option.hiddleDown && options.push(down)
  props.canEdit && !option.hiddleEdit && options.push(edit)
  props.canDelete && !option.hiddleDelete && options.push(deleteItem)
  props.canAddChild && !option.hiddleAddChild && options.push(addChild)

  return options
}
function renderSuffix(info: {
  option: TreeOption & { originData: any }
  checked: boolean
  selected: boolean
}) {
  return h(
    'div',
    { onClick: handleClickSuffix },
    h(
      NPopselect,
      {
        trigger: 'click',
        placement: 'bottom-start',
        options: getSuffixOptions(info.option),
        onUpdateValue: (v: any) => handleSelectValueChange(v, info.option),
      },
      {
        default: () =>
          h(
            NIcon,
            { size: 20, color: '#999' },
            { default: () => h(MoreVertRound) },
          ),
      },
    ),
  )
}
// 树节点内容渲染函数
function labelRenderer(info: {
  option: TreeOption
  checked: boolean
  selected: boolean
}) {
  return h(
    NEllipsis,
    { style: 'max-width: 130px' },
    { default: () => info.option.label },
  )
}

// 树节点选中
function handleSelectedKeysChange(
  keys: Array<string | number>,
  option: Array<TreeOption | null>,
) {
  if (Array.isArray(option) && option.length > 0 && option[0]) {
    selectedKeys.value = [option[0]!.id as string]
    emits('selectNodeKey', option[0])
  }
}

/** 添加分类 */
const handleAddRoot = () => {
  emits('addChildNode', { type: 'root' })
}
</script>
<template>
  <side-tree-ctn
    :tree-title="title"
    :show-root-btn="showRootBtn"
    @add-root="handleAddRoot"
  >
    <template #tree>
      <div style="margin-bottom: 16px">
        <n-input v-model:value="patternRef" size="small" placeholder="搜索分类">
          <template #prefix>
            <n-icon>
              <search-round />
            </n-icon>
          </template>
        </n-input>
      </div>
      <n-scrollbar
        :style="`max-height: ${maxHeight}; ${
          maxHeight.length > 0
            ? 'box-sizing: border-box;padding-right: 20px;'
            : ''
        }`"
        :class="{ pr: maxHeight.length > 0 }"
      >
        <n-tree
          :key="refreshKey"
          block-line
          expand-on-click
          cascade
          default-expand-all
          key-field="id"
          :data="treeData"
          :pattern="patternRef"
          :render-suffix="renderSuffix"
          :render-label="labelRenderer"
          :selected-keys="selectedKeys"
          :show-irrelevant-nodes="false"
          @update-selected-keys="handleSelectedKeysChange"
        />
      </n-scrollbar>
    </template>
  </side-tree-ctn>
</template>

<style lang="scss" scoped></style>
