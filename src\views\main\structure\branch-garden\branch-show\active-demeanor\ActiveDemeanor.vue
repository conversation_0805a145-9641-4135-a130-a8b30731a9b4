<script setup lang="ts">
import { NButton } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import ActiveForm from './cpn/ActiveForm.vue'
import { getTableColumns } from './config'
// import NoticeForm from './cpn/NoticeForm.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import {
  deleteActivity,
  getActivityTableList,
} from '@/services/structure/branch-garden'
const route = useRoute()
const branchId = computed(() => route.query.id)
const filterReactive = ref<{ title: string; branchId: string }>({
  title: '',
  branchId: String(branchId.value),
})
// 有接口后添加：loading,tableData
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getActivityTableList, filterReactive, {
  batchDeleteTable: true,
  delApi: deleteActivity,
})

watch(filterReactive.value, (newVal) => {
  loadData()
})

// 新增/编辑党建清单抽屉
const idEditRef = ref()
const addNoticeFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('风采展示', handelConfirmEdit)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addNoticeFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          idEditRef.value = row.id
          editTypeRef.value = 'modify'
          showEditRef.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '编辑',
      },
    ),
    h(DeleteButton, {
      handleConfirm: () => handleSingleDelete(String(row.id)),
    }),
  ]
})

onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :loading="loading"
    title=""
    style="padding-top: 0"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :show-delete="false"
    :checked-row-keys="checkedRowKeys"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button>
    </template>
    <template #filters>
      <n-input
        v-model:value="filterReactive.title"
        size="small"
        placeholder="请输入活动名称"
        clearable
      />
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <active-form
        :id="idEditRef"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        :branch-id="(branchId as string)"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
