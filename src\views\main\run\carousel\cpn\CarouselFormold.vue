<script setup lang="ts">
import { NForm } from 'naive-ui'
import { formRules } from './config'
import type { ICarousel } from '@/services/run/carousel/types'
import {
  addCarousel,
  getCarouselDetail,
  updateCarousel,
} from '@/services/run/carousel/carousel'
import { uploadImg } from '@/services/common'
import type { uploadFileItem } from '@/services/affairs/party-building-list/exam-indicators/types'
import { SLIDER_POSITION } from '@/store/dict'
const emits = defineEmits<{
  (e: 'saved'): void
}>()
interface Props {
  type?: string
  id?: number
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: 0,
})
// 移动端模块选择列表
const oriFormDataReactive = reactive<ICarousel>({
  title: '', // 图片名称
  sort: null, // 排序
  isMobile: '0', // 是否显示 1显示 0不显示
  imgId: null,
  isPc: 0,
  content: '',
  location: 0,
})
const formDataReactive = reactive<ICarousel>({
  title: '', // 图片名称
  sort: null, // 排序
  isMobile: '0', // 是否显示 1显示 0不显示
  imgId: null,
  isPc: 0,
  content: '',
  location: 0,
})
const imgSizeReactive = reactive<{ width: number; height: number }>({
  width: 590,
  height: 350,
})
const formRef = ref<InstanceType<typeof NForm>>()
const oldImgUrlRef = ref('')
const oldImgUrlFirstFlag = ref(true)
// 图片裁剪完毕
const handleCoverDone = async(file: File) => {
  // 修改的时候 传入旧图片会触发done 此时不能上传 否则与原图id不同 会被视为更改了图片
  if (
    (!oldImgUrlFirstFlag.value && props.type === 'modify')
    || props.type === 'add'
  ) {
    const imgFileData = new FormData()
    imgFileData.append('file', file)
    try {
      if (formDataReactive.imgId) {
        return
      }
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formDataReactive.imgId = data.fileId || ''
      }
    }
    catch (error) {}
  }
  oldImgUrlFirstFlag.value = false
}
// 图片删除
function handleCoverDelete() {
  formDataReactive.imgId = undefined
}
// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
// 关闭时判断是否存在改变了的数据
function getChangedFlag() {
  return deepEqual(oriFormDataReactive, formDataReactive)
}
function deepEqual(obj1: ICarousel, obj2: ICarousel) {
  if (Object.keys(obj1).length !== Object.keys(obj2).length) {
    return false
  }

  for (const key in obj1) {
    if (!Object.prototype.hasOwnProperty.call(obj2, key)) {
      return false
    }
    if (obj1[key] !== obj2[key]) {
      return false
    }
  }

  return true
}
// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (props.id === 0 || props.type === 'add') {
        addCarousel(formDataReactive).then((res) => {
          window.$message.success('保存成功')
          emits('saved')
        })
      }
      else {
        updateCarousel(formDataReactive).then((res) => {
          window.$message.success('保存成功')
          emits('saved')
        })
      }
    }
  })
}
defineExpose({
  validateAndSave,
  resetForm,
  getChangedFlag,
})
onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getCarouselDetail(props.id).then((res) => {
      for (const k in formDataReactive) {
        formDataReactive[k] = res[k]
        oriFormDataReactive[k] = res[k]
      }
      formDataReactive.id = props.id
      oriFormDataReactive.id = props.id
      oldImgUrlRef.value = res.imgUrl ?? ''
    })
  }
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
    :disabled="props.type === 'view'"
  >
    <n-grid>
      <n-form-item-gi span="24" label="轮播图名称：" path="title">
        <n-input
          v-model:value="formDataReactive.title"
          placeholder="请输入轮播图名称"
          clearable
          maxlength="30"
          show-count
        />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="轮播图位置：" required>
        <n-select
          v-model:value="formDataReactive.location"
          style="width: 100%"
          placeholder="请选择轮播图展示位置"
          filterable
          :options="SLIDER_POSITION"
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="移动端是否展示：" required>
        <n-switch
          v-model:value="formDataReactive.isMobile"
          :checked-value="'1'"
          :unchecked-value="'0'"
        />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="排序：" path="sort">
        <n-input-number
          v-model:value="formDataReactive.sort"
          style="width: 100px"
          :min="1"
          placeholder=""
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi span="14" label="轮播图图片" path="imgId" required>
        <img-uploader
          :need-cropper="false"
          :is-readonly="props.type === 'view'"
          :width="imgSizeReactive.width"
          :height="imgSizeReactive.height"
          :old-img-url="oldImgUrlRef"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="详细说明：" path="content" required>
        <RichEditor
          v-model:value="formDataReactive.content"
          style="width: 100%"
          :disabled="props.type === 'view'"
          :rich-height="350"
        />
      </n-form-item-gi>
    </n-grid>
  </n-form>
</template>
<style lang="scss" scoped></style>
