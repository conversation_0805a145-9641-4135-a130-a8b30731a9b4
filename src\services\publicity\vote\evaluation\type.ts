// 党建考核table item type
export interface PartyBuildingEvaluationTableItemType {
  id: string
  title: string
  evaluationYear: string
  publishTime: string
  organizations: string
  targetNum: number
  targetFinishNum: string
  evaluationProgress: number
  status: string
}

// 添加党建考核Type
export interface addPartyBuildingEvaluationType {
  id?: string
  title: string
  evaluationYear: string | null
  organizationList: string[] | null
}

export interface CategoryListObj {
  // id: number | undefined
  // label: string
  examNumber: number | undefined | null
  examItem: string | null
}

/** 查看单个党建清单 */
export interface PartyBuildingDetail {
  id: string
  title: string
  evaluationYearAndMonth: string
  organizationVoList: OrganizationList[] | null
}

export interface OrganizationList {
  id: string
  name: string
}

export interface EvaluationTypeList {
  id: string
  name: string
}

/** 考核类别 */
export interface EvaluationType {
  id?: string
  /** 清单id */
  inventoryId?: string
  /** 分类名称 */
  name: string
}

/** 查看清单下的所有指标 */
export interface AllTargetList {
  assessId: string
  targetItemForm: TargetItemForm[]
  totalScore: number
}

export interface TargetItemForm {
  id: string
  categoryName: string
  targetItemList: AllTargetItemList[]
}

export interface AllTargetItemList {
  id: string
  matter: string
  evaluationRequirements: string
  evaluationMode: string
  fileList: FileList[]
  evaluationScore: number
  endTime: string
  passNum: number
  totalNum: number
  targetId: string
  dept: string
  relatedStatus: boolean
  relationId: string
}
export type AllTargetItemListRow = AllTargetItemList & { editing: boolean }

/** 关联指标项 */
export interface RelateAdd {
  categoryId: string
  targetItemIds: number[]
  inventoryId: string
}

/** 关联指标项打分列表 */
export interface RelateMarkList {
  id: string
  organizationName: string
  performanceDescription: string
  meetinglinkList: string[]
  votelinkList: string[]
  fileList: FileList[]
  performance: string
  score: number
}
export type RelateMarkListRow = RelateMarkList & { editing: boolean }

/** 关联指标项打分列表搜索参数 */
export interface RelateMarkListSearchParams {
  /** 关联打分Id */
  relationId: string
}

/** 关联指标项打分 */
export interface RelateMark {
  id: string
  /** 完成情况 */
  performance: string
  score: number
}

/**  关联指标项填写截止日期 */
export interface RelateWriteDeadLine {
  id: string
  deadline: string
}

/** 得分统计列表 */
export interface StatisticScoreList {
  organization: string
  totalScore: string
  targetItemList: TargetItemList[]
}

export interface TargetItemList {
  id: string
  evaluationRequirements: string
  evaluationMode: string
  performance: string
  score: number
}

/** 关联指标项列表 */
export interface SearchRelateList {
  inventoryId: string
  title: string
}

export interface RelateListItem {
  id: string
  title: string
  matter: string
  evaluationRequirements: string
  evaluationMode: string
  evaluationScore: number
  fileIds: FileId[]
  isRelated: boolean
}

export interface FileId {
  id: string
  fileName: string
  original: string
}

/**  考核指标表格 */
export interface ExamIndicatorsListItem {
  [key: string]: any
  id?: string
  /** 标题 */
  title: string
  /** 事项 */
  matter: string
  /** 考核标准 */
  evaluationRequirements: string
  /** 考核方式 */
  evaluationMode: string
  /** 部门 */
  dept: string
  /** 考核分数 */
  evaluationScore: number
  /** 状态 */
  relatedStatus?: string
}

// 查看指标项详情
export interface IssueDetailType {
  id?: string
  matter: string
  title: string
  evaluationMode: string
  evaluationRequirements: string
  evaluationScore: string | number
  dept: string
  relatedStatus: boolean
}

/**  指标项清单添加 */
export interface ExamIndicatorsAdd {
  [key: string]: any
  id?: number
  /** 清单标题 */
  title: string
  /** 工作要求 */
  jobRequire: string
  /** 考核方式 */
  examMethods: string
  /** 考核分数 */
  examScore: number | null
  /** 附件 */
  attachment: null | File
}

/** 党组织下拉选择树 */
export interface partyOrganizationItem {
  id?: string
  parentId?: string
  weight?: number
  name: string
  org_type?: string
  createTime?: string
  code?: string
  children: partyOrganizationItem[] | null
}
