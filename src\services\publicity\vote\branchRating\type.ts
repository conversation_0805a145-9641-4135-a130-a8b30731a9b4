export interface BranchRatingListType {
  id: string
  name: string
  year: string
  startTime: string
  endTime: string
  selfEvaluationNum: number
  reEvaluation: number
  status: number
  reEvaluationNum: number
  reviewEvaluationNum: number
  partyBrandNum: number
  partyBrandStarAssessItemId?: string | number
}

// 添加党支部星级评定
export interface AddBranchRatingType {
  id?: string
  name: string
  year: string | null
  startTime: string | null
  endTime: string | null
  partyBrandStarAssessItemId: string | null
  partyBrandStarAssessItemName?: string | null
}

// 复评其他项
export interface ReEvaluationItemOther {
  examineItemOtherId: string
  assessResultId: string
  examineItemOtherName: string
  resultContent: string
}

// 复评单项
export interface ReEvaluationItem {
  id: string
  partyBrandStarAssessId: string
  partyBrandStarExamineId: string
  partyBrandStarExamineItemId: string
  partyBrandStarExamineItemName: string
  rankResult: string
  score: string
  resultOtherVO: Array<ReEvaluationItemOther>
}

// 复评总数据
export interface ReEvaluationDataType {
  partyBrandStarAssessId: string | number
  orgId: string | number
  assessScore: string | number
  selfEvaluationAnswer: string | number
  selfEvaluationStatus: string | number
  reviewEvaluationAnswer: string | number
  reviewEvaluationStatus: string | number
  resultVOList: Array<ReEvaluationItem>
}
