import { debounce } from 'lodash-es'

export function useDrawerEdit(
  name: string | (() => string),
  confirmFn: () => void,
  titleAdd?: string,
  titleModify?: string,
) {
  const showEditRef = ref(false)
  const editTypeRef = ref<'add' | 'addSubset' | 'modify' | 'view'>('add')
  const drawerTitle = computed(() =>
    editTypeRef.value === 'add' || editTypeRef.value === 'addSubset'
      ? `${titleAdd ?? `新增${name}`}`
      : editTypeRef.value === 'modify'
        ? `${titleModify ?? `编辑${name}`}`
        : `${titleModify ?? `查看${name}`}`,
  )
  function handleClickCancel() {
    showEditRef.value = false
  }

  const debouncedConfirm = debounce(confirmFn, 800)
  function handleClickConfirm() {
    if (editTypeRef.value !== 'view') {
      // confirmFn()
      debouncedConfirm()
    }
    else {
      showEditRef.value = false
    }
  }
  return {
    showEditRef,
    editTypeRef,
    drawerTitle,

    handleClickConfirm,
    handleClickCancel,
  }
}
