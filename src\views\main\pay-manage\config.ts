import type { DataTableColumns } from 'naive-ui'
import type { VNodeChild } from 'vue'
import type { OrganizationItemTableItem } from '@/services/organization/types'

// 审核状态字典
export const REVIEWSTATUS = {
  // 审核状态（1-待审核 2-通过 3-未通过）
  WAIT: '1',
  PASS: '2',
  NOT: '3',
}

// 支付状态字典
export const PAYSTATUS = {
  //  缴纳状态（1-未支付 2-已支付）
  NOT: '1',
  PASS: '2',
}

// 支付方式字典
export const PAYTYPESTATUS = {
  // 支付方式（1-微信 2-支付宝 3-工商银行（微信）4-工商银行（支付宝））
  WECHAT: '1',
  ALI: '2',
  BANK_WECHAT: '3',
  BANK_ALI: '4',
}

// 审核状态选项
export const REVIEWSTATUSOPTIONS = [
  {
    label: '待审核',
    value: REVIEWSTATUS.WAIT,
  },
  {
    label: '通过',
    value: REVIEWSTATUS.PASS,
  },
  {
    label: '未通过',
    value: REVIEWSTATUS.NOT,
  },
]

// 缴费记录列表
export function getPayRecordColumns(
  optionColumnRenderer: (row: any) => VNodeChild,
): DataTableColumns<OrganizationItemTableItem> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'orderCode',
      title: '订单号',
    },
    {
      key: 'userName',
      title: '姓名',
    },
    {
      key: 'userPhone',
      title: '手机号码',
    },
    {
      key: 'unitName',
      title: '所在组织',
      width: '14%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'returnTime',
      title: '月份',
      render: (row: any) => {
        if (row.returnTime) {
          return row.returnTime.slice(0, 7)
        } else {
          return ''
        }
      },
    },
    {
      key: 'totalAmount',
      title: '应缴金额（元）',
    },
    {
      key: 'payStatus',
      title: '缴纳状态',
      render: (row: any) => {
        if (row.payStatus === PAYSTATUS.NOT) {
          return '未支付'
        } else if (row.payStatus === PAYSTATUS.PASS) {
          return '已支付'
        } else {
          return ''
        }
      },
    },
    {
      key: 'paymentType',
      title: '支付方式',
      render: (row: any) => {
        if (row.paymentType === PAYTYPESTATUS.WECHAT) {
          return '微信'
        } else if (row.paymentType === PAYTYPESTATUS.ALI) {
          return '支付宝'
        } else if (row.paymentType === PAYTYPESTATUS.BANK_WECHAT) {
          return '工商银行（微信）'
        } else if (row.paymentType === PAYTYPESTATUS.BANK_ALI) {
          return '工商银行（支付宝）'
        } else {
          return ''
        }
      },
    },
    {
      key: 'payTime',
      title: '支付时间',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '10%',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 党费统计列表
export function getPayStatisticsColumns(): DataTableColumns<OrganizationItemTableItem> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'userName',
      title: '姓名',
    },
    {
      key: 'userPhone',
      title: '手机号码',
    },
    {
      key: 'unitName',
      title: '所在组织',
      width: '14%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'jan',
      title: '1月',
    },
    {
      key: 'feb',
      title: '2月',
    },
    {
      key: 'mar',
      title: '3月',
    },
    {
      key: 'apr',
      title: '4月',
    },
    {
      key: 'may',
      title: '5月',
    },
    {
      key: 'jun',
      title: '6月',
    },
    {
      key: 'jul',
      title: '7月',
    },
    {
      key: 'aug',
      title: '8月',
    },
    {
      key: 'sep',
      title: '9月',
    },
    {
      key: 'oct',
      title: '10月',
    },
    {
      key: 'nov',
      title: '11月',
    },
    {
      key: 'dec',
      title: '12月',
    },
    {
      key: 'total',
      title: '合计',
    },
  ]
}

// 党费基数设定列表
export function getPayBaseColumns(
  optionColumnRenderer: (row: any) => VNodeChild,
): DataTableColumns<OrganizationItemTableItem> {
  return [
    {
      type: 'selection',
      multiple: true,
    },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'userName',
      title: '姓名',
    },
    {
      key: 'userPhone',
      title: '手机号码',
    },
    {
      key: 'unitName',
      title: '所在组织',
      width: '14%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'partyIdentityName',
      title: '职务',
    },
    {
      key: 'returnTime',
      title: '月份',
      render: (row: any) => {
        if (row.returnTime) {
          return row.returnTime.slice(0, 7)
        } else {
          return ''
        }
      },
    },
    {
      key: 'totalAmount',
      title: '应缴金额（元）',
    },
    {
      key: 'payStatus',
      title: '支付状态',
      render: (row: any) => {
        if (row.payStatus === PAYSTATUS.NOT) {
          return '未支付'
        } else if (row.payStatus === PAYSTATUS.PASS) {
          return '已支付'
        } else {
          return ''
        }
      },
    },
    {
      key: 'reviewStatus',
      title: '审核状态',
      render: (row: any) => {
        if (row.reviewStatus === REVIEWSTATUS.NOT) {
          return '未通过'
        } else if (row.reviewStatus === REVIEWSTATUS.PASS) {
          return '通过'
        } else if (row.reviewStatus === REVIEWSTATUS.WAIT) {
          return '待审核'
        } else {
          return ''
        }
      },
    },
    {
      key: 'updateTime',
      title: '最后更新时间',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '10%',
      render: row => optionColumnRenderer(row),
    },
  ]
}
