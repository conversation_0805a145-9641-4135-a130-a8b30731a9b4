/*
 * @Description: 资讯管理相关数据
 * @Author: 朱备 <zhu<PERSON>@easestrategy.com>
 * @Date: 2021-11-22 15:03:46
 * @LastEditTime: 2022-01-10 17:54:14
 * @LastEditors: 朱备 <<EMAIL>>
 */
import { defineStore } from 'pinia'
import type { SelectOption } from 'naive-ui'
import type { INews, INewsColumn, INewsQuery } from '@/service'
import { getNewsColumnList, getNewsList } from '@/service'
import type { ITreeNode } from '@/components/awesome-tree'

function transformToTreeNode(data: INewsColumn[]): ITreeNode[] {
  return data.map((item) => {
    const node: ITreeNode = {
      nodeId: item.id!,
      nodeName: item.columnName,
      pid: item.pid,
      children: null,
    }
    if (item.childrenList && item.childrenList.length) {
      node.children = transformToTreeNode(item.childrenList)
    }
    return node
  })
}

const useNewsStore = defineStore('news', {
  state: () => ({
    newsColumnList: [] as ITreeNode[], // 资讯类别列表
    newsList: [] as INews[], // 资讯列表
    columnOptions: [] as SelectOption[], // 类别选项
  }),

  actions: {
    async getNewsColumnListAction() {
      const res = await getNewsColumnList()

      this.newsColumnList = transformToTreeNode(res)
      this.columnOptions = this.newsColumnList.map(item => ({
        value: item.nodeId,
        label: item.nodeName,
      }))
    },

    async getNewsListAction(columnId: number, params: INewsQuery) {
      const res = await getNewsList(columnId, params)
      this.newsList = res.list
      return Promise.resolve(res.total)
    },
  },
})

export default useNewsStore
