import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { CultureShowListItem } from '@/services/run/interaction/types'

export function getTableColumns(
  handleUpdateValueRender: (row: CultureShowListItem) => VNodeChild,
  optionColumnRenderer: (row: CultureShowListItem) => VNodeChild,
): DataTableColumns<CultureShowListItem> {
  return [
    {
      type: 'selection',
    },
    {
      key: 'index',
      title: '序号',
      width: '100',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'username',
      title: '姓名',
    },
    {
      key: 'orgName',
      title: '组织名称',
      render: row => (row.orgName ? row.orgName : '--'),
    },
    // {
    //   key: 'title',
    //   title: '主题',
    // },
    {
      key: 'content',
      title: '心得内容',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },

    {
      key: 'publishTime',
      title: '发布时间',
    },
    {
      key: 'topStatus',
      title: '置顶',
      render: row => handleUpdateValueRender(row),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
