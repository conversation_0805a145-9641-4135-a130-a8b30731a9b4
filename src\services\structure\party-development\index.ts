import type {
  ContactListItem,
  DevAndSaveInfo,
  DevMemberChoseList,
  PartyDevelopmentListItem,
  PartyDevelopmentMemberAdd,
  StageListAndBaseInfo,
} from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 获取党员发展列表 */
export function getPartyDevelopmentList(
  params: { name: string; sort: number } & PaginationReq,
) {
  return commonReq.get<PaginationRes<PartyDevelopmentListItem>>({
    url: '/party-affairs/backend/v1/party-member-develop/user',
    params,
  })
}

/** 撤销当前阶段 */
export function putCancelCurrentStage(params: { id: string }) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/party-member-develop',
    params,
  })
}

/** 删除党员发展表格记录 */
export function deletePartyMember(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/party-member-develop/user',
    params: { ids },
  })
}

/** 获取党员的基本信息和发展阶段 */
export function getStageListAndBaseInfo(id: string) {
  return commonReq.get<StageListAndBaseInfo>({
    url: `/party-affairs/backend/v1/party-member-develop/${id}`,
  })
}

/** 发展党员保存信息 */
export function postPartyDevSaveInfo(data: DevAndSaveInfo) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/party-member-develop',
    data,
  })
}
/** 发展党员编辑信息 */
export function putPartyDevSaveInfo(data: DevAndSaveInfo) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/party-member-develop',
    data,
  })
}

/** 获取联系人列表 */
export function getContractList(deptId: string) {
  return commonReq.get<ContactListItem>({
    url: `/party-affairs/backend/v1/party-member-develop/user/contact/${deptId}`,
  })
}

/** 获取党员选择列表 */
export function getUserList() {
  return commonReq.get<DevMemberChoseList>({
    url: '/party-affairs/backend/v1/party-member-develop/user/chosen-list',
  })
}

/** 新建入党申请 */
export function postPartyDevelopmentList(data: PartyDevelopmentMemberAdd) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/party-member-develop/user',
    data,
  })
}

/** 编辑入党申请 */
export function putPartyDevelopmentList(data: PartyDevelopmentMemberAdd) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/party-member-develop',
    data,
  })
}
