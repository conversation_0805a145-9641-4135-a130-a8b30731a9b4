<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { formRules } from './config'

import type { NoticeDetail } from '@/services/structure/branch-garden/types'
import {
  getNoticeDetail,
  postNotice,
  putNotice,
} from '@/services/structure/branch-garden'
import type {
  uploadFileItem,
  uploadFileItemNew,
} from '@/services/affairs/party-building-list/exam-indicators/types'
import { uploadImg } from '@/services'

interface Props {
  branchId: string
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<NoticeDetail>({
  branchId: props.branchId,
  id: '',
  title: '',
  content: '',
  isTop: '0',
  fileIds: [],
  fileList: [],
})
const fileIdObj = reactive<{ fileIdsArr: Array<string> }>({ fileIdsArr: [] })
const loading = ref(false)
const formRef = ref<InstanceType<typeof NForm>>()
// 文件相关
async function handleFileChange(
  fileInfoList: uploadFileItemNew[],
  isDelIDs: string,
) {
  try {
    const fileData = new FormData()
    // 获取所有FileID
    if (!fileIdObj.fileIdsArr.length) {
      formDataReactive.fileList?.forEach((item: any) =>
        fileIdObj.fileIdsArr.push(item.id),
      )
    }
    // 删除动作
    if (isDelIDs || isDelIDs === null) {
      if (isDelIDs === null && fileIdObj.fileIdsArr.length) {
        fileIdObj.fileIdsArr.splice(fileIdObj.fileIdsArr.length - 1, 1)
      } else {
        fileIdObj.fileIdsArr.forEach((item, index) => {
          if (item === isDelIDs) {
            fileIdObj.fileIdsArr.splice(index, 1)
          }
        })
      }
      formDataReactive.fileIds = [...fileIdObj.fileIdsArr]
    } else {
      // 新增动作
      const lastFileItem = fileInfoList[fileInfoList.length - 1]?.file
      fileData.append('file', lastFileItem as Blob)
      const data: uploadFileItem = await uploadImg(fileData)
      formDataReactive.fileList?.push({
        original: lastFileItem?.name as string,
        fileName: '',
        id: '',
      })
      if (data) {
        fileIdObj.fileIdsArr.push(data.fileId)
        formDataReactive.fileIds = [...fileIdObj.fileIdsArr]
      }
    }
  } catch (error) {}
}
onMounted(async() => {
  loading.value = true
  if (props.type === 'modify' && props.id) {
    try {
      const res = await getNoticeDetail(props.id)
      formDataReactive.id = res.id
      formDataReactive.title = res.title
      formDataReactive.content = res.content
      formDataReactive.isTop = String(res.isTop)
      formDataReactive.fileList = res.fileList?.map((file: any) => {
        return {
          id: file.id,
          original: file.original,
          fileName: file.fileName,
        }
      })
      formDataReactive.fileIds = res.fileList?.map(item => item.id) || []

      loading.value = false
    } catch (error) {}
  }
})

// 验证表单,调用接口
async function validateAndSave() {
  loading.value = true
  const errors = await new Promise((resolve) => {
    formRef.value?.validate((errors) => {
      resolve(errors)
    })
  })

  if (!errors) {
    const saveFunction
      = props.type === 'modify' && props.id ? putNotice : postNotice
    const saveData = { ...formDataReactive, fileList: undefined }
    try {
      const res = await saveFunction(saveData)
      if (res) {
        window.$message.success('保存成功')
        emits('saved')
      }
      loading.value = false
    } catch (error) {}
  }
}
// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="90"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-grid>
      <n-form-item-gi label="公告标题" :span="24" path="title">
        <n-input
          v-model:value="formDataReactive.title"
          placeholder="请输入公告标题"
          maxlength="40"
          show-count
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi label="内容" :span="24" path="content">
        <RichEditor
          v-model:value="formDataReactive.content"
          style="width: 100%"
          :rich-height="350"
        />
      </n-form-item-gi>
      <n-form-item-gi label="是否置顶：" path="isTop" :span="24">
        <n-switch
          v-model:value="formDataReactive.isTop"
          checked-value="1"
          unchecked-value="0"
        />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="附件：">
        <file-uploader
          :max="100"
          accept=".pdf"
          :size-limit="50"
          :original-file-list="(formDataReactive.fileList as any)"
          @file-list-change="handleFileChange"
        >
          <template #tips>
            <span class="tips">
              可上传多个文件，仅支持扩展名：.pdf,大小 50M 以内
            </span>
          </template>
        </file-uploader>
      </n-form-item-gi>
    </n-grid>
  </n-form>
</template>

<style lang="scss" scoped></style>
