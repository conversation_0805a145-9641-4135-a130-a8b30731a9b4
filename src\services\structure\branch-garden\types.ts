export interface BranchGardenTableItem {
  id: string
  orgName: string
  coverUrl: string
  content: string
  updateTime: string
}

export interface BranchGardenDetailItem {
  id: string
  orgId: string | null
  orgName: string | null
  content: string
  coverId: string
  coverUrl: string
  moduleList: ModuleList[]
}
/** 支部详情 - 变更后版本 */
export interface BranchDetail {
  /** 简介 */
  content: string
  id?: string
  /** 支部介绍 */
  introduce: string
  /** 支部 */
  orgId: string | null
  /** 支部名称 */
  orgName: string | null
}

export interface ModuleList {
  id: string
  name: string
  content: string
}

/** 公示公告列表 */
export interface NoticeItem {
  id: string
  title: string
  isTop: number
  updateTime: string
}

// /** 公示公告详情 */
// export interface NoticeDetail{
//   id: string
//   title: string
//   orgName: any
//   content: string
//   isTop: number
//   createTime: string
//   fileList: any
// }

/** 公示公告详情 */
export interface NoticeDetail {
  /** 支部园地id */
  branchId: string
  /** 内容 */
  content: string
  /** 附件列表 */
  fileIds: Array<string>
  fileList?: Array<fileItem>
  /** 主键ID */
  id?: string
  /** 是否置顶 */
  isTop?: string
  /** 标题 */
  title: string
}

export interface fileItem {
  fileName: string
  id: string
  original: string
}

/** 活动展示列表 */
export interface ActiveDemeanorItem {
  id: string
  title: string
  imgId: string
  imgUrl: string
  sort: number
  isMobile: string
  updateTime: string
}

/** 活动展示列表详情 */
export interface ActiveDemeanorDetail {
  /** 支部园地id */
  branchId: string
  id: string
  title: string
  imgId: string | null | undefined
  imgUrl: string
  sort: number | null
  isMobile: string | null
  updateTime?: string
}
