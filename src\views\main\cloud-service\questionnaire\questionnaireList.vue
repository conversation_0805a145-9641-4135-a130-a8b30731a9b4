<template>
  <div class="px-[20px] pt-[25px]">
    <div class="flex justify-between mt-[20px]">
      <div class="flex">
        <n-button type="primary" @click="addQuestionnaireBtn">
          <template #icon>
            <n-icon size="16" class="mr-[5px]">
              <group-add-round />
            </n-icon>
          </template>
          添加
        </n-button>
      </div>
      <div>
        <n-input
          v-model:value="title"
          class="mr-[10px]"
          placeholder="请输入问卷名称进行搜索"
          style="width: 220px"
          clearable
        />
      </div>
    </div>
    <n-data-table
      :columns="columns"
      :data="tableData"
      class="mt-[20px]"
      :loading="loadingFlag"
      max-height="calc(100vh - 380px)"
    />
    <div class="flex items-center justify-between mt-[30px] pr-[2px]">
      <span class="text-[#BDBDBD] text-[12px] mr-[30px]">共
        <span class="text-[#262626] mx-[6px]">{{ total }}</span>
        条</span>
      <n-pagination
        v-model:page="pageNum"
        v-model:page-size="pageSize"
        :item-count="total"
        :page-sizes="[5, 10, 20, 30, 50]"
        show-quick-jumper
        show-size-picker
      />
    </div>
  </div>
  <my-drawer
    :confirm-fn="addConfirm"
    :show="showVisible"
    :width="602"
    :title="drawerTitle"
    :show-btn="editTypeRef !== 'view'"
    @change-visible="(res:any)=>showVisible = res"
  >
    <questionnaireForm
      :id="currentQuestionnaireId"
      ref="formRef"
      :type="editTypeRef"
      :status="editStatusRef"
      @saved="
        () => {
          showVisible = false
          loadData()
        }
      "
    />
  </my-drawer>
</template>
<script lang="ts" setup>
import { GroupAddRound } from '@vicons/material'
import { debounce } from 'lodash-es'
import { NButton } from 'naive-ui'
import { createColumn } from './config'
import questionnaireForm from './questionnaireForm.vue'
import { usePagination } from '@/hooks/use-pagination'
import {
  cancelPublishQuestionnaire,
  delQuestionnaire,
  exportQuestionnaire,
  getQuestionnaireList,
  publishQuestionnaire,
} from '@/services/cloud-service/questionnaire'
// import type { Questionnaire } from '@/services/cloud-service/questionnaire/types'
import { QUESTIONNAIRESTATUSMAP } from '@/constant'
import { downloadArrayBuffer } from '@/utils/downloader'
import { formatTimeStamp } from '@/utils/format'

const { total, pageNum, pageSize } = usePagination(loadData, 10)
const tableData = ref()
const title = ref()
const showVisible = ref(false)
const formRef = ref()
const editTypeRef = ref('')
// 编辑问卷的状态
const editStatusRef = ref('')
const drawerTitle = ref('')
const loadingFlag = ref(false)
const currentQuestionnaireId = ref('')
const router = useRouter()

function updateFn(
  id: string,
  type: 'delete' | 'publish' | 'cancelPublish' | 'export',
) {
  if (!id) {
    window.$message.error('请选择删除项')
    return
  }
  let typeName = ''
  let func = delQuestionnaire
  if (type === 'delete') {
    typeName = '删除'
    func = delQuestionnaire
  } else if (type === 'publish') {
    typeName = '发布'
    func = publishQuestionnaire
  } else if (type === 'cancelPublish') {
    typeName = '取消发布'
    func = cancelPublishQuestionnaire
  } else if (type === 'export') {
    typeName = '导出'
    func = exportQuestionnaire
  }
  window.$dialog.create({
    type: 'default',
    closable: false,
    content: `确认${typeName}吗？`,
    showIcon: false,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      func({ id }).then((res) => {
        if (type === 'export') {
          downloadArrayBuffer(
            res,
            `问卷导出-${formatTimeStamp(Date.now(), 'YYYYMMDDHHmmss')}`,
          )
        }
        window.$message.success(`${typeName}成功！`)
        loadData()
      })
    },
  })
}

const columns = createColumn((row) => {
  const viewBtn = h(
    NButton,
    {
      text: true,
      type: 'primary',
      onClick: () => {
        editTypeRef.value = 'view'
        currentQuestionnaireId.value = row.id as string
        drawerTitle.value = '查看问卷'
        showVisible.value = true
      },
    },
    { default: () => '查看' },
  )

  const modifyBtn = h(
    NButton,
    {
      text: true,
      type: 'primary',
      onClick: () => {
        editTypeRef.value = 'modify'
        editStatusRef.value = row.questionnaireStatus
        currentQuestionnaireId.value = row.id as string
        drawerTitle.value = '修改问卷'
        showVisible.value = true
      },
    },
    { default: () => '修改' },
  )

  const publishBtn = h(
    NButton,
    {
      text: true,
      type: 'primary',
      onClick: () => updateFn(row.id, 'publish'),
    },
    { default: () => '发布' },
  )

  const cancelPublishBtn = h(
    NButton,
    {
      text: true,
      type: 'primary',
      onClick: () => updateFn(row.id, 'cancelPublish'),
    },
    { default: () => '取消发布' },
  )

  const exportBtn = h(
    NButton,
    {
      text: true,
      type: 'primary',
      onClick: () => updateFn(row.id, 'export'),
    },
    { default: () => '导出' },
  )

  const delBtn = h(
    NButton,
    {
      text: true,
      type: 'primary',
      onClick: () => updateFn(row.id, 'delete'),
    },
    { default: () => '删除' },
  )

  const toDetailsBtn = h(
    NButton,
    {
      text: true,
      type: 'primary',
      onClick: () => {
        router.push({
          name: 'questionnaireDetail',
          query: { id: row.id, questionnaireStatus: row.questionnaireStatus },
        })
      },
    },
    { default: () => '问卷项管理' },
  )

  const res = []
  res.push(viewBtn)
  res.push(modifyBtn)
  if (row.questionnaireStatus === QUESTIONNAIRESTATUSMAP.NOPUBLISH) {
    res.push(publishBtn)
    // res.push(toDetailsBtn)
    res.push(delBtn)
  }
  if (row.questionnaireStatus === QUESTIONNAIRESTATUSMAP.NOSTARTED) {
    res.push(cancelPublishBtn)
  }
  if (
    row.questionnaireStatus !== QUESTIONNAIRESTATUSMAP.NOPUBLISH
    && row.questionnaireStatus !== QUESTIONNAIRESTATUSMAP.NOSTARTED
  ) {
    res.push(exportBtn)
  }
  res.push(toDetailsBtn)

  return h(
    'div',
    {
      style: {
        color: '#AC241D',
        cursor: 'pointer',
        display: 'flex',
        gap: '10px',
      },
    },
    res,
  )
})
async function initPageSize() {
  pageNum.value = 1
  loadData()
}
async function loadData() {
  loadingFlag.value = true
  const payload = {
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }

  if (title.value) {
    Object.assign(payload, { title: title.value })
  }
  const res = await getQuestionnaireList(payload)
  total.value = res.total
  tableData.value = res.records
  loadingFlag.value = false
}

function addConfirm() {
  formRef.value?.validateAndSave()
}
function addQuestionnaireBtn() {
  editTypeRef.value = 'add'
  drawerTitle.value = '添加问卷'
  showVisible.value = true
}
// function modifyQuestionnaireBtn(row: Questionnaire) {
//   drawerTitle.value = '修改问卷'
//   currentQuestionnaireId.value = row.id as string
//   showVisible.value = true
// }

watch(title, debounce(initPageSize, 500))

watch(showVisible, (newV) => {
  if (!newV) {
    formRef.value?.resetForm()
  }
})
</script>

<style lang="scss" scoped></style>
