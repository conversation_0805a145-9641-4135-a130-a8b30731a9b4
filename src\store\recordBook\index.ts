import { defineStore } from 'pinia'
import type { UnwrapRef } from 'vue'
import type { Department, StateType } from './types'
import { getRecordBookCategoryList } from '@/services/recordBookMaintenance'
import type { CatalogType } from '@/services/recordBookMaintenance/types'
import { fetchDownloadFile } from '@/utils/downloader'
import { numberToChinese } from '@/utils/utils'

export const useRecordBookStore = defineStore('recordBook', {
  persist: {
    key: 'recordBook',
    storage: sessionStorage,
  }, // 开启持久化
  state: (): StateType => ({
    currentSelectedDept: {
      deptId: '',
      deptName: '',
    },
    currentSelectedYear: new Date().getFullYear().toString(),
    currentSelectedMeetingType: null, // 当前选中的会议类型
    currentSelectedTransferType: null, // 当前选中的转接类型
    recordBookList: [],
    currentSelectedChapter: {
      title: '',
      value: '',
    },
    organizationListTreeList: [],
  }),

  actions: {
    // 设置组织树list
    setOrganizationListTreeList(list: Array<any>): void {
      this.organizationListTreeList = list
    },
    // 设置当前选中的转接类型
    setCurrentSelectedTransferType(transferType: string | null): void {
      this.currentSelectedTransferType = transferType
    },
    // 设置当前选中的会议类型
    setCurrentSelectedMeetingType(meetingType: string | null): void {
      this.currentSelectedMeetingType = meetingType
    },
    // 设置当前选中的章节
    setCurrentSelectedChapter(chapter: { title: string; value: string }): void {
      this.currentSelectedChapter = { ...chapter }
    },
    // 设置当前选中的部门
    setCurrentSelectedDept(dept: Department): void {
      this.currentSelectedDept = { ...dept }
    },

    // 设置当前选中的年份
    setCurrentSelectedYear(year: string): void {
      this.currentSelectedYear = year
    },

    // 获取目录列表
    getChapterList() {
      getRecordBookCategoryList().then((res) => {
        this.recordBookList = res.map((item: CatalogType, index: number) => {
          return {
            ...item,
            title: `${numberToChinese(index + 1)}、${item.title}`,
            // type: item.title.includes('会议记录') ? 'meetingRecord' : '',
            type: calcType(item.title),
          }
        })
      })
    },

    async fetchDownLoadFile(
      params: {
        deptId: string
        year: string
        meetingType?: string
        startTimeSort?: string
        transferType?: string
        transferTimeSort?: string
        type?: 'word' | 'pdf'
      },
      callback?: () => void,
    ) {
      try {
        const downLoadFlag: boolean = await fetchDownloadFile(
          this.currentSelectedChapter.value,
          `${
            params.year
          }年度组织生活记录本-${this.currentSelectedChapter.title.replace(
            /^[一二三四五六七八九十]+、/,
            '',
          )}`,
          {
            ...params,
            deptId: params.deptId,
            year: params.year,
          },
        )
        if (downLoadFlag) {
          callback?.()
          window.$message.success('下载成功')
        }
        else {
          callback?.()
          window.$message.error('下载失败')
        }
      }
      catch (error) {
        callback?.()
        window.$message.error('下载失败')
      }
    },
  },

  getters: {
    // 获取组织树list
    getOrganizationListTreeList(): UnwrapRef<
    StateType['organizationListTreeList']
    > {
      return this.organizationListTreeList
    },
    getRecordBookList(): UnwrapRef<StateType['recordBookList']> {
      return this.recordBookList
    },

    getCurrentSelectedDept(): Department {
      return this.currentSelectedDept
    },

    getCurrentSelectedYear(): string {
      return this.currentSelectedYear
    },

    // 获取当前会议类型
    getCurrentSelectedMeetingType(): string | null {
      return this.currentSelectedMeetingType
    },
    getCurrentSelectedTransferType(): string | null {
      return this.currentSelectedTransferType
    },

    getYearOptions(): Array<{ label: string; value: string }> {
      const currentYear = new Date().getFullYear()
      const startYear = 2020
      const years = []

      for (let year = currentYear; year >= startYear; year--) {
        years.push({
          label: year.toString(),
          value: year.toString(),
        })
      }

      return years
    },
  },
})

function calcType(title: string): string {
  const typeList = [
    {
      label: '会议记录',
      value: 'meetingRecord',
    },
    {
      label: '组织关系转接',
      value: 'partyOrganizationalTransfer',
    },
    { label: '党员名册', value: 'partyMembersRosterType' },
    { label: '党员听党课情况', value: 'partyMembersAttendLectures' },
    { label: '入党积极分子名册', value: 'activeMembersParty' },
    { label: '支部基本组织状况', value: 'branchBasicOrganizationalStatus' },
    { label: '民主评议党员情况', value: 'democraticEvaluationPartyMembers' },
    { label: '党员议事记录', value: 'partyMemberMeetingMinutes' },
    { label: '党内情况通报记录', value: 'internalPartyReportingRecords' },
    { label: '重要事项征求意见记录', value: 'importantMattersOpinionsRecords' },
    { label: '党员交纳党费记录', value: 'partyDuesPaymentRecords' },
    { label: '党支部年度工作计划', value: 'annualPlanPartyBranch' },
    {
      label: '党支部书记参加上级党组织集中轮训情况',
      value: 'trainingForPartySecretary',
    },
    { label: '党支部年度工作总结', value: 'partyBranchAnnualSummary' },
    { label: '主题党日活动情况登记', value: 'partyDayActivity' },
    { label: '谈心谈话记录', value: 'heartTalkRecord' },
    { label: '党员受党内、行政奖励情况', value: 'rewardStatus' },
    {
      label: '党员受党内、行政处分和不合格党员处置情况',
      value: 'handleStatus',
    },
    {
      label: '组织入党积极分子学习、活动情况',
      value: 'partyMembersActivities',
    },
    {
      label: '党支部参加党委（总支）组织的活动情况登记',
      value: 'partyBranchActivities',
    },
  ]
  for (let i = 0; i < typeList.length; i += 1) {
    const item = typeList[i]
    if (title.includes(item.label)) {
      return item.value
    }
  }
  return ''
}
