<script lang="ts" setup>
import { debounce } from 'lodash-es'
import { NSwitch } from 'naive-ui'
import { createColumns } from './config'
import SideMenu from '@/components/SideMenu.vue'
import {
  addCategoryClassification,
  deleteCategoryClassification,
  deleteNewsItem,
  fetchCategoryClassification,
  loadCategoryTable,
  modifyRecommend,
  modifyTop,
  operateCategoryClassification,
} from '@/services'
import { usePagination } from '@/hooks/use-pagination'
import { sessionCache } from '@/utils/cache'
import DeleteButton from '@/components/DeleteButton.vue'
// import { useDelete } from '@/hooks'
// const { delItem } = useDelete(deleteNewsItem)

interface itemType {
  id: string
  name: string
  sort: number
  categoryFlag: string
  rename?: boolean
}

const { total, pageNum, pageSize } = usePagination(loadData, 10)
const sideMenuInfo = ref<itemType[]>([])
const selectedId = ref('0')
const title = ref()
/** 表格标题 */
const tableTitle = ref('')

async function loadClassification() {
  // 从 sessionStorage 中获取之前选中的类别
  const previousSelectedCategory = sessionStorage.getItem('current_news_column')
    ? JSON.parse(sessionStorage.getItem('current_news_column') || '')
    : null
  const res = await fetchCategoryClassification()
  if (Array.isArray(res) && res.length) {
    res.forEach(item => (item.rename = false))
    sideMenuInfo.value = res

    // 使用 sessionStorage 中的数据来设置选中的类别
    if (previousSelectedCategory) {
      selectedId.value = previousSelectedCategory.id
      tableTitle.value = previousSelectedCategory.name
    } else {
      selectedId.value = res[0].id || '-1'
      tableTitle.value = res[0].name || ''
    }
  } else {
    sideMenuInfo.value = []
  }
}

const tableData = ref()
/** 表格删除单项逻辑 */
const delNewItemFn = (ids: string) => {
  if (!ids) {
    window.$message.error('请选择删除项')
    return
  }
  window.$dialog.create({
    type: 'default',
    closable: false,
    content: '确认删除该条资讯？',
    showIcon: false,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      deleteNewsItem({ ids }).then((res) => {
        window.$message.success('删除成功！')
        loadData()
      })
    },
  })
}
async function handleSingleDelete(ids: string) {
  try {
    await deleteNewsItem({ ids })
    loadData()
    window.$message.success('删除成功')
  } catch {}
}

const loading = ref(false)
async function loadData() {
  try {
    loading.value = true
    const payload = {
      current: pageNum.value,
      size: pageSize.value,
      categoryId: selectedId.value,
    }
    if (selectedId.value) {
      Object.assign(payload, { categoryId: selectedId.value })
    }
    if (title.value) {
      Object.assign(payload, { title: title.value })
    }
    const res = await loadCategoryTable(payload)
    total.value = res.total
    tableData.value = res.records
  } catch (error) {
  } finally {
    loading.value = false
  }
}

watch(title, debounce(loadData, 500))
watch(selectedId, () => {
  title.value = ''
  pageNum.value = 1
  loadData()
})

async function switchTop(value: boolean, id: string) {
  await modifyTop(id)
  window.$message.success('修改置顶成功')
  loadData()
}

async function switchRecommend(value: boolean, id: string) {
  await modifyRecommend(id)
  window.$message.success('修改推荐成功')
  loadData()
}

const router = useRouter()
const columns = createColumns(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
        },
      },
      { default: () => `查看（${row.commentNum}）` },
    )
  },
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
        },
      },
      [
        h(
          'span',
          {
            style: { margin: '0 15px' },
            onClick: () =>
              router.push({
                name: 'noticeAdd',
                query: { id: row.id },
              }),
          },
          { default: () => '编辑' },
        ),
        // h(
        //   'span',
        //   {
        //     onClick: () => {
        //       delNewItemFn(row.id)
        //     },
        //   },
        //   { default: () => '删除' },
        // ),
        h(DeleteButton, {
          handleConfirm: () => handleSingleDelete(String(row.id)),
        }),
      ],
    )
  },
  row =>
    h(NSwitch, {
      onUpdateValue: value => switchTop(value, row.id),
      value: Boolean(Number(row.isTop)),
    }),
  row =>
    h(NSwitch, {
      onUpdateValue: value => switchRecommend(value, row.id),
      value: Boolean(Number(row.isRecommend)),
    }),
)

async function operateClassication(arg: any, operateName: string) {
  const data = {}
  for (const key in arg) {
    Object.assign(data, { [key]: arg[key] })
  }
  const idsList = [arg.id]
  const res = ref()
  switch (operateName) {
    case 'rename':
      res.value = await operateCategoryClassification(data)
      if (!res.value) {
        window.$message.success('重命名成功')
      }
      break
    case 'add':
      res.value = await addCategoryClassification(data)
      if (!res.value) {
        window.$message.success('添加成功')
      }
      break
    case 'move':
      await operateCategoryClassification(data)
      break
    case 'delete':
      res.value = await deleteCategoryClassification({ ids: idsList.join(',') })
      if (!res.value) {
        window.$message.success('删除成功')
        sessionCache.remove('current_news_column')
      }
  }
  loadClassification()
}

/** 选择大类资讯时逻辑模块 */
const handelSelect = (item: itemType | '0' | undefined) => {
  if (item && item !== '0') {
    // 处理选中的 itemType
    selectedId.value = item.id
    tableTitle.value = item.name
    sessionCache.set('current_news_column', item)
  }
}

/** 获取表格多项的keys */
const checkedRowKeysRef = ref()
const checkedRowKeysFn = (value: string[]) => {
  checkedRowKeysRef.value = value.join(',')
}
/** 添加资讯 */
const addNewsItem = () => {
  router.push({ name: 'noticeAdd' })
  sessionStorage.setItem('selectedIDForNews', selectedId.value)
}

onMounted(() => {
  loadClassification()
})
</script>
<template>
  <div>
    <div>
      <SideMenu
        :operate-fn="operateClassication"
        :value="sideMenuInfo"
        :activated-i-d="selectedId"
        search-id="111"
        title="分类列表"
        @select="handelSelect"
      />
      <div class="pl-[280px] pt-[25px] pr-[30px]">
        <p class="font-semibold text-[20px]">
          {{ tableTitle || '全部资讯' }}
        </p>
        <div class="mt-[30px] flex justify-between">
          <div>
            <n-button
              style="margin-right: 10px"
              type="primary"
              @click="addNewsItem"
            >
              添加
            </n-button>
            <n-button @click="() => delNewItemFn(checkedRowKeysRef)">
              删除
            </n-button>
          </div>
          <n-input
            v-model:value="title"
            placeholder="请输入资讯标题"
            style="width: 400px"
          />
        </div>
        <n-data-table
          :loading="loading"
          :columns="columns"
          :data="tableData"
          :row-key="(row:any)=>row.id"
          class="mt-[20px]"
          @update:checked-row-keys="checkedRowKeysFn"
        />
        <div class="flex items-center justify-between my-[30px] pr-[2px]">
          <span class="text-[#BDBDBD] text-[12px] mr-[30px]">共
            <span class="text-[#262626] mx-[6px]">{{ total }}</span>
            条</span>
          <n-pagination
            v-model:page="pageNum"
            v-model:page-size="pageSize"
            :item-count="total"
            :page-sizes="[5, 10, 20, 30, 50]"
            show-quick-jumper
            show-size-picker
          />
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped></style>
