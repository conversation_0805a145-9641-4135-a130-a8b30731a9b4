import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import { useFetchEnumerationOptions } from '@/hooks/use-select-options'
import type { OrganizationItemTableItem } from '@/services/organization/types'
const { enumerationList } = useFetchEnumerationOptions('org_type')

export function getTableColumns(
  optionColumnRenderer: (row: OrganizationItemTableItem) => VNodeChild,
): DataTableColumns<OrganizationItemTableItem> {
  return [
    {
      key: 'name',
      title: '组织名称',
    },
    {
      key: 'parentName',
      title: '上级组织名称',
    },
    {
      key: 'code',
      title: '组织编码',
    },
    {
      key: 'orgType',
      title: '组织类型',
      render: (row) => {
        if (Array.isArray(enumerationList.value)) {
          const target = enumerationList.value?.find(
            item => String(item.value) === String(row.orgType),
          )
          if (target) {
            return target.label
          }
        }
        else {
          return ''
        }
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '450',
      render: (row) => optionColumnRenderer(row),
    },
  ]
}

export function getTeamTableColumns(
  optionColumnRenderer: (row: OrganizationItemTableItem) => VNodeChild,
): DataTableColumns<OrganizationItemTableItem> {
  return [
    {
      key: 'weight',
      title: '排序',
    },
    {
      key: 'name',
      title: '组织名称',
    },
    {
      key: 'code',
      title: '届期名称',
    },
    {
      key: 'orgTypeName',
      title: '届期开始时间',
    },
    {
      key: 'createTime',
      title: '届期结束时间',
    },
    {
      key: 'createTime',
      title: '届期所设职务',
    },
    {
      key: 'createTime',
      title: '状态',
    },
    {
      key: 'createTime',
      title: '领导班子相关说明',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '200',
      render: row => optionColumnRenderer(row),
    },
  ]
}
