import type { ParentCategoryItem } from './types'
import { commonReq } from '@/services/request'

/** 获取分类列表 */
export function getCategoryList(categoryName: string) {
  return commonReq.get<ParentCategoryItem>({
    url: '/propaganda/backend/v1/party/category',
    params: { categoryName },
  })
}

/** 添加分类 */
export function postInsertCategory(parentId: string, name: string) {
  return commonReq.post({
    url: '/propaganda/backend/v1/party/category',
    params: { parentId, name },
  })
}

/** 编辑分类 */
export function putEditorCategory(id: string, name: string) {
  return commonReq.put({
    url: '/propaganda/backend/v1/party/category',
    params: { id, name },
  })
}
/** 删除分类 */
export function delCategory(id: string) {
  return commonReq.delete({
    url: `/propaganda/backend/v1/party/category/${id}`,
  })
}

/**
 * @sort 入参1上移 -1下移
 * 分类排序
 */
export function postCategorySort(id: string, sort: number) {
  return commonReq.put({
    url: '/propaganda/backend/v1/party/category/sort',
    params: { id, sort },
  })
}
