<!--
 * @Description: 试卷新增、编辑页
-->
<template>
  <div class="paper-edit">
    <div class="title">
      {{ pageTitle }}
    </div>

    <div class="area-title">
      <span class="order">1</span>
      <span class="text">基本信息</span>
    </div>

    <n-form
      ref="baseFormRef"
      class="base-form"
      require-mark-placement="left"
      label-width="80"
      label-align="right"
      label-placement="left"
      :model="baseFormDataReactive"
      :rules="formRules"
    >
      <n-form-item label="试卷名称：" path="name">
        <n-input
          v-model:value="baseFormDataReactive.name"
          style="width: 400px"
        />
      </n-form-item>
      <n-form-item label="试卷类型：" path="paperType">
        <n-select
          v-model:value="baseFormDataReactive.paperType"
          style="width: 400px"
          :options="paperTypeOptions"
        />
      </n-form-item>
    </n-form>

    <div class="area-title">
      <span class="order">2</span>
      <span class="text">试卷内容</span>
    </div>
    <!-- 试卷内容 -->
    <choose
      v-if="paperMethod === 0"
      ref="chooseRef"
      @commit="handleCommitChoose"
    />
    <random
      v-else-if="paperMethod === 1"
      ref="randomRef"
      @commit="handleCommitRandom"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { NForm } from 'naive-ui'
import { NFormItem, NInput, NSelect } from 'naive-ui'
import { formRules, getBaseFormData } from './config'
import Choose from './cpn/choose/Choose.vue'
import Random from './cpn/random/Random.vue'
import { paperTypeOptions } from '@/views/main/examination/config'
import type {
  IExaminationPaperPost,
  IExaminationPaperPostQuestion,
  IExaminationPaperPostRule,
} from '@/services/examination/index'
import {
  getExaminationPaperDetail,
  postExaminationPaper,
} from '@/services/examination/index'

const route = useRoute()
const router = useRouter()
const paperMethod = computed(() => Number(route.params.method)) // 出题方式
const editType = computed(() => route.params.editType) // 新增 or 编辑
const pageTitle = computed(() =>
  editType.value === 'add' ? '创建试卷' : '编辑试卷',
)
const baseFormRef = ref<InstanceType<typeof NForm>>()
const baseFormDataReactive = reactive(getBaseFormData())
const chooseRef = ref()
const randomRef = ref()

onBeforeMount(() => {
  const id = route.query.id
  if (editType.value === 'modify' && id) {
    getExaminationPaperDetail(id).then((res) => {
      baseFormDataReactive.name = res.name
      baseFormDataReactive.paperType = res.paperType
      const method = res.paperMethod
      if (method === 4) {
        randomRef.value?.setRuleList(res.ruleList)
      } else if (method === 5) {
        chooseRef.value?.setQuestionList(res.subjectList)
      }
    })
  }
})

// 提交从题库选择
function handleCommitChoose(
  questionList: IExaminationPaperPostQuestion[],
  total: number,
) {
  handleSave('choose', questionList, total)
}

// 提交随机组卷
function handleCommitRandom(
  ruleList: IExaminationPaperPostRule[],
  total: number,
) {
  handleSave('random', ruleList, total)
}

// 保存
function handleSave(method: 'choose' | 'random', list: any[], total: number) {
  baseFormRef.value?.validate((errors: any) => {
    if (!errors) {
      const data: IExaminationPaperPost = {
        name: baseFormDataReactive.name,
        paperType: baseFormDataReactive.paperType!,
        score: total,
        type: paperMethod.value,
        paperMethod: 5, // 4随机录入，5固定题目
      }
      if (method === 'choose') {
        if (list.length < 1) {
          window.$message.warning('请选择题目')
          return
        }
        data.answerList = list
        data.paperMethod = 5
      } else if (method === 'random') {
        data.ruleList = list
        data.paperMethod = 4
      }
      if (editType.value === 'modify') {
        const id = route.query.id
        if (!id) {
          return router.replace({ name: 'paperList' })
        }
        data.id = id
      }
      postExaminationPaper(data).then(() => {
        window.$message.success('保存成功')
        router.replace({ name: 'paperList' })
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.paper-edit {
  height: calc(100vh - 98px);
  box-sizing: border-box;
  padding: 25px 20px 36px;

  > .title {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    line-height: 20px;
    margin-bottom: 20px;
  }

  > .area-title {
    height: 48px;
    background: #f6faff;
    display: flex;
    align-items: center;
    padding-left: 25px;

    > .order {
      font-size: 22px;
      font-weight: 600;
      color: #c5e4ff;
    }

    > .text {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      margin-left: 17px;
    }
  }

  > .base-form {
    padding: 33px 0 20px 44px;
  }
}
</style>
