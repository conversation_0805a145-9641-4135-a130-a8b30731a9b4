/**  党建清单表格 */
export interface PartyBuildingListBranchItem {
  [key: string]: any
  id?: string
  /** 清单标题 */
  title: string
  /** 考核年月 */
  evaluationYearAndMonth: string
  /** 考核组织 */
  organizations: string | null
  /** 附件 */
  fileList?: FileList[]
  /** 考核分数 */
  evaluationScore?: string
  /** 考核进度 */
  evaluationProgress?: string
  /** 状态 */
  status?: string
}

/** 上传凭证/修改凭证传参 */
export interface CredentialsData {
  /** 文件列表 */
  fileIds: string[]
  /** id */
  id: string
  /** 会议名称，逗号相隔 */
  meetinglink: string
  /** 完成情况概述 */
  performanceDescription: string
  /** 投票名称，逗号相隔 */
  votelink: string
  /** 附件 */
  fileList?: FileList[]
}

/** 党内会议列表 */
export interface BranchPartyMeetingList {
  id: string
  title: string
  orgId: string
  org: string
  hostId: string
  host: string
  startTime: string
  endTime: string
  meetingType: string
  meetingAddr: string
  userId: string
  user: string
  meetingStatus: any
  relatedStatus?: boolean
}

/** 投票打分列表 */
export interface BranchVoteScoreList {
  id: string
  title: string
  year: string
  voteType: string
  relatedStatus?: boolean
}

export interface FileList {
  id: string
  fileName: string
  original: string
}

/** 查看清单下的所有指标 */
export interface AllTargetListBranch {
  inventoryId: string
  targetItemForm: TargetItemForm[]
  totalScore: number
}

export interface TargetItemForm {
  id: string
  categoryName: string
  targetItemList: AllTargetItemListBranch[]
}

export interface AllTargetItemListBranch {
  id: string
  targetId: string
  matter: string
  title: string
  evaluationMode: string
  evaluationRequirements: string
  evaluationScore: string
  fileList: FileList[]
  endTime: any
  passNum: any
  totalNum: any
  fileNum: number
  relatedStatus: boolean
  submitStatus: string
  performance: any
  score: any
  isScore: boolean
}

export type AllTargetItemListRowBranch = AllTargetItemListBranch & {
  editing: boolean
}
