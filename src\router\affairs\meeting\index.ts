import type { RouteRecordRaw } from 'vue-router'

const meetingRoute: RouteRecordRaw = {
  path: 'party-meetings',
  name: 'partyMeetings',
  meta: { title: '党内会议', icon: 'lesson', category: 'affairs', order: 2 },
  component: () => import('@/views/main/affairs/meeting/Index.vue'),
  redirect: { name: 'meetingList' },
  children: [
    {
      path: 'meeting-list',
      name: 'meetingList',
      meta: { title: '会议列表', icon: 'role' },
      component: () =>
        import('@/views/main/affairs/meeting/list/MeetingList.vue'),
    },
    {
      path: 'meeting-manage',
      name: 'meetingManage',
      meta: { title: '会议管理', icon: 'lesson' },
      component: () =>
        import('@/views/main/affairs/meeting/manage/MeetingManage.vue'),
    },
  ],
}
export default meetingRoute
