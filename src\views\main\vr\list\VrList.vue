<script lang="ts" setup>
import { NButton, NSwitch } from 'naive-ui'
import VrForm from '../cpn/VrForm.vue'
import { getTableColumns } from './config'
import { useDrawerEdit } from '@/hooks/use-drawer-edit'
import {
  deleteVR,
  getVRList,
  putHideVR,
  putVRJumpOutLink,
  topVR,
} from '@/services/vr'
import { useMyTable } from '@/hooks/use-my-table'
import DeleteButton from '@/components/DeleteButton.vue'

const filterRef = ref({
  title: null,
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  onUpdatePage,
  onUpdatePageSize,
  handleSingleDelete,
  handleBatchDelete,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getVRList, filterRef, {
  batchDeleteTable: true,
  delApi: deleteVR,
})

// 抽屉
const idEditRef = ref()
const vrFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('线上展馆', handelConfirmEdit)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  idEditRef.value = '0'
  showEditRef.value = true
}

/** 确定保存 */
function handelConfirmEdit() {
  vrFormRef.value?.validateAndSave()
}

/** 保存成功 */
function handleCarouselSaved() {
  showEditRef.value = false
  loadData()
}

function handleOuterClickCancel() {
  if (!vrFormRef.value?.getChangedFlag()) {
    window.$dialog.warning({
      title: '提示',
      content:
        editTypeRef.value === 'modify'
          ? '更新数据未发布，关闭后本次编辑内容不保存，是否继续？'
          : '关闭后本次内容不保存，是否继续？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        handleClickCancel()
      },
    })
  }
  else {
    handleClickCancel()
  }
}

watch(showEditRef, (newV) => {
  if (!newV) {
    vrFormRef.value?.resetForm()
  }
})

async function switchJump(value: boolean, id: string) {
  await putVRJumpOutLink(id, value ? '1' : '0')
  window.$message.success('修改跳转成功')
  loadData()
}

async function switchHidden(value: boolean, id: string) {
  await putHideVR(id, value ? '1' : '0')
  window.$message.success('隐藏成功')
  loadData()
}

async function switchTop(id: string) {
  await topVR(id)
  window.$message.success('置顶成功')
  loadData()
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
          display: 'flex',
          gap: '15px',
        },
      },
      [
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'view'
              showEditRef.value = true
            },
          },
          { default: () => '查看' },
        ),
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'modify'
              showEditRef.value = true
            },
          },
          { default: () => '编辑' },
        ),
        h(DeleteButton, {
          handleConfirm: () => handleSingleDelete(String(row.id)),
        }),
      ],
    )
  },
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => switchJump(value, row.id),
      value: Boolean(Number(row.isOutside)),
    }),
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => switchHidden(value, row.id),
      value: Boolean(Number(row.isHidden)),
    }),
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => switchTop(row.id),
      value: Boolean(Number(row.isTop)),
    }),
)
onMounted(() => {
  loadData()
})
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    title="线上展馆"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #filters>
      <n-input
        v-model:value="filterRef.title"
        clearable
        placeholder="请输入线上展馆标题"
        style="width: 260px; height: 32px"
      />
    </template>
  </table-container>
  <n-drawer v-model:show="showEditRef" :mask-closable="false" :width="620">
    <n-drawer-content :title="drawerTitle" closable>
      <vr-form
        :id="idEditRef"
        ref="vrFormRef"
        :type="editTypeRef"
        @saved="handleCarouselSaved"
      />
      <template #footer>
        <div v-if="editTypeRef !== 'view'" class="w-full flex justify-center">
          <n-button
            style="margin-right: 10px"
            type="primary"
            @click="handleClickConfirm"
          >
            保存
          </n-button>
          <n-button @click="handleOuterClickCancel">
            取消
          </n-button>
        </div>

        <div v-else class="w-full flex justify-center">
          <n-button @click="handleClickCancel">
            关闭
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped>
:deep(.n-drawer .n-drawer-content .n-drawer-footer) {
  display: flex !important;
  justify-content: center !important;
}
</style>
