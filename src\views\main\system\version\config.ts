import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { VersionItem } from '@/services/system/Version/types'

export function getTableColumns(
  optionColumnRenderer: (row: VersionItem) => VNodeChild,
  statusColumnRenderer: (row: VersionItem) => VNodeChild,
): DataTableColumns<VersionItem> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    { key: 'version', title: '版本号' },
    { key: 'name', title: '版本标题' },
    { key: 'updateInfo', title: '升级信息' },
    { key: 'downloadAddress', title: '下载地址' },
    {
      key: 'status',
      title: '是否启用',
      render: row => statusColumnRenderer(row),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumn<PERSON>enderer(row),
    },
  ]
}
