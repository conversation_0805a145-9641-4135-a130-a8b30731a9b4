import type { FormRules } from 'naive-ui'
import { isHttpOrHttpsLink } from '@/utils/utils'

export const specialRootFormRules: FormRules = {
  name: {
    required: true,
    message: '请输入专题分类名称',
    trigger: 'input',
    type: 'string',
  },
  coverUrl: {
    required: true,
    message: '请上传专题分类图片',
    trigger: 'change',
    type: 'string',
  },

  sliderFlag: {
    required: true,
    message: '请选择是否加入首页轮播池',
    trigger: 'change',
    type: 'number',
  },
  // configStudyScore: {
  //   required: true,
  //   message: '请输入学习学分',
  //   trigger: 'input',
  //   type: 'number',
  // },
  // configCommentScore: {
  //   required: true,
  //   message: '请输入评论学分',
  //   trigger: 'input',
  //   type: 'number',
  // },
  categoryDesc: {
    required: true,
    message: '请输入专题分类简介',
    trigger: 'input',
    type: 'string',
  },
}

export const specialSubFormRules: FormRules = {
  name: {
    required: true,
    message: '请输入专题分类名称',
    trigger: 'input',
    type: 'string',
  },
  columnName: {
    required: true,
    message: '请输入专题栏目名称',
    trigger: 'input',
    type: 'string',
  },
  briefDesc: {
    required: true,
    message: '请输入专题栏目简介',
    trigger: 'input',
    type: 'string',
  },
  // configStudyScore: {
  //   required: true,
  //   message: '请输入学习学分',
  //   trigger: 'input',
  //   type: 'number',
  // },
}

export const specialDetailFormRules: FormRules = {
  title: {
    required: true,
    message: '请输入新闻标题',
    trigger: 'input',
    type: 'string',
  },
  readNum: {
    required: true,
    message: '请输入阅读数量',
    trigger: 'input',
    type: 'number',
  },
  coverUrl: {
    required: true,
    message: '请上传图片',
    trigger: 'change',
    type: 'string',
  },
  isTop: {
    required: true,
    message: '请选择是否置顶',
    trigger: 'change',
    type: 'number',
  },
  isRecommand: {
    required: true,
    message: '请选择是否推荐',
    trigger: 'change',
    type: 'number',
  },
  isHidden: {
    required: true,
    message: '请选择是否隐藏',
    trigger: 'change',
    type: 'number',
  },
  content: {
    required: true,
    message: '请输入正文',
    trigger: 'input',
    type: 'string',
  },
  linkUrl: {
    required: true,
    // message: '请输入链接地址',
    validator(rule: any, value: any) {
      if (value === null || !isHttpOrHttpsLink(value)) {
        return new Error('请输入合法的链接地址')
      }
      return true
    },
    trigger: 'input',
    type: 'string',
  },
}

// export const checkFormRules: FormRules = {
//   reviewed: {
//     required: true,
//     message: '请选择是否审核通过',
//     trigger: 'change',
//     type: 'number',
//   },
// }
