import { defineStore } from 'pinia'

/**
 * 创建并导出字典存储的 Vue3 store 对象
 * @function
 * @returns {DictionaryStore} - 返回创建的字典存储对象
 */
export const dict = defineStore('dict', {
  state: () => ({
    dict: [] as any[],
  }),
  actions: {
    /**
     * 获取指定键的值
     * @function
     * @param {String} key - 需要获取的键
     * @returns {Object|null} - 返回指定键对应的值，如果找不到则返回 null
     */
    getDict(key: String) {
      try {
        const item = this.dict.find(item => item.key === key)
        return item ? item.value : null
      }
      catch (e) {
        return null
      }
    },

    /**
     * 设置一个键值对
     * @function
     * @param {String} key - 需要设置的键
     * @param {Object} value - 需要设置的值
     */
    setDict(key: String, value: Object) {
      if (!key || typeof key !== 'string') {
        return
      }
      this.dict.push({ key, value })
    },

    /**
     * 删除指定键值对
     * @function
     * @param {String} key - 需要删除的键
     * @returns {boolean} - 返回删除操作是否成功
     */
    removeDict(key: String) {
      try {
        const index = this.dict.findIndex(item => item.key === key)
        if (index !== -1) {
          this.dict.splice(index, 1)
          return true
        }
      }
      catch (e) {
        return false
      }
      return false
    },
  },
})

/** 图片位置下拉选型 */
export const LAYOUT_OPTIONS = [
  { value: 1, label: '首页顶部' },
  { value: 2, label: '首页中部' },
  { value: 4, label: '学习页顶部' },
]
/** 跳转类型 */
export const JUMP_TYPES = [
  { value: 1, label: '不跳转' },
  { value: 2, label: 'APP内部' },
  { value: 3, label: 'URL' },
]

/** 轮播图是否展示 */
export const CAROUSEL_SHOW = [
  { value: 1, label: '是' },
  { value: 0, label: '否' },
]

/** 数据权限 */
export const DATA_AUTHORITY = [
  { value: 1, label: '全部' },
  { value: 2, label: '所在组织及下级组织' },
  { value: 3, label: '自定义' },
]

// /** 发展阶段 */
export const DEVELOPMENT_STAGE = [
  { value: 0, label: '群众' },
  { value: 1, label: '确定为入党申请人' },
  { value: 2, label: '列为积极分子' },
  { value: 3, label: '列为发展对象' },
  { value: 4, label: '列为预备党员' },
  // { value: 5, label: '预备党员转正' },
]

/** 性别 */
export const SEX = [
  { value: 0, label: '男' },
  { value: 1, label: '女' },
]

/** 会议类型 */
export const MEETING_TYPE = [
  { value: 1, label: '支部大会' },
  { value: 2, label: '支委会' },
  { value: 3, label: '党课' },
  { value: 4, label: '主题党日' },
  { value: 5, label: '组织生活会' },
  // { value: 6, label: '第一议题' },
  // { value: 7, label: '中心组学习' },
  // { value: 8, label: '民主生活会' },
  { value: 9, label: '党小组会' },
]

/** 投票打分类型 */
export const VOTESCORE_TYPE = [
  { value: 1, label: '民主评议' },
  { value: 2, label: '党支部星级评定' },
  { value: 3, label: '党建考核' },
]

/** 会议类型 */
export const MEETING_ATTEND_STATUS = [
  { value: 0, label: '未签到' },
  { value: 1, label: '已签到' },
  { value: 2, label: '请假中' },
]

/** 政治面貌 */
export const POLITICAL_STATUS = [
  { value: '1', label: '中共党员' },
  { value: '2', label: '预备党员' },
  { value: '3', label: '群众' },
]

/** 轮播图展示位置 */
export const SLIDER_POSITION = [
  { value: 0, label: '首页' },
  { value: 1, label: '组织页' },
]

// 轮播图链接的类型
export const CAROUSEL_LINK_TYPE = [
  { value: 10, label: 'H5链接' },
  { value: 20, label: 'tab模块' },
  { value: 30, label: '应用' },
]
// 移动端四个tab的页面路由
export const TAB_MODULE_ROUTE = [
  { value: '/home', label: '首页' },
  { value: '/news', label: '资讯' },
  { value: '/garden-plot-page', label: '园地' },
  { value: '/zhi-yun-page', label: '学习' },
  { value: '/mine', label: '我的' },
]
// 移动端各个模块的入口地址
export const TAB_MODULE_ENTRY = [
  { value: '/party-pay-list', label: '党费缴纳' },
  { value: '/mine-collect', label: '我的收藏' },
  { value: '/mine-message', label: '我的消息' },
  { value: '/mine-note-List', label: '学习笔记' },
  { value: '/category', label: '专题活动' },
  { value: '/honor-wall-page', label: '荣誉墙' },
  { value: '/party-affairs-open', label: '党务公开' },
  { value: '/questionnaire-investigation', label: '问卷调查' },
  { value: '/political-birthday-list', label: '政治生日' },
  { value: '/red-vr', label: '线上展馆' },
  { value: '/secretary-mailbox', label: '书记信箱' },
  { value: 'networkVoting', label: '网络投票' },
  { value: '/party-affairs', label: '党务通' },
  { value: '/learning-question-bank-page', label: '学习题库' },
  { value: '/partyDevelopmentList', label: '党员发展' },
  { value: '/party-meeting', label: '党内会议' },
  { value: '/vote-talk-about-page', label: '民主评议' },
  { value: '/organizationStructure', label: '基层组织' },
  { value: '/credit-cards', label: '学分榜' },
  { value: '/party-analyze', label: '党建分析' },
  { value: '/mine-feedback', label: '意见反馈' },
  { value: 'userManual', label: '使用手册' },
]

/** 转入历史原因类型 */
export const TO_HISTORY_TYPE = [
  { value: '1', label: '开除党籍' },
  { value: '2', label: '录入错误' },
  { value: '3', label: '转移至其他单位' },
  { value: '4', label: '取消党籍' },
]

export const SYSTEM_FLAG = {
  service: '0',
  system: '1',
}
/** 转入历史原因类型 */
export const SYSTEM_FLAGArr = [
  { value: '0', label: '业务字典' },
  { value: '1', label: '系统字典' },
]

/** 通知管理-状态 */
export const NOTICE_MANAGE_STATUS = [
  { value: '0', label: '未发布' },
  { value: '1', label: '已发布' },
]
/** 通知管理-类型 */
export const NOTICE_MANAGE_TYPES = [
  { value: '0', label: '会议通知' },
  { value: '1', label: '活动通知' },
  { value: '2', label: '系统通知' },
]
/** 荣誉级别 */
export const HONOR_LEVEL = [
  { value: '1', label: '国家级' },
  { value: '2', label: '省级' },
  { value: '3', label: '市级' },
  { value: '10', label: '集团级' },
]

/** 审核状态 */
export const REVIEWED_STATUS = [
  { value: 0, label: '未审核' },
  { value: 1, label: '审核通过' },
  { value: 2, label: '审核不通过' },
]

/** 岗位类别 */
export const POST_TYPE = [
  { value: '10', label: '管理岗' },
  { value: '20', label: '一线岗' },
]
/** 用户启用状态 */
export const USER_ENABLE_STATUS = [
  { value: '0', label: '启用' },
  { value: '9', label: '禁用' },
]
/** 公开范围 */
export const PUBLIC_SCOPE = [
  { value: 10, label: '党员' },
  { value: 30, label: '群众' },
]
/**
 * 根据 `value` 获取对应字典项的 `label`
 * @param dictList 字典项列表
 * @param value 字典项的值
 * @returns 字典项的文本
 */
export function getDictLabelByValue(
  dictList: Array<{ value: number | string; label: string }>,
  value: number | string,
) {
  // eslint-disable-next-line eqeqeq
  return dictList.find(item => item.value == value)?.label || ''
}
