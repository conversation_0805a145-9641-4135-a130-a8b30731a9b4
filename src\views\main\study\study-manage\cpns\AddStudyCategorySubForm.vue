<template>
  <div>
    <n-form
      ref="studySubCategoryRef"
      require-mark-placement="left"
      label-placement="left"
      label-width="80px"
      :model="formData"
      :rules="formRules"
    >
      <n-form-item label="栏目名称" path="name">
        <n-input
          v-model:value="formData.name"
          placeholder="请输入栏目名称"
          clearable
        />
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import type { FormRules } from 'naive-ui'
const formRules: FormRules = {
  name: {
    required: true,
    // validator(rule: any, value: any) {
    //   if (value === '' || !value) {
    //     return new Error('资讯类别不能为空')
    //   } else if (/^[\u4E00-\u9FA5]+$/.test(value)) {
    //     return true
    //   } else {
    //     return new Error('资讯类别仅支持中文')
    //   }
    // },
    message: '栏目名称不能为空',
    trigger: 'input',
    type: 'string',
  },
}
const studySubCategoryRef = ref()
const formData = ref({
  name: '',
})

function handleValidate() {
  return new Promise((resolve, reject) => {
    studySubCategoryRef.value?.validate((errors: any) => {
      if (!errors) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  })
}

function handleSetFormData(data: any) {
  formData.value = data
}

defineExpose({
  formData,
  handleValidate,
  handleSetFormData,
})
</script>

<style scoped lang="scss"></style>
