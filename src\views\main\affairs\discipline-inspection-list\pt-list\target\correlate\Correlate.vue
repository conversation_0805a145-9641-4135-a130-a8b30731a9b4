<script setup lang="ts">
import { tableColumns } from './config'
import { useMyTable } from '@/hooks'
import { getRelateList } from '@/services/affairs/discipline-inspection-list'

interface Props {
  categoryId: string
}
const props = withDefaults(defineProps<Props>(), {
  categoryId: '',
})

const filterReactive = ref({
  categoryId: props.categoryId,
  title: '',
})
// 有接口后添加：loading,tableData
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(
  getRelateList,
  filterReactive,
  {
    batchDeleteTable: false,
  },
  false,
  ref(true),
)
// ref({ title: filterReactive }) && ref({ inventoryId: props.categoryId })
const emits = defineEmits<{
  (e: 'update:value', value: number[]): void
}>()

watch(checkedRowKeys, (newV) => {
  emits('update:value', newV as number[])
})

watch(filterReactive.value, (newV) => {
  if (newV) {
    loadData()
  }
})
onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :loading="loading"
    class="relation-table"
    :show-title="false"
    :show-add="false"
    :show-delete="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #filters>
      <n-input
        v-model:value="filterReactive.title"
        clearable
        type="text"
        placeholder="请输入搜索内容"
      />
    </template>
  </table-container>
</template>
<style lang="scss" scoped>
.relation-table:deep(.n-data-table .n-data-table-thead) {
  display: contents;
}
</style>
<style lang="scss" scoped>
.relation-table:deep(.n-data-table .n-data-table-thead) {
  display: contents;
}
</style>
