<script setup lang="ts">
import type { DataTableColumns } from 'naive-ui'
import CustomDialog from './CustomDialog.vue'
import type {
  StatisticScoreList,
  TargetItemList,
} from '@/services/affairs/party-building-list/types'
import { getStatisticScoreList } from '@/services/affairs/party-building-list'
import { getAssessIssueScoreStatistic } from '@/services/publicity/vote/evaluation'

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  width: {
    type: String,
    default: '1200px',
  },
  showAction: {
    type: Boolean,
    default: true,
  },
  maskClosable: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: '',
  },
  belong: {
    type: String,
  },
})
const emits = defineEmits(['confirm', 'update:show'])

// 点击确定
function handleConfirm() {
  emits('confirm', false)
  emits('update:show', false)
}

const tableColumns: DataTableColumns<TargetItemList> = [
  {
    key: 'index',
    title: '序号',
    align: 'center',
    render: (_, i) => i + 1,
  },
  {
    key: 'matter',
    title: '考核指标',
    render: (row) => {
      return row.evaluationRequirements
        ? h('span', {
          innerHTML: row.evaluationRequirements,
        })
        : '--'
    },
  },
  {
    key: 'evaluationRequirements',
    title: '计分标准',
    render: (row) => {
      return row.evaluationRequirements
        ? h('span', {
          innerHTML: row.evaluationRequirements,
        })
        : '--'
    },
  },
  {
    key: 'evaluationMode',
    title: '检查材料',
    render: (row) => {
      return row.evaluationMode
        ? h('span', {
          innerHTML: row.evaluationMode,
        })
        : '--'
    },
  },
  {
    key: 'score',
    title: '得分',
    render: row => (row.score ? row.score : '--'),
  },
]

const scoreList = ref<StatisticScoreList[]>([])
const defaultTab = ref()
function loadScoreList(id: string) {
  if (props.belong === 'discipline') {
    getAssessIssueScoreStatistic(String(id)).then((res: any) => {
      if (res.length > 0) {
        scoreList.value = res
        defaultTab.value = res[0].organization
      }
    })
  }
  else {
    getStatisticScoreList(String(id)).then((res: any) => {
      if (res.length > 0) {
        scoreList.value = res
        defaultTab.value = res[0].organization
      }
    })
  }
}

defineExpose({
  loadScoreList,
})
</script>

<template>
  <CustomDialog
    :show="show"
    :title="title"
    btn-title="导出"
    width="1200px"
    @update:show="(v) => emits('update:show', v)"
    @confirm="handleConfirm"
  >
    <div class="score-statistic">
      <n-tabs v-model:value="defaultTab" type="line">
        <n-tab-pane
          v-for="(item, index) in scoreList"
          :key="index"
          :name="item.organization"
          :tab="item.organization"
        >
          <n-data-table :columns="tableColumns" :data="item.targetItemList" />
          <div class="total">
            <span>总得分：{{ item.totalScore }} 分</span>
          </div>
        </n-tab-pane>
      </n-tabs>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.dialog-header {
  width: 100%;
  height: 50px;
  background: #f2f4f8;
  line-height: 50px;
  padding-left: 29px;
}

.score-statistic {
  padding: 20px 20px 10px 20px;
  position: relative;
  .total {
    position: absolute;
    bottom: 0px;
    right: 30px;
    color: #cb0000;
  }
}
:deep(.n-tabs .n-tab-pane) {
  padding-bottom: 40px;
}

.dialog-action {
  width: 100%;
  height: 60px;
  background: #fcfdfe;
  text-align: right;
  line-height: 60px;
  padding-right: 29px;
  border-top: 1px solid #f2f3f6;

  .n-button {
    width: 64px;
    height: 30px;
    border-radius: 15px;

    &:nth-child(n + 2) {
      margin-left: 8px;
    }
  }
}
</style>
