<!-- 荣誉墙 -->
<template>
  <div class="fixed left-0 w-[259px] bg-[#F9FAFB] bottom-0 px-[15px] root">
    <div class="px-[8px] pt-[25px] flex justify-between text-[14px] font-[500]">
      荣誉墙管理
    </div>
    <div class="mt-[20px]">
      <n-input v-model:value="searchVal" placeholder="搜索分类" />
    </div>
    <div class="mt-[12px]">
      <n-collapse class="custom-collapse">
        <template #arrow>
          <n-icon class="text-size-[12px] w-[12px] custom-arror">
            <icon-right-arrow
              class="text-size-[12px] w-[12px] cursor-pointer transform"
            />
          </n-icon>
        </template>
        <n-collapse-item title="组织荣誉" name="1">
          <template #header-extra>
            <span class="cursor-pointer" @click="() => handleAddCategory('1')">+</span>
          </template>
          <div class="custom-collapse-item">
            <div v-for="item in organizationalHonors" :key="item.title">
              {{ item.title }}
            </div>
          </div>
        </n-collapse-item>
      </n-collapse>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  getOrganizationTableList,
  getVagueOrganizationTableList,
} from '@/services/organization'
const filterReactive = ref<{ orgName: string }>({
  orgName: '',
})

const loading = ref(false)
const tableData = ref([])
const isShowAddRotBtn = ref(false)
/** 获取组织列表 */
const getTableList = () => {
  loading.value = true
  getOrganizationTableList()
    .then((res: any) => {
      tableData.value = res || []
      if (Array.isArray(res) && res.length >= 1) {
        isShowAddRotBtn.value = true
      } else {
        isShowAddRotBtn.value = false
      }
    })
    .finally(() => {
      loading.value = false
    })
}
/** 获取过滤组织列表 */
const filterTableList = () => {
  getVagueOrganizationTableList(filterReactive.value).then((res: any) => {
    tableData.value = res || []
  })
}
const filterVal = computed(() => {
  if (filterReactive.value.orgName && filterReactive.value.orgName !== '') {
    return filterReactive.value.orgName
  } else {
    return null
  }
})

watch(
  () => filterVal.value,
  (newVal) => {
    if (newVal && newVal !== '') {
      filterTableList()
    } else {
      getTableList()
    }
  },
)

/** 搜索分类 */
const searchVal = ref('')

const organizationalHonors = ref([
  {
    title: '全国先进党组织',
  },
  {
    title: '部、委级先进党组织',
  },
  {
    title: '基层先进党组织',
  },
])

// 添加分类
function handleAddCategory(flag: string) {
  modalVisible.value = true
  categoryFlag.value = flag
}

onMounted(getTableList)
onActivated(() => {
  filterVal.value ? filterTableList() : getTableList()
})
</script>

<style lang="scss" scoped>
.root {
  top: 110px;
}

.custom-arror {
  font-size: 12px !important;
}

.custom-collapse {
  width: 220px;
}

.custom-collapse-item {
  margin-left: 16px;

  div {
    margin-bottom: 10px;
    height: 40px;
    border-radius: 6px;
    display: flex;
    align-items: center;
  }

  div:hover {
    background: rgb(228, 231, 239);
  }
}
</style>
