<script setup lang="ts">
import { formRules } from './config'
import {
  getContractList,
  getStageListAndBaseInfo,
  postPartyDevSaveInfo,
  putPartyDevSaveInfo,
} from '@/services/structure/party-development'
import type {
  ContactListItem,
  StageListAndBaseInfo,
} from '@/services/structure/party-development/types'
import { useCurrentOrganizationListOptions } from '@/hooks/use-select-options'
import type {
  uploadFileItem,
  uploadFileItemNew,
} from '@/services/affairs/party-building-list/exam-indicators/types'
import { uploadImg } from '@/services'

interface Props {
  id: string
  currentStage: number
}
const props = defineProps<Props>()

const stages = [
  { key: 0, label: '群众' },
  { key: 1, label: '确认为入党申请人' },
  { key: 2, label: '列为积极分子' },
  { key: 3, label: '列为发展对象' },
  { key: 4, label: '列为预备党员' },
  // { key: 5, label: '预备党员转正' },
]

/** 获取发展阶段值 */
const devData = ref<StageListAndBaseInfo>()
watchEffect(() => {
  getStageListAndBaseInfo(props.id).then((res) => {
    devData.value = res
  })
})

const { organizationCurrentListTree } = useCurrentOrganizationListOptions()

const emits = defineEmits<{
  (e: 'saved'): void
  (e: 'tabChange', value: string): void
  (e: 'currentTab', value: string): void
}>()

/** 获取下一阶段 tab */
function getNextStageLabel(currentStage: number): string {
  const currentStageIndex = stages.findIndex(
    stage => stage.key === currentStage,
  )
  if (currentStageIndex === -1 || currentStageIndex === stages.length - 1) {
    return stages[currentStageIndex].label
  }
  else {
    return stages[currentStageIndex + 1].label
  }
}

const currentTab = ref(getNextStageLabel(props.currentStage))
// emits('currentTab', currentTab.value)

/** 获取所有阶段值 */
const currentStageData = ref()

/** 可见的Tab */
const visibleStages = computed(() => {
  const currentStageIndex = stages.findIndex(
    stage => stage.key === props.currentStage,
  )
  const nextStageIndex = currentStageIndex + 1
  const stagesToShow
    = nextStageIndex >= stages.length
      ? stages.slice(1, currentStageIndex + 1)
      : stages.slice(1, nextStageIndex + 1)

  currentStageData.value = stagesToShow.map((stage) => {
    const stageData = devData.value?.stageList.find(
      item => item.stageName === stage.label,
    )
    if (stageData) {
      return {
        ...stageData,
        contactId: stageData.contacts || [],
      }
    }
    else {
      const { trueName, phone } = devData.value?.partyMemberBaseInfo || {}
      return {
        trueName: trueName || '',
        phone: phone || '',
        fileList: [],
        fileIds: [],
      }
    }

    // return (
    //   (stageData && {
    //     ...stageData,
    //     // contactId: stageData?.contactId ? stageData.contactId.split(',') : [],
    //     contactId: stageData?.contacts || [],
    //   }) || {
    //     trueName: devData.value?.partyMemberBaseInfo.trueName,
    //     phone: devData.value?.partyMemberBaseInfo.phone,
    //     fileList: [],
    //     fileIds: [],
    //   }
    // )
  })
  return { stagesToShow, currentStageData }
})

// 表单禁用
function isFormDisabled(stageKey: number) {
  const currentStageIndex = stages.findIndex(
    stage => stage.key === props.currentStage,
  )
  const stageIndex = stages.findIndex(stage => stage.key === stageKey)
  return stageIndex <= currentStageIndex && stageKey !== 0
}

// 点击 tab 页触发
const activeTab = ref(getNextStageLabel(props.currentStage))
function handleTabChange(v: string) {
  activeTab.value = v
}
const contactList = ref([])
// 选择组织
function handleChangeOrg(v: string, index: number) {
  currentStageData.value[index]!.organizationId = v
  currentStageData.value[index]!.contactId = null
  handelContactList(v)
}
// 获取联系人列表
function handelContactList(deptId: string) {
  if (deptId) {
    getContractList(deptId).then((res: any) => {
      contactList.value = res
    })
  }
}

const contract = computed(() =>
  contactList.value.map((item: ContactListItem) => ({
    value: item.userId,
    label: item.trueName,
  })),
)

watchEffect(() => {
  if (currentTab.value === '列为预备党员') {
    setTimeout(() => {
      const lastObject = currentStageData.value[3]
      handelContactList(lastObject.organizationId)
    }, 400)
  }
  // nextTick(() => {
  //   const lastObject = currentStageData.value[3]
  //   if (lastObject && lastObject.organizationId) {
  //     handelContactList(lastObject.organizationId)
  //   }
  // })
})

watch(activeTab, (newV) => {
  // if (newV !== currentTab.value) {

  const currentSort = stages.filter(item => item.label === activeTab.value)[0]
    .key

  const lastObject = currentStageData.value[currentSort - 1]

  handelContactList(lastObject.organizationId)
  // }
  emits('tabChange', newV)
})

const fileIdObj = reactive<{ fileIdsArr: Array<string> }>({ fileIdsArr: [] })
// 文件相关
async function handleFileChange(
  fileInfoList: uploadFileItemNew[],
  isDelIDs: string,
) {
  try {
    const fileData = new FormData()

    const currentSort = stages.filter(
      item => item.label === activeTab.value,
    )[0].key
    const lastObject
      = currentTab.value !== activeTab.value
        ? currentStageData.value[currentSort - 1]
        : currentStageData.value[
          Object.keys(currentStageData.value)[
            Object.keys(currentStageData.value).length - 1
          ]
        ]

    // 获取所有FileID
    if (!fileIdObj.fileIdsArr.length) {
      lastObject.fileList.forEach((item: any) =>
        fileIdObj.fileIdsArr.push(item.id),
      )
    }

    // 删除动作
    if (isDelIDs || isDelIDs === null) {
      if (isDelIDs === null && fileIdObj.fileIdsArr.length) {
        fileIdObj.fileIdsArr.splice(fileIdObj.fileIdsArr.length - 1, 1)
      }
      else {
        fileIdObj.fileIdsArr.forEach((item, index) => {
          if (item === isDelIDs) {
            fileIdObj.fileIdsArr.splice(index, 1)
          }
        })
      }
      lastObject.fileIds = [...fileIdObj.fileIdsArr]
    }
    else {
      // 新增动作
      if (fileInfoList) {
        const lastFileItem = fileInfoList[fileInfoList?.length - 1]?.file
        fileData.append('file', lastFileItem as Blob)
        const data: uploadFileItem = await uploadImg(fileData)

        lastObject.fileList.push({
          original: lastFileItem?.name as string,
          fileName: data.url || '',
          id: data.fileId,
        })
        if (data) {
          fileIdObj.fileIdsArr.push(data.fileId)
          lastObject.fileIds = [...fileIdObj.fileIdsArr]
        }
      }
    }
  }
  catch (err) {}
}

const loading = ref(false)

// 发展信息表单
const formRef = ref()

async function validateAndSave() {
  loading.value = true
  let saveData = {
    id: '',
    memberId: '',
    sort: 0,
    organizationId: null,
    confirmTime: null,
    // contactId: null,
    fileIds: [],
    fileList: [],
    contacts: [],
  }

  const currentSort = stages.filter(item => item.label === activeTab.value)[0]
    .key
  if (currentTab.value !== activeTab.value) {
    const lastObject = currentStageData.value[currentSort - 1]

    saveData = {
      confirmTime: lastObject.confirmTime,
      organizationId: lastObject.organizationId,
      contacts:
        activeTab.value !== '确认为入党申请人' ? lastObject.contactId : null,
      sort: currentSort,
      memberId: props.id,
      id: lastObject.id,
      fileIds: lastObject.fileIds || [],
      fileList: lastObject.fileList || [],
    }
    try {
      const res = await putPartyDevSaveInfo(saveData)
      if (res) {
        fileIdObj.fileIdsArr = []
        window.$message.success('保存成功')
        emits('saved')
      }
      loading.value = false
    }
    catch (error) {}
  }
  else if (currentTab.value === activeTab.value) {
    const stageDataKeys = Object.keys(currentStageData.value)
    const lastObjectKey = stageDataKeys[stageDataKeys.length - 1]
    const lastObject = currentStageData.value[lastObjectKey]

    saveData = {
      id: lastObject.id,
      confirmTime: lastObject.confirmTime,
      organizationId: lastObject.organizationId,
      contacts:
        activeTab.value !== '确认为入党申请人' ? lastObject.contactId : null,
      sort: currentSort,
      memberId: props.id,
      fileIds: lastObject.fileIds || [],
      fileList: lastObject.fileList || [],
    }
    try {
      if (saveData.organizationId === undefined) {
        return window.$message.error('接收组织不能为空')
      }
      if (saveData.confirmTime === undefined) {
        return window.$message.error('确认时间不能为空')
      }

      const saveFn
        = props.currentStage === 5 ? putPartyDevSaveInfo : postPartyDevSaveInfo
      const res = await saveFn(saveData)

      if (res) {
        fileIdObj.fileIdsArr = []
        window.$message.success('保存成功')
        emits('saved')
      }
    }
    catch (error) {
    }
    finally {
      loading.value = false
    }
  }
}

defineExpose({
  getNextStageLabel,
  validateAndSave,
})

// onMounted(loadTabAndFormData)
</script>
<template>
  <div>
    <n-tabs
      v-model:value="activeTab"
      type="card"
      @update:value="(v: string) => handleTabChange(v)"
    >
      <n-tab-pane
        v-for="(stage, index) in visibleStages.stagesToShow"
        :key="stage.key"
        :tab="stage.label"
        :name="stage.label"
      >
        <n-form
          ref="formRef"
          size="small"
          require-mark-placement="left"
          label-width="90"
          label-align="right"
          label-placement="left"
          :rules="formRules"
        >
          <n-grid :cols="24" x-gap="12" y-gap="8">
            <n-form-item-gi :span="12" label="姓名：">
              <n-input
                v-model:value="currentStageData[index]!.trueName"
                disabled
                placeholder="请输入姓名"
                clearable
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="手机号：">
              <n-input
                v-model:value="currentStageData[index]!.phone"
                disabled
                placeholder="请输入手机号"
                clearable
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="接收组织：" required>
              <!-- <n-cascader
                v-model:value="currentStageData[index]!.organizationId"
                :disabled="isFormDisabled(stage.key)"
                :options="(organizationListTree as any)"
                placeholder="请选择接收组织"
                value-field="id"
                label-field="name"
                children-field="children"
                check-strategy="child"
                :show-path="false"
                clearable
                @update:value="(v:any) =>handleChangeOrg(v,index)"
              /> -->
              <n-tree-select
                v-model:value="currentStageData[index]!.organizationId"
                :options="organizationCurrentListTree"
                value-field="id"
                label-field="name"
                key-field="id"
                children-field="children"
                check-strategy="all"
                placeholder="请选择所属党组织"
                :show-path="false"
                clearable
                filterable
                :disabled="isFormDisabled(stage.key)"
                @update:value="(v:any) =>handleChangeOrg(v,index)"
              />
            </n-form-item-gi>

            <n-form-item-gi :span="12" label="确认时间：" required>
              <n-date-picker
                v-model:formatted-value="currentStageData[index]!.confirmTime"
                :disabled="isFormDisabled(stage.key)"
                style="width: 100%"
                placeholder="请选择确认时间"
                clearable
                type="datetime"
                @update:formatted-value="(v:string)=>currentStageData[index]!.confirmTime=v"
              />
            </n-form-item-gi>

            <n-form-item-gi
              v-if="currentStageData[index]!.organizationId && activeTab!=='确认为入党申请人'"
              :span="12"
              label="联系人："
            >
              <n-select
                v-model:value="currentStageData[index]!.contactId"
                :options="contract"
                placeholder="请选择培养联系人"
                filterable
                clearable
                multiple
                @update:value="(v:any)=>currentStageData[index]!.contactId=v"
              />
            </n-form-item-gi>

            <n-form-item-gi :span="24" label="附件：">
              <file-uploader
                :max="4"
                accept=".doc, .docx, .pdf"
                :size-limit="200"
                :original-file-list="(currentStageData[index].fileList as any)"
                @file-list-change="handleFileChange"
              >
                <template #tips>
                  <span class="tips">
                    最多可上传4个文件，支持扩展名：.doc，docx，.pdf，大小200M以内
                  </span>
                </template>
              </file-uploader>
            </n-form-item-gi>
          </n-grid>
        </n-form>
      </n-tab-pane>
    </n-tabs>
  </div>
</template>
<style lang="scss" scoped></style>
