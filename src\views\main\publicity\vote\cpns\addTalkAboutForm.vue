<script setup lang="ts">
import type { FormRules, NForm } from 'naive-ui'
// import dayjs from 'dayjs'
import { talkAboutFormRules } from './config'
import type { TalkAboutTableItem } from '@/services/publicity/vote/talkAbout/type'
import {
  addTalkAboutInfoItem,
  getReviewitemsBranchList,
  getReviewitemsPartList,
  getTalkAboutInfoNew,
  putTalkAboutInfo,
} from '@/services/publicity/vote/talkAbout'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<TalkAboutTableItem>({
  reviewTitle: '',
  reviewYear: null,
  reviewStartTime: null,
  reviewEndTime: null,
  reviewStatus: '',
  isPartReview: '0',
  isBranchReview: '0',
  branchReviewId: '',
  partReviewId: '',
  reviewRemark: '',
})

const formRules = ref<FormRules>(talkAboutFormRules)
const formRef = ref<InstanceType<typeof NForm>>()
interface Option {
  label: string
  value: string
}
const reviewItemsPart = ref<Option[]>()
const reviewItemsBranch = ref<Option[]>()

// 时间检验判断
const selfStartTimeVal = ref()
const selfEndTimeVal = ref()
const validateSelfStartTime = (time: number) => {
  selfStartTimeVal.value = time
}
const validateSelfEndTime = (time: number) => {
  selfEndTimeVal.value = time
}

watch(
  () => [formDataReactive.isPartReview, formDataReactive.isBranchReview],
  ([newPartReview, newBranchReview], [oldPartReview, oldBranchReview]) => {
    if (newPartReview === '1') {
      formRules.value.partReviewId = {
        required: true,
        message: '关联党员评议项不能为空',
        trigger: 'change',
      }
    } else {
      formRules.value.partReviewId = {
        required: false,
        message: '关联党员评议项不能为空',
        trigger: 'change',
      }
    }

    if (newBranchReview === '1') {
      formRules.value.branchReviewId = {
        required: true,
        message: '关联支部班子评议项不能为空',
        trigger: 'change',
      }
    } else {
      formRules.value.branchReviewId = {
        required: false,
        message: '关联支部班子评议项不能为空',
        trigger: 'change',
      }
    }
  },
)

onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getTalkAboutInfoNew(props.id).then((res) => {
      formDataReactive.id = res.id || ''
      formDataReactive.reviewTitle = res.reviewTitle || ''
      formDataReactive.reviewYear = res.reviewYear || ''
      formDataReactive.reviewStartTime = res.reviewStartTime || ''
      formDataReactive.reviewEndTime = res.reviewEndTime || ''
      formDataReactive.reviewStatus = res.reviewStatus || ''
      formDataReactive.isPartReview = res.isPartReview || '0'
      formDataReactive.isBranchReview = res.isBranchReview || '0'
      formDataReactive.branchReviewId = res.branchReviewId || ''
      formDataReactive.partReviewId = res.partReviewId || ''
      formDataReactive.reviewRemark = res.reviewRemark || ''
    })
  }

  getReviewitemsPartList({ title: '', pageNum: 1, pageSize: 99999999 }).then(
    (res) => {
      reviewItemsPart.value = res.records.map((item) => {
        return {
          label: item.reviewItemTitle,
          value: item.id as string,
        }
      })
    },
  )

  getReviewitemsBranchList({
    title: '',
    pageNum: 1,
    pageSize: 99999999,
  }).then((res) => {
    reviewItemsBranch.value = res.records.map((item) => {
      return {
        label: item.reviewItemTitle,
        value: item.id as string,
      }
    })
  })
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (formDataReactive.id) {
        // 更新
        putTalkAboutInfo(formDataReactive).then(() => {
          window.$message.success('更新成功')
          emits('saved')
        })
      } else {
        // 新增
        const data: TalkAboutTableItem = {
          reviewTitle: formDataReactive.reviewTitle,
          reviewYear: formDataReactive.reviewYear,
          reviewStartTime: formDataReactive.reviewStartTime,
          reviewEndTime: formDataReactive.reviewEndTime,
          isPartReview: formDataReactive.isPartReview,
          isBranchReview: formDataReactive.isBranchReview,
          branchReviewId: formDataReactive.branchReviewId,
          partReviewId: formDataReactive.partReviewId,
          reviewRemark: formDataReactive.reviewRemark,
        }
        addTalkAboutInfoItem(data).then(() => {
          window.$message.success('添加成功')
          emits('saved')
        })
      }
    }
  })
}

// 屏蔽年份
// const yearDisable = (time: number) => {
//   // 1、当前之前的年份不能选；2、当前年份后5年之外不能选
//   return (
//     time < dayjs(`${dayjs().year()}-01-01`).valueOf() ||
//     time >
//       dayjs(
//         `${dayjs().year() + 5}-${dayjs().month()}-${dayjs().date()}`
//       ).valueOf()
//   )
// }

// 不能选择当前之前的日期
const formatterStartTime = (time: number) => {
  return time < Date.now() - 86400000
}

const formatterEndTime = (time: number) => {
  if (selfStartTimeVal.value) {
    return time < selfStartTimeVal.value - 86400000
  } else {
    return time < Date.now() - 86400000
  }
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :disabled="props.type === 'view'"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item span="24" label="评议名称：" path="reviewTitle">
      <n-input
        v-model:value="formDataReactive.reviewTitle"
        rows="5"
        maxlength="20"
        show-count
      />
    </n-form-item>
    <n-form-item span="24" label="是否需要党员评议：" path="isPartReview">
      <n-switch
        v-model:value="formDataReactive.isPartReview"
        checked-value="1"
        unchecked-value="0"
      />
    </n-form-item>
    <n-form-item
      v-if="formDataReactive.isPartReview === '1'"
      span="24"
      label="关联党员评议项："
      path="partReviewId"
    >
      <n-select
        v-model:value="formDataReactive.partReviewId"
        :options="reviewItemsPart"
        filterable
      />
    </n-form-item>
    <n-form-item span="24" label="是否需要支部班子评议：" path="isBranchReview">
      <n-switch
        v-model:value="formDataReactive.isBranchReview"
        checked-value="1"
        unchecked-value="0"
      />
    </n-form-item>
    <n-form-item
      v-if="formDataReactive.isBranchReview === '1'"
      span="24"
      label="关联支部班子评议项："
      path="branchReviewId"
    >
      <n-select
        v-model:value="formDataReactive.branchReviewId"
        :options="reviewItemsBranch"
        filterable
      />
    </n-form-item>
    <n-form-item span="24" label="评议年度：" path="reviewYear">
      <n-date-picker
        v-model:formatted-value="formDataReactive.reviewYear"
        style="width: 100%"
        placeholder="请选择评议年度"
        clearable
        type="year"
        @update:formatted-value="
          (v:any) => (formDataReactive.reviewYear = v)
        "
      />
    </n-form-item>
    <n-form-item span="24" label="开始时间：" path="reviewStartTime">
      <n-date-picker
        v-model:formatted-value="formDataReactive.reviewStartTime"
        style="width: 100%"
        placeholder="请选择开始时间"
        clearable
        type="datetime"
        :is-date-disabled="formatterStartTime"
        @update:value="validateSelfStartTime"
        @update:formatted-value="
          (v:any) => (formDataReactive.reviewStartTime = v)
        "
      />
    </n-form-item>
    <n-form-item span="24" label="结束时间：" path="reviewEndTime">
      <n-date-picker
        v-model:formatted-value="formDataReactive.reviewEndTime"
        style="width: 100%"
        placeholder="请选择结束时间"
        clearable
        type="datetime"
        :is-date-disabled="formatterEndTime"
        @update:value="validateSelfEndTime"
        @update:formatted-value="
          (v:any) => (formDataReactive.reviewEndTime = v)
        "
      />
    </n-form-item>
    <n-form-item span="24" label="评议说明：" path="reviewRemark">
      <n-input
        v-model:value="formDataReactive.reviewRemark"
        rows="5"
        maxlength="500"
        show-count
        type="textarea"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
