export interface UserItem {
  id?: string
  userId: string
  trueName: string
  phone: string
  deptName: string
  partyIdentity: string
  updateTime: string
  lockFlag: string
  isLoading?: boolean
  political: string
  reasonType?: string | null
}

export interface UserDetailItem {
  userId?: string | null
  username: string
  trueName: string
  sex: string | null
  ethnic: number | null
  customEthnic?: string | null
  edu: string | null
  jobTime: string | null
  origin: string | null
  departmentId: string | null
  address: string
  memberStatus: string | null
  phone: string
  identityId: string
  partyIdentity: string | null
  joinTime: string | null
  regularTime: string | null
  deptId: string | null
  avatarId: string
  avatarUrl?: string
  partyMember?: string | null
  political?: string | null
}
