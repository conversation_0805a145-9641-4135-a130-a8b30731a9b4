<template>
  <div class="system-dic-dialog-container">
    <CustomDialog
      :show="visible"
      :title="dataForm.id ? '编辑' : '添加'"
      width="600"
      @update:show="(v:boolean) => (visible = v)"
      @cancel="visible = false"
      @confirm="onSubmit"
    >
      <n-form
        ref="dicDialogFormRef"
        label-placement="left"
        :model="dataForm"
        :rules="dataRules"
        label-width="90px"
        class="px-[15px] py-[30px]"
      >
        <n-form-item label="配置类型" path="systemFlag">
          <n-radio-group v-model:value="dataForm.systemFlag">
            <n-radio
              v-for="(item, index) in SYSTEM_FLAGArr"
              :key="index"
              border
              :value="item.value"
            >
              {{ item.label }}
            </n-radio>
          </n-radio-group>
        </n-form-item>
        <n-form-item label="字典类型" path="dictType">
          <n-input
            v-model:value="dataForm.dictType"
            placeholder="请输入字典类型"
            :disabled="dataForm.id !== ''"
            clearable
          />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="dataForm.description"
            placeholder="请输入描述"
            clearable
          />
        </n-form-item>
        <n-form-item label="备注" path="remarks">
          <n-input
            v-model:value="dataForm.remarks"
            type="textarea"
            maxlength="150"
            rows="3"
            placeholder="请输入备注"
          />
        </n-form-item>
      </n-form>
    </CustomDialog>
  </div>
</template>

<script lang="ts" name="systemDicDialog" setup>
import {
  addDict,
  getDict,
  putDict,
  validateDictType,
} from '@/services/system/Admin/index'
import { useDict } from '@/hooks/dict'
import { rule } from '@/utils/validate'
import { SYSTEM_FLAGArr } from '@/store/dict'

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh'])
const { dict_type } = useDict('dict_type')
// 定义变量内容
const dicDialogFormRef = ref()

const visible = ref(false)
const loading = ref(false)

const dataForm = reactive({
  id: '',
  dictType: '',
  description: '',
  systemFlag: '0',
  remarks: '',
})

const dataRules = reactive({
  dictType: [
    { required: true, message: '类型不能为空', trigger: 'blur' },
    { validator: rule.validatorNameCn, trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        validateDictType(rule, value, callback, dataForm.id !== '')
      },
      trigger: 'blur',
    },
  ],
  systemFlag: [
    { required: true, message: '字典类型不能为空', trigger: 'blur' },
  ],
  description: [{ required: true, message: '描述不能为空', trigger: 'blur' }],
})

// 打开弹窗
const openDialog = (id: string) => {
  visible.value = true
  dataForm.id = ''
  nextTick(() => {
    dicDialogFormRef.value?.restoreValidation()
  })

  if (id) {
    getDict(id).then((res) => {
      Object.assign(dataForm, res)
    })
  }
}

// 提交
const onSubmit = async() => {
  dicDialogFormRef.value?.validate(async(errors: any) => {
    if (!errors) {
      try {
        loading.value = true
        const result = dataForm.id
          ? await putDict(dataForm)
          : await addDict(dataForm)
        window.$message.success(dataForm.id ? '编辑成功！' : '添加成功！')
        visible.value = false
        emit('refresh', result.data)
      } catch (err: any) {
        window.$message.error(err.msg)
      } finally {
        loading.value = false
      }
    } else {
      window.$message.error('请校验表单！')
    }
  })
}

// 暴露变量
defineExpose({
  openDialog,
})
</script>
