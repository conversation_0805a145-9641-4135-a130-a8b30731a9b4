import { type RouteRecordRaw, RouterView } from 'vue-router'

const voteRoute: RouteRecordRaw = {
  path: 'vote',
  name: 'vote-activity',
  meta: { title: '投票打分', icon: 'vote', category: 'publicity', order: 3 },
  redirect: { name: 'talk-about' },
  component: () => import('@/views/main/publicity/vote/Vote.vue'),
  children: [
    {
      path: 'talkAbout',
      name: 'talk-about',
      meta: { title: '明主评议', icon: 'vote' },
      redirect: { name: 'talk-about-index' },
      component: () => RouterView,
      children: [
        {
          path: 'talkAboutIndex',
          name: 'talk-about-index',
          meta: { title: '明主评议', icon: 'vote' },
          component: () => import('@/views/main/publicity/vote/talkAbout.vue'),
        },
        {
          path: 'talkAboutPartyMember',
          name: 'talk-about-party-member',
          meta: { title: '民主评议党员', icon: 'vote' },
          redirect: { name: 'talk-about-party-member-index' },
          component: () => RouterView,
          children: [
            {
              path: 'talkAboutPartyMemberIndex',
              name: 'talk-about-party-member-index',
              meta: { title: '民主评议党员', icon: 'vote' },
              component: () =>
                import(
                  '@/views/main/publicity/vote/page/talkAboutPartyMember.vue'
                ),
            },
            {
              path: 'talkAboutPartyMemberDetail',
              name: 'talk-about-party-member-Detail',
              meta: { title: '民主评议党员详情', icon: 'vote' },
              component: () =>
                import(
                  '@/views/main/publicity/vote/page/talkAboutPartyMemberDetail.vue'
                ),
            },
          ],
        },
        // 民主评议支部班子
        {
          path: 'talkAboutBranch',
          name: 'talk-about-branch',
          meta: { title: '民主评议党支部班子', icon: 'vote' },
          redirect: { name: 'talk-about-branch-index' },
          component: () => RouterView,
          children: [
            {
              path: 'talkAboutBranchIndex',
              name: 'talk-about-branch-index',
              meta: { title: '民主评议党支部班子', icon: 'vote' },
              component: () =>
                import('@/views/main/publicity/vote/page/talkAboutBranch.vue'),
            },
            {
              path: 'talkAboutBranchDetail',
              name: 'talk-about-branch-detail',
              meta: { title: '民主评议党支部班子详情', icon: 'vote' },
              component: () =>
                import(
                  '@/views/main/publicity/vote/page/talkAboutBranchDetail.vue'
                ),
            },
          ],
        },
      ],
    },
    {
      path: 'branchRating',
      name: 'branch-rating',
      meta: { title: '党支部星级评定', icon: 'vote' },
      redirect: { name: 'branch-rating-index' },
      component: () => RouterView,
      children: [
        // 支部星期评定
        {
          path: 'branchRatingIndex',
          name: 'branch-rating-index',
          meta: { title: '党支部星级评定', icon: 'vote' },
          component: () =>
            import('@/views/main/publicity/vote/branchRating.vue'),
        },
        // 复评
        {
          path: 'branchRatingReEvaluation',
          name: 'branch-rating-reEvaluation',
          meta: { title: '复评', icon: 'vote' },
          component: () =>
            import('@/views/main/publicity/vote/reEvaluationPage.vue'),
        },
      ],
    },
    {
      path: 'evaluation',
      name: 'evaluation',
      meta: { title: '党建考核', icon: 'vote' },
      component: () => import('@/views/main/publicity/vote/evaluation.vue'),
    },
  ],
}
export default voteRoute
