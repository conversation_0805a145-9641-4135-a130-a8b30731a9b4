import { defineStore } from 'pinia'
import type { ILoginReq, UserInfo } from './../../services/auth/types'
import { getVerCode, login } from '@/services/auth/auth'
import { sessionCache } from '@/utils/cache'
import router from '@/router'
import { encryption } from '@/utils/encryption'
import { useMenuAuthStore } from '@/store/menuAuth/index'
export const useAuthStore = defineStore('auth', () => {
  const accessToken = ref()
  const refreshToken = ref()
  const codeUrl = ref()
  const wholeUserInfo = ref<UserInfo>(
    JSON.parse(window.sessionStorage.getItem('wholeUserInfoCache') as string)
      || null,
  )

  /** 获取图形验证码 */
  async function getVerificationCode(randomStr: string) {
    sessionCache.set('randomStr', randomStr)
    const res = await getVerCode(randomStr)
    codeUrl.value = res
  }
  /** 用户登录 */
  async function handleUserLogin(
    payload: ILoginReq,
    randomStr: string,
    code: string,
  ) {
    window.$loadingBar.start()
    try {
      const encryptionRes = encryption({
        data: payload,
        key: 'easestrategyzhdj',
        param: ['password'],
      })
      const res = await login(encryptionRes, randomStr, code)
      if (res instanceof Error) {
        window.$loadingBar.error()
        return Promise.reject(new Error('登录失败'))
      } else {
        accessToken.value = res.data.accessToken
        refreshToken.value = res.data.refreshToken
        wholeUserInfo.value = res.data.user_info
        window.sessionStorage.setItem(
          'wholeUserInfoCache',
          JSON.stringify(wholeUserInfo.value),
        )
        window.sessionStorage.setItem('access_token', accessToken.value)
        sessionCache.set('refresh_token', refreshToken.value)
        window.$loadingBar.finish()
        router.replace({
          path: '/main',
        })
        window.$message.success('登录成功')
      }
    } catch (err) {
      window.$loadingBar.error()
    }
  }

  // async function handleUserLogin() {
  //   router.replace({
  //     name: 'main',
  //   })
  //   window.$message.success('登录成功')
  // }

  function loadCache() {
    accessToken.value = sessionCache.get('access_token') ?? ''
    refreshToken.value = sessionCache.get('refresh_token') ?? ''
  }

  /** 退出登录 */
  async function handleUserLogout() {
    const store = useMenuAuthStore()
    // await loginOut()
    accessToken.value = ''
    refreshToken.value = ''
    codeUrl.value = ''
    wholeUserInfo.value = {} as UserInfo
    sessionCache.clear()
    store.resetMenu()
    router.replace({
      name: 'login',
    })
    window.$message.info('已退出')
  }

  return {
    codeUrl,
    wholeUserInfo,
    getVerificationCode,
    handleUserLogin,
    loadCache,
    handleUserLogout,
  }
})
