import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { VNodeChild } from 'vue'
import type { FormRules } from 'naive-ui'
import type { managerListType } from '@/services/system/types'

export function createColumn(
  opeartionRender: (row: managerListType) => VNodeChild,
  roleRender: (row: managerListType) => VNodeChild,
  deptRender: (row: managerListType) => VNodeChild,
): TableColumns<managerListType> {
  return [
    {
      title: '管理员名称',
      key: 'nickName',
    },
    {
      title: '账号',
      key: 'username',
    },
    {
      title: '管理部门',
      key: 'deptName',
      render: deptRender,
      width: '30%',
      ellipsis: {
        tooltip: {
          contentStyle: {
            width: '500px',
            maxHeight: '400px',
            'word-break': 'break-all',
            overflow: 'auto',
          },
        },
      },
    },
    {
      title: '角色',
      key: 'role',
      render: roleRender,
    },
    {
      title: '操作',
      key: 'options',
      render: opeartionRender,
    },
  ]
}

export const formRules: FormRules = {
  nickName: {
    required: true,
    message: '请输入管理员名称',
    trigger: 'blur',
  },
  username: {
    required: true,
    message: '请输入管理员账号',
    trigger: 'blur',
  },
  role: {
    required: true,
    message: '请选择角色',
    trigger: 'input',
  },
  dept: {
    required: true,
    message: '请选择管辖组织',
    trigger: 'change',
    validator(rule: any, value: any) {
      if (!value || !value.length) {
        return new Error('请选择管辖组织')
      }
      else {
        return true
      }
    },
  },
}
