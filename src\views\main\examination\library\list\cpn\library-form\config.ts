/*
 * @Description: 题库表单相关配置
 */
import type { FormRules } from 'naive-ui'
import type { IExaminationLibraryForm } from '@/services/examination/index'

export const formRules: FormRules = {
  name: [{ required: true, message: '请输入题库名称', trigger: 'change' }],
  types: [
    {
      required: true,
      validator(rules: any, value: null | number[]) {
        if (!value || value.length < 1) {
          return new Error('请选择适用答题任务')
        }
        return true
      },
      trigger: 'change',
    },
  ],
  file: {
    required: true,
    validator(rule: any, value: any) {
      if (!value) {
        return new Error('请上传题库封面')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  allowUse: [{ required: true }],
}

export function getFormData(): IExaminationLibraryForm & {
  types?: string[]
} {
  return {
    name: '',
    description: '',
    allowUse: 0,
    type: '',
    types: [],
    file: null,
  }
}
