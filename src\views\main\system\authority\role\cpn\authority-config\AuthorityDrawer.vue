<script setup lang="ts">
interface Target {
  id: string
  parentId: string
  weight: number
  name: string
  children?: Target[]
}

const props = defineProps({
  menuOptions: {
    type: Array as () => Array<Target>,
    required: true,
  },
  selectMenuTree: {
    type: Array as () => Array<string>,
  },
})
const menuIds = ref({
  menuIds: [] as any,
  menuIdsAll: [] as any,
})
const emits = defineEmits(['update:value'])
function updateCheckedKeys(keys: Array<string | number>) {
  menuIds.value.menuIdsAll = keys
  emits('update:value', menuIds.value)
}
function updateCheckedKeys2(keys: Array<string | number>) {
  menuIds.value.menuIds = keys
  emits('update:value', menuIds.value)
}

function findItemById(arr: Target[] | undefined, id: string): Target | null {
  if (!arr) {
    return null
  }

  for (let i = 0; i < arr.length; i++) {
    if (arr[i]?.id === id) {
      return arr[i]
    } else if (
      arr[i]
      && arr[i].children
      && Array.isArray(arr[i].children)
      && arr[i].children.length > 0
    ) {
      const result = findItemById(arr[i]?.children, id)
      if (result) {
        return result
      }
    }
  }
  return null
}

function filterByIds(ids: string[], targets: Target[]): string[] {
  const result: string[] = []

  function hasAllDescendantsInIds(target: Target): boolean {
    if (!target.children || target.children.length === 0) {
      return true
    }
    return target.children.every(
      child => ids.includes(child.id) && hasAllDescendantsInIds(child),
    )
  }

  ids.forEach((id) => {
    const target = findItemById(targets, id)
    if (target && hasAllDescendantsInIds(target)) {
      result.push(id)
    }
  })

  return result
}
const filterCheckedRef = ref<any>(null)

filterCheckedRef.value = filterByIds(
  props.selectMenuTree || [],
  props.menuOptions || [],
)
</script>
<template>
  <div>
    <n-tree
      block-line
      :default-checked-keys="filterCheckedRef"
      :data="menuOptions"
      key-field="id"
      label-field="name"
      checkable
      multiple
      cascade
      selectable
      :default-expand-all="true"
      check-strategy="all"
      @update:checked-keys="updateCheckedKeys"
      @update:indeterminate-keys="updateCheckedKeys2"
    />
  </div>
</template>

<style scoped lang="scss"></style>
