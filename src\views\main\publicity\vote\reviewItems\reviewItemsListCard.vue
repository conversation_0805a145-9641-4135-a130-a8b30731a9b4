<template>
  <div>
    <div class="greySplit">
      <div class="subTitle">
        {{ title }}
      </div>
      <NSwitch
        v-model:value="questionnaire.isDisplay"
        class="ml-auto mr-[23px]"
        checked-value="1"
        unchecked-value="0"
      >
        <template #checked>
          展示
        </template>
        <template #unchecked>
          关闭
        </template>
      </NSwitch>
    </div>
    <NForm
      v-show="questionnaire.isDisplay === BOOLEANTYPE.YES"
      :show-feedback="false"
      class="relative"
      label-placement="left"
      :model="questionnaire"
    >
      <NCard
        :bordered="true"
        hoverable
        class="ncard"
        title="题目列表"
        :segmented="{
          content: true,
          footer: 'soft',
        }"
        header-style="font-size:14px;height:48px"
        content-style="padding:0px"
      >
        <template #header-extra>
          <n-checkbox
            v-model:checked="questionnaire.isTemplate"
            checked-value="1"
            unchecked-value="0"
          >
            启用选项模板
          </n-checkbox>
          <n-button
            quaternary
            type="info"
            class="!text-sm"
            @click="
              () => {
                showModal = true
              }
            "
          >
            配置模板
          </n-button>
        </template>
        <NSpin :show="loading">
          <NGrid :cols="8">
            <!-- 预览区域  -->
            <NGi :span="5" class="pr-25px">
              <NCard
                :segmented="{
                  content: true,
                  footer: true,
                }"
                footer-style="font-size:14px;height:48px;display:flex;align-items:center"
                class="h-full"
              >
                <TransitionGroup
                  v-if="questionnaire.reviewSubjectArr.length"
                  tag="ul"
                  name="fade"
                >
                  <div
                    v-for="(item, index) in questionnaire.reviewSubjectArr"
                    :key="index"
                  >
                    <reviewItems
                      :key="index"
                      :data="item"
                      :index="index"
                      :total="questionnaire.reviewSubjectArr.length"
                      :on-click="
                        () => {
                          clearEditing()
                          item.editing = true
                        }
                      "
                      :on-upward="() => swapItem(index, index - 1)"
                      :on-downward="() => swapItem(index, index + 1)"
                      :on-delete="
                        () => questionnaire.reviewSubjectArr.splice(index, 1)
                      "
                      :on-copy="() => copyQuestion(index, item)"
                    />
                  </div>
                </TransitionGroup>
                <div v-else class="flex justify-center p-10">
                  <img src="@/assets/image/vote/emptyQuestion.png">
                </div>
                <template #footer>
                  <n-button size="small" @click="addQuestion()">
                    <template #icon>
                      <n-icon :size="10">
                        <IosAdd />
                      </n-icon>
                    </template>
                    <div class="text-xs">
                      新增题目
                    </div>
                  </n-button>
                </template>
              </NCard>
            </NGi>

            <!-- 编辑区域 -->
            <NGi :span="3" class="px-20px">
              <ConfigForm
                v-if="editingIndex > -1"
                v-model:value="questionnaire.reviewSubjectArr[editingIndex]"
                v-model:template-detail="templateDetail"
                :have-template="questionnaire.isTemplate === '1'"
                class="sticky top-80px"
              />
            </NGi>
          </NGrid>
        </NSpin>
      </NCard>
      <NGrid :cols="8">
        <NGi :span="5">
          <div class="ml-5">
            <NFormItem
              class="text-13px text-highlightBlue mt-4"
              path="questionnaireScoreUpper"
            >
              <span>分数上限：</span>
              <NInputNumber
                v-model:value="questionnaire.questionnaireScoreUpper"
                :precision="2"
                class="mx-10px !w-40"
                placeholder="请输入分数上限"
              />
              <span class="ml-4">该分数由各分数项的上限分数相加获得。</span>
            </NFormItem>

            <NFormItem
              class="text-13px text-highlightBlue mt-4"
              label="开启总分项："
              path="isScoreItem"
            >
              <n-radio-group
                v-model:value="questionnaire.isScoreItem"
                name="isScoreItem"
              >
                <n-space>
                  <n-radio :key="'1'" :value="'1'">
                    是
                  </n-radio>
                  <n-radio :key="'0'" :value="'0'">
                    否
                  </n-radio>
                </n-space>
              </n-radio-group>
            </NFormItem>
            <NFormItem
              v-show="questionnaire.isScoreItem === BOOLEANTYPE.YES"
              class="text-13px text-highlightBlue mt-4"
              label="总分项名称："
              path="ruleScoreName"
            >
              <NInput
                v-model:value="questionnaire.questionnaireRuleObj!.ruleScoreName"
                class="mx-10px"
                placeholder="请输入总分项名称"
              />
            </NFormItem>
            <NDataTable
              v-show="questionnaire.isScoreItem === BOOLEANTYPE.YES"
              class="mt-4"
              size="small"
              :columns="getColumns()"
              :data="
                questionnaire.questionnaireRuleObj!.questionnaireRuleDetailArr
              "
            />
            <NButton
              v-show="questionnaire.isScoreItem === BOOLEANTYPE.YES"
              @click="addScoreItem()"
            >
              <template #icon>
                <NIcon size="18">
                  <Add />
                </NIcon>
              </template>
            </NButton>
          </div>
        </NGi>
      </NGrid>
    </NForm>
    <n-drawer
      v-model:show="showModal"
      preset="dialog"
      title="Dialog"
      :width="700"
      :mask-closable="false"
    >
      <n-drawer-content
        title="模板配置"
        header-style="font-size:14px;font-weight:700;padding:20px 26px"
        body-content-style="padding:26px 31px 0px 21px"
        footer-style="padding:14px 0px;display: flex;justify-content:center;"
        closable
      >
        <div class="w-full">
          <div class="mb-[10px] font-semibold">
            题型
          </div>
          <NSelect :options="QUESTION_TYPE" :value="''" />
          <div class="my-[10px] font-semibold">
            选项
          </div>
          <div v-for="(item, index) in templateDetailTmp" :key="index">
            <div class="flex items-center justify-between gap-x-10px">
              <NInput
                placeholder="请输入选项内容"
                :value="item.templateItemName"
                :on-update-value="
                  (v:any) =>
                    updateTemplateItem(
                      { ...item, templateItemName: v },
                      index
                    )
                "
              />
              <NInputNumber
                class="ml-[8px]"
                placeholder="分值"
                :precision="2"
                :show-button="false"
                :value="item.templateItemScore"
                :on-update-value="
                  (v:any) =>
                    updateTemplateItem(
                      { ...item, templateItemScore: v },
                      index
                    )
                "
              />

              <NButton
                class="!ml-[14px]"
                text
                type="error"
                :on-click="() => deleteTemplateItem(index)"
              >
                <NIcon :size="14" class="text-black">
                  <Close />
                </NIcon>
              </NButton>
            </div>
            <NCheckbox
              class="mb-[18px] mt-[12px]"
              size="small"
              label="需要填理由"
              checked-value="1"
              unchecked-value="0"
              :checked="item.isReason"
              :on-update-checked="
                (v:string) => updateTemplateItem({ ...item, isReason: v }, index)
              "
            />
          </div>

          <NButton quaternary type="info" @click="addTemplateItem()">
            <template #icon>
              <n-icon>
                <AddAlt />
              </n-icon>
            </template>
            添加选项
          </NButton>
        </div>
        <template #footer>
          <div>
            <NButton
              class="!w-[80px] !h-[30px]"
              type="primary"
              @click="saveTemplate()"
            >
              保存
            </NButton>
            <NButton
              class="!w-[80px] !h-[30px] !ml-[10px]"
              @click="
                () => {
                  showModal = false
                }
              "
            >
              取消
            </NButton>
          </div>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { NButton, NInput, NInputNumber } from 'naive-ui'
import { cloneDeep } from 'lodash-es'
import type { PropType } from 'vue'
import { Add, AddAlt, Close } from '@vicons/carbon'
import { IosAdd } from '@vicons/ionicons4'
import type {
  Questionnaire,
  ReviewItem,
  RuleItem,
  Rules,
  TemplateItem,
} from './type'
import reviewItems from './reviewItems.vue'
import ConfigForm from './configForm.vue'
import { BOOLEANTYPE, QUESTIONTYPE } from '@/constant'
import DeleteButton from '@/components/DeleteButton.vue'

const props = defineProps({
  questionnaireInfo: {
    type: Object as PropType<Questionnaire>,
    default: () => ({
      questionnaireType: '1', // 问卷类型
      isTemplate: '0', // 是否展示模板
      isDisplay: '1', // 是否展示问卷
      isScoreItem: '1', // 是否开启总分项
      ruleScoreName: '', // 总分项名称
      questionnaireScoreUpper: 0, // 分数上限
      reviewSubjectArr: [] as ReviewItem[], // 题目集合
      questionnaireRuleObj: {
        id: '',
        questionnaireId: '',
        ruleScoreName: '',
        questionnaireRuleDetailArr: [] as RuleItem[],
      } as Rules,
    }),
  },
  title: {
    type: String,
    default: '',
  },
  initLoaded: {
    type: Boolean,
    default: false,
  },
  templateItemList: {
    type: Array as PropType<TemplateItem[]>,
    default: () => [],
  },
})

const emits = defineEmits(['update:questionnaireInfo'])

const questionnaire = computed(() => props.questionnaireInfo)
const route = useRoute()
const loading = ref(false)
const showModal = ref<Boolean>(false) // 模板弹出框

let needComputedUpper = true // 是否需要计算分数上限
const QUESTION_TYPE = [{ value: '', label: '单选题' }]

// 模板数据
const templateDetail = ref<TemplateItem[]>([])

// 临时模板数据用于表单修改
const templateDetailTmp = ref<TemplateItem[]>([])

const ShowOrEdit = defineComponent({
  props: {
    value: {
      type: [String, Number, undefined] as PropType<
      string | number | undefined
      >,
      required: true,
    },
    onUpdateValue: {
      type: [Function, Array] as PropType<
      ((value: string) => void) | Array<any>
      >,
      required: true,
    },
    type: {
      type: String,
      default: 'input',
    },
  },
  setup(props) {
    const isEdit = ref(false)
    const inputRef = ref<HTMLInputElement | null>(null)
    const inputValue = ref<string | number | undefined>(props.value)
    function handleOnClick() {
      isEdit.value = true
      nextTick(() => {
        inputRef.value?.focus()
      })
    }
    function handleChange() {
      if (typeof props.onUpdateValue === 'function') {
        props.onUpdateValue(inputValue.value as string)
      }
      isEdit.value = false
    }
    return () =>
      h(
        'div',
        {
          style: 'min-height: 22px',
          onClick: handleOnClick,
        },
        props.type === 'input'
          ? h(NInput, {
            ref: inputRef,
            value: inputValue.value as string,
            onUpdateValue: (v: string) => {
              inputValue.value = v as string
              handleChange()
            },
            // onChange: handleChange,
            onBlur: handleChange,
          })
          : h(NInputNumber, {
            ref: inputRef,
            value: inputValue.value as number,
            precision: 2,
            onUpdateValue: (v: any) => {
              inputValue.value = v as number
              handleChange()
            },
            // onChange: handleChange,
            onBlur: handleChange,
          }),
      )
  },
})

/** 正在编辑的题目索引 */
const editingIndex = computed(() => {
  return questionnaire.value!.reviewSubjectArr.findIndex(item => item.editing)
})

// 分数上限
const upperLimit = computed(() => {
  let upper = 0
  questionnaire.value?.reviewSubjectArr.forEach((item) => {
    if (item.reviewSubjectItemArr && item.reviewSubjectItemArr.length) {
      const maxItem = item.reviewSubjectItemArr.reduce((prev, cur) => {
        return (prev.subjectItemScore ?? 0) > (cur.subjectItemScore ?? 0)
          ? prev
          : cur
      })
      upper = upper + (maxItem.subjectItemScore ?? 0)
    }
  })
  return upper
})

const needFilterTemplate = ref(false) // 根据监听过滤模板

// 监听模板、启用模板的话、把旧的单选题过滤掉
watch(
  () => questionnaire.value!.isTemplate,
  (nval) => {
    if (nval === BOOLEANTYPE.YES && needFilterTemplate.value) {
      questionnaire.value!.reviewSubjectArr
        = questionnaire.value!.reviewSubjectArr.filter(
          item => item.subjectType !== QUESTIONTYPE.RADIO,
        )
    }
  },
)

// 模板数据改变需要过滤原单选题
watch(
  () => templateDetail,
  (nval) => {
    if (
      questionnaire.value!.isTemplate === BOOLEANTYPE.YES
      && needFilterTemplate.value
    ) {
      questionnaire.value!.reviewSubjectArr
        = questionnaire.value!.reviewSubjectArr.filter(
          item => item.subjectType !== QUESTIONTYPE.RADIO,
        )
    }
  },
  {
    deep: true,
  },
)

watch(upperLimit, (nval) => {
  if (needComputedUpper) {
    // 获取详情后第一次访问不用监听变化
    questionnaire.value!.questionnaireScoreUpper = nval
  } else {
    needComputedUpper = true
  }
})

watch(
  () => props.initLoaded,
  (nval) => {
    if (nval) {
      // route.query.action === 'copy' || route.query.action === 'modify'
      if (route.query.id) {
        needComputedUpper = false

        templateDetail.value = JSON.parse(
          JSON.stringify(generateTemplate() || props.templateItemList),
        )
      } else {
        templateDetail.value = JSON.parse(
          JSON.stringify(props.templateItemList),
        )
      }
      templateDetailTmp.value = JSON.parse(JSON.stringify(templateDetail.value))

      setTimeout(() => {
        needFilterTemplate.value = true
      }, 0)
    }
  },
)

// 获取数据的模板数据
function generateTemplate() {
  let templateItemListTmp
  if (questionnaire.value.isTemplate === BOOLEANTYPE.YES) {
    // 启用模板时、因为模板未持久化、需要搜索第一条单选数据作为模板数据
    const first = questionnaire.value.reviewSubjectArr.find(
      (item: ReviewItem) => item.subjectType === QUESTIONTYPE.RADIO,
    )

    let index = 0
    templateItemListTmp = first?.reviewSubjectItemArr?.map((item: any) => {
      return {
        id: `${++index}`,
        isReason: item.isReason,
        sort: item.sort,
        templateItemName: item.subjectItemName,
        templateItemScore: item.subjectItemScore,
      }
    })
  }
  return templateItemListTmp
}

function addScoreItem() {
  questionnaire.value.questionnaireRuleObj!.questionnaireRuleDetailArr!.push({
    id: '',
    scoreItemName: '', // 各项名称
    scoreUpper: 0, // 分数上限
    scoreLower: 0, // 分数下限
    questionnaireRuleId: '',
    questionnaireId: '',
  })
}

/** 排他 */
function clearEditing() {
  questionnaire.value.reviewSubjectArr.forEach(
    (item: ReviewItem) => (item.editing = false),
  )
}
function getColumns() {
  return [
    {
      key: 'scoreItemName',
      title: '各项名称',
      render(row: RuleItem, index: number) {
        return h(ShowOrEdit, {
          value: row.scoreItemName,
          onUpdateValue(v: string) {
            questionnaire.value.questionnaireRuleObj!.questionnaireRuleDetailArr![
              index
            ].scoreItemName = v
          },
        })
      },
    },

    {
      key: 'scoreUpper',
      title: '分数上限',
      render(row: RuleItem, index: number) {
        return h(ShowOrEdit, {
          value: row.scoreUpper,
          type: 'number',
          onUpdateValue(v: string) {
            questionnaire.value.questionnaireRuleObj!.questionnaireRuleDetailArr![
              index
            ].scoreUpper = v
          },
        })
      },
    },
    {
      key: 'scoreLower',
      title: '分数下限',
      render(row: RuleItem, index: number) {
        return h(ShowOrEdit, {
          value: row.scoreLower,
          type: 'number',
          onUpdateValue(v: string) {
            questionnaire.value.questionnaireRuleObj!.questionnaireRuleDetailArr![
              index
            ].scoreLower = v
          },
        })
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: (row: RuleItem, index: number) => {
        return [
          h(DeleteButton, {
            handleConfirm: async() => {
              await questionnaire.value.questionnaireRuleObj!.questionnaireRuleDetailArr!.splice(
                index,
                1,
              )
            },
          }),
        ]
      },
    },
  ]
}
// 保存模板数据
function saveTemplate() {
  templateDetail.value = JSON.parse(JSON.stringify(templateDetailTmp.value))
  showModal.value = false
}
// 添加模板选项
function addTemplateItem() {
  templateDetailTmp.value = templateDetailTmp.value ?? []
  templateDetailTmp.value.push({
    isReason: '',
    sort: templateDetailTmp.value.length + 1,
    templateItemName: '',
    templateItemScore: 0,
  })
}
// 删除模板选项
function deleteTemplateItem(i: number) {
  templateDetailTmp.value.splice(i, 1)
}
function updateTemplateItem(item: TemplateItem, i: number) {
  templateDetailTmp.value[i] = item
}

/** 交换 */
function swapItem(a: number, b: number) {
  const temp = questionnaire.value.reviewSubjectArr[a]
  questionnaire.value.reviewSubjectArr[a]
    = questionnaire.value.reviewSubjectArr[b]
  questionnaire.value.reviewSubjectArr[b] = temp
}

/** 复制题目 */
function copyQuestion(index: number, item: ReviewItem) {
  clearEditing()
  const data = cloneDeep(item)
  data.renderId = `question-${Date.now()}`
  // 移除题目和选项的 id
  delete data.questionnaireId
  data.reviewSubjectItemArr?.forEach((choice, i) => {
    choice.renderId = `choice-${Date.now()}-${i}`
    delete choice.subjectId
  })
  questionnaire.value.reviewSubjectArr.splice(index + 1, 0, data)
  setEditing(index + 1)
}

/** 新增题目 */
function addQuestion() {
  clearEditing()
  questionnaire.value.reviewSubjectArr.push({
    renderId: `question-${Date.now()}`,
    subjectType: '',
    subjectName: '',
    sort: questionnaire.value.reviewSubjectArr.length + 1,
    isRequired: '1',
    // questionDimension: '',
    editing: true,
    // questionObject: null,
  })
}

/** 设置第 n 个题目为编辑中 */
function setEditing(i: number) {
  questionnaire.value.reviewSubjectArr[i].editing = true
}
</script>

<style lang="scss" scoped>
.greySplit {
  width: calc(100% - 40px);
  height: 48px;
  background: #f5f6f8;
  margin-left: 20px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  .subTitle {
    position: relative;
    padding-left: 28px;
    font-size: 15px;
    font-weight: 600;
  }
  .subTitle::before {
    content: ''; /* 创建伪元素 ::before */
    display: block; /* 将其显示为块级元素 */
    width: 3px; /* 设置竖线的宽度 */
    height: 16px; /* 根据需求调整高度（去除上下边距）*/
    background-color: #cb0000; /* 设置竖线颜色 */
    position: absolute; /* 设置绝对定位 */
    left: 15px;
    top: 3px; /* 根据需求调整与文本之间的距离 */
    border-radius: 2px;
  }
}

.ncard {
  margin: 20px;
  width: calc(100% - 40px);
}

:deep(.n-form-item) .n-form-item-label,
:deep(.n-form-item) .n-input {
  font-size: 14px;
}
</style>
