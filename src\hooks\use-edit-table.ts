import type { Ref } from 'vue'

export function useEditTable<T extends { editing: boolean }>(
  tableData: Ref<T[]>,
) {
  /** 正在编辑的行数据，从表格数据浅拷贝而来，为了实现"取消按钮恢复之前状态"的功能 */
  const editingData = ref<T>()
  /** 是否禁用新增、编辑，如果 editingData 有值，说明某一行正在编辑，此时禁用 */
  const editDisabled = computed(() => !!editingData.value)

  /** 清除所有行的编辑状态 */
  function clearEditing() {
    tableData.value.forEach(item => (item.editing = false))
    editingData.value = undefined
  }

  /** 新增/编辑 */
  const editType = ref<'add' | 'modify'>('add')

  /** 点击新增按钮 */
  function onClickAdd() {
    editType.value = 'add'
    clearEditing()
    const tmp = { editing: true } as T
    editingData.value = tmp
    tableData.value.unshift(tmp)
  }

  /** 点击编辑按钮 */
  function onClickEdit(index: number) {
    editType.value = 'modify'
    clearEditing()
    const target = tableData.value[index]
    editingData.value = { ...target }
    target.editing = true
  }

  /** 点击取消按钮 */
  function onClickCancel(index: number) {
    if (editType.value === 'add') {
      tableData.value.shift()
    } else {
      tableData.value[index].editing = false
    }
    clearEditing() // 更新编辑状态和 editDisabled
  }

  return {
    editingData,
    editDisabled,

    onClickAdd,
    onClickEdit,
    onClickCancel,
    clearEditing,
  }
}
