import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  orgId: {
    required: true,
    message: '党支部不能为空',
    trigger: 'change',
  },
  content: {
    required: true,
    message: '简介不能为空',
    trigger: 'input',
  },
  coverId: {
    required: true,
    message: '请上传封面图',
    trigger: 'change',
    type: 'string',
  },
  introduce: {
    required: true,
    message: '支部介绍不能为空',
    trigger: 'change',
  },
  moduleList: {
    '0': {
      name: {
        required: true,
        message: '模块标题不能为空',
        trigger: 'input',
      },
      content: {
        required: true,
        message: '模块介绍不能为空',
        trigger: ['input', 'change'],
      },
    },
  },
}
