<script setup lang="ts">
import type { RouteRecordRaw } from 'vue-router'
import { createMenuRoutes } from './config'
import useTabsStore from '@/store/tabs/tabs'
const router = useRouter()
const tabsStore = useTabsStore()
const menus = createMenuRoutes()
function handleClickMenu(secondMenu: RouteRecordRaw) {
  router.push({
    name: secondMenu.name,
  })
  tabsStore.addTab({
    fullPath: `${secondMenu.path}`,
    icon: secondMenu.meta?.icon as string,
    routeName: secondMenu.name as string,
    title: secondMenu.meta?.title as string,
  })
}
</script>
<template>
  <div
    v-if="menus"
    class="mt-[16px] bg-[#fff] rounded-[5px] pt-[18px] pr-[166px] pb-[10px] pl-[33px]"
  >
    <div v-for="menu in menus" :key="menu.category.name" class="mb-[60px]">
      <div class="h-[20px] flex items-center">
        <Component
          :is="`icon-${menu.category.name}`"
          class="w-[20px] h-[20px] mr-[10px]"
        />

        <span class="text-[14px] font-[600] text-[#333]">{{
          menu.category.title
        }}</span>
      </div>
      <div class="block h-[1px] bg-[#f5f5f5] my-[20px]"></div>
      <div class="grid grid-cols-4 gap-[20px]">
        <div
          v-for="secondMenu in menu.children"
          :key="secondMenu.name"
          class="second-menu-box flex items-center select-none cursor-pointer h-[70px] pl-[25px] bg-[#fafafa] rounded-[5px]"
          @click="handleClickMenu(secondMenu)"
        >
          <Component
            :is="`icon-${secondMenu.meta?.icon}`"
            class="w-[29px] h-[29px] mr-[18px]"
          />

          <span class="text-[14px] font-[400] text-[#333]">{{
            secondMenu.meta?.title
          }}</span>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="h-[calc(100vh-35vh)] flex justify-center items-center">
    <n-empty size="large" description="暂未配置任何功能，请联系管理员" />
  </div>
</template>
<style lang="scss" scoped>
.second-menu-box {
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.15);
  }
}
</style>
