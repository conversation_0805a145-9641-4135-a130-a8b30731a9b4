<script lang="ts" setup>
import { AddTwotone, DownloadRound } from '@vicons/material'
import { NButton, type TreeSelectOption } from 'naive-ui'
import HeartTalkItemForm from '../cpn/heart-talk-record/HeartTalkItemForm.vue'
import { getHeartTalkRecordTableColumns } from './config'
import { useRecordBookStore } from '@/store/recordBook'
import { deleteHeartTalkRecordItem, getHeartTalkRecordList } from '@/services/recordBookMaintenance'
import { useDrawerEdit, useMyTable } from '@/hooks'
import DeleteButton from '@/components/DeleteButton.vue'

const recordBookStore = useRecordBookStore()

const fileLoading = ref<boolean>(false)

const transferTimeSort = ref<'' | '0' | '1'>('')
const currentDownloadFileType = ref<'word' | 'pdf'>('word')
const showPopConfirm = ref<boolean>(false)

// 筛选项：组织id和年份
const filterRef = ref({
  deptId: '',
  year: '',
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  handleSingleDelete,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(getHeartTalkRecordList, filterRef, {
  batchDeleteTable: true,
  delApi: deleteHeartTalkRecordItem,
})

const calcOrganizationListTree = computed(() => {
  return recordBookStore.getOrganizationListTreeList
})
const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})

// 获取当前年份
const currentYearOptions = computed(() => {
  return recordBookStore.getYearOptions
})

const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

// 新增/编辑党务公开抽屉
const idEditRef = ref()
const addHeartTalkFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('谈心谈话记录', handelConfirmEdit)
/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
function handelConfirmEdit() {
  addHeartTalkFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addHeartTalkFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

const tableColumns = getHeartTalkRecordTableColumns(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
          display: 'flex',
          gap: '15px',
        },
      },
      [
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'view'
              showEditRef.value = true
            },
          },
          { default: () => '查看' },
        ),
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'modify'
              showEditRef.value = true
            },
          },
          { default: () => '编辑' },
        ),
        h(DeleteButton, {
          handleConfirm: () => handleSingleDelete(String(row.id)),
        }),
      ],
    )
  },
)

function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  currentPage.value = 1
  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  currentPage.value = 1
  recordBookStore.setCurrentSelectedYear(option.value as string)
}

function handleDownLoadFile() {
  fileLoading.value = true
  recordBookStore.fetchDownLoadFile(
    {
      deptId: filterRef.value.deptId,
      year: filterRef.value.year!,
      type: currentDownloadFileType.value,
    },
    () => {
      fileLoading.value = false
    },
  )
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleCancelShowPopConfirm() {
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleUpdateSorter({
  columnKey,
  order,
}: {
  columnKey: string
  order: 'ascend' | 'descend' | false
}) {
  if (columnKey === 'transferTime') {
    if (order === 'descend') {
      transferTimeSort.value = '0'
    }
    else if (order === 'ascend') {
      transferTimeSort.value = '1'
    }
    else {
      transferTimeSort.value = ''
    }
  }
  loadData()
}

// function loadData() {
//   if (!selectedDeptId.value || !selectedYear.value) {
//     return
//   }
//   loading.value = true
//   getTransferList({
//     pageNum: pageNum.value,
//     pageSize: pageSize.value,
//     deptId: selectedDeptId.value!,
//     year: selectedYear.value!,
//     transferTimeSort: transferTimeSort.value as string,
//   })
//     .then((res) => {
//       total.value = res.total || 0
//       tableData.value = res.records || []
//     })
//     .finally(() => {
//       loading.value = false
//     })
// }

onMounted(() => {
  filterRef.value.deptId = currentSelectedDept.value.deptId
  filterRef.value.year = currentYear.value
})
</script>

<template>
  <div>
    <table-container
      v-model:page="currentPage"
      v-model:page-size="pageSize"
      :loading="loading"
      :show-delete="false"
      :show-pagination="true"
      :show-toolbar="false"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      custom-toolbar
      default-expand-all
      title="谈心谈话情况"
      @update-page="onUpdatePage"
      @update-page-size="onUpdatePageSize"
    >
      <template #filters>
        <div class="flex gap-[10px]">
          <n-button type="primary" @click="handleClickAdd">
            <template #icon>
              <n-icon size="16">
                <AddTwotone />
              </n-icon>
            </template>
            新增
          </n-button>
          <n-popconfirm
            :show="showPopConfirm"
            :show-icon="false"
            placement="left"
          >
            <template #trigger>
              <n-button
                :loading="fileLoading"
                @click="showPopConfirm = !showPopConfirm"
              >
                <template #icon>
                  <n-icon size="16">
                    <DownloadRound />
                  </n-icon>
                </template>
                下载
              </n-button>
            </template>
            <div class="py-[10px]">
              <n-radio-group v-model:value="currentDownloadFileType">
                <n-radio
                  v-for="(fileType, fileTypeIndex) in [
                    { label: 'word', value: 'word' },
                    { label: 'pdf', value: 'pdf' },
                  ]"
                  :key="fileTypeIndex"
                  :value="fileType.value"
                >
                  {{ fileType.label }}
                </n-radio>
              </n-radio-group>
            </div>

            <template #action>
              <div class="w-[100%] flex flex-row justify-center items-center">
                <n-button
                  size="small"
                  type="primary"
                  @click="handleDownLoadFile()"
                >
                  确定
                </n-button>
                <n-button size="small" @click="handleCancelShowPopConfirm">
                  取消
                </n-button>
              </div>
            </template>
          </n-popconfirm>
        </div>
      </template>
      <template #btns>
        <div class="flex flex-row items-center justify-between gap-[10px]">
          <n-tree-select
            v-model:value="filterRef.deptId"
            :options="calcOrganizationListTree"
            :show-path="false"
            check-strategy="all"
            children-field="children"
            filterable
            key-field="id"
            label-field="name"
            placeholder="请选择所属党组织"
            style="width: 400px"
            value-field="id"
            @update:value="handleUpdateTreeSelectedValue"
          />
          <n-select
            v-model:value="filterRef.year"
            :options="currentYearOptions"
            placeholder="请选择年份"
            size="medium"
            style="width: 200px"
            @update:value="handleUpdateYearSelectedValue"
          />
        </div>
      </template>
    </table-container>
  </div>
  <n-drawer v-model:show="showEditRef" :width="600" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <heart-talk-item-form
        :id="idEditRef"
        ref="addHeartTalkFormRef"
        :dept-id="filterRef.deptId"
        :year="filterRef.year"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template v-if="editTypeRef !== 'view'" #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<style lang="scss" scoped></style>
