import type { VersionItem } from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq2, PaginationRes } from '@/services/types'

/** 获取版本信息列表 */
export function getVersionList(params: PaginationReq2) {
  return commonReq.get<PaginationRes<VersionItem>>({
    url: '/upms/admin/version',
    params,
  })
}

/** 获取版本详细信息 */
export function getVersionInfo(id: string) {
  return commonReq.get({
    url: `/upms/admin/version/${id}`,
  })
}

/** 添加版本 */
export function postVersionItem(data: FormData) {
  return commonReq.post({
    url: '/upms/admin/version',
    data,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  })
}

/** 编辑版本 */
export function putVersionItem(data: FormData) {
  return commonReq.put({
    url: '/upms/admin/version',
    data,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  })
}

/** 删除版本 */
export function deleteVersion(ids: string) {
  return commonReq.delete({
    url: '/upms/admin/version',
    params: { ids },
  })
}

/** 更改启用状态 */
export function postChangeStatus(params: { id: string; updateStatus: number }) {
  return commonReq.post({
    url: '/upms/admin/version/status',
    params,
  })
}

/** 获取最新版本号 */
export function getLatestVersion() {
  return commonReq.get({
    url: '/upms/admin/version/newVerSion',
  })
}
