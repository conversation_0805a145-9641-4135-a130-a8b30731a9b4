import type { RouteRecordRaw } from 'vue-router'

const pioneerRoute: RouteRecordRaw = {
  path: 'pioneer',
  name: 'pioneer',
  meta: { title: '先锋号', icon: 'pioneer', category: 'publicity', order: 1 },
  component: () => import('@/views/main/publicity/pioneer/Pioneer.vue'),
  redirect: { name: 'pioneerList' },
  children: [
    {
      path: 'list',
      name: 'pioneerList',
      component: () =>
        import('@/views/main/publicity/pioneer/cpns/PioneerList.vue'),
    },
    {
      path: 'manage',
      name: 'pioneerManage',
      component: () =>
        import('@/views/main/publicity/pioneer/cpns/PioneerManage.vue'),
    },
    {
      path: 'add',
      name: 'pioneerAdd',
      component: () => import('@/views/main/publicity/pioneer/cpns/Detail.vue'),
    },
  ],
}
export default pioneerRoute
