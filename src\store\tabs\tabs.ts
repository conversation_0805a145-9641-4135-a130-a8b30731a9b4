import { defineStore } from 'pinia'
import { sessionCache } from '@/utils/cache'
import router from '@/router'
import type { ITab } from '@/services/tabs/types'

const useTabsStore = defineStore('tabs', () => {
  const tabsOpened = ref<ITab[]>([])
  const currentTab = ref()

  const currentFullPath = computed(() => {
    const curFullPath = tabsOpened.value.find(
      item => item.routeName === currentTab.value,
    )?.fullPath
    if (curFullPath) {
      sessionCache.set('cur_full_path', curFullPath)
    }
    return curFullPath
  })

  function addTab(tab: ITab) {
    const tabIndex = tabsOpened.value.findIndex(
      item => item.routeName === tab.routeName,
    )
    if (tabIndex < 0) {
      tabsOpened.value.push(tab)
    } else {
      tabsOpened.value[tabIndex].fullPath = tab.fullPath
    }
    sessionCache.set('tabs_opened', tabsOpened.value)
    setCurrentTab(tab.routeName)
  }

  function setCurrentTab(currentTabRouterName: string) {
    currentTab.value = currentTabRouterName
    sessionCache.set('current_tab', currentTab.value)
  }

  function setTabFullPath(routeName: string, fullPath: string) {
    const tabIndex = tabsOpened.value.findIndex(
      item => item.routeName === routeName,
    )
    if (tabIndex >= 0) {
      tabsOpened.value[tabIndex].fullPath = fullPath
    }
  }

  function deleteTab(tab: ITab) {
    const tabIndex = tabsOpened.value.findIndex(
      item => item.routeName === tab.routeName,
    )
    // 判断是否是当前打开的tab
    if (tab.routeName === currentTab.value) {
      router.replace(tabsOpened.value[tabIndex - 1].fullPath)
    }
    tabsOpened.value.splice(tabIndex, 1)
    sessionCache.set('tabs_opened', tabsOpened.value)
  }

  // 同步sessionStorage
  function loadCache() {
    tabsOpened.value = sessionCache.get('tabs_opened') ?? []
    currentTab.value = sessionCache.get('current_tab') ?? []
  }

  // 重置
  function clearAction() {
    tabsOpened.value = []
    currentTab.value = ''
  }

  return {
    tabsOpened,
    currentTab,
    currentFullPath,

    addTab,
    setCurrentTab,
    setTabFullPath,
    deleteTab,
    loadCache,
    clearAction,
  }
})

export default useTabsStore
