<template>
  <n-config-provider
    :theme-overrides="themeOverides"
    :locale="zhCN"
    :date-locale="dateZhCN"
  >
    <n-loading-bar-provider>
      <n-message-provider :duration="1000" :keep-alive-on-hover="true" :max="3">
        <n-dialog-provider>
          <App />
        </n-dialog-provider>
      </n-message-provider>
    </n-loading-bar-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import type { GlobalThemeOverrides } from 'naive-ui'
import { dateZhCN, zhCN } from 'naive-ui'
import App from './App.vue'

// 覆盖主题样式
const themeOverides: GlobalThemeOverrides = {
  common: {
    // 主题色
    primaryColor: '#AC241D',
    primaryColorHover: '#a8332d',
    primaryColorSuppl: '#cb0000',
    primaryColorPressed: '#c72c24',
    // primaryColor: '#006FFF',
    // primaryColorHover: '#338CFF',
    // primaryColorSuppl: '#cb0000',
    // primaryColorPressed: '#075ce5',
    fontSizeSmall: '12px',
  },
  Button: {
    iconSizeMedium: '12px',
    iconSizeLarge: '16px',
    fontSizeMedium: '12px',
    fontSizeLarge: '14px',
  },
  Form: {
    labelFontSizeLeftMedium: '12px',
    feedbackFontSizeMedium: '12px',
    feedbackHeightMedium: '30px',
    labelTextColor: '#333',
    labelFontSizeLeftSmall: '12px',
    feedbackFontSizeSmall: '12px',
    feedbackHeightSmall: '20px',
  },
  Input: {
    fontSizeMedium: '12px',
    heightMedium: '32px',
    heightLarge: '36px',
    fontSizeLarge: '12px',
  },
  Select: {
    peers: {
      InternalSelection: { heightMedium: '32px', fontSizeMedium: '12px' },
    },
  },
  Cascader: {
    peers: {
      InternalSelection: { heightMedium: '32px', fontSizeMedium: '12px' },
    },
  },
  InputNumber: {
    peers: {
      Input: {
        heightMedium: '32px',
      },
    },
  },
  Tree: {
    nodeColorActive: '#e4e8f0',
    nodeColorPressed: '',
    nodeColorHover: '#f3f3f5',
  },
  DataTable: {
    fontSizeMedium: '12px',
    thColor: '#FAFAFA',
    thTextColor: '#333',
    thPaddingSmall: '11px',
    tdTextColor: '#333',
    tdPaddingSmall: '9px 11px',
  },
  Popselect: {
    peers: {
      InternalSelectMenu: {
        optionFontSizeMedium: '12px',
        optionTextColor: '#333',
        optionPaddingMedium: '0 20px',
      },
    },
  },
  Menu: {
    fontSize: '12px',
    itemTextColor: '#333',
    itemTextColorActive: '#333',
    itemTextColorHover: '#333',
    itemColorActive: '#E4E8F0',
    itemHeight: '33px',
    itemIconColor: '#333',
    itemIconColorActive: '#333',
    itemIconColorHover: '#333',
  },
  Modal: {
    peers: { Dialog: { closeMargin: '16px 20px 0 0' } },
  },

  Layout: {
    siderColor: '#F5F6F8',
  },
  Checkbox: {
    textColor: '#333',
  },
}
</script>
