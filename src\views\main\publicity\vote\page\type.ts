// 用户基本信息
export interface UserInfo {
  avatarId: any
  avatarUrl: any
  trueName: string
  sex: string
  birthday: any
  ethnic: string
  edu: string
  phone: string
  identityId: string
  deptName: string
  joinTime: any
  regularTime: any
}

// 支部班子基本信息
export interface BranchInfo {
  orgName: string
  orgTypeName: string
  partNum: string
  reviewedNum: string
}

// 自评
export interface SelfType {
  selfReviewTime: string
  distributeId: string
  totalStatistic?: Object
}

// 党员互评
export interface EachType {
  hasScoreRule?: boolean
  isOpenTemplate?: boolean
  ruleScoreName?: string
  totalStatistic?: Object
  itemColumn?: string[]
  itemData?: Object[]
  reviewDetailList?: EachDetail[]
}

// 党员互评
export interface BranchType {
  hasScoreRule?: boolean
  isOpenTemplate?: boolean
  ruleScoreName?: string
  totalStatistic?: Object
  itemColumn?: string[]
  itemData?: Object[]
  reviewDetailList?: EachDetail[]
}

export interface EachDetail {
  reviewId?: string
  distributeId?: string
  commentatorId?: string
  commentatorName?: string
  revieweeUserId?: string
  revieweeUserName?: string
  commentTime?: string
  reviewDistributeScore?: number
}

export interface BranchDetail {
  reviewId?: string
  distributeId?: string
  commentatorId?: string
  commentatorName?: string
  commentTime?: string
}

// 党支部评价
export interface PartyBranchEvaluated {
  evaluation: string
  evaluationTime: string
}

// 党小组评价（详情页数据）
export interface PartyGroupType {
  partyGroupReviewTime: string
  distributeId: string
}

// 党支部评价（详情页数据）
export interface PartyBranchType {
  partyBranchReviewTime: string
  distributeId: string
}
