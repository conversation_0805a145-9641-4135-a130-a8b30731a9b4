<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui'
import { NButton } from 'naive-ui'
import { DocumentImport } from '@vicons/carbon'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import { getTableColumns } from './config'
import NoticeForm from './cpn/NoticeForm.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import {
  delPartyBuildingIndicator,
  // downloadTemplateExamIndIcatorsItem,
  getPartyBuildingExamIndIcatorsList,
  postImportIndicator,
} from '@/services/affairs/discipline-inspection-list/exam-indicators'
import CustomDialog from '@/components/CustomDialog.vue'
const filterReactive = ref<{ title: string }>({
  title: '',
})
// 有接口后添加：loading,tableData
const {
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getPartyBuildingExamIndIcatorsList, filterReactive, {
  batchDeleteTable: true,
  delApi: delPartyBuildingIndicator,
})

watch(filterReactive.value, (newVal) => {
  loadData()
})

// 新增/编辑党建清单抽屉
const idEditRef = ref()
const addNoticeFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('考核指标项', handelConfirmEdit)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addNoticeFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          idEditRef.value = row.id
          editTypeRef.value = row.relatedStatus ? 'view' : 'modify'
          showEditRef.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => (row.relatedStatus ? '查看' : '编辑'),
      },
    ),
    h(DeleteButton, {
      handleConfirm: () => handleSingleDelete(String(row.id)),
    }),
  ]
})
const showExam = ref(false)
const handelImportFn = () => {
  showExam.value = true
}
/** 导入指标 */
const handleClickImportExcel = (options: {
  file: UploadFileInfo | any
  fileList: Array<UploadFileInfo>
  event?: Event
}) => {
  const formData = new FormData()
  formData.append('file', options.file.file)
  postImportIndicator(formData)
    .then((res) => {
      window.$message.success('指标项导入成功')
      loadData()
      showExam.value = false
    })
    .catch((_) => {})
}

/** 下载模板 */
const handleClickDownloadTemplate = () => {
  const url = '/static/assessmentTemplate.xls'
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', '考核指标项模板.xls')
  link.click()
  showExam.value = false
}

onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="考核指标项"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :show-delete="false"
    :checked-row-keys="checkedRowKeys"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <n-button size="small" @click="handelImportFn">
        <template #icon>
          <n-icon>
            <document-import />
          </n-icon>
        </template>
        导入指标
      </n-button>
      <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button>
    </template>
    <template #filters>
      <n-input
        v-model:value="filterReactive.title"
        size="small"
        placeholder="请输入搜索内容"
        clearable
      />
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <notice-form
        :id="idEditRef"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
  <custom-dialog
    v-model:show="showExam"
    :show-action="false"
    title="添加考核指标项"
  >
    <div class="flex justify-center items-center h-[100px]">
      <div class="flex gap-[30px]">
        <n-button size="small" @click="handleClickDownloadTemplate">
          <template #icon>
            <n-icon>
              <document-import />
            </n-icon>
          </template>
          下载模板
        </n-button>
        <n-upload
          accept=".xls,.xlsx"
          :show-file-list="false"
          @change="handleClickImportExcel"
        >
          <n-button size="small">
            <template #icon>
              <n-icon>
                <document-import />
              </n-icon>
            </template>
            导入指标
          </n-button>
        </n-upload>
      </div>
    </div>
  </custom-dialog>
</template>
<style lang="scss" scoped></style>
