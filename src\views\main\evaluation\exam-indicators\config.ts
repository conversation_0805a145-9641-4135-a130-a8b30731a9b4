import type { VNodeChild } from 'vue'
import { type DataTableColumns, NEllipsis } from 'naive-ui'
import type { ExamIndicatorsListItem } from '@/services/publicity/vote/evaluation/type'

export function getTableColumns(
  optionColumnRenderer: (row: ExamIndicatorsListItem) => VNodeChild,
): DataTableColumns<ExamIndicatorsListItem> {
  return [
    { type: 'selection' },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '4%',
      render: (_, i) => i + 1,
    },
    {
      key: 'title',
      title: '标题',
      width: '14%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'matter',
      title: '考核指标',
      width: '13%',
      render: (row) => {
        return h(
          NEllipsis,
          {
            expandTrigger: 'click',
            lineClamp: '1',
            tooltip: {
              contentStyle: { width: '400px', 'word-break': 'break-all' },
            },
          },
          {
            default: () => {
              return h('div', {
                style: {
                  width: '100%',
                },
                innerHTML: row.matter,
              })
            },
          },
        )
      },
    },
    {
      key: 'evaluationRequirements',
      title: '计分标准',
      width: '13%',
      render: (row) => {
        return h(
          NEllipsis,
          {
            expandTrigger: 'click',
            lineClamp: '1',
            tooltip: {
              contentStyle: { width: '400px', 'word-break': 'break-all' },
            },
          },
          {
            default: () => {
              return h('div', {
                style: {
                  width: '100%',
                },
                innerHTML: row.evaluationRequirements,
              })
            },
          },
        )
      },
    },
    {
      key: 'evaluationMode',
      title: '检查材料',
      width: '13%',
      render: (row) => {
        return h(
          NEllipsis,
          {
            expandTrigger: 'click',
            lineClamp: '1',
            tooltip: {
              contentStyle: { width: '400px', 'word-break': 'break-all' },
            },
          },
          {
            default: () => {
              return h('div', {
                style: {
                  width: '100%',
                },
                innerHTML: row.evaluationMode,
              })
            },
          },
        )
      },
    },
    {
      key: 'dept',
      title: '检查部门',
      width: '13%',
      render: (row) => {
        return h(
          NEllipsis,
          {
            expandTrigger: 'click',
            lineClamp: '1',
            tooltip: {
              contentStyle: { width: '400px', 'word-break': 'break-all' },
            },
          },
          {
            default: () => {
              return h('div', {
                style: {
                  width: '100%',
                },
                innerHTML: row.dept,
              })
            },
          },
        )
      },
    },
    {
      key: 'evaluationScore',
      title: '分值',
      width: '10%',
    },
    {
      key: 'relatedStatus',
      title: '状态',
      width: '10%',
      render: (row) => {
        return row.relatedStatus ? '已关联' : '未关联'
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '10%',
      render: row => optionColumnRenderer(row),
    },
  ]
}
