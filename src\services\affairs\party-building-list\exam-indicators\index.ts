import type { EditExamIndIcatorsIdsItem, PartyBuildingExamAdd } from './types'
import type { PaginationReq, PaginationRes } from '@/services/types'
import { commonReq, downloadReq } from '@/services/request'

/**
 * @postPartyBuildingExamAdd
 * 新增党建清单考核指标项
 *  */
export function postPartyBuildingExamAdd(
  data: PartyBuildingExamAdd | FormData,
) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/building-inventory/target-item',
    data,
  })
}

/**
 * @postImportIndicator
 * 导入指标项
 *  */
export function postImportIndicator(file: FormData) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/building-inventory/target-item/import',
    data: file,
  })
}

/**
 * @delPartyBuildingIndicator
 * 删除单个党建清单考核指标项
 *  */
export function delPartyBuildingIndicator(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/building-inventory/target-item',
    params: { ids },
  })
}

/**
 * @putEditExamIndIcatorsIdsItem
 * 编辑党建清单考核指标项
 *  */
export function putEditExamIndIcatorsIdsItem(
  data: EditExamIndIcatorsIdsItem | FormData,
) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/building-inventory/target-item',
    data,
  })
}

/**
 * @getPartyBuildingExamIndIcatorsList
 * 获取党建清单考核指标项列表
 *  */
export function getPartyBuildingExamIndIcatorsList(
  params: { title: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<EditExamIndIcatorsIdsItem>>({
    url: '/party-affairs/backend/v1/building-inventory/target-item',
    params,
  })
}

/**
 * @getViewPartyBuildingExamIndIcatorsItem
 * 查看单个党建清单考核指标项
 *  */
export function getViewPartyBuildingExamIndIcatorsItem(id: string) {
  return commonReq.get<EditExamIndIcatorsIdsItem>({
    url: `/party-affairs/backend/v1/building-inventory/target-item/${id}`,
  })
}

/**
 *
 * @returns downloadTemplateExamIndIcatorsItem
 * 下载考核指标项模板
 */
export function downloadTemplateExamIndIcatorsItem() {
  return downloadReq.post({
    url: '/party-affairs/backend/v1/building-inventory/target-item/template',
    responseType: 'blob',
  })
}
