<script setup lang="ts">
import type { NForm } from 'naive-ui'
import dayjs from 'dayjs'
import { formRules } from './config'
import { useOrganizationListOptions } from '@/hooks/use-select-options'
import {
  addPartyBuildingEvaluation,
  editorPartyBuildingEvaluation,
  viewPartyBuildingEvaluation,
} from '@/services/publicity/vote/evaluation'
import type { addPartyBuildingEvaluationType } from '@/services/publicity/vote/evaluation/type'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()
const { organizationListTree } = useOrganizationListOptions()

const formDataReactive = reactive<addPartyBuildingEvaluationType>({
  title: '', // 考核名称
  evaluationYear: null, // 考核年度
  organizationList: null, // 考核组织列表
})
const loading = ref(false)
const formRef = ref<InstanceType<typeof NForm>>()
onMounted(async() => {
  loading.value = true
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    try {
      const res = await viewPartyBuildingEvaluation(props.id)
      formDataReactive.title = res.title
      formDataReactive.evaluationYear = res.evaluationYear
      formDataReactive.organizationList
        = res.organizationVoList?.map((item: any) => item.id) ?? null
      formDataReactive.id = props.id
      loading.value = false
    } catch (error) {}
  }
})

// 验证表单,调用接口
async function validateAndSave() {
  loading.value = true
  const errors = await new Promise((resolve) => {
    formRef.value?.validate((errors) => {
      resolve(errors)
    })
  })

  if (!errors) {
    try {
      let res: any = null
      if (props.type === 'modify' && props.id) {
        res = await editorPartyBuildingEvaluation({
          id: formDataReactive.id,
          title: formDataReactive.title,
          evaluationYear: formDataReactive.evaluationYear,
          organizationList: formDataReactive.organizationList,
        })
      } else {
        res = await addPartyBuildingEvaluation({
          title: formDataReactive.title,
          evaluationYear: formDataReactive.evaluationYear,
          organizationList: formDataReactive.organizationList,
        })
      }
      if (res) {
        window.$message.success('保存成功')
        emits('saved')
      }
      loading.value = false
    } catch (error) {}
  }
}
// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

// 屏蔽年份
const yearDisable = (time: number) => {
  // 1、当前之前的年份不能选；2、当前年份后5年之外不能选
  return (
    time < dayjs(`${dayjs().year()}-01-01`).valueOf()
    || time
      > dayjs(
        `${dayjs().year() + 5}-${dayjs().month()}-${dayjs().date()}`,
      ).valueOf()
  )
}

const updateOrganization = (v: any) => {
  formDataReactive.organizationList = [...v]
}

defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="90"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-grid>
      <n-form-item-gi span="24" label="考核名称：" path="title">
        <n-input
          v-model:value="formDataReactive.title"
          :disabled="props.type === 'view'"
          placeholder="请输入考核名称"
          maxlength="20"
          show-count
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="考核年度：" path="evaluationYear">
        <n-date-picker
          v-model:formatted-value="formDataReactive.evaluationYear"
          style="width: 100%"
          placeholder="请选择考核年度"
          :disabled="props.type === 'view'"
          clearable
          type="year"
          :is-date-disabled="yearDisable"
          @update:formatted-value="
            (v:any) => (formDataReactive.evaluationYear = v)
          "
        />
      </n-form-item-gi>

      <n-form-item-gi span="24" label="考核组织：" path="organizationList">
        <n-cascader
          v-model:value="formDataReactive.organizationList"
          :disabled="props.type === 'view'"
          :options="(organizationListTree as any)"
          value-field="id"
          label-field="name"
          children-field="children"
          check-strategy="child"
          :show-path="false"
          clearable
          multiple
          @update:value="updateOrganization"
        />
      </n-form-item-gi>
    </n-grid>
  </n-form>
</template>
<style lang="scss" scoped></style>
