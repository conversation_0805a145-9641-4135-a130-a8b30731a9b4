<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { formRules } from './config'
import { postInteractionSingleDataAudit } from '@/services/run/interaction'

interface Props {
  id: string
}
const props = defineProps<Props>()

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const unReviewedFormRef = ref<InstanceType<typeof NForm>>()

const formDataReactive = reactive({
  unReviewedReason: '',
})

// 验证表单,调用接口
async function validateAndSave() {
  const errors = await new Promise((resolve) => {
    unReviewedFormRef.value?.validate((errors) => {
      resolve(errors)
    })
  })

  if (!errors) {
    const params = {
      id: props.id,
      reason: formDataReactive.unReviewedReason,
      status: '2',
    }
    try {
      const res = await postInteractionSingleDataAudit(params)
      if (res) {
        window.$message.success('驳回成功')
        emits('saved')
      }
    } catch (error) {}
  }
}
// 重置表单
function resetForm() {
  unReviewedFormRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="unReviewedFormRef"
    size="small"
    require-mark-placement="left"
    label-width="80"
    label-align="right"
    label-placement="left"
    :rules="formRules"
    :model="formDataReactive"
    class="p-[22px] py-[25px]"
  >
    <n-form-item path="unReviewedReason">
      <n-input
        v-model:value="formDataReactive.unReviewedReason"
        type="textarea"
        placeholder="请输入驳回原因"
        clearable
        round
        maxlength="200"
        show-count
        :autosize="{ minRows: 5, maxRows: 8 }"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
