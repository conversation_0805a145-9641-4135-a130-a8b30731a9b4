<script lang="ts" setup>
import { NIcon, NInputNumber, NSwitch } from 'naive-ui'
import { debounce } from 'lodash-es'
import { DeleteForeverFilled } from '@vicons/material'
import { CalendarEdit16Regular } from '@vicons/fluent'
import { getTableColumns } from './config'
import CarouselForm from './cpn/CarouselForm.vue'
import { useDrawerEdit } from '@/hooks/use-drawer-edit'
import {
  deleteCarousel,
  getCarouselList,
  postChangeSort,
  postMobileIsShow,
} from '@/services/run/carousel/carousel'

import { useMyTable } from '@/hooks/use-my-table'

const filterReactive = ref<{
  title: string
}>({
  title: '',
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  handleSingleDelete,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(getCarouselList, filterReactive, {
  batchDeleteTable: true,
  delApi: deleteCarousel,
})

/** 图片名称过滤 */
function handleResourceNameChange() {
  currentPage.value = 1
  filterReactive.value.title = filterReactive.value.title.trim()
  loadData()
}

watch(filterReactive, debounce(handleResourceNameChange, 50000))

// 抽屉
const idEditRef = ref()
const carouselFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('轮播图', handelConfirmEdit)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}

/** 确定保存 */
function handelConfirmEdit() {
  carouselFormRef.value?.validateAndSave()
}

/** 保存成功 */
function handleCarouselSaved() {
  showEditRef.value = false
  loadData()
}

function handleOuterClickCancel() {
  if (!carouselFormRef.value?.getChangedFlag()) {
    window.$dialog.warning({
      title: '提示',
      content: '数据未发布，关闭后本次编辑内容不保存，是否继续？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        handleClickCancel()
      },
    })
  }
  else {
    handleClickCancel()
  }
}

watch(showEditRef, (newV) => {
  if (!newV) {
    carouselFormRef.value?.resetForm()
  }
})
// 修改和删除按钮渲染
const tableColumns = getTableColumns(
  (row) => {
    return [
      row.belongingFunction === 99
        ? h(
          NIcon,
          {
            size: '30',
            style: {
              cursor: 'pointer',
            },
            color: '#AC241D',
            onClick: () => {
              editTypeRef.value = 'modify'
              idEditRef.value = row.id
              showEditRef.value = true
            },
          },
          {
            default: () => h(CalendarEdit16Regular),
          },
        )
        : '',
      h(
        NIcon,
        {
          size: '30',
          style: {
            cursor: 'pointer',
          },
          color: '#AC241D',
          onClick: () => {
            handleSingleDelete(String(row.id)).then((_) => {
              loadData()
            })
          },
        },
        {
          default: () => h(DeleteForeverFilled),
        },
      ),
    ]
  },
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
          display: 'flex',
          gap: '15px',
        },
      },
      [
        h(
          NInputNumber,
          {
            value: row.sort,
            min: 0,
            max: 999999,
            step: 1,
            precision: 0,
            onUpdateValue: (val) => {
              row.sort = val as number
            },
            onBlur: () => {
              if (row.sort === null) {
                return
              }

              postChangeSort({
                id: String(row.id),
                sort: row.sort as number,
              }).then((res) => {
                window.$message.success('操作成功')
                loadData()
              })
            },
          },
          {},
        ),
      ],
    )
  },
  (row) => {
    return h(NSwitch, {
      checkedValue: '是',
      uncheckedValue: '否',
      disabled: row.isRelease === '否', // 未发布的不能选择移动端是否显示
      value: row.isMobile,

      onUpdateValue() {
        const params = {
          id: String(row.id),
          isRelease: row.isMobile === '是' ? 0 : 1,
        }
        postMobileIsShow(params)
          .then((res) => {
            row.isMobile = res
          })
          .catch(() => {})
          .finally(() => {
            loadData()
          })
      },
    })
  },
)
onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :loading="loading"
    :show-delete="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    title="轮播图管理"
    @click-add="handleClickAdd"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
  >
    <template #btns>
      <div class="flex flex-row justify-between items-center gap-x-[10px]">
        <n-button type="primary" size="small" @click="handleClickAdd">
          新增
        </n-button>
        <n-input
          v-model:value="filterReactive.title"
          clearable
          placeholder="请输入轮播图名称"
          size="small"
        />
      </div>
    </template>
  </table-container>
  <n-drawer v-model:show="showEditRef" :mask-closable="false" :width="600">
    <n-drawer-content :title="drawerTitle" closable>
      <carousel-form
        :id="idEditRef"
        ref="carouselFormRef"
        :type="editTypeRef"
        @saved="handleCarouselSaved"
      />
      <template #footer>
        <n-button
          style="margin-right: 10px"
          type="primary"
          @click="handleClickConfirm"
        >
          保存
        </n-button>
        <n-button @click="handleOuterClickCancel">
          取消
        </n-button>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
