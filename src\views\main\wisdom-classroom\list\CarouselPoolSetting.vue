<template>
  <div>
    <n-data-table
      :bordered="false"
      :columns="tableColumns"
      :data="tableData.value"
      :loading="loading"
    />
    <div class="flex justify-end mt-4">
      <n-pagination
        v-model:page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :item-count="total"
        :page-sizes="pagination.pageSizes"
        :show-size-picker="false"
        @change="pagination.onChange"
        @update:page-size="pagination.onUpdatePageSize"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { NIcon, NInputNumber, NSwitch } from 'naive-ui'
import { DeleteForeverFilled } from '@vicons/material'
import { getTableColumnsOfCarouselPool } from './config'
import {
  deleteCarouselPool,
  getCarouselPoolList,
  sortCarouselPool,
  updateCarouselPoolTop,
} from '@/services/wisdom'
import type { CarousePoolType } from '@/services/wisdom/types'

const emits = defineEmits<{
  (e: 'refreshParentCategory'): void
}>()

const tableData = reactive<{ value: CarousePoolType[] }>({ value: [] })
const total = ref(0)
const loading = ref(false)

const pagination = reactive({
  page: 1,
  pageSize: 5,
  pageSizes: [5],
  onChange: (page: number) => {
    pagination.page = page
    loadData()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    loadData()
  },
})

/** 列表操作 */
const tableColumns = getTableColumnsOfCarouselPool(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
          display: 'flex',
          gap: '15px',
        },
      },
      [
        h(
          NInputNumber,
          {
            value: row.sort,
            min: 0,
            max: 999999,
            step: 1,
            precision: 0,
            onUpdateValue: (val) => {
              row.sort = val as number
            },
            onBlur: () => {
              if (row.sort === null) {
                return
              }

              sortCarouselPool({ id: row.id, sort: row.sort as number }).then(
                (res) => {
                  window.$message.success('操作成功')
                  loadData()
                },
              )
            },
          },
          {},
        ),
      ],
    )
  },
  row =>
    h(
      NSwitch,
      {
        onUpdateValue: (value: any) => topCarousePool({ id: row.id }),
        value: Number(row.isAppShow),
        checkedValue: 1,
        uncheckedValue: 0,
      },
      {
        checked: '是',
        unchecked: '否',
      },
    ),
  row =>
    h(
      NIcon,
      {
        size: '30',
        style: {
          cursor: 'pointer',
        },
        color: '#AC241D',
        onClick: () => {
          deleteCarouselPool({ id: row.id }).then((res) => {
            window.$message.success('删除成功')
            loadData()
            // 通知父组件刷新分类的接口
            emits('refreshParentCategory')
          })
        },
      },
      {
        default: () => h(DeleteForeverFilled),
      },
    ),
)

// 表格中是否加入轮播池 更新轮播状态
async function topCarousePool(data: { id: string }) {
  await updateCarouselPoolTop(data)
  window.$message.success('操作成功')
  loadData()
}

// 加载轮播图列表
function loadData() {
  loading.value = true
  getCarouselPoolList({
    pageNum: pagination.page,
    pageSize: pagination.pageSize,
  })
    .then((res) => {
      total.value = res.total
      tableData.value = res.records
    })
    .finally(() => {
      loading.value = false
    })
}

loadData()
</script>

<style lang="scss" scoped></style>
