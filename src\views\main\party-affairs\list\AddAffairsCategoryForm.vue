<template>
  <div>
    <n-form
      ref="affairsCategoryRef"
      require-mark-placement="left"
      label-placement="left"
      label-width="80px"
      :model="formData"
      :rules="formRules"
    >
      <n-form-item label="类别名称" path="name">
        <n-input
          v-model:value="formData.name"
          placeholder="请输入类别名称"
          maxlength="60"
          show-count
          clearable
        />
      </n-form-item>
      <!-- <n-form-item label="学习可得的学分" path="studyScore">
        <n-input-number
          v-model:value="formData.studyScore"
          style="width: 100%"
        />
      </n-form-item> -->
      <n-form-item span="24" label="图片：" path="cover_url">
        <ImgUploader
          v-model:oldImgUrl="formData.coverUrl"
          :width="380"
          :height="200"
          :need-cropper="false"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import type { FormRules } from 'naive-ui'
import { uploadImg } from '@/services'
import type { uploadFileItem } from '@/services/types'
const formRules: FormRules = {
  name: {
    required: true,
    message: '党务通类别不能为空',
    trigger: 'input',
    type: 'string',
  },
  // studyScore: {
  //   required: true,
  //   message: '请输入学习可得的分数',
  //   trigger: 'input',
  //   type: 'number',
  // },
  coverUrl: {
    required: true,
    message: '请上传图片',
    trigger: 'change',
    type: 'string',
  },
}
const affairsCategoryRef = ref()
const formData = ref({
  name: '',
  // studyScore: '',
  coverUrl: '',
})

function handleValidate() {
  return new Promise((resolve, reject) => {
    affairsCategoryRef.value?.validate((errors: any) => {
      if (!errors) {
        resolve(true)
      }
      else {
        resolve(false)
      }
    })
  })
}

function handleSetFormData(data: any) {
  formData.value = data
}

/**
 * 处理封面图片上传完成的异步函数。
 * @param file 需要上传的文件对象。
 * @returns {Promise<void>} 不返回任何内容。
 */
const handleCoverDone = async(file: File) => {
  const imgFileData = new FormData() // 创建一个FormData对象用于存放文件数据
  imgFileData.append('file', file) // 将文件添加到FormData对象中
  try {
    if (formData.value.coverUrl) {
      return // 如果已存在封面URL，则直接返回，不进行上传
    }
    // 异步上传图片文件
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      // 上传成功后，更新formData中的封面URL
      formData.value.coverUrl = data.url || ''
    }
  }
  catch (error) {
    // 捕获并处理上传过程中的错误
  }
}

// PC封面图片删除操作
const handleCoverDelete = () => {
  formData.value.coverUrl = ''
}

defineExpose({
  formData,
  handleValidate,
  handleSetFormData,
})
</script>

<style scoped lang="scss"></style>
