import { acceptHMRUpdate, defineStore } from 'pinia'

export const useUserStore = defineStore('user', () => {
  const userName = ref('')

  function setNewName(name: string) {
    userName.value = name
    window.$message.success('success!😄', {
      duration: 3000,
      closable: true,
      keepAliveOnHover: true,
    })
  }
  return {
    userName,
    setNewName,
  }
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useUserStore, import.meta.hot))
}
