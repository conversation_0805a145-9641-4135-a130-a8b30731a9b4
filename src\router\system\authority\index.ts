import type { RouteRecordRaw } from 'vue-router'

const authorityRoute: RouteRecordRaw = {
  path: 'authority',
  name: 'authority',
  meta: { title: '权限管理', icon: 'authority', category: 'system', order: 3 },
  component: () => import('@/views/main/system/authority/Authority.vue'),
  redirect: { name: 'role' },
  children: [
    {
      path: 'role',
      name: 'role',
      meta: { title: '角色配置', icon: 'role' },
      component: () =>
        import('@/views/main/system/authority/role/list/RoleList.vue'),
    },
    {
      path: 'admin',
      name: 'admin',
      meta: { title: '管理员配置', icon: 'admin' },
      component: () =>
        import('@/views/main/system/authority/admin/list/AdminList.vue'),
    },
    {
      path: 'menu',
      name: 'admin',
      meta: { title: '菜单配置', icon: 'admin' },
      component: () => import('@/views/main/system/authority/menu/Munu.vue'),
    },
  ],
}
export default authorityRoute
