<template>
  <div class="py-[25px] px-[22px]">
    <div>
      <span class="text-[#333333] text-[14px] leading-[20px]">组织生活记录本维护</span>
    </div>
    <div class="flex flex-row justify-between items-start">
      <div class="flex flex-row gap-x-[10px] py-[33px]">
        <n-tree-select
          v-model:value="selectedDeptId"
          :options="calcOrganizationListTree"
          :show-path="false"
          check-strategy="all"
          children-field="children"
          filterable
          key-field="id"
          label-field="name"
          placeholder="请选择所属党组织"
          style="width: 400px"
          value-field="id"
          @update:value="handleUpdateValue"
        />
        <n-select
          v-model:value="selectedYear"
          :options="yearOptions"
          placeholder="请选择年份"
          style="width: 100px"
          @update:value="handleUpdateYearValue"
        />
      </div>

      <div class="flex flex-row gap-x-[10px] py-[33px]">
        <div>
          <n-popconfirm
            :show="showDownloadAllFilePopConfirm"
            :show-icon="false"
            placement="bottom"
          >
            <template #trigger>
              <n-button
                :loading="allDownloadFileLoading"
                type="primary"
                @click="
                  showDownloadAllFilePopConfirm = !showDownloadAllFilePopConfirm
                "
              >
                <template #icon>
                  <n-icon size="16">
                    <DownloadRound />
                  </n-icon>
                </template>
                下载
              </n-button>
            </template>
            <div class="py-[10px]">
              <n-radio-group v-model:value="currentDownloadAllFileType">
                <n-radio
                  v-for="(fileType, fileTypeIndex) in [
                    { label: 'word', value: 'word' },
                    { label: 'pdf', value: 'pdf' },
                  ]"
                  :key="fileTypeIndex"
                  :value="fileType.value"
                >
                  {{ fileType.label }}
                </n-radio>
              </n-radio-group>
            </div>

            <template #action>
              <div class="w-[100%] flex flex-row justify-center items-center">
                <n-button
                  size="small"
                  type="primary"
                  @click="handleClickDownloadAll"
                >
                  确定
                </n-button>
                <n-button size="small" @click="handleClickCancelDownloadAll">
                  取消
                </n-button>
              </div>
            </template>
          </n-popconfirm>
        </div>

        <div class="flex flex-col gap-y-[10px]">
          <n-button
            :disabled="isShowAsyncBtn"
            :loading="allAsyncLoading"
            type="primary"
            @click="handleAllAsync"
          >
            查询同步结果
          </n-button>
          <span
            v-show="syncRecordTime"
            class="text-[#333333] text-[12px] leading-[17px] flex flex-row items-center gap-x-[6px]"
          ><n-icon color="#AC241D" size="16"><CachedSharp /></n-icon>{{ syncRecordTime }}</span>
        </div>
      </div>
    </div>

    <div class="flex flex-row justify-center items-start min-w-[1240px]">
      <div class="flex flex-[0.92] justify-center items-start">
        <div class="relative">
          <img
            :src="RecordBookIcon"
            alt=""
            class="w-[329px] h-[450px]"
            srcset=""
          />
          <div class="absolute top-0 left-0 w-[100%] h-[100%]">
            <div class="relative">
              <div class="absolute top-[235px] left-[166px]">
                <span class="text-[8px]">江苏交通控股有限公司</span>
              </div>
              <div class="absolute top-[272px] left-[166px]">
                <span class="text-[8px]">{{
                  currentSelectedDept.deptName
                }}</span>
              </div>
              <div class="absolute top-[374px] left-[156px]">
                <span class="text-[8px] text-right">{{
                  translateYear(currentYear)
                }}</span>
              </div>
            </div>
          </div>
          <div class="flex flex-row justify-between pt-[30px]">
            <n-button type="primary" @click="handleChangeYear('prev')">
              上一年
            </n-button>
            <n-button type="primary" @click="handleChangeYear('next')">
              下一年
            </n-button>
          </div>
        </div>
      </div>

      <div class="flex flex-wrap w-[870px] gap-[10px]">
        <div
          v-for="(item, index) in recordBookList"
          :key="index"
          class="flex flex-row justify-between items-center w-[428px] h-[48px] px-[20px] border cursor-pointer"
        >
          <span>{{ item.title }}</span>
          <div class="flex flex-row gap-x-[10px]">
            <n-popconfirm
              :show="item.showPopconfirm"
              :show-icon="false"
              placement="bottom"
            >
              <template #trigger>
                <img
                  :src="downLoadIcon"
                  alt=""
                  srcset=""
                  @click="handleShowPopConfirm(item)"
                />
              </template>
              <div class="py-[10px]">
                <n-radio-group v-model:value="currentDownloadFileType">
                  <n-radio
                    v-for="(fileType, fileTypeIndex) in [
                      { label: 'word', value: 'word' },
                      { label: 'pdf', value: 'pdf' },
                    ]"
                    :key="fileTypeIndex"
                    :value="fileType.value"
                  >
                    {{ fileType.label }}
                  </n-radio>
                </n-radio-group>
              </div>

              <template #action>
                <div class="w-[100%] flex flex-row justify-center items-center">
                  <n-button
                    size="small"
                    type="primary"
                    @click="handleDownLoadFile(item)"
                  >
                    确定
                  </n-button>
                  <n-button
                    size="small"
                    @click="handleCancelShowPopConfirm(item)"
                  >
                    取消
                  </n-button>
                </div>
              </template>
            </n-popconfirm>
            <img
              :src="editIcon"
              alt=""
              srcset=""
              @click="handleToRecordBookDetailPage(item)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TreeSelectOption } from 'naive-ui'
import { NButton } from 'naive-ui'
import { CachedSharp, DownloadRound } from '@vicons/material'
import JSONbig from 'json-bigint'
import RecordBookIcon from '@/assets/image/recordBook.png'
import downLoadIcon from '@/assets/image/download_icon.png'
import editIcon from '@/assets/image/edit_icon.png'
import { useOrganizationListOptions } from '@/hooks/use-select-options'
import { useRecordBookStore } from '@/store/recordBook'
import { useAuthStore } from '@/store/auth/auth'
import type { CatalogType } from '@/services/recordBookMaintenance/types'
import {
  getSyncTime,
  syncWorkRecordBook,
} from '@/services/recordBookMaintenance'

const JSONbigString = JSONbig({ storeAsString: true })

const authStore = useAuthStore()
const router = useRouter()
const selectedYear = ref<string | null>('2025')
const selectedDeptId = ref<string | null>(null)
const { organizationListTree } = useOrganizationListOptions()
const calcOrganizationListTree = ref<any>([])
const currentDownloadFileType = ref<'word' | 'pdf'>('word')

const allDownloadFileLoading = ref<boolean>(false)
const currentDownloadAllFileType = ref<'word' | 'pdf'>('word')
const showDownloadAllFilePopConfirm = ref<boolean>(false)
const allAsyncLoading = ref<boolean>(false)
const syncRecordTime = ref<string>('')

const recordBookStore = useRecordBookStore()

const isShowAsyncBtn = computed(() => {
  // 判断当前选择的年份是不是当年
  if (Number(selectedYear.value) !== new Date().getFullYear()) {
    return !!syncRecordTime.value
  }
  return false
})

const yearOptions = computed(() => {
  return recordBookStore.getYearOptions
})

const recordBookList = computed(() => {
  const recordList = recordBookStore.getRecordBookList || []
  recordList.forEach((item: CatalogType) => {
    item.showPopconfirm = false
  })
  return recordList
})

// 当前登录人的部门id
const currentDeptId = computed(() => {
  const ids = JSONbigString.parse(authStore.wholeUserInfo.deptIds || '[]')
  return ids.map((item: string) => item.toString())
})

const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})

// 获取当前年份
const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

watchEffect(() => {
  calcOrganizationListTree.value = filterTreeByUserScope(
    organizationListTree.value as any,
  )

  recordBookStore.setOrganizationListTreeList(
    toRaw(calcOrganizationListTree.value),
  )
})

watch(
  () => calcOrganizationListTree.value.length,
  () => {
    if (
      calcOrganizationListTree.value.length
      && !currentSelectedDept.value.deptId
    ) {
      // 查找第一个可选的党支部
      const firstSelectablePartyBranch = findFirstSelectablePartyBranch(
        calcOrganizationListTree.value,
      )

      if (firstSelectablePartyBranch) {
        selectedDeptId.value = firstSelectablePartyBranch.id
        recordBookStore.setCurrentSelectedDept({
          deptId: firstSelectablePartyBranch.id,
          deptName: firstSelectablePartyBranch.name,
        })
      }
    }
  },
)

function translateYear(year: string): string {
  const digitMap: Record<string, string> = {
    '0': '〇',
    '1': '一',
    '2': '二',
    '3': '三',
    '4': '四',
    '5': '五',
    '6': '六',
    '7': '七',
    '8': '八',
    '9': '九',
  }

  let result = ''
  for (const char of year) {
    if (digitMap[char] !== undefined) {
      result += digitMap[char]
    }
    else {
      result += char
    }
  }
  return result
}

interface TreeNode {
  id: string
  parentId?: string
  name: string
  org_type: string
  children?: TreeNode[]
  disabled?: boolean
}

function filterTreeByUserScope(nodes: TreeNode[]): TreeNode[] {
  return nodes
    .map((node) => {
      const filteredChildren = node.children
        ? filterTreeByUserScope(node.children)
        : []

      // 检查当前节点是否符合条件：党支部且在用户部门范围内
      const isCurrentNodeValid
        = ['党支部'].includes(node.org_type)
        && currentDeptId.value.includes(node.id)

      // 检查是否有有效的子节点
      const hasValidChildren = filteredChildren.length > 0

      // 如果当前节点有效或有有效的子节点，则保留该节点
      if (isCurrentNodeValid || hasValidChildren) {
        return {
          ...node,
          disabled: !isCurrentNodeValid, // 只有符合条件的节点才可选
          children: filteredChildren.length > 0 ? filteredChildren : undefined,
        }
      }

      return null
    })
    .filter(node => node !== null) as TreeNode[]
}

// 查找第一个可选的党支部
function findFirstSelectablePartyBranch(nodes: TreeNode[]): TreeNode | null {
  const stack: TreeNode[] = [...nodes]

  while (stack.length > 0) {
    const node = stack.pop()!

    // 如果是可选的党支部，直接返回
    if (node.org_type === '党支部' && !node.disabled) {
      return node
    }

    // 将子节点加入栈中继续查找
    if (node.children) {
      stack.push(...[...node.children].reverse())
    }
  }

  return null
}

function handleUpdateValue(v: string, option: TreeSelectOption) {
  selectedDeptId.value = v

  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearValue(v: string, option: TreeSelectOption) {
  selectedYear.value = v
  recordBookStore.setCurrentSelectedYear(option.value as string)
}

function handleChangeYear(type: string) {
  if (type === 'prev') {
    const minYear = yearOptions.value[yearOptions.value.length - 1].value
    if (Number(selectedYear.value) <= Number(minYear)) {
      selectedYear.value = minYear
      window.$message.warning('暂未有上一年记录')
      return
    }
    selectedYear.value = `${Number(selectedYear.value) - 1}`
  }

  if (type === 'next') {
    const maxYear = yearOptions.value[0].value
    if (Number(selectedYear.value) >= Number(maxYear)) {
      selectedYear.value = maxYear
      window.$message.warning('暂未有下一年记录')
      return
    }

    selectedYear.value = `${Number(selectedYear.value) + 1}`
  }

  recordBookStore.setCurrentSelectedYear(selectedYear.value || '')
}

function handleToRecordBookDetailPage(data: CatalogType) {
  recordBookStore.setCurrentSelectedChapter({
    value: data.value!,
    title: data.title,
  })
  // 重置会议类型
  recordBookStore.setCurrentSelectedMeetingType(null)
  // 重置调动类型
  recordBookStore.setCurrentSelectedTransferType(null)
  router.push({
    name: 'recordBookDetail',
    query: {
      type: data.type,
    },
  })
}

function handleShowPopConfirm(data: any) {
  nextTick(() => {
    recordBookList.value.forEach((item: CatalogType) => {
      item.showPopconfirm = false
    })
    data.showPopconfirm = !data.showPopconfirm
  })
}

function handleCancelShowPopConfirm(data: any) {
  nextTick(() => {
    data.showPopconfirm = false
  })
}

function handleDownLoadFile(data: CatalogType) {
  recordBookStore.setCurrentSelectedChapter({
    value: data.value!,
    title: data.title,
  })
  recordBookStore.fetchDownLoadFile({
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
    type: currentDownloadFileType.value,
  })

  nextTick(() => {
    data.showPopconfirm = false
  })
}

function loadData() {
  recordBookStore.getChapterList()
}

function handleClickCancelDownloadAll() {
  nextTick(() => {
    showDownloadAllFilePopConfirm.value = false
  })
}

async function handleClickDownloadAll() {
  showDownloadAllFilePopConfirm.value = false
  allDownloadFileLoading.value = true
  const params = {
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
    deptName: '江苏交通控股有限公司',
    partyBranchName: currentSelectedDept.value.deptName,
    type: currentDownloadAllFileType.value,
    yearUpCase: translateYear(currentYear.value),
  }

  // 1. 使用fetch获取文件blob
  const queryString = new URLSearchParams(params).toString()
  const urlWithParams = `/party-affairs/work-record-book/word/all/download?${queryString}`
  const response = await fetch(import.meta.env.VITE_API_BASE + urlWithParams, {
    headers: {
      Authorization: `Bearer ${sessionStorage.getItem('access_token')}`,
      responseType: 'blob',
    },
    method: 'POST',
  })

  if (!response.ok) {
    allDownloadFileLoading.value = false
    throw new Error(`下载失败: ${response.status} ${response.statusText}`)
  }

  // 2. 获取blob数据
  const blob = await response.blob()

  // 3. 从响应头或URL中提取文件名（如果没有提供filename参数）江苏交通控股有限公司-
  const finalFilename = `${currentSelectedDept.value.deptName}-党支部工作记录本`

  // 4. 创建临时链接并触发下载
  const blobUrl = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = blobUrl
  a.download = finalFilename
  document.body.appendChild(a)
  a.click()

  window.$message.success('下载成功')
  allDownloadFileLoading.value = false

  // 5. 清理
  setTimeout(() => {
    document.body.removeChild(a)
    window.URL.revokeObjectURL(blobUrl)
  }, 100)
}

function handleAllAsync() {
  allAsyncLoading.value = true
  syncWorkRecordBook({
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
    syncType: '1',
  })
    .then((res) => {
      window.$message.success('同步成功！')
      fetchSyncTime()
    })
    .finally(() => {
      allAsyncLoading.value = false
    })
}

function fetchSyncTime() {
  getSyncTime({
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
    fieldType: '99', // 99 全量更新时间
  }).then((res) => {
    syncRecordTime.value = (res as any) || null
  })
}

watch(
  () => [selectedDeptId.value, selectedYear.value],
  () => {
    if (selectedDeptId.value && selectedYear.value) {
      fetchSyncTime()
    }
  },
  {
    immediate: true,
  },
)

onMounted(() => {
  selectedDeptId.value = recordBookStore.getCurrentSelectedDept.deptId
  selectedYear.value = recordBookStore.getCurrentSelectedYear
  loadData()
})
</script>

<style lang="scss" scoped></style>
