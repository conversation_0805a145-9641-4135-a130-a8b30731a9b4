<!-- 备份最开始组织架构管理 -->
<template>
  <table-container
    :loading="loading"
    :page="1"
    :page-size="10"
    :show-delete="false"
    :show-pagination="false"
    :show-toolbar="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    custom-toolbar
    default-expand-all
    title="组织管理"
    @click-add="handleClickAddRootNode"
  >
    <template #btns>
      <n-button
        :disabled="isShowAddRotBtn"
        size="small"
        type="primary"
        @click="handleClickAddRootNode"
      >
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加根节点
      </n-button>
    </template>
    <template #filters>
      <n-input
        v-model:value="filterReactive.orgName"
        clearable
        placeholder="请输入组织名称"
        size="small"
      />
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :mask-closable="false" :width="700">
    <n-drawer-content :title="drawerTitle" closable>
      <notice-form
        :id="idEditRef"
        ref="addNoticeFormRef"
        :level="idEditLevel"
        :parent-id="idEditParentID"
        :parent-name="idEditParentName"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            style="width: 80px"
            type="primary"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<script lang="ts" setup>
import { NButton } from 'naive-ui'
import { PlusRound } from '@vicons/material'
import { getTableColumns } from './config'
import NoticeForm from './cpn/NoticeForm.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEditOrganize } from '@/hooks'
import {
  deleteOrganizationItem,
  getOrganizationTableList,
  getVagueOrganizationTableList,
} from '@/services/organization'

const filterReactive = ref<{ orgName: string }>({
  orgName: '',
})

const loading = ref(false)
const tableData = ref([])
const isShowAddRotBtn = ref(false)
/** 获取组织列表 */
const getTableList = () => {
  loading.value = true
  getOrganizationTableList()
    .then((res: any) => {
      tableData.value = res || []
      if (Array.isArray(res) && res.length >= 1) {
        isShowAddRotBtn.value = true
      }
      else {
        isShowAddRotBtn.value = false
      }
    })
    .finally(() => {
      loading.value = false
    })
}
/** 获取过滤组织列表 */
const filterTableList = () => {
  getVagueOrganizationTableList(filterReactive.value).then((res: any) => {
    tableData.value = res || []
  })
}
const filterVal = computed(() => {
  if (filterReactive.value.orgName && filterReactive.value.orgName !== '') {
    return filterReactive.value.orgName
  }
  else {
    return null
  }
})

const deleteOrganization = (id: string) => {
  deleteOrganizationItem(id).then((res) => {
    if (filterVal.value) {
      filterTableList()
    }
    else {
      getTableList()
    }
  })
}

watch(
  () => filterVal.value,
  (newVal) => {
    if (newVal && newVal !== '') {
      filterTableList()
    }
    else {
      getTableList()
    }
  },
)

// 新增/编辑党建清单抽屉
const idEditRef = ref()
const idEditParentID = ref()
const idEditParentName = ref()
const idEditLevel = ref()
const addNoticeFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  editTitle,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEditOrganize({ name: '根节点', confirmFn: handelConfirmEdit })

/** 添加根节点 */
const handleClickAddRootNode = () => {
  idEditRef.value = '0'
  idEditParentID.value = '0'
  editTitle.value = '根节点'
  editTypeRef.value = 'add'
  showEditRef.value = true
}

/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}

watch(showEditRef, (newV) => {
  if (!newV) {
    addNoticeFormRef.value?.resetForm()
  }
})

/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  getTableList()
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          idEditRef.value = row.id
          idEditParentID.value = row.parentId
          idEditParentName.value = row.name
          editTitle.value = '子级'
          editTypeRef.value = 'addSubset'
          showEditRef.value = true
        },
        disabled: row.level === 6,
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '新增子级',
      },
    ),
    h(
      NButton,
      {
        onClick: () => {
          idEditRef.value = row.id
          editTitle.value = row.parentId === '0' ? '根节点' : '子级'
          editTypeRef.value = 'modify'
          showEditRef.value = true
          idEditLevel.value = row.level
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '修改',
      },
    ),
    h(DeleteButton as any, {
      handleConfirm: () => {
        deleteOrganization(String(row.id))
      },
    }),
  ]
})

onMounted(getTableList)
onActivated(() => {
  filterVal.value ? filterTableList() : getTableList()
})
</script>

<style lang="scss" scoped></style>
