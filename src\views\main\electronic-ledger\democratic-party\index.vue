<script setup lang="ts">
import { NButton } from 'naive-ui'
import { debounce } from 'lodash-es'
import { computed, h, onMounted, ref, watch } from 'vue'
import { getTableColumns } from './config'
import { useMyTable } from '@/hooks/use-my-table'
import {
  getDemocraticById,
  getDemocraticList,
} from '@/services/electronic-ledger/index'

// (0:党委书记,1:党委副书记、2:纪委书记、3:党委委员、4:党支部书记、5:组宣委员、6:纪检委员 7:非党员 99 普通党员
const partyIdentityDicts: any = {
  '0': '党委书记',
  '1': '党委副书记',
  '2': '纪委书记',
  '3': '党委委员',
  '4': '党支部书记',
  '5': '组宣委员',
  '6': '纪检委员',
  '7': '非党员',
  '99': '普通党员',
}

const detailVisible = ref<boolean>(false)
const filterReactive = ref<{
  orgName: string
  startTime: string | null
  endTime: string | null
}>({
  orgName: '',
  startTime: null,
  endTime: null,
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(getDemocraticList, filterReactive, {
  batchDeleteTable: true,
})

// 详情数据
const detailRow = ref([
  { label: '姓名', value: '' },
  { label: '入党时间', value: '' },
  { label: '性别', value: '' },
  { label: '出生日期', value: '' },
  { label: '所在组织', value: '' },
  { label: '文化程度', value: '' },
  { label: '党内外职务', value: '' },
  { label: '奖惩情况', value: '' },
  { label: '个人小结', value: '' },
])
const detailData = ref<any>({})

const timeRangeFormattedValue = computed<[string, string] | null>(() => {
  const { startTime, endTime } = filterReactive.value
  if (startTime && endTime) {
    return [startTime, endTime]
  }
  return null
})

const handleUpdateMonthRange = (formattedValue: [string, string] | null) => {
  if (formattedValue) {
    filterReactive.value.startTime = formattedValue[0]
    filterReactive.value.endTime = formattedValue[1]
  }
  else {
    filterReactive.value.startTime = ''
    filterReactive.value.endTime = ''
  }
}

/** 图片名称过滤 */
function handleResourceNameChange() {
  currentPage.value = 1
  filterReactive.value.orgName = filterReactive.value.orgName.trim()
  loadData()
}
watch(filterReactive.value, debounce(handleResourceNameChange, 50000))

// 评价项目列表
const projectList = ref<any>([])

// 对我的建议
const mySuggestion = ref('')
// 党小组评审意见
const partyGroupfillRecordList = ref([])
// 党支部评审意见
const partyBranchfillRecordList = ref([])

/** 点击添加按钮 */
function getDetail(id: any) {
  getDemocraticById(id).then((res: any) => {
    detailRow.value = [
      { label: '姓名', value: res?.user?.trueName },
      { label: '入党时间', value: res?.user?.joinTime },
      { label: '性别', value: res?.user?.sex },
      { label: '出生日期', value: res?.user?.birthday },
      { label: '所在组织', value: res?.user?.deptName },
      { label: '文化程度', value: res?.user?.edu },
      {
        label: '党内外职务',
        value: partyIdentityDicts[res?.user?.partyIdentity],
      },
      // { label: '奖惩情况', value: res?.user?.trueName },
      // { label: '个人小结', value: res?.user?.trueName },
    ]
    detailData.value = res
    // 评价项目数组
    try {
      if (res?.each?.eachUserData && Array.isArray(res.each?.eachUserData)) {
        // 转换数据结构
        let arr = res.each?.eachUserData.slice(0)
        arr = arr.map((item: any) => {
          const keys = Object.keys(item)
          item.title = keys[0]
          item.options = item[keys[0]]
          item.lables = []
          Object.keys(item.options).forEach((optionKey: any) => {
            item.lables.push({
              key: optionKey,
              value: item.options[optionKey],
            })
          })
          return item
        })

        // 总体评价
        if (res?.each?.hasScoreRule) {
          const lables: any = []
          const totalStatistic = res?.each?.totalStatistic
          Object.keys(totalStatistic).forEach((optionKey: any) => {
            lables.push({
              key: optionKey,
              value: totalStatistic[optionKey],
            })
          })
          arr.unshift({
            title: '总体评价',
            lables,
          })
        }

        projectList.value = arr
      }
    }
    catch (error) {}
    // 对我的建议
    if (res?.each?.fillRecordList && Array.isArray(res?.each?.fillRecordList)) {
      const suggestStr = res?.each?.fillRecordList.join(' ')
      mySuggestion.value = suggestStr
    }
    // 党小组评审意见
    if (
      res?.partyGroupfillRecordList
      && Array.isArray(res?.partyGroupfillRecordList)
    ) {
      partyGroupfillRecordList.value = res?.partyGroupfillRecordList
    }
    // 党支部评审意见
    if (
      res?.partyBranchfillRecordList
      && Array.isArray(res?.partyBranchfillRecordList)
    ) {
      partyBranchfillRecordList.value = res?.partyBranchfillRecordList
    }
  })
  detailVisible.value = true
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          getDetail(row.id)
        },
        type: 'primary',
        text: true,
      },
      {
        default: () => '查看',
      },
    ),
  ]
})

function judgeShowLine(arr: any, index: number) {
  if (!Array.isArray(arr)) {
    return false
  }
  const length = arr.length
  if (length === 0 || index === length - 1) {
    return false
  }
  else {
    return true
  }
}

onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="民主评议党员"
    :loading="loading"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :show-delete="false"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
  >
    <template #btns>
      <div class="flex flex-row justify-start items-center gap-x-[10px]">
        <n-input
          v-model:value="filterReactive.orgName"
          clearable
          size="small"
          placeholder="请输入组织名称或发布者"
          style="width: 180px"
        />
        <n-date-picker
          v-model:formatted-value="timeRangeFormattedValue"
          style="width: 240px"
          type="monthrange"
          format="yyyy-MM"
          clearable
          @update:formatted-value="handleUpdateMonthRange"
        />
      </div>
    </template>
    <template #filters>
      <n-button type="primary">
        同步查询结果
      </n-button>
    </template>
  </table-container>

  <!-- 查看详情弹框 -->
  <n-modal
    v-model:show="detailVisible"
    preset="dialog"
    title="Dialog"
    :show-icon="false"
    :mask-closable="false"
    style="width: 1200px"
  >
    <template #header>
      <div>&nbsp;</div>
    </template>
    <div class="h-[600px] overflow-y-auto">
      <div
        class="flex flex-row justify-center items-center mt-[20px] mb-[20px] text-[20px]"
      >
        <span>民主评议党员</span>
      </div>
      <div class="border-top border-left border-right">
        <!-- 基础信息 -->
        <div
          v-for="(item, index) in detailRow"
          :key="index"
          class="flex flex-row justify-start items-center border-bottom h-[auto] min-h-[40px]"
        >
          <div
            class="w-[120px] h-[auto] min-h-[40px] flex flex-row justify-center items-center font-bold"
          >
            <span>{{ item.label }}</span>
          </div>
          <div
            class="flex flex-1 h-[auto] min-h-[40px] flex-row justify-start items-center box-border border-left px-[20px]"
          >
            <span>{{ item.value }}</span>
          </div>
        </div>
        <!-- 党员自评 -->
        <div>
          <div
            class="w-[100%]h-[auto] min-h-[40px] flex flex-row justify-center items-center font-bold border-bottom"
          >
            <span>党员自评</span>
          </div>
          <div
            class="w-[100%] h-[auto] min-h-[40px] flex flex-row justify-center items-center border-bottom"
          >
            <span>{{ detailData?.self?.selfLevel }} </span>
          </div>
        </div>
        <!-- 党员互评 -->
        <div>
          <div
            class="w-[100%]h-[auto] min-h-[40px] flex flex-row justify-center items-center font-bold border-bottom"
          >
            <span>党员互评</span>
          </div>
          <div>
            <div
              v-for="(item, key) in projectList"
              :key="key"
              class="project-item"
            >
              <div class="project-item-title custom-table-head">
                <div
                  class="min-h-[40px] w-[33%] flex items-center justify-center border-bottom"
                >
                  <span>评定项目</span>
                </div>
                <div
                  class="flex-1 h-[auto] min-h-[40px] w-[33%] flex items-center justify-center border-bottom"
                >
                  <span>评定意见</span>
                </div>
                <div
                  class="flex-1 h-[auto] min-h-[40px] w-[33%] flex items-center justify-center border-bottom border-right-f5"
                >
                  <span>人数</span>
                </div>
              </div>
              <div class="project-item-title project-item-content">
                <div class="flex-1 w-[33%] flex items-center justify-center">
                  <span>{{ item?.title }} </span>
                </div>
                <div
                  class="flex-1 min-h-[40px] w-[33%] flex-col flex items-center justify-center border-bottom custom-column border-right-f5"
                >
                  <div
                    v-for="(option, index) in item.lables"
                    :key="option.key"
                    class="custom-row"
                  >
                    {{ option.key }}
                    <div
                      :class="{
                        'gray-line': judgeShowLine(item.lables, index),
                      }"
                    ></div>
                  </div>
                </div>
                <div
                  class="flex-1 min-h-[40px] w-[33%] flex-col flex items-center justify-center border-bottom border-right-f5"
                >
                  <div
                    v-for="(option, index) in item.lables"
                    :key="option.key"
                    class="custom-row"
                  >
                    {{ option.value }}
                    <div
                      :class="{
                        'gray-line': judgeShowLine(item.lables, index),
                      }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div
                class="flex flex-row justify-start items-center border-bottom"
              >
                <div
                  class="w-[120px] h-[auto] min-h-[40px] flex flex-row justify-center items-center font-bold"
                >
                  <span>对我的建议</span>
                </div>
                <div
                  class="suggest-container flex-1 h-[auto] min-h-[40px] flex flex-row justify-center items-center font-bold border-bottom border-left"
                >
                  {{ mySuggestion }}
                </div>
              </div>
            </div>
            <div>
              <div
                class="flex flex-row justify-start items-center border-bottom"
              >
                <div
                  class="w-[120px] h-[auto] min-h-[40px] flex flex-row justify-center items-center font-bold"
                >
                  <span>党小组评审意见</span>
                </div>
                <div
                  class="suggest-container flex-1 h-[auto] min-h-[40px] flex flex-row border-left justify-center items-center font-bold border-bottom"
                >
                  <div
                    v-for="item in partyGroupfillRecordList"
                    :key="item"
                    calss="px-[10px]"
                  >
                    <div class="mb-[15px]">
                      {{ item }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div
                class="flex flex-row justify-start items-center border-bottom"
              >
                <div
                  class="w-[120px] h-[auto] min-h-[40px] flex flex-row justify-center items-center font-bold"
                >
                  <span>党支部评审意见</span>
                </div>
                <div
                  class="suggest-container flex-1h-[auto] min-h-[40px] flex flex-row justify-center items-center border-left font-bold border-bottom"
                >
                  <div
                    v-for="item in partyBranchfillRecordList"
                    :key="item"
                    calss="px-[10px]"
                  >
                    <div class="mb-[15px]">
                      {{ item }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </n-modal>
</template>
<style lang="scss" scoped>
.border-bottom {
  border-bottom: 1px solid #f1f1f1;
}

.border-top {
  border-top: 1px solid #f1f1f1;
}

.border-left {
  border-left: 1px solid #f1f1f1;
}

.border-right {
  border-right: 1px solid #f1f1f1;
}

.border-bottom-f5 {
  border-bottom: 1px solid #f1f1f1;
}

.border-right-f5 {
  border-right: 1px solid #f1f1f1;
}

.project-item {
  width: 100%;
  font-weight: bold;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.project-item-title {
  width: 100%;
  display: flex;
  align-items: center;
}

.custom-table-head {
  background: rgb(249, 250, 251);
}

.custom-column {
  border-left: 1px solid #f1f1f1;
}

.gray-line {
  width: 100%;
  height: 0;
  border-bottom: 1px solid #f1f1f1;
}

.custom-row {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.project-item-content {
  height: auto;
  border-bottom: 1px solid #f1f1f1;
}

.suggest-container {
  flex-direction: column;
  overflow-y: auto;
  padding: 10px;
}
</style>
