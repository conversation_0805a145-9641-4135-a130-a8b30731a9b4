import type { FormRules } from 'naive-ui'
export const newsDetailFormRules: FormRules = {
  title: {
    required: true,
    message: '请输入书记项目标题',
    trigger: 'input',
    type: 'string',
  },
  coverUrl: {
    required: true,
    message: '请上传图片',
    trigger: 'change',
    type: 'string',
  },
  isTop: {
    required: true,
    message: '请选择是否置顶',
    trigger: 'change',
    type: 'number',
  },
  isHidden: {
    required: true,
    message: '请选择是否隐藏',
    trigger: 'change',
    type: 'number',
  },
  summary: {
    required: true,
    message: '请输入正文',
    trigger: 'input',
    type: 'string',
  },
}
