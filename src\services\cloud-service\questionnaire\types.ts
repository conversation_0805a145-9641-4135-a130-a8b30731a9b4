export interface Questionnaire {
  id?: number | string
  title?: string
  description?: string
  beginTime?: string | null
  endTime?: string | null
  questionnaireText?: string
  coverUrl?: string
  questionnaireStatus?: string
  createTime?: string | null
  orgidList?: String[]
  participantsNum?: number
  subjectList?: QuestionnaireSubj[]
  deptId?: string
}

export interface QuestionnaireSubj {
  id?: string
  questionnaireId?: string
  renderId?: string
  /** 题目 */
  subjectName: string
  // 类型 0 - 单选，1 - 多选， 2 - 填空
  subjectType: string
  /** 选项列表 */
  optionList?: OptionItem[]
  // 填空题回答
  subjectItemContent?: string
  /** 是否必填， 1 - 是， 0 - 否 */
  isRequired: string
  maxWordCount?: number
  /** 顺序 */
  sort: number
  editing?: boolean
}

/** 评议项题目选项 */
export interface OptionItem {
  id?: string
  subjectId?: string
  renderId?: string
  /** 选项顺序 */
  sort: number
  /** 选项内容 */
  optionContent: string
  /** 是否填写理由 1 - 是，0 - 否 */
  isReason: string
  // 是否选择 1-是 0-否
  checked?: string
}

// 整个评议项
export interface Template {
  [index: string]: any
  templateItemList: TemplateItem[]
}

// 整个评议项
export interface TemplateItem {
  [index: string]: any
  id?: string
  isReason: string
  templateId?: string
  sort?: number
  templateItemName: string
  templateItemScore: number
}
