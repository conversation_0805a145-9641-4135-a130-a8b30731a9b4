<script setup lang="ts">
import { NDataTable } from 'naive-ui'
import type { DataTableColumns } from 'naive-ui'
import { reactive } from 'vue'
import ContentBox from '../cpns/ContentBox.vue'
import type { ChoiceItem, Questionnaire } from '../reviewItems/type'
import type {
  EachDetail,
  EachType,
  PartyBranchType,
  PartyGroupType,
  SelfType,
  UserInfo,
} from './type'
import {
  getPartDetailEachDetail,
  getTalkAboutPartyMemberDetail,
} from '@/services/publicity/vote/talkAbout'
import { emitter } from '@/utils/event-bus'
import { BOOLEANTYPE, QUESTIONTYPE } from '@/constant'
import { useVoteStore } from '@/store/vote/vote'

const voteStore = useVoteStore()

const backQuery = {
  reviewId: voteStore.getQueryData.reviewId,
  year: voteStore.getQueryData.year,
  partReviewItemId: voteStore.getQueryData.partReviewItemId,
  branchReviewItemId: voteStore.getQueryData.branchReviewItemId,
}

const showModal = ref(false)
const modalLoading = ref(false)
const modalTitle = ref<string>()
const selectedQuesDetail = reactive<Questionnaire>({
  reviewSubjectArr: [],
})

// 锚点相关
const anchorList = reactive([
  {
    title: '基本信息',
    anchor: 'basic',
  },
  {
    title: '党员自评',
    anchor: 'materials',
  },
  {
    title: '党员互评',
    anchor: 'notes',
  },
  {
    title: '党员小组评价',
    anchor: 'group',
  },
  {
    title: '党员支部评价',
    anchor: 'branch',
  },
])
const displayMap = reactive<{
  mutualEvaluationDisplay: string
  mutualEvaluationIsScoreItem: string
  mutualEvaluationIsTemplate: string
  selfEvaluationDisplay: string
  selfEvaluationIsScoreItem: string
  partyGroupEvaluationDisplay: string
  partyBranchEvaluationDisplay: string
}>({
  mutualEvaluationDisplay: BOOLEANTYPE.NO, // 互评展示
  mutualEvaluationIsScoreItem: BOOLEANTYPE.NO, // 互评总分项展示
  mutualEvaluationIsTemplate: BOOLEANTYPE.NO, // 互评模板展示
  selfEvaluationDisplay: BOOLEANTYPE.NO, // 自评展示
  selfEvaluationIsScoreItem: BOOLEANTYPE.NO, // 自评总分项展示
  partyGroupEvaluationDisplay: BOOLEANTYPE.NO, // 党小组评价展示
  partyBranchEvaluationDisplay: BOOLEANTYPE.NO, // 党支部评价展示
})

const selfEvaluatedTableColumns: DataTableColumns<Object> = reactive([])
const eachEvaluatedTableColumns: DataTableColumns<Object> = reactive([])
const evaluationProjectColumns: DataTableColumns<Object> = reactive([])
const eachDetailColumns: DataTableColumns<EachDetail> = [
  { key: 'index', title: '序号', render: (_, i) => i + 1 },
  { key: 'revieweeUserName', title: '被评人' },
  { key: 'commentTime', title: '评价时间' },
  { key: 'reviewDistributeScore', title: '得分' },
  {
    key: 'distributeId',
    title: '互评详情',
    render: (row) => {
      return h(
        'div',
        {
          class: 'text-blue-500 cursor-pointer',
          onClick: () => {
            openModal(row.distributeId, '互评详情')
          },
        },
        '互评详情',
      )
    },
  },
]
// 党员基本信息
const userInfo = reactive<{ data: UserInfo }>({
  data: {
    avatarId: undefined,
    avatarUrl: undefined,
    trueName: '',
    sex: '',
    birthday: undefined,
    ethnic: '',
    edu: '',
    phone: '',
    identityId: '',
    deptName: '',
    joinTime: undefined,
    regularTime: undefined,
  },
})
// 总体评价统计-自评
const totalSelfEvaluatedTable = reactive<{ data: Array<Object> }>({ data: [] })
// 总体评价统计-互评
const totalEachEvaluatedTable = reactive<{ data: Array<Object> }>({ data: [] })
// 评定项目统计
const evaluationProject = reactive<{ data: Array<Object> }>({ data: [] })

const route = useRoute()
const userName = computed(() => `${userInfo.data.trueName}的评价详情` || '')

// 自评
const selfInfo = reactive<{ data: SelfType }>({
  data: {
    selfReviewTime: '',
    distributeId: '',
    totalStatistic: {},
  },
})
// 党员互评
const eachInfo = reactive<{ data: EachType }>({
  data: {
    itemColumn: [],
    itemData: [],
    reviewDetailList: [],
    totalStatistic: {},
    isOpenTemplate: false,
    hasScoreRule: false,
    ruleScoreName: '',
  },
})
// 党小组评价
const groupInfo = reactive<{ data: PartyGroupType }>({
  data: {
    partyGroupReviewTime: '',
    distributeId: '',
  },
})
// 党支部评价
const branchInfo = reactive<{ data: PartyBranchType }>({
  data: {
    partyBranchReviewTime: '',
    distributeId: '',
  },
})
const loadData = () => {
  const data = {
    reviewItemId: route.query.reviewItemId as string,
    commentatorId: route.query.commentatorId as string,
    orgId: route.query.orgId as string,
    reviewId: route.query.reviewId as string,
  }
  getTalkAboutPartyMemberDetail(data).then((res) => {
    userInfo.data = res.user || userInfo.data // 党员基本信息
    selfInfo.data = res.self || selfInfo.data // 党员自评
    eachInfo.data = res.each || eachInfo.data // 党员互评
    groupInfo.data = res.partyGroup || groupInfo.data
    branchInfo.data = res.partyBranch || branchInfo.data
    displayMap.selfEvaluationDisplay = res.selfEvaluationDisplay // 自评展示
    displayMap.selfEvaluationIsScoreItem = res.selfEvaluationIsScoreItem // 自评总分项展示
    displayMap.mutualEvaluationIsScoreItem = res.mutualEvaluationIsScoreItem // 互评展示
    displayMap.mutualEvaluationDisplay = res.mutualEvaluationDisplay // 互评总分项展示
    displayMap.mutualEvaluationIsTemplate = res.mutualEvaluationIsTemplate // 互评模板开启
    displayMap.partyGroupEvaluationDisplay = res.partyGroupEvaluationDisplay // 党小组展示
    displayMap.partyBranchEvaluationDisplay = res.partyBranchEvaluationDisplay // 党支部展示

    anchorList.splice(0, anchorList.length)

    anchorList.push({
      title: '基本信息',
      anchor: 'basic',
    })
    if (displayMap.selfEvaluationDisplay === BOOLEANTYPE.YES) {
      anchorList.push({
        title: '党员自评',
        anchor: 'materials',
      })
    }
    if (displayMap.mutualEvaluationDisplay === BOOLEANTYPE.YES) {
      anchorList.push({
        title: '党员互评',
        anchor: 'notes',
      })
    }
    if (displayMap.partyGroupEvaluationDisplay === BOOLEANTYPE.YES) {
      anchorList.push({
        title: '党员小组评价',
        anchor: 'group',
      })
    }
    if (displayMap.partyBranchEvaluationDisplay === BOOLEANTYPE.YES) {
      anchorList.push({
        title: '党员支部评价',
        anchor: 'branch',
      })
    }
    // 自评总体评价统计
    if (
      selfInfo.data.totalStatistic
      && Object.keys(selfInfo.data.totalStatistic).length > 0
    ) {
      const tmp = {} as Record<string, any>
      for (const key in selfInfo.data.totalStatistic) {
        selfEvaluatedTableColumns.push({ key, title: key })

        tmp[key]
          = selfInfo.data.totalStatistic[
            key as keyof typeof selfInfo.data.totalStatistic
          ]
      }
      totalSelfEvaluatedTable.data.push(tmp)
    }
    // 互评总体评价统计
    if (
      eachInfo.data.totalStatistic
      && Object.keys(eachInfo.data.totalStatistic).length > 0
    ) {
      const tmp = {} as Record<string, any>
      for (const key in eachInfo.data.totalStatistic) {
        eachEvaluatedTableColumns.push({ key, title: key })

        tmp[key]
          = eachInfo.data.totalStatistic[
            key as keyof typeof eachInfo.data.totalStatistic
          ]
      }
      totalEachEvaluatedTable.data.push(tmp)
    }
    // 评定项目统计表头
    if (eachInfo.data.itemColumn) {
      evaluationProjectColumns.push(
        ...eachInfo.data.itemColumn.map((item) => {
          return {
            key: item,
            title: item,
          }
        }),
      )
    }

    // 评定项目统计数据
    if (eachInfo.data.itemData) {
      evaluationProject.data = eachInfo.data.itemData
    }
  })
}
// onMounted(() => {
//   loadData()
// })

const basicRef = ref<HTMLElement | null>(null)
const materialsRef = ref<HTMLElement | null>(null)
const notesRef = ref<HTMLElement | null>(null)
const groupRef = ref<HTMLElement | null>(null)
const branchRef = ref<HTMLElement | null>(null)
const summaryRef = ref<HTMLElement | null>(null)

const activeAnchor = ref<string | null>('basic')

const scrollTo = (refName: string) => {
  activeAnchor.value = refName
  const targetRef
    = refName === 'basic'
      ? basicRef
      : refName === 'materials'
        ? materialsRef
        : refName === 'notes'
          ? notesRef
          : refName === 'group'
            ? groupRef
            : refName === 'branch'
              ? branchRef
              : summaryRef

  if (targetRef.value) {
    targetRef.value.style.scrollMarginTop = '100px'
    targetRef.value.scrollIntoView({ block: 'start', behavior: 'smooth' })
  }
}
const isActive = (anchor: string) => {
  return activeAnchor.value === anchor
}
const emitScrollTop = ref()

emitter.on('my-scroller', (v: any) => (emitScrollTop.value = v))
watch(
  () => emitScrollTop.value,
  (newV) => {
    anchorList.forEach((item) => {
      const element = document.getElementById(item.anchor)
      if (element) {
        const offsetTop = element.offsetTop - 60
        const offsetBottom = offsetTop + element.offsetHeight
        if (newV >= offsetTop && newV < offsetBottom) {
          activeAnchor.value = item.anchor // 更新临时变量
        }
      }
    })
  },
)

watchEffect(() => {
  // 遍历锚点位置，找到当前视口位置应激活的锚点
})

onMounted(() => {
  loadData()
})
onBeforeUnmount(() => {
  emitter.off('my-scroller')
})

function formatChoiceTitle(i: number, str: string) {
  let trimmed = str.trim()
  trimmed = trimmed ? `${trimmed}` : '请输入选项内容'
  return `${String.fromCharCode(65 + i)}. ${trimmed}`
}

function openModal(distributeId: string | undefined, title: string) {
  if (distributeId) {
    modalLoading.value = true

    modalTitle.value = title
    getPartDetailEachDetail({ distributeId }).then((res) => {
      modalLoading.value = false
      Object.assign(selectedQuesDetail, res)
      showModal.value = true
      if (!selectedQuesDetail?.reviewSubjectArr?.length) {
        showModal.value = false
        window.$message.error('暂无数据!')
      }
    })
  }
  else {
    window.$message.error('暂无数据!')
  }
}

// 为单选按钮获取数组中选中的第一个值
function getRadioCheckedValue(arr: ChoiceItem[]) {
  return arr.findIndex(item => item.checked === BOOLEANTYPE.YES)
}
</script>
<template>
  <div>
    <DetailHeader
      :header-title="userName"
      :is-show-confirm-btn="false"
      :back-name="'talk-about-party-member'"
      :back-query="backQuery"
    />

    <div class="flex pb-[500px] pl-[50px] relative">
      <div class="fixed">
        <div class="flex flex-col pt-[100px] w-[100px]">
          <div class="flex items-start gap-[76px] justify-between">
            <div></div>
            <div class="flex flex-col items-center cursor-pointer">
              <div
                class="w-[12px] h-[12px] rounded-[50%] border-2 border-[#d3d8df]"
              ></div>
              <div class="w-[2px] h-[25px] bg-[#f2f3f4]"></div>
            </div>
          </div>
          <div
            v-for="(item, index) in anchorList"
            :key="index"
            class="flex items-start justify-between"
            @click="scrollTo(item.anchor)"
          >
            <div class="mt-[-4px] cursor-pointer">
              <span
                class="text-[#b1b1b1]"
                :class="{ 'text-[#AC241D]': isActive(item.anchor) }"
              >
                {{ item.title }}
              </span>
            </div>
            <div class="flex flex-col items-center cursor-pointer">
              <div
                class="w-[12px] h-[12px] rounded-[50%] bg-[#d3d8df]"
                :class="{ 'bg-[#AC241C]': isActive(item.anchor) }"
              ></div>
              <div
                v-if="index < anchorList.length - 1"
                class="w-[2px] h-[44px] bg-[#f2f3f4]"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex-1 pl-[100px]">
        <div id="basic" ref="basicRef">
          <!-- 基本信息 -->
          <div class="pl-[100px] pr-[100px] box-border">
            <div class="flex flex-row mt-[50px] justify-start items-center">
              <span class="text-[14px] font-bold leading-[14px]">基本信息</span>
              <span class="line"></span>
            </div>
            <div class="mt-[50px] flex flex-row justify-start items-start">
              <div class="meetingInfo">
                <div>
                  <div>
                    <span class="text-[#b1b1b1]">
                      姓&emsp;&emsp;名：
                      <span class="ml-[20px] text-[#000000]">{{ userInfo.data.trueName || '--' }}
                      </span>
                    </span>
                  </div>
                  <div>
                    <span class="text-[#b1b1b1]">
                      性&emsp;&emsp;别：
                      <span class="ml-[20px] text-[#000000]">{{
                        userInfo.data.sex || '--'
                      }}</span>
                    </span>
                  </div>
                </div>
                <div>
                  <div>
                    <span class="text-[#b1b1b1]">出生日期：
                      <span class="ml-[20px] text-[#000000]">
                        {{ userInfo.data.birthday || '--' }}
                      </span>
                    </span>
                  </div>
                  <div>
                    <span class="text-[#b1b1b1]">民&emsp;&emsp;族：
                      <span class="ml-[20px] text-[#000000]">
                        {{ userInfo.data.ethnic || '--' }}
                      </span>
                    </span>
                  </div>
                </div>
                <div>
                  <div>
                    <span class="text-[#b1b1b1]">学&emsp;&emsp;历：
                      <span class="ml-[20px] text-[#000000]">
                        {{ userInfo.data.edu || '--' }}
                      </span>
                    </span>
                  </div>
                  <div>
                    <span class="text-[#b1b1b1]">手机号码：
                      <span class="ml-[20px] text-[#000000]">
                        {{ userInfo.data.phone || '--' }}
                      </span>
                    </span>
                  </div>
                </div>
                <div>
                  <div>
                    <span class="text-[#b1b1b1]">身份证号：
                      <span class="ml-[20px] text-[#000000]">
                        {{ userInfo.data.identityId || '--' }}
                      </span>
                    </span>
                  </div>
                  <div>
                    <span class="text-[#b1b1b1]">组&emsp;&emsp;织：
                      <span class="ml-[20px] text-[#000000]">{{
                        userInfo.data.deptName || '--'
                      }}</span>
                    </span>
                  </div>
                </div>
                <div>
                  <div>
                    <span class="text-[#b1b1b1]">入党时间：
                      <span class="ml-[20px] text-[#000000]">
                        {{ userInfo.data.joinTime || '--' }}
                      </span>
                    </span>
                  </div>
                  <div>
                    <span class="text-[#b1b1b1]">转正时间：
                      <span class="ml-[20px] text-[#000000]">
                        {{ userInfo.data.regularTime || '--' }}
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-show="displayMap.selfEvaluationDisplay === BOOLEANTYPE.YES"
          id="materials"
          ref="materialsRef"
        >
          <!-- 党员自评 -->
          <ContentBox title="党员自评">
            <template #default>
              <div
                class="w-[70%] pt-[30px] flex flex-col justify-center items-start gap-[20px]"
              >
                <div class="summarize">
                  <div class="title">
                    自评时间:
                  </div>
                  <span class="meeting-content break-words break-all">{{
                    selfInfo.data.selfReviewTime || '--'
                  }}</span>
                </div>
                <div class="summarize">
                  <div class="title">
                    自评详情:
                  </div>
                  <div
                    class="text-blue-500 cursor-pointer"
                    @click="openModal(selfInfo.data.distributeId, '自评详情')"
                  >
                    自评详情
                  </div>
                </div>
                <div
                  v-show="
                    displayMap.selfEvaluationIsScoreItem === BOOLEANTYPE.YES
                  "
                  class="w-[70%] pt-[30px]"
                >
                  <div class="pb-[10px]">
                    <span>总体评价统计：</span>
                  </div>
                  <NDataTable
                    :columns="selfEvaluatedTableColumns"
                    :data="totalSelfEvaluatedTable.data"
                  />
                </div>
              </div>
            </template>
          </ContentBox>
        </div>
        <div
          v-show="displayMap.mutualEvaluationDisplay === BOOLEANTYPE.YES"
          id="notes"
          ref="notesRef"
        >
          <!-- 党员互评 -->
          <ContentBox title="党员互评">
            <template #default>
              <div
                v-show="
                  displayMap.mutualEvaluationIsScoreItem === BOOLEANTYPE.YES
                "
                class="w-[70%] pt-[30px]"
              >
                <div class="pb-[10px]">
                  <span>总体评价统计：</span>
                </div>
                <NDataTable
                  :columns="eachEvaluatedTableColumns"
                  :data="totalEachEvaluatedTable.data"
                />
              </div>
              <div
                v-show="
                  displayMap.mutualEvaluationIsTemplate === BOOLEANTYPE.YES
                "
                class="w-[70%] pt-[30px]"
              >
                <div class="pb-[10px]">
                  <span>评定项目统计：</span>
                </div>
                <NDataTable
                  :columns="evaluationProjectColumns"
                  :data="evaluationProject.data"
                />
              </div>
              <div class="w-[70%] pt-[30px]">
                <div class="pb-[10px]">
                  <span>评价详情：</span>
                </div>
                <NDataTable
                  :columns="eachDetailColumns"
                  :data="eachInfo.data.reviewDetailList"
                />
              </div>
            </template>
          </ContentBox>
        </div>
        <div
          v-show="displayMap.partyGroupEvaluationDisplay === BOOLEANTYPE.YES"
          id="group"
          ref="groupRef"
        >
          <!-- 党员小组评价 -->
          <ContentBox title="党员小组评价">
            <template #default>
              <div
                class="w-[70%] pt-[30px] flex flex-col justify-center items-start gap-[20px]"
              >
                <div class="summarize">
                  <div class="title">
                    小组评价时间:
                  </div>
                  <span class="meeting-content break-words break-all">{{
                    groupInfo.data.partyGroupReviewTime || '--'
                  }}</span>
                </div>
                <div class="summarize">
                  <div class="title">
                    小组评价详情:
                  </div>
                  <div
                    class="text-blue-500 cursor-pointer"
                    @click="openModal(groupInfo.data.distributeId, '自评详情')"
                  >
                    小组评价详情
                  </div>
                </div>
              </div>
            </template>
          </ContentBox>
        </div>
        <div
          v-show="displayMap.partyBranchEvaluationDisplay === BOOLEANTYPE.YES"
          id="branch"
          ref="branchRef"
        >
          <!-- 党员支部评价 -->
          <ContentBox title="党员支部评价">
            <template #default>
              <div
                class="w-[70%] pt-[30px] flex flex-col justify-center items-start gap-[20px]"
              >
                <div class="summarize">
                  <div class="title">
                    支部评价时间:
                  </div>
                  <span class="meeting-content break-words break-all">{{
                    branchInfo.data.partyBranchReviewTime || '--'
                  }}</span>
                </div>
                <div class="summarize">
                  <div class="title">
                    支部评价详情:
                  </div>
                  <div
                    class="text-blue-500 cursor-pointer"
                    @click="
                      openModal(branchInfo.data.distributeId, '支部评价详情')
                    "
                  >
                    支部评价详情
                  </div>
                </div>
              </div>
            </template>
          </ContentBox>
        </div>
        <n-modal
          v-model:show="showModal"
          preset="dialog"
          title="Dialog"
          :loading="modalLoading"
        >
          <template #header>
            <div>{{ modalTitle }}</div>
          </template>
          <div class="w-full">
            <div
              v-for="(data, index1) in selectedQuesDetail.reviewSubjectArr"
              :key="index1"
              class="card"
            >
              <div class="text-13px text-[#333] font-500 leading-26px mb-8px">
                <span class="questionTitle">
                  {{ index1 + 1 }}、{{
                    data.subjectName.trim() || '请输入题目信息'
                  }}
                </span>
              </div>
              <div v-if="data.subjectType === QUESTIONTYPE.FILLED">
                <NInput
                  v-model:value="data.subjectItemContent"
                  placeholder="请输入意见内容"
                  :resizable="false"
                  type="textarea"
                />
              </div>
              <div
                v-else-if="
                  data.subjectType === QUESTIONTYPE.MULTI &&
                    data.reviewSubjectItemArr?.length
                "
              >
                <NCheckboxGroup :value="[]" size="small">
                  <div
                    v-for="(item, index) in data.reviewSubjectItemArr"
                    :key="item.subjectId ?? item.renderId"
                  >
                    <NCheckbox
                      v-model:checked="item.checked"
                      class="leading-32px"
                      :label="formatChoiceTitle(index, item.subjectItemName)"
                      checked-value="1"
                      unchecked-value="0"
                    />
                  </div>
                </NCheckboxGroup>
              </div>
              <div
                v-else-if="
                  data.subjectType === QUESTIONTYPE.RADIO &&
                    data.reviewSubjectItemArr?.length
                "
              >
                <NRadioGroup
                  size="small"
                  :value="getRadioCheckedValue(data.reviewSubjectItemArr)"
                >
                  <div
                    v-for="(item, index) in data.reviewSubjectItemArr"
                    :key="item.subjectId ?? item.renderId"
                  >
                    <NRadio
                      :label="formatChoiceTitle(index, item.subjectItemName)"
                      :checked="item.checked === BOOLEANTYPE.YES"
                      :value="index"
                    />
                  </div>
                </NRadioGroup>
              </div>
            </div>
          </div>
        </n-modal>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.line {
  margin-left: 20px;
  width: 80%;
  display: inline-block;
  border-top: 0.5px solid #ccc;
  font-weight: 100;
}
.meetingInfo {
  min-width: 1000px;
  display: flex;
  flex-direction: column;
  gap: 30px;
  div {
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
}

.summarize {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  .title {
    width: 100px;
    color: #b1b1b1;
  }
  .meeting-content {
    flex: 1;
    color: #000000;
  }
}
.attachment {
  padding-top: 30px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .title {
    width: 100px;
    color: #666666;
  }
}

.time-line-item-active {
  :deep(.n-timeline-item-content__title) {
    font-size: 12px;
  }
}
</style>
