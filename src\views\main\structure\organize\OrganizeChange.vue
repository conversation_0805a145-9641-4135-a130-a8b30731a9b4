<!-- 组织变动 -->
<template>
  <table-container
    :loading="loading"
    :page="1"
    :page-size="10"
    :show-delete="false"
    :show-pagination="false"
    :show-toolbar="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    custom-toolbar
    default-expand-all
    title="组织管理"
    @click-add="handleClickAddRootNode"
  >
    <template #btns>
      <n-button
        :disabled="isShowAddRotBtn"
        size="small"
        type="primary"
        @click="handleClickAddRootNode"
      >
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加根节点
      </n-button>
    </template>
    <template #filters>
      <n-input
        v-model:value="filterReactive.orgName"
        clearable
        placeholder="请输入组织名称"
        size="small"
      />
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :mask-closable="false" :width="700">
    <n-drawer-content :title="drawerTitle" closable>
      <notice-form
        :id="idEditRef"
        ref="addNoticeFormRef"
        :level="idEditLevel"
        :parent-id="idEditParentID"
        :parent-name="idEditParentName"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            style="width: 80px"
            type="primary"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>

  <!-- 升降级弹框 -->
  <n-modal
    :show="relegationVisible"
    :show-icon="false"
    class="relegation-dialog"
    preset="dialog"
    style="width: 600px"
    @close="handleRelegationClose"
  >
    <template #header>
      <slot name="header">
        <div class="dialog-header">
          组织变动管理-升/降级
        </div>
      </slot>
    </template>
    <div class="relegation-container">
      <div>组织名称：{{ currentRow?.name }}</div>
      <div class="current-level-container">
        组织评定等级：
        <span>当前等级</span>
        <n-select
          v-model:value="currentLevel"
          :options="currentLevelOptions"
          class="relegation-select"
        />
        <span>调整后等级</span>
        <n-select
          v-model:value="currentLevel"
          :options="currentLevelOptions"
          class="relegation-select"
        />
      </div>
    </div>
    <template #action>
      <slot name="action">
        <div class="dialog-action">
          <n-button size="small" type="primary" @click="hanldleRelegationSure">
            确定
          </n-button>
          <n-button
            class="relegation-cancel-btn"
            size="small"
            @click="handleRelegationClose"
          >
            取消
          </n-button>
        </div>
      </slot>
    </template>
  </n-modal>

  <!-- 合并弹框 -->
  <n-modal
    :show="mergeVisible"
    :show-icon="false"
    class="relegation-dialog"
    preset="dialog"
    style="width: 600px"
    @close="handleMergeClose"
  >
    <template #header>
      <slot name="header">
        <div class="dialog-header">
          组织变动管理-合并
        </div>
      </slot>
    </template>
    <div class="relegation-container">
      <n-form
        ref="formRef"
        :model="mergeData"
        :rules="mergeRules"
        label-placement="left"
      >
        <n-form-item label="合并前主组织名称" path="currentLevel">
          <n-select
            v-model:value="mergeData.currentLevel"
            :options="currentLevelOptions"
            class="merge-select"
          />
        </n-form-item>
        <n-form-item label="被合并组织名称" path="currentLevel2">
          <n-select
            v-model:value="mergeData.currentLevel2"
            :options="currentLevelOptions"
          />
        </n-form-item>
        <n-form-item label="合并后组织名称" path="inputValue">
          <n-input v-model:value="mergeData.name2" />
        </n-form-item>
      </n-form>
    </div>
    <template #action>
      <slot name="action">
        <div class="dialog-action">
          <n-button size="small" type="primary" @click="hanldleMergeSure">
            确定
          </n-button>
          <n-button
            class="relegation-cancel-btn"
            size="small"
            @click="handleMergeClose"
          >
            取消
          </n-button>
        </div>
      </slot>
    </template>
  </n-modal>

  <!-- 拆分弹框 -->
  <n-modal
    :show="splitVisible"
    :show-icon="false"
    class="relegation-dialog"
    preset="dialog"
    style="width: 600px"
    @close="handleSpliteClose"
  >
    <template #header>
      <slot name="header">
        <div class="dialog-header">
          组织变动管理-拆分
        </div>
      </slot>
    </template>
    <div class="relegation-container">
      <n-form
        ref="formRef"
        :model="splitData"
        :rules="splitRules"
        label-placement="left"
      >
        <n-form-item label="拆分前主组织名称" path="currentLevel">
          <n-select
            v-model:value="splitData.currentLevel"
            :options="currentLevelOptions"
            class="merge-select"
          />
        </n-form-item>
        <n-form-item label="拆分后组织1名称" path="currentLevel2">
          <n-input v-model:value="splitData.name2" />
        </n-form-item>
        <n-form-item label="拆分后组织2名称" path="inputValue">
          <n-input v-model:value="splitData.name2" />
        </n-form-item>
      </n-form>
    </div>
    <template #action>
      <slot name="action">
        <div class="dialog-action">
          <n-button size="small" type="primary" @click="hanldleSplitSure">
            确定
          </n-button>
          <n-button
            class="relegation-cancel-btn"
            size="small"
            @click="handleMergeClose"
          >
            取消
          </n-button>
        </div>
      </slot>
    </template>
  </n-modal>

  <!-- 删除弹框 -->
  <n-modal
    :show="deleteVisible"
    :show-icon="false"
    class="relegation-dialog"
    preset="dialog"
    style="width: 600px"
    @close="handleDeleteClose"
  >
    <template #header>
      <slot name="header">
        <div class="dialog-header">
          组织变动管理-删除
        </div>
      </slot>
    </template>
    <div class="relegation-container">
      <div>组织名称：{{ currentRow?.name }}</div>
    </div>
    <template #action>
      <slot name="action">
        <div class="dialog-action">
          <n-button size="small" type="primary" @click="hanldleDeleteSure">
            确定删除
          </n-button>
          <n-button
            class="relegation-cancel-btn"
            size="small"
            @click="handleDeleteClose"
          >
            取消
          </n-button>
        </div>
      </slot>
    </template>
  </n-modal>
</template>
<script lang="ts" setup>
import { NButton } from 'naive-ui'
import { PlusRound } from '@vicons/material'
import { getTableColumns } from './config'
import NoticeForm from './cpn/NoticeForm.vue'
import { useDrawerEditOrganize } from '@/hooks'
import {
  getOrganizationTableList,
  getVagueOrganizationTableList,
} from '@/services/organization'

const filterReactive = ref<{ orgName: string }>({
  orgName: '',
})

const loading = ref(false)
const tableData = ref([])
const isShowAddRotBtn = ref(false)
/** 获取组织列表 */
const getTableList = () => {
  loading.value = true
  getOrganizationTableList()
    .then((res: any) => {
      tableData.value = res || []
      if (Array.isArray(res) && res.length >= 1) {
        isShowAddRotBtn.value = true
      }
      else {
        isShowAddRotBtn.value = false
      }
    })
    .finally(() => {
      loading.value = false
    })
}
/** 获取过滤组织列表 */
const filterTableList = () => {
  getVagueOrganizationTableList(filterReactive.value).then((res: any) => {
    tableData.value = res || []
  })
}
const filterVal = computed(() => {
  if (filterReactive.value.orgName && filterReactive.value.orgName !== '') {
    return filterReactive.value.orgName
  }
  else {
    return null
  }
})

watch(
  () => filterVal.value,
  (newVal) => {
    if (newVal && newVal !== '') {
      filterTableList()
    }
    else {
      getTableList()
    }
  },
)

interface rowInstance {
  name?: string
}

// 新增/编辑党建清单抽屉
const idEditRef = ref()
const idEditParentID = ref()
const idEditParentName = ref()
const idEditLevel = ref()
const addNoticeFormRef = ref()
// 升降级弹框
const relegationVisible = ref(false)
// 合并弹框
const mergeVisible = ref(false)
// 拆分弹框
const splitVisible = ref(false)
// 删除弹框
const deleteVisible = ref(false)
// 当前操作的row
const currentRow = ref<rowInstance>({})
// 当前等级选择器的值
const currentLevel = ref('')
// 当前等级选择器的选项
const currentLevelOptions = ref([
  {
    label: 'Everybody\'s Got Something to Hide Except Me and My Monkey',
    value: 'song0',
  },
  {
    label: 'Drive My Car',
    value: 'song1',
  },
])
// 合并表单数据
const mergeData = ref({ currentLevel: '', currentLevel2: '', name2: '' })
// 拆分表单数据

const splitData = ref({ currentLevel: '', currentLevel2: '', name2: '' })

const mergeRules = ref({
  currentLevel: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入密码',
    },
  ],
  currentLevel2: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入密码',
    },
  ],
  name2: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入密码',
    },
  ],
})

const splitRules = ref({
  currentLevel: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入密码',
    },
  ],
  currentLevel2: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入密码',
    },
  ],
  name2: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入密码',
    },
  ],
})

const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  editTitle,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEditOrganize({ name: '根节点', confirmFn: handelConfirmEdit })

/** 添加根节点 */
const handleClickAddRootNode = () => {
  idEditRef.value = '0'
  idEditParentID.value = '0'
  editTitle.value = '根节点'
  editTypeRef.value = 'add'
  showEditRef.value = true
}

/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}

watch(showEditRef, (newV) => {
  if (!newV) {
    addNoticeFormRef.value?.resetForm()
  }
})

/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  getTableList()
}

// 确定升降级
function hanldleRelegationSure() {
  relegationVisible.value = false
}

// 确定合并
function hanldleMergeSure() {
  mergeVisible.value = false
}

// 确定拆分
function hanldleSplitSure() {
  splitVisible.value = false
}

// 确定删除
function hanldleDeleteSure() {
  deleteVisible.value = false
}

// 取消合并
function handleMergeClose() {
  mergeVisible.value = false
}

// 取消拆分
function handleSpliteClose() {
  splitVisible.value = false
}

// 取消删除
function handleDeleteClose() {
  deleteVisible.value = false
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          currentRow.value = row
          relegationVisible.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '升降级',
      },
    ),
    h(
      NButton,
      {
        onClick: () => {
          currentRow.value = row
          mergeVisible.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '合并',
      },
    ),
    h(
      NButton,
      {
        onClick: () => {
          currentRow.value = row
          splitVisible.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '拆分',
      },
    ),
    h(
      NButton,
      {
        onClick: () => {
          currentRow.value = row
          deleteVisible.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '组织撤销',
      },
    ),
  ]
})

// 关闭升降级遮罩
function handleRelegationClose() {
  relegationVisible.value = false
}

onMounted(getTableList)
onActivated(() => {
  filterVal.value ? filterTableList() : getTableList()
})
</script>

<style lang="scss" scoped>
.relegation-select {
  width: 100px;
  margin-left: 10px;
  margin-right: 10px;
}

.current-level-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}

.relegation-container {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  margin-top: 30px;
}

.relegation-cancel-btn {
  margin-left: 10px;
}

.merge-select {
  width: 400px;
}
</style>
