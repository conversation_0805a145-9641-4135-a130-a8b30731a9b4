import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import { NProgress } from 'naive-ui'
import type { PartyBuildingEvaluationTableItemType } from '@/services/publicity/vote/evaluation/type'

export function getTableColumns(
  optionColumnRenderer: (
    row: PartyBuildingEvaluationTableItemType
  ) => VNodeChild,
): DataTableColumns<PartyBuildingEvaluationTableItemType> {
  return [
    {
      type: 'selection',
    },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '50',
      render: (_, i) => i + 1,
    },
    {
      key: 'title',
      title: '考核名称',
    },
    {
      key: 'evaluationYear',
      title: '考核时间',
    },
    {
      key: 'publishTime',
      title: '发布时间',
    },
    {
      key: 'evaluationProgress',
      width: '15%',
      title: '打分进度',
      render: (row) => {
        return h(NProgress, {
          type: 'line',
          style: {
            width: '220px',
          },
          processing: true,
          percentage: row.evaluationProgress as any,
        })
      },
    },
    {
      key: 'status',
      title: '状态',
      render: (row) => {
        return row.status ? row.status : '--'
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
