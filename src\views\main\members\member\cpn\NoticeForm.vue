<script lang="ts" setup>
import { NForm } from 'naive-ui'
import { formRules } from './config'
import {
  addUserItem,
  editorUserItem,
  getUserInfo,
} from '@/services/system/User'
import type { uploadFileItem } from '@/services/affairs/party-building-list/exam-indicators/types'
import { uploadImg } from '@/services'
import type { UserDetailItem } from '@/services/system/User/types'
import {
  useCurrentOrganizationListOptionsNew,
  useFetchEnumerationOptions,
  useFetchOriginOptions,
} from '@/hooks/use-select-options'
import { POST_TYPE } from '@/store/dict'
import { getGenderFromIdCard } from '@/utils/utils'
import { judgePermission } from '@/directive/permission/ifHasPermi'

interface Props {
  type?: string
  id?: string
  partyIdentity?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
  partyIdentity: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const sexType = ref(useFetchEnumerationOptions('sex'))
const eduType = ref(useFetchEnumerationOptions('edu'))
const ethnicType = ref(useFetchEnumerationOptions('ethnic'))
const memberStatusType = ref(useFetchEnumerationOptions('member_status'))
const orgType = ref(useCurrentOrganizationListOptionsNew())
// const partyIdentityType = ref(useFetchEnumerationOptions('party_identity'))
const originType = ref(useFetchOriginOptions())

const politicalType = ref(useFetchEnumerationOptions('political')) // 政治面貌
const partyDutyType = ref(useFetchEnumerationOptions('party_duty')) // 党内职务
const partyProcessStatusType = ref(
  useFetchEnumerationOptions('party_process_status'),
) // 入党流程状态

watchEffect(() => {
  partyProcessStatusType.value.enumerationList.forEach((item) => {
    if (item.value === '4') {
      item!.disabled = true
    }
  })
})

const formDataReactive = reactive<UserDetailItem>({
  userId: null,
  username: '',
  trueName: '',
  sex: null,
  ethnic: null,
  postCategory: null,
  // customEthnic: null,
  edu: null,
  jobTime: null,
  origin: null,
  workDepartment: null,
  address: '',
  memberStatus: '0',
  phone: '',
  identityId: '',
  partyIdentity: null,
  joinTime: null,
  regularTime: null,
  deptId: null,
  avatarId: '',
  avatarUrl: '',
  // partyMember: null,
  political: null, // 政治面貌
  dutyName: null, // 党内职务
  partyProcessStatus: null, // 入党流程状态
  partyDuty: null, // 支部内职务

  oldDeptName: '', // 原组织信息
  paymentTime: null, // 党费缴纳时间
  transferTime: null, // 转入时间
})

const formRef = ref<InstanceType<typeof NForm>>()

// 编辑状态下-党内职务是否 disabled
// const isDutyNameDisabled = computed(() => {
//   if (props.type === 'add' || props.type === 'modify') {
//     // 如果新增时政治面貌填写的是 群众（3）  则支部内职务禁用
//     if (formDataReactive.political === '3') {
//       return true
//     }
//   }
//   else if (
//     props.type === 'modify'
//     && ['发展对象', '入党积极分子', '积极分子', '入党申请人', '群众'].includes(
//       props.partyIdentity,
//     )
//   ) {
//     return true
//   }
//   return false
// })

onBeforeMount(() => {
  if (props.type === 'modify' && props.id) {
    getUserInfo(props.id).then((res) => {
      formDataReactive.userId = res.userId
      formDataReactive.username = res.username
      formDataReactive.trueName = res.trueName
      formDataReactive.sex = res.sex
      formDataReactive.ethnic = res.ethnic ? res.ethnic : null
      // formDataReactive.customEthnic = String(res.ethnic)
      //   ? String(res.ethnic)
      //   : null
      formDataReactive.edu = res.edu
      formDataReactive.jobTime = res.jobTime ? res.jobTime.slice(0, 10) : null
      formDataReactive.origin = res.origin
      formDataReactive.workDepartment = res.workDepartment
      formDataReactive.address = res.address
      formDataReactive.memberStatus = res.memberStatus
      formDataReactive.phone = res.phone
      formDataReactive.identityId = res.identityId
      formDataReactive.partyIdentity = res.partyIdentity
      formDataReactive.joinTime = res.joinTime?.slice(0, 10) || null
      formDataReactive.regularTime = res.regularTime
        ? res.regularTime.slice(0, 10)
        : null
      formDataReactive.deptId = res.deptId
      formDataReactive.avatarId = res.avatarId
      formDataReactive.avatarUrl = res.avatarUrl
      // === '中共党员' ? '中共党员' : res.political === '预备党员' ? '预备党员' : '群众'
      formDataReactive.political = res.political
      // formDataReactive.partyMember
      //   = res.partyIdentity !== '6' ? '中共党员' : '预备党员' // 6 为群众
      formDataReactive.dutyName = res.dutyName // 党内职务
      formDataReactive.partyDuty = res.partyDuty // 支部内职务
      formDataReactive.postCategory = res.postCategory
      formDataReactive.partyProcessStatus = res.partyProcessStatus // 入党流程状态

      // formDataReactive.oldDeptName = res.oldDeptName
      // formDataReactive.paymentTime = res.paymentTime?.slice(0, 7) || null
      // formDataReactive.transferTime = res.transferTime?.slice(0, 10) || null
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // if (formDataReactive.political === '3') {
      //   formDataReactive.partyIdentity = null
      //   formDataReactive.deptId = null
      //   formDataReactive.joinTime = null
      //   formDataReactive.regularTime = null
      // }
      if (formDataReactive.userId) {
        // 编辑为非党员情况下 入党时间、转正时间、所属组织重置为null
        editorUserItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
      else {
        addUserItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

// 照片上传
const handleAppCoverDone = async(file: File) => {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formDataReactive.avatarId) {
      return
    }
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      formDataReactive.avatarId = data.fileId
    }
  }
  catch (error) {}
}
// 照片删除
const handleAppCoverDelete = () => {
  formDataReactive.avatarId = ''
}

/** 获取党组织id */
const handleUpdateValue = (v: string) => {
  formDataReactive.deptId = v
}

/** 获取籍贯 */
const handleUpdateOriginValue = (v: string) => {
  formDataReactive.origin = v
}

/** 获取民族枚举值 转为 number类型 */
// const handleUpdateEthnicValue = (v: string) => {
//   formDataReactive.customEthnic = v
//   formDataReactive.ethnic = Number(v)
// }

// 递归给每一项添加disabled属性，党小组和党支部允许被选择
function addDisabledProperty(items: any) {
  items.forEach((item: any) => {
    // 根据org_type设置disabled属性
    item.disabled = item.org_type !== '党小组' && item.org_type !== '党支部'

    // 如果有子项，递归调用
    if (item.children && item.children.length > 0) {
      addDisabledProperty(item.children)
    }
  })
}

watch(
  () => orgType.value,
  (newValue) => {
    addDisabledProperty(newValue.organizationCurrentListTree)
  },
  { deep: true, immediate: true },
)

// 根据身份证号获取用户性别
watch(
  () => formDataReactive.identityId,
  (newValue) => {
    // console.log('🚀 ~ newValue:', newValue)
    const genderValue = getGenderFromIdCard(newValue)
    if (genderValue !== '未知') {
      if (genderValue === '男') {
        formDataReactive.sex = '0'
      }
      else if (genderValue === '女') {
        formDataReactive.sex = '1'
      }
    }
  },
  { deep: true, immediate: true },
)

/** 禁用时间 */
const handleDateDisabled = (s: number) => {
  return s >= new Date().getTime()
}

// 是否有转入党员信息的权限
const hasTransferPermission = computed(() => {
  return judgePermission('portal_user_add_transfer_info')
})

// 判断是否为党员政治面貌（中共党员或预备党员）
const isPartyMemberPolitical = computed(() => {
  return ['1', '2'].includes(formDataReactive.political as string)
})

// 判断是否为中共党员
const isCommunistPartyMember = computed(() => {
  return ['1'].includes(formDataReactive.political as string)
})

// 判断是否为群众
const isMassPolitical = computed(() => {
  return ['3'].includes(formDataReactive.political as string)
})

const calcFormRules = computed(() => {
  const rulesList = { ...formRules }
  rulesList.partyProcessStatus = {
    required: false,
    message: '入党流程状态不能为空',
    trigger: 'change',
  }
  rulesList.joinTime = {
    required: false,
    message: '入党时间不能为空',
    trigger: 'change',
  }
  rulesList.regularTime = {
    required: false,
    message: '入党时间不能为空',
    trigger: 'change',
  }

  rulesList.partyDuty = {
    required: true,
    message: '支部内职务不能为空',
    trigger: 'input',
  }

  // 政治面貌是群众时，需要填写
  if (isMassPolitical.value) {
    rulesList.partyProcessStatus.required = true
  }

  // 政治面貌为中共党员、预备党员时需要填写
  if (isPartyMemberPolitical.value) {
    rulesList.joinTime.required = true
  }
  // 政治面貌为中共党员时需要填写
  if (isCommunistPartyMember.value) {
    rulesList.regularTime.required = true
  }

  // 如果 isDutyNameDisabled.value 为 true 时，不需要校验必填项
  // if (isDutyNameDisabled.value) {
  //   rulesList.partyDuty.required = false
  // }

  return rulesList
})

// 计算政治面貌选项，根据权限禁用中共党员和预备党员
const politicalOptions = computed(() => {
  return politicalType.value.enumerationList.map(option => ({
    ...option,
    disabled:
      !hasTransferPermission.value && (option.value === '1' || option.value === '2'),
  }))
})

// 判断是否显示转入党员相关信息
const showTransferInfo = computed(() => {
  return isPartyMemberPolitical.value
    && hasTransferPermission.value
    && props.type === 'add'
})

// 判断是否显示预备党员提示信息
const showPartyMemberTip = computed(() => {
  return !hasTransferPermission.value
})

function handleUpdatePoliticalValue(v: string) {
  if (v === '2') {
    // 政治面貌为  预备党员 入党流程也需要重置为 预备党员
    formDataReactive.partyProcessStatus = '4'
  }
  else {
    formDataReactive.partyProcessStatus = null
  }
}

defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    :model="formDataReactive"
    :rules="calcFormRules"
    label-align="right"
    label-placement="left"
    label-width="110"
    require-mark-placement="left"
    size="small"
  >
    <div
      v-if="type === 'add'"
      class="w-full flex flex-row justify-center items-center"
    >
      <span class="text-[#F5A53C]">新用户初始密码为身份证后六位！请提醒相关人员及时修改密码。</span>
    </div>
    <SectionTitle text="个人信息" style="margin-bottom: 10px" />
    <n-grid :cols="24">
      <!-- 一排 -->
      <n-form-item-gi :span="12" label="用户名：">
        <n-input
          v-model:value="formDataReactive.username"
          clearable
          maxlength="20"
          placeholder="请输入用户名"
          show-count
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="姓名：" path="trueName">
        <n-input
          v-model:value="formDataReactive.trueName"
          clearable
          maxlength="20"
          placeholder="请输入姓名"
          show-count
        />
      </n-form-item-gi>
      <!-- 二排 -->
      <n-form-item-gi :span="12" label="手机号：" path="phone">
        <n-input
          v-model:value="formDataReactive.phone"
          placeholder="请输入手机号"
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="身份证：" path="identityId">
        <n-input
          v-model:value="formDataReactive.identityId"
          clearable
          placeholder="请输入身份证"
          maxlength="18"
          show-count
        />
      </n-form-item-gi>
      <!-- 三排 -->
      <n-form-item-gi :span="12" label="性别：" path="sex">
        <n-select
          v-model:value="formDataReactive.sex"
          :options="sexType.enumerationList"
          disabled
          clearable
          placeholder="请选择性别"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="参加工作时间：">
        <n-date-picker
          v-model:formatted-value="formDataReactive.jobTime"
          :is-date-disabled="handleDateDisabled"
          class="w-full"
          clearable
          placeholder="请选择参加工作时间"
          size="medium"
          type="date"
          value-format="yyyy-MM-dd"
        />
      </n-form-item-gi>

      <!-- 四排 -->
      <n-form-item-gi :span="12" label="学历：" path="edu">
        <n-select
          v-model:value="formDataReactive.edu"
          :options="eduType.enumerationList"
          clearable
          filterable
          placeholder="请选择学历"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="民族：" path="ethnic">
        <n-select
          v-model:value="formDataReactive.ethnic"
          :options="ethnicType.enumerationList"
          clearable
          filterable
          placeholder="请选择民族"
        />
      </n-form-item-gi>
      <!-- 五排 -->
      <n-form-item-gi :span="24" label="工作部门：">
        <n-input
          v-model:value="formDataReactive.workDepartment"
          placeholder="请输入工作部门"
        />
      </n-form-item-gi>
      <!-- <n-form-item-gi :span="12" label="政治面貌：" path="political">
        <n-select
          v-model:value="formDataReactive.political"
          placeholder="请选择政治面貌"
          :options="POLITICAL_STATUS"
          clearable
        />
      </n-form-item-gi> -->

      <!-- 六排 -->
      <n-form-item-gi :span="24" label="家庭住址：">
        <n-input
          v-model:value="formDataReactive.address"
          clearable
          maxlength="50"
          placeholder="请输入家庭住址"
          show-count
        />
      </n-form-item-gi>
      <!-- 七排 -->
      <n-form-item-gi :span="12" label="状态：">
        <n-select
          v-model:value="formDataReactive.memberStatus"
          :options="memberStatusType.enumerationList"
          clearable
          filterable
          placeholder="请选择状态"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="籍贯：" path="origin">
        <n-cascader
          v-model:value="formDataReactive.origin"
          :options="originType.OriginOptions"
          :show-path="true"
          check-strategy="child"
          children-field="children"
          clearable
          filterable
          label-field="name"
          placeholder="请选择籍贯"
          value-field="id"
          @update:value="handleUpdateOriginValue"
        />
      </n-form-item-gi>

      <n-form-item-gi :span="12" label="照片：">
        <ImgUploader
          v-model:old-img-url="formDataReactive.avatarUrl"
          :height="430"
          :need-cropper="false"
          :width="295"
          @delete="handleAppCoverDelete"
          @done="handleAppCoverDone"
        />
      </n-form-item-gi>
    </n-grid>

    <SectionTitle text="党员信息" style="margin-bottom: 10px" />

    <n-grid :cols="24">
      <n-form-item-gi :span="24" label="所属党组织：" path="deptId">
        <n-tree-select
          v-model:value="formDataReactive.deptId"
          :options="orgType.organizationCurrentListTree"
          :show-path="false"
          check-strategy="all"
          children-field="children"
          clearable
          default-expand-all
          filterable
          key-field="deptId"
          label-field="name"
          placeholder="请选择所属党组织"
          value-field="deptId"
          @update:value="handleUpdateValue"
        />
      </n-form-item-gi>

      <n-form-item-gi :span="24" label="政治面貌：" path="political">
        <div class="w-full flex flex-col gap-[4px]">
          <n-select
            v-model:value="formDataReactive.political"
            style="width: 100%"
            :options="politicalOptions"
            clearable
            filterable
            placeholder="请选择政治面貌"
            @update:value="handleUpdatePoliticalValue"
          />
          <span
            v-if="showPartyMemberTip"
            class="text-[#F5A53C] text-[12px]"
          >新增内部发展的预备党员，请选择群众添加后在党员发展里进行操作</span>
        </div>
      </n-form-item-gi>
      <n-form-item-gi
        v-if="showTransferInfo"
        :span="24"
        label="原组织信息"
        path="oldDeptName"
      >
        <n-input
          v-model:value="formDataReactive.oldDeptName"
          clearable
          maxlength="50"
          placeholder="请输入原组织信息"
          show-count
        />
      </n-form-item-gi>

      <n-form-item-gi
        v-if="showTransferInfo"
        :span="12"
        label="党费缴纳时间："
        path="paymentTime"
      >
        <n-date-picker
          v-model:formatted-value="formDataReactive.paymentTime"
          :is-date-disabled="handleDateDisabled"
          class="w-full"
          clearable
          placeholder="请选择党费缴纳时间"
          size="medium"
          type="month"
          value-format="yyyy-MM"
        />
      </n-form-item-gi>
      <n-form-item-gi
        v-if="showTransferInfo"
        :span="12"
        label="转入时间："
        path="transferTime"
      >
        <n-date-picker
          v-model:formatted-value="formDataReactive.transferTime"
          :is-date-disabled="handleDateDisabled"
          class="w-full"
          clearable
          placeholder="请选择转入时间"
          size="medium"
          type="date"
          value-format="yyyy-MM-dd"
        />
      </n-form-item-gi>

      <!-- <n-form-item-gi :span="24" label="支部内身份：" path="partyIdentity">
        <n-select
          v-model:value="formDataReactive.partyIdentity"
          :options="partyIdentityType.enumerationList"
          placeholder="请选择党支部身份"
          clearable
          filterable
        />
      </n-form-item-gi> -->
      <!-- 基建盐城版本添加个职位字段 -->
      <n-form-item-gi
        v-if="props.type !== 'add'"
        :span="24"
        label="支部内职务："
      >
        <n-select
          v-model:value="formDataReactive.partyDuty"
          :disabled="props.type === 'modify'"
          :options="partyDutyType.enumerationList"
          clearable
          filterable
          placeholder="请选择支部内职务"
        />
      </n-form-item-gi>

      <n-form-item-gi :span="24" label="行政职务：" path="dutyName">
        <n-input
          v-model:value="formDataReactive.dutyName"
          clearable
          maxlength="20"
          placeholder="请输入行政职务"
          show-count
        />
      </n-form-item-gi>

      <!-- 入党流程状态 -->
      <n-form-item-gi
        :span="24"
        label="入党流程状态："
        path="partyProcessStatus"
      >
        <n-select
          v-model:value="formDataReactive.partyProcessStatus"
          :disabled="isPartyMemberPolitical"
          :options="partyProcessStatusType.enumerationList"
          clearable
          filterable
          placeholder="请选择入党流程状态"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="岗位类别：">
        <n-select
          v-model:value="formDataReactive.postCategory"
          :options="POST_TYPE"
          clearable
          filterable
          placeholder="请选择岗位类别"
        />
      </n-form-item-gi>

      <n-form-item-gi :span="12" label="入党时间：" path="joinTime">
        <n-date-picker
          v-model:formatted-value="formDataReactive.joinTime"
          :disabled="!isPartyMemberPolitical"
          :is-date-disabled="handleDateDisabled"
          class="w-full"
          clearable
          placeholder="请选择入党时间"
          size="medium"
          type="date"
          value-format="yyyy-MM-dd"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="转正时间：" path="regularTime">
        <n-date-picker
          v-model:formatted-value="formDataReactive.regularTime"
          :disabled="!isCommunistPartyMember"
          :is-date-disabled="handleDateDisabled"
          class="w-full"
          clearable
          placeholder="请选择转正时间"
          size="medium"
          type="date"
          value-format="yyyy-MM-dd"
        />
      </n-form-item-gi>
    </n-grid>
  </n-form>
</template>
<style lang="scss" scoped>
:deep(.n-upload-file-list .n-upload-file.n-upload-file--image-card-type) {
  width: 160px;
  height: 160px;
}
</style>
