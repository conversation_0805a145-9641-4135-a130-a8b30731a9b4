<!--
 * @Description: 题目编辑表单
-->
<template>
  <n-spin :show="loadingRef">
    <n-form
      ref="formRef"
      size="small"
      require-mark-placement="left"
      label-width="100"
      label-align="right"
      label-placement="left"
      :model="formDataReactive"
      :rules="formRules"
    >
      <n-form-item label="难度：" path="level">
        <n-radio-group v-model:value="formDataReactive.level">
          <n-radio
            v-for="item in difficultyOptions"
            :key="item.label"
            style="margin-right: 28px"
            :value="item.value"
          >
            {{ item.label }}
          </n-radio>
        </n-radio-group>
      </n-form-item>

      <n-form-item label="题干：" path="name">
        <!-- 填空题 -->
        <div v-if="questionType === 3" style="width: 100%">
          <n-input
            v-model:value="formDataReactive.name"
            type="textarea"
            placeholder="示例：一年四季分别是：[ ]，[ ]，[ ]，[ ]"
            :rows="5"
            :maxlength="500"
            show-count
            @update-value="handleGapFillingNameChange"
          />
          <div class="tips">
            注：题干中的英文中括号“[ ]”代表填空，与下面的正确答案依次对应
          </div>
        </div>
        <!-- 其它 -->
        <n-input
          v-else
          v-model:value="formDataReactive.name"
          type="textarea"
          :rows="5"
          :maxlength="500"
          show-count
        />
      </n-form-item>

      <n-form-item v-if="questionType != 4" label="选项：" path="optionList">
        <div class="options">
          <div
            v-for="(item, index) in formDataReactive.optionList"
            :key="'gap' + index"
            class="option-item"
          >
            <span v-if="questionType != 3" class="option-name">{{
              String.fromCharCode(65 + index)
            }}</span>
            <span v-else class="option-name">填空{{ index + 1 }}：</span>

            <n-input
              v-model:value="item.content"
              size="small"
              :maxlength="100"
              show-count
              :placeholder="
                questionType === 3 ? '示例：春天|春季|春' : '请输入'
              "
            />

            <n-button
              v-if="questionType != 3"
              text
              @click="() => handleDeleteOption(index)"
            >
              <n-icon size="20">
                <Close />
              </n-icon>
            </n-button>
          </div>

          <div
            v-if="formDataReactive.optionList.length < 10 && questionType != 3"
            class="option-add"
          >
            <n-button text type="primary" @click="handleAddOption">
              <n-icon style="margin-right: 6px" size="14">
                <add-alt />
              </n-icon>
              添加选项
            </n-button>
            <span>最多10个选项</span>
          </div>
          <div v-if="questionType === 3" class="tips">
            注：一个空允许多个正确答案，使用“|”分隔
          </div>
        </div>
      </n-form-item>

      <n-form-item
        v-if="questionType != 3"
        label="正确答案："
        :path="questionType === 2 ? 'answerArr' : 'answer'"
      >
        <!-- 单选 -->
        <n-radio-group
          v-if="questionType === 1"
          v-model:value="formDataReactive.answer"
          class="choices"
        >
          <n-radio
            v-for="(item, index) in formDataReactive.optionList"
            :key="'answer' + item.name"
            :value="String.fromCharCode(65 + index)"
          >
            {{ String.fromCharCode(65 + index) }}
          </n-radio>
        </n-radio-group>
        <!-- 多选 -->
        <n-checkbox-group
          v-else-if="questionType === 2"
          v-model:value="formDataReactive.answerArr"
          class="choices"
        >
          <n-checkbox
            v-for="(item, index) in formDataReactive.optionList"
            :key="'answer' + item.name"
            :value="String.fromCharCode(65 + index)"
            :label="String.fromCharCode(65 + index)"
          />
        </n-checkbox-group>
        <!-- 判断 -->
        <n-radio-group
          v-else-if="questionType === 4"
          v-model:value="formDataReactive.answer"
        >
          <n-radio style="margin-right: 25px" value="正确">
            正确
          </n-radio>
          <n-radio value="错误">
            错误
          </n-radio>
        </n-radio-group>
      </n-form-item>

      <n-form-item label="答案解析：">
        <n-input
          v-model:value="formDataReactive.analysis"
          type="textarea"
          :rows="4"
          :maxlength="500"
          show-count
        />
      </n-form-item>
      <n-form-item label="题目来源：">
        <n-input
          v-model:value="formDataReactive.source"
          type="textarea"
          :rows="4"
          :maxlength="100"
          show-count
        />
      </n-form-item>
      <n-form-item label="标签：" path="labelIdList">
        <question-label-selector v-model:value="formDataReactive.labelIdList" />
      </n-form-item>
    </n-form>
  </n-spin>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { onBeforeMount, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import {
  NButton,
  NCheckbox,
  NCheckboxGroup,
  NForm,
  NFormItem,
  NIcon,
  NInput,
  NRadio,
  NRadioGroup,
  NSpin,
} from 'naive-ui'
import { Close } from '@vicons/carbon'
import { formRules, getFormData } from './config'
import QuestionLabelSelector from './QuestionLabelSelector.vue'
import { difficultyOptions } from '@/views/main/examination/config'
import {
  getExaminationQuestionDetail,
  postExaminationQuestion,
} from '@/services/examination/index'

const props = defineProps({
  questionType: {
    type: Number,
    required: true,
  },
  editType: {
    type: String as PropType<'add' | 'modify' | 'view' | 'addSubset'>,
    default: 'add',
  },
  id: {
    type: Number,
    default: 0,
  },
})
const emits = defineEmits(['update:questionType', 'saved'])

const route = useRoute()

const libraryId = route.params.libraryId

const loadingRef = ref(false)
const formRef = ref<InstanceType<typeof NForm>>()
const formDataReactive = reactive<any>(getFormData())

onBeforeMount(() => {
  const { id, editType } = props
  // 判断是否编辑，回显数据
  if (editType === 'modify' && id) {
    loadingRef.value = true
    getExaminationQuestionDetail(id).then((res: any) => {
      emits('update:questionType', res.type)
      formDataReactive.id = id
      formDataReactive.level = res.level
      formDataReactive.name = res.name
      formDataReactive.optionList = res.optionList.map((item: any) => ({
        content: item.content,
      }))
      if (res.type === 1) {
        formDataReactive.answer
          = res.optionList.find(
            (item: any) => String(item.id) === String(res.answer),
          )?.name ?? ''
      }
      else if (res.type === 2) {
        formDataReactive.answerArr = res.optionList
          .filter((item: any) =>
            res.answer.split(',').includes(String(item.id ?? '')),
          )
          .map((item: any) => item.name ?? '')
      }
      else if (res.type === 4) {
        formDataReactive.answer = res.answer === '2' ? '错误' : '正确'
      }
      formDataReactive.analysis = res.analysis
      formDataReactive.source = res.source
      formDataReactive.labelIdList = res.labelIdList

      loadingRef.value = false
    })
  }
})

// 填空题题干发生改变
function handleGapFillingNameChange(v: string) {
  const reg = /\[\s*\]/g
  formDataReactive.optionList = (v.match(reg) ?? []).map(() => ({
    content: '',
  }))
}
// 删除选项
function handleDeleteOption(index: number) {
  formDataReactive.optionList.splice(index, 1)
}
// 添加选项
function handleAddOption() {
  const len = formDataReactive.optionList.length
  if (len >= 10) {
    window.$message.warning('最多添加10个选项')
    return
  }
  formDataReactive.optionList.push({
    content: '',
  })
}

// 验证表单并保存
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      const type = props.questionType
      formDataReactive.type = type
      formDataReactive.libraryId = libraryId
      if (type === 1 || type === 2) {
        formDataReactive.optionList.forEach((item: any, index: number) => {
          item.name = String.fromCharCode(65 + index)
        })
        // 将答案转成ABCD...
        if (type === 2) {
          formDataReactive.answer = formDataReactive.answerArr?.join('') ?? ''
        }
      }
      else if (type === 4) {
        // 判断题答案映射：正确->1，错误->2
        formDataReactive.answer = formDataReactive.answer === '正确' ? '1' : '2'
      }
      delete formDataReactive.answerArr
      // 调接口
      postExaminationQuestion(formDataReactive).then(() => {
        window.$message.success('保存成功')
        emits('saved')
      })
    }
  })
}

defineExpose({
  validateAndSave,
})
</script>

<style lang="scss" scoped>
.options {
  width: 90%;

  > .option-item {
    display: flex;
    align-items: center;
    margin-bottom: 14px;

    > .option-name {
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      margin-right: 9px;
      flex-shrink: 0;
    }

    > button {
      margin-left: 10px;
    }
  }

  > .option-add {
    display: flex;
    align-items: center;

    > span {
      margin-left: 15px;
      font-size: 12px;
      font-weight: 400;
      color: #333333;
    }
  }
}

.choices {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  .n-radio,
  .n-checkbox {
    line-height: 30px;
    width: 15%;
  }
}

.tips {
  height: 30px;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  line-height: 30px;
  margin-top: 3px;
}
</style>
