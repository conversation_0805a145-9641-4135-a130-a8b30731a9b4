import type {
  CurrentStyleItem,
  PublicityListTypes,
  StyleItem,
  viewNewsDetailType,
} from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/**
 * 先锋号列表是一级分类
 *
 */
export function getPublicityList() {
  return commonReq.get<PublicityListTypes[]>({
    url: '/propaganda/backend/v1/pionner',
  })
}

/**
 * 新增先锋号
 */
export function addPublicity(data: any) {
  return commonReq.post({
    url: '/propaganda/backend/v1/pionner',
    data,
  })
}

/**
 * 修改先锋号
 */
export function modifyPublicity(data: any) {
  return commonReq.put({
    url: '/propaganda/backend/v1/pionner',
    data,
  })
}

/**
 * 删除先锋号
 */
export function delPublicity(ids: any) {
  return commonReq.delete({
    url: '/propaganda/backend/v1/pionner',
    params: {
      ids,
    },
  })
}

/**
 * 移动先锋号
 */
export function movePublicity(data: any) {
  return commonReq.put({
    url: '/propaganda/backend/v1/pionner/move',
    data,
  })
}

/**
 * 是否隐藏先锋号
 */
export function hiddenPublicity(id: string) {
  return commonReq.post({
    url: `/propaganda/backend/v1/pionner/hidden/${id}`,
  })
}

/**
 * 是否推荐先锋号
 */
export function recommendPublicity(id: string) {
  return commonReq.post({
    url: `/propaganda/backend/v1/pionner/recommand/${id}`,
  })
}

/**
 * 先锋号二级是资讯分类
 * 先锋号分类列表
 * @param {any} params:any
 * @returns {any}
 */
export function getPublicityCategoryList(id: string) {
  return commonReq.get<PaginationRes<any>>({
    url: `/propaganda/pionner/category/${id}`,
  })
}

/**
 * 新增先锋号分类资讯
 */
export function addPublicityCategory(data: any) {
  return commonReq.post({
    url: '/propaganda/pionner/category',
    data,
  })
}

/**
 * 修改先锋号分类资讯
 */
export function modifyPublicityCategory(data: any) {
  return commonReq.put({
    url: '/propaganda/pionner/category',
    data,
  })
}

/**
 * 删除先锋号资讯分类
 */
export function delPublicityCategory(ids: any) {
  return commonReq.delete({
    url: '/propaganda/pionner/category',
    params: {
      ids,
    },
  })
}

/**
 * 移动先锋号资讯分类
 */
export function movePublicityCategory(data: any) {
  return commonReq.put({
    url: '/propaganda/pionner/category/move',
    data,
  })
}

// 资讯列表------------------------------
export function getNewsListForPublicity(
  params: { categoryId: string; title: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<PublicityListTypes[]>>({
    url: '/propaganda/pionner/news',
    params,
  })
}

/**
 * 新增资讯
 */
export function addNews(data: viewNewsDetailType) {
  return commonReq.post({
    url: '/propaganda/pionner/news',
    data,
  })
}

/**
 * 编辑资讯
 */
export function modifyNews(data: viewNewsDetailType) {
  return commonReq.put({
    url: '/propaganda/pionner/news',
    data,
  })
}

/**
 * 查看资讯详情
 */
export function viewNewsDetail(id: string) {
  return commonReq.get<viewNewsDetailType>({
    url: `/propaganda/pionner/news/${id}`,
  })
}

/**
 * 删除资讯
 */
export function delNews(ids: string) {
  return commonReq.delete({
    url: '/propaganda/pionner/news',
    params: {
      ids,
    },
  })
}

/**
 * 置顶资讯
 */
export function topNews(id: string) {
  return commonReq.post({
    url: `/propaganda/pionner/news/top/${id}`,
  })
}

/**
 * 推荐资讯
 */
export function recommendNews(id: string) {
  return commonReq.post({
    url: `/propaganda/pionner/news/recommend/${id}`,
  })
}

/**
 * 隐藏资讯
 */
export function hiddenNews(id: string) {
  return commonReq.post({
    url: `/propaganda/pionner/news/hidden/${id}`,
  })
}

/**
 * 是否加入首页轮播池
 */
export function joinRotationPool(id: string) {
  return commonReq.post({
    url: `/propaganda/pionner/news/slider/${id}`,
  })
}

/**
 * 审核资讯
 */
export function checkNews(id: string, reviewed: string) {
  return commonReq.post({
    url: `/propaganda/pionner/news/reviewed/${id}?reviewed=${reviewed}`,
  })
}

/**
 * 获取展示设置样式列表（all）
 * 主要用于在前端展示设置界面中动态加载可供选择的样式项
 *
 * @param fieldIdentify 字段标识，用于指定需要查询的业务模块
 * @returns 返回一个Promise对象，解析后包含样式项的数组
 */
export function getShowSettingStyleList(fieldIdentify: string) {
  return commonReq.get<Array<StyleItem>>({
    url: '/party-affairs/backend/v1/style-config/list',
    params: {
      fieldIdentify,
    },
  })
}

/**
 * 获取先锋号展示设置
 * 主要用于控制新闻在前端展示的样式和配置
 *
 * @param fieldIdentify 字段标识，用于指定需要查询的业务模块
 * @returns 返回一个Promise对象，解析后包含新闻展示设置的相关信息
 */
export function getNewsShowSetting(fieldIdentify: string) {
  return commonReq.get<Array<CurrentStyleItem>>({
    url: '/party-affairs/backend/v1/style-enable/list',
    params: {
      fieldIdentify,
    },
  })
}

/**
 * 保存先锋号展示设置
 * 主要用于保存新闻在特定字段或类型下的展示设置，包括样式和配置等
 *
 * @param data 包含字段标识、样式标识、样式内容等字段
 * @returns 返回一个Promise对象，解析后包含保存结果的相关信息
 */
export function saveNewsShowSetting(data: {
  styleId: string
  fieldIdentify: string
}) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/style-enable/save',
    data,
  })
}
