<script setup lang="ts">
import type { NForm, NInput } from 'naive-ui'
import { formRules, themeOverrides } from './config'
import { useAuthStore } from '@/store/auth/auth'
import { sessionCache } from '@/utils/cache'
const formData = ref({
  username: '',
  password: '',
  code: '',
})

const codeRef = ref<InstanceType<typeof NInput>>() // 验证码输入框引用
const authStore = useAuthStore()
const codeUrl = computed(() => authStore.codeUrl) // 验证码url
const date = Date.now()
// let randomStr = uuid()

authStore.getVerificationCode(String(date)) // 加载验证码

// 验证码获取
function handleClickCode() {
  formData.value.code = ''
  const randomStr = String(Date.now())
  authStore.getVerificationCode(randomStr)
  codeRef.value?.focus()
}
// 登录
const formRef = ref<InstanceType<typeof NForm>>() // 表单组件引用
function handlerLogin() {
  formRef.value?.validate((errors: any) => {
    const loginForm = {
      username: formData.value.username,
      password: formData.value.password,
    }
    if (!errors) {
      authStore
        .handleUserLogin(
          loginForm,
          String(sessionCache.get('randomStr')),
          formData.value.code,
        )
        .catch(() => {
          handleClickCode()
        })
    }
  })
}

// 忘记密码
function handleClickForget() {
  window.$dialog.info({
    maskClosable: true,
    title: '忘记密码',
    showIcon: false,
    positiveText: '知道了',
    content: () =>
      h(
        'div',
        {
          style:
            'height:100px;display:flex;justify-content:center;align-items:center',
        },
        '请联系客服 QQ:123456789 重置密码',
      ),
  })
}
</script>
<template>
  <n-config-provider class="w-full" :theme-overrides="themeOverrides">
    <n-form
      ref="formRef"
      class="w-full"
      size="large"
      :rules="formRules"
      :model="formData"
      label-placement="left"
    >
      <n-form-item path="username">
        <n-input
          v-model:value="formData.username"
          class="!bg-[#F6F8F9]"
          placeholder="请输入账号"
        />
      </n-form-item>
      <n-form-item path="password">
        <n-input
          v-model:value="formData.password"
          class="!bg-[#F6F8F9]"
          type="password"
          placeholder="请输入密码"
          show-password-on="mousedown"
        />
      </n-form-item>
      <n-form-item path="code">
        <n-input
          ref="codeRef"
          v-model:value="formData.code"
          class="!bg-[#F6F8F9]"
          placeholder="请输入验证码"
          @keyup.enter="handlerLogin"
        />
        <img
          class="w-[84px] h-[36px] ml-[6px] cursor-pointer"
          :src="codeUrl"
          alt="验证码"
          @click="handleClickCode"
        >
      </n-form-item>
      <div class="flex flex-col gap-y-[21px]">
        <n-button class="login-btn" type="primary" @click="handlerLogin">
          登录
        </n-button>
        <n-button
          text
          type="primary"
          class="forget-btn"
          @click="handleClickForget"
        >
          忘记密码?
        </n-button>
      </div>
    </n-form>
  </n-config-provider>
</template>
<style lang="scss" scoped>
.login-btn {
  width: 100%;
  height: 46px;
  border-radius: 5px;
}

.forget-btn {
  font-size: 12px;
  color: #c22b2a;
}
</style>
