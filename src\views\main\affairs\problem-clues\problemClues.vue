<script setup lang="ts">
import { debounce } from 'lodash-es'
import { getTableColumns } from './config'
import {
  deleteProblem,
  getProblemList,
} from '@/services/affairs/problem-clues/problem-clues'

import { useMyTable } from '@/hooks/use-my-table'
import DeleteButton from '@/components/DeleteButton.vue'

const filterReactive = ref<{
  name: string
  title: string
}>({
  name: '',
  title: '',
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  handleSingleDelete,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(getProblemList, filterReactive, {
  batchDeleteTable: true,
  delApi: deleteProblem,
})

/** 名称过滤 */
function handleResourceNameChange() {
  currentPage.value = 1
  filterReactive.value.name = filterReactive.value.name.trim()
  filterReactive.value.title = filterReactive.value.title.trim()
  loadData()
}
watch(filterReactive, debounce(handleResourceNameChange, 50000))

// 修改和删除按钮渲染
const tableColumns = getTableColumns(
  (row) => {
    return [
      h(DeleteButton, {
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    ]
  },
  (row, index) => {
    return (currentPage.value - 1) * pageSize.value + index + 1
  },
  (row) => {
    if (row.phone && row.phone.length === 11) {
      const middle = row.phone.slice(3, 7)
      return row.phone.replace(middle, '*'.repeat(middle.length))
    }
    return '-'
  },
)
onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="问题线索"
    :loading="loading"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :show-delete="false"
    :show-add="false"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
  >
    <template #filters>
      <n-form label-placement="left">
        <n-grid :cols="42">
          <n-form-item-gi span="20" label="">
            <n-input
              v-model:value="filterReactive.name"
              clearable
              size="small"
              placeholder="请输入姓名"
            />
          </n-form-item-gi>
          <n-form-item-gi span="2" label="" />
          <n-form-item-gi span="20" label="">
            <n-input
              v-model:value="filterReactive.title"
              clearable
              size="small"
              placeholder="请输入标题"
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </template>
  </table-container>
</template>
<style lang="scss" scoped></style>
