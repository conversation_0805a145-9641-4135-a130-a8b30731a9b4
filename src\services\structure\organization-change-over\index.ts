import type { AddChangeOverItem, TableItem, UpdateChangeOver } from './type'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/**
 * @module 组织关系转接
 * @description 查询转接关系表
 * @param {any} params.transferType 调动类型 0:系统内调动 1:系统外调动 2:调出系统外
 * @param {any} params.phaseStatus 审核状态 0:待审批 1:已驳回 2:已撤销 3:已完成
 */
export function getOrganizationChangeOverTableList(
  params: {
    userName: string
    transferType: string | number | null
    phaseStatus: string | number | null
  } & PaginationReq,
) {
  return commonReq.get<PaginationRes<TableItem>>({
    // url: '/org-construction/backend/v1/transfer', // 旧版接口
    url: '/org-construction/backend/v1/transfer_flow_approval/page',
    params,
  })
}

/**
 * @module 组织关系转接
 * @description 新增转接关系
 * @param {any} data:AddChangeOverItem
 */
export function addOrganizationChangeOverItem(data: AddChangeOverItem) {
  return commonReq.post({
    url: '/org-construction/backend/v1/transfer',
    data,
  })
}

/**
 * @module 组织关系转接
 * @description 查询转接关系详情
 * @param {any} id:string
 */
export function getOrganizationChangeOverItemDetail(id: string) {
  return commonReq.get<AddChangeOverItem>({
    url: `/org-construction/backend/v1/transfer/${id}`,
  })
}

/**
 * @module 组织关系转接
 * @description 审批转接关系
 * @param {any} data:UpdateChangeOver
 */
export function putUpdateOrganizationChangeOver(data: UpdateChangeOver) {
  return commonReq.put({
    url: '/org-construction/backend/v1/transfer',
    data,
  })
}

/**
 * @module 组织关系转接
 * @description 组织转接获取当前组织下的党员
 * @param {string} params.id 组织id
 * @param {string} params.username 用户名称
 */
export function putPartyUserList(
  params: { id: string; username: string } & PaginationReq,
) {
  return commonReq.get({
    url: '/org-construction/backend/v1/transfer/user/list',
    params,
  })
}
