<script setup lang="ts">
import SliderMenu from '@/components/SliderMenu.vue'
</script>
<template>
  <slider-menu />
</template>

<style lang="scss" scoped>
// 树选择器样式
:deep(.filter-operate .n-tree-select) {
  width: 184px !important;
  height: 28px !important;
  box-sizing: border-box !important;
}

:deep(.filter-operate .n-base-selection__border) {
  width: 184px !important;
  height: 28px !important;
  box-sizing: border-box !important;
}

:deep(.filter-operate .n-base-selection) {
  width: 184px !important;
  height: 28px !important;
  box-sizing: border-box !important;
}

:deep(.filter-operate .n-base-selection-label) {
  width: 184px !important;
  height: 28px !important;
  box-sizing: border-box !important;
}

:deep(.filter-operate .n-base-selection__state-border) {
  width: 184px !important;
  height: 28px !important;
  box-sizing: border-box !important;
}

:deep(.filter-operate .custom-date .n-input) {
  width: 115px !important;
  height: 28px;
}

:deep(.filter-operate .custom-input .n-input) {
  width: 220px !important;
  height: 28px;
}

:deep(.filter-operate .custom-select .n-select) {
  width: 184px !important;
  height: 28px;
}

:deep(.filter-operate .custom-select .n-base-selection__border) {
  width: 184px !important;
  height: 28px;
}

:deep(.filter-operate .custom-select .n-base-selection-label) {
  width: 184px !important;
  height: 28px;
}

:deep(.filter-operate .custom-select .n-base-selection__state-border) {
  width: 184px !important;
  height: 28px;
}

:deep(.filter-operate .custom-select .n-base-selection .n-base-selection) {
  width: 184px !important;
  height: 28px;
}

:deep(.filter-operate .custom-select) {
  width: 184px !important;
  height: 28px;
}
</style>
