<!--
 * @Description: 对话框自定义样式
 * @Author: 朱备 <<EMAIL>>
 * @Date: 2021-12-14 17:17:55
 * @LastEditTime: 2021-12-25 14:13:34
 * @LastEditors: 朱备 <zhu<PERSON>@easestrategy.com>
-->
<template>
  <n-modal
    :style="`width: ${width}; padding: 0`"
    preset="dialog"
    :show="show"
    :show-icon="false"
    :mask-closable="maskClosable"
    @update-show="(v) => emits('update:show', v)"
  >
    <template #header>
      <slot name="header">
        <div class="dialog-header">
          {{ title }}
        </div>
      </slot>
    </template>

    <div class="dialog-content">
      <slot></slot>
    </div>

    <template v-if="showAction" #action>
      <slot name="action">
        <div class="dialog-action">
          <n-button size="small" type="primary" @click="emits('confirm')">
            确定
          </n-button>
          <n-button size="small" @click="emits('update:show', false)">
            取消
          </n-button>
        </div>
      </slot>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { NButton, NModal } from 'naive-ui'

defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    default: '',
  },
  width: {
    type: String,
    default: '600px',
  },
  showAction: {
    type: Boolean,
    default: true,
  },
  maskClosable: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['confirm', 'update:show'])
</script>

<style lang="scss" scoped>
.dialog-header {
  width: 100%;
  height: 50px;
  background: #f2f4f8;
  line-height: 50px;
  padding-left: 29px;
}

.dialog-content {
  padding: 30px 40px;

  :deep(.n-form-item) {
    margin-bottom: 10px;
  }
}

.dialog-action {
  width: 100%;
  height: 60px;
  background: #fcfdfe;
  text-align: right;
  line-height: 60px;
  padding-right: 29px;
  border-top: 1px solid #f2f3f6;

  .n-button {
    width: 64px;
    height: 30px;
    border-radius: 15px;

    &:nth-child(n + 2) {
      margin-left: 8px;
    }
  }
}
</style>
