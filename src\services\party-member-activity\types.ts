export interface PartyMemberActivityListItem {
  /** 活动发布作者id */
  authorId?: number | null
  /** 收藏量 */
  collectNum?: number | null
  /** 评论量 */
  commentNum?: number | null
  /** 正文内容 */
  content?: null | string
  /** 活动封面图url */
  coverUrl?: null | string
  /** 创建者名称 */
  createBy?: null | string
  /** 主键ID */
  id?: string | null
  /** 点赞量 */
  likeNum?: number | null
  /** 阅读量 */
  readNum?: number | null
  /** 资讯阅读时长（单位：秒） */
  readTimeConfig?: number | null
  /** 是否展示 (0-否，1-是) */
  showStatus?: null | string
  /** 排序 */
  sort?: number | null
  /** 活动名称 */
  title?: null | string
  [property: string]: any
}

export interface PartyMemberAddOrEditData {
  /** 活动发布作者id */
  id?: string | null
  /** 正文内容 */
  content?: null | string
  /** 活动封面图url */
  coverUrl?: null | string
  /** 阅读量 */
  readNum?: number | null
  /** 是否展示 (0-否，1-是) */
  showStatus?: null | string
  /** 排序 */
  sort?: number | null
  /** 活动名称 */
  title?: null | string
  [property: string]: any
}
