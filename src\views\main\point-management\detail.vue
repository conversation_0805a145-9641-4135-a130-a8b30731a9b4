<template>
  <div>
    <table-container
      v-model:page="currentPage"
      v-model:page-size="pageSize"
      :show-other="true"
      title=""
      :show-toolbar="false"
      custom-toolbar
      :table-columns="detailColumns"
      :table-data="tableData"
      :total="total"
      :checked-row-keys="checkedRowKeys"
      :loading="loading"
      :show-padding-y="false"
      @update-page="onUpdatePage"
      @update-page-size="onUpdatePageSize"
      @update-checked-row-keys="onUpdateCheckedRowKeys"
    >
      <template #others>
        <div
          class="h-[60px] flex flex-col justify-start items-start gap-y-[10px]"
        >
          <div>
            <span>姓&emsp;&emsp;名：</span><span class="font-bold">{{ currentRow.userName }}</span>
          </div>
          <div>
            <span>所在组织：</span><span class="font-bold">{{ currentRow.orgName }}</span>
          </div>
        </div>
      </template>

      <template #btns>
        <n-date-picker
          v-model:formatted-value="filterDate"
          type="monthrange"
          value-format="yyyy-MM"
          clearable
        />
      </template>
    </table-container>
  </div>
</template>

<script setup lang="ts">
import { getIntegralDetailColumns } from './config'
import type { PointTableItemType } from '@/services/point-management/types'
import { getPointDetailList } from '@/services/point-management'
import { useMyTable } from '@/hooks'

interface Props {
  currentRow: PointTableItemType
}

const props = withDefaults(defineProps<Props>(), {})
const filterDate = ref<null | Array<string>>(null)
const filterReactive = ref({
  userId: props.currentRow.userId,
  startTime: '',
  endTime: '',
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getPointDetailList, filterReactive, {})

watch(
  () => filterDate.value,
  (newVal) => {
    if (newVal && newVal.length) {
      filterReactive.value.startTime = newVal[0]
      filterReactive.value.endTime = newVal[1]
    } else {
      filterReactive.value.startTime = ''
      filterReactive.value.endTime = ''
    }
  },
)

// 详情列表
const detailColumns = getIntegralDetailColumns()

onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss"></style>
