<script lang="ts" setup>
import { DownloadRound } from '@vicons/material'
import type { TreeSelectOption } from 'naive-ui'
import { NButton, NSelect } from 'naive-ui'
import { ref } from 'vue'
import { getDemocraticEvaluationColumns } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/config'
import { useRecordBookStore } from '@/store/recordBook'
import {
  editDemocraticReview,
  getDemocraticReview,
} from '@/services/recordBookMaintenance'
import type { DemocraticEvaluationType } from '@/services/recordBookMaintenance/types'
import emptyImg from '@/assets/image/content-empty.png'
import { useVoteStore } from '@/store/vote/vote'

const router = useRouter()
const fileLoading = ref<boolean>(false)
const recordBookStore = useRecordBookStore()
const showPopConfirm = ref<boolean>(false)
const currentDownloadFileType = ref<'word' | 'pdf'>('word')

const voteStore = useVoteStore()
const originalData = ref<DemocraticEvaluationType>({})
const formData = ref<DemocraticEvaluationType>({
  time: null,
  location: null,
  totalNum: 0,
  excellentNum: 0,
  qualifiedNum: 0,
  baseQualifiedNum: 0,
  unqualifiedNum: 0,
  userEvaluationList: [],
})

const isNoData = computed(() => {
  return !originalData.value
})

const selectedDeptId = ref<string | null>(null)
const selectedYear = ref<string | null>(null)
const isCanEdit = ref<boolean>(false)
const tableColumns = computed(() => {
  return getDemocraticEvaluationColumns(
    (row) => {
      return isCanEdit.value
        ? h(
          NSelect,
          {
            value: row.evaluationLevel,
            placeholder: '请选择评议结果',
            clearable: true,
            options: [
              {
                label: '优秀',
                value: '优秀',
              },
              {
                label: '合格',
                value: '合格',
              },
              {
                label: '基本合格',
                value: '基本合格',
              },
              {
                label: '不合格',
                value: '不合格',
              },
            ],
            onUpdateValue: (v) => {
              row.evaluationLevel = v
            },
          },
          {},
        )
        : h('span', {}, { default: () => row.evaluationLevel })
    },
    (row) => {
      return h(
        'div',
        {
          class: 'flex flex-row justify-start items-center gap-x-[10px]',
        },
        {
          default: () => {
            return [
              h(
                NButton,
                {
                  type: 'primary',
                  text: true,
                  onClick: () => {
                    voteStore.setVoteMenuDataAction({
                      showMenu: true,
                      currentYear: selectedYear.value!,
                      query: {
                        reviewId: row.reviewId,
                        year: selectedYear.value!,
                        partReviewItemId: row.reviewItemId,
                        branchReviewItemId: row.reviewItemId,
                      },
                    })

                    router.push({
                      name: 'talk-about-party-member-Detail',
                      query: {
                        reviewItemId: row.reviewItemId,
                        commentatorId: row.userId,
                        orgId: selectedDeptId.value!,
                        reviewId: row.reviewId,
                      },
                    })
                  },
                },
                {
                  default: () => '查看',
                },
              ),
              // h(
              //   NButton,
              //   {
              //     type: 'primary',
              //     text: true,
              //     onClick: () => {
              //       window.$dialog.create({
              //         type: 'default',
              //         closable: false,
              //         content: '确认删除该条记录？',
              //         showIcon: false,
              //         positiveText: '确认',
              //         negativeText: '取消',
              //         onPositiveClick: () => {
              //           const deleteParams = {
              //             ...formData.value,
              //             deptId: selectedDeptId.value!,
              //             year: selectedYear.value!,
              //             userEvaluationList:
              //               formData.value.userEvaluationList.filter(
              //                 (item: any) => item.id !== row.id
              //               ),
              //           }
              //
              //           editDemocraticReview(deleteParams).then((res) => {
              //             window.$message.success('删除成功！')
              //             loadData()
              //           })
              //         },
              //       })
              //     },
              //   },
              //   {
              //     default: () => '删除',
              //   }
              // ),
            ]
          },
        },
      )
    },
  )
})

const calcOrganizationListTree = computed(() => {
  return recordBookStore.getOrganizationListTreeList
})
const currentYearOptions = computed(() => {
  return recordBookStore.getYearOptions
})
const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})
// 获取当前年份
const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

const isDateDisabled = (ts: number) => {
  const date = new Date(ts)
  const year = date.getFullYear()

  // 如果年份不是目标年份，禁用该日期
  return year !== Number(selectedYear.value)
}

function handleDownLoadFile() {
  fileLoading.value = true
  recordBookStore.fetchDownLoadFile(
    {
      deptId: selectedDeptId.value!,
      year: selectedYear.value!,
      type: currentDownloadFileType.value,
    },
    () => {
      fileLoading.value = false
    },
  )

  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleCancelShowPopConfirm() {
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  selectedDeptId.value = v
  loadData()
  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearSelectedValue(v: string) {
  if (!v) {
    return
  }
  selectedYear.value = v
  loadData()
  recordBookStore.setCurrentSelectedYear(v || '')
}

function handleEdit() {
  isCanEdit.value = true
}

function handleCancelEdit() {
  window.$dialog.create({
    type: 'default',
    closable: false,
    content: '是否确认取消编辑？',
    showIcon: false,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      isCanEdit.value = false
      loadData()
    },
  })
}

function handleSaveEdit() {
  editDemocraticReview({
    ...formData.value,
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
  })
    .then((res) => {
      window.$message.success('修改成功')
    })
    .finally(() => {
      loadData()
      isCanEdit.value = false
    })
}

function loadData() {
  getDemocraticReview({
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
  }).then((res) => {
    originalData.value = res
    formData.value = res
  })
}

onMounted(() => {
  selectedDeptId.value = currentSelectedDept.value.deptId
  selectedYear.value = currentYear.value
  loadData()
})
</script>

<template>
  <div class="p-[20px]">
    <div><span class="font-bold">民主评议党员情况</span></div>

    <div class="flex flex-row justify-between items-center py-[20px]">
      <div class="flex flex-row gap-x-[10px] justify-start items-start">
        <n-tree-select
          v-model:value="selectedDeptId"
          :options="calcOrganizationListTree"
          :show-path="false"
          check-strategy="all"
          children-field="children"
          filterable
          key-field="id"
          label-field="name"
          placeholder="请选择所属党组织"
          style="width: 400px"
          value-field="id"
          @update:value="handleUpdateTreeSelectedValue"
        />
        <n-select
          v-model:value="selectedYear"
          :options="currentYearOptions"
          placeholder="请选择年份"
          size="medium"
          style="width: 200px"
          @update:value="handleUpdateYearSelectedValue"
        />
      </div>
      <div class="flex flex-row gap-x-[10px]">
        <n-button
          v-if="!isCanEdit"
          :disabled="isNoData"
          type="primary"
          @click="handleEdit"
        >
          开启编辑
        </n-button>
        <div v-if="isCanEdit" class="flex flex-row gap-x-[10px]">
          <n-button @click="handleCancelEdit">
            取消编辑
          </n-button>
          <n-button type="primary" @click="handleSaveEdit">
            保存编辑
          </n-button>
        </div>
        <n-popconfirm
          :show="showPopConfirm"
          :show-icon="false"
          placement="left"
        >
          <template #trigger>
            <n-button
              :disabled="isNoData"
              :loading="fileLoading"
              @click="showPopConfirm = !showPopConfirm"
            >
              <template #icon>
                <n-icon size="16">
                  <DownloadRound />
                </n-icon>
              </template>
              下载
            </n-button>
          </template>
          <div class="py-[10px]">
            <n-radio-group v-model:value="currentDownloadFileType">
              <n-radio
                v-for="(fileType, fileTypeIndex) in [
                  { label: 'word', value: 'word' },
                  { label: 'pdf', value: 'pdf' },
                ]"
                :key="fileTypeIndex"
                :value="fileType.value"
              >
                {{ fileType.label }}
              </n-radio>
            </n-radio-group>
          </div>

          <template #action>
            <div class="w-[100%] flex flex-row justify-center items-center">
              <n-button
                size="small"
                type="primary"
                @click="handleDownLoadFile(item)"
              >
                确定
              </n-button>
              <n-button size="small" @click="handleCancelShowPopConfirm">
                取消
              </n-button>
            </div>
          </template>
        </n-popconfirm>
      </div>
    </div>

    <template v-if="isNoData">
      <div class="w-[100%] flex flex-col justify-center items-center">
        <div class="">
          <img :src="emptyImg" alt="" srcset="" />
        </div>
        <div
          class="p-[20px] w-[100%] flex flex-row justify-center items-center box-border pr-[80px]"
        >
          <span class="font-bold text-[#cccccc]">暂无数据</span>
        </div>
      </div>
    </template>

    <template v-else>
      <div class="flex flex-row justify-between items-center">
        <div><span class="font-bold">基本信息</span></div>
      </div>
      <div v-if="isCanEdit">
        <n-form :model="formData" label-placement="left" label-width="140">
          <div class="flex flex-row py-[20px]">
            <n-form-item label="时间：">
              <n-date-picker
                v-model:formatted-value="formData.time"
                :is-date-disabled="isDateDisabled"
                clearable
                placeholder="请选择时间"
                style="width: 240px"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </n-form-item>
            <n-form-item label="地点：">
              <n-input
                v-model:value="formData.location"
                clearable
                placeholder="请输入地点"
                style="width: 240px"
              />
            </n-form-item>
            <n-form-item label="参加评议党员数：">
              <n-input-number
                v-model:value="formData.totalNum"
                :max="999999"
                :min="0"
                :show-button="false"
                :step="1"
                clearable
                disabled
                placeholder="请输入人数"
                style="width: 220px"
              />
              <span class="ml-[6px]">人</span>
            </n-form-item>
          </div>
          <div class="flex flex-row py-[20px]">
            <n-form-item label="优秀等次人数：">
              <n-input-number
                v-model:value="formData.excellentNum"
                :max="999999"
                :min="0"
                :show-button="false"
                :step="1"
                clearable
                disabled
                placeholder="请输入人数"
                style="width: 220px"
              />
              <span class="ml-[6px]">人</span>
            </n-form-item>
            <n-form-item label="合格等次人数：">
              <n-input-number
                v-model:value="formData.qualifiedNum"
                :max="999999"
                :min="0"
                :show-button="false"
                :step="1"
                clearable
                disabled
                placeholder="请输入人数"
                style="width: 220px"
              />
              <span class="ml-[6px]">人</span>
            </n-form-item>
            <n-form-item label="基本合格等次人数：">
              <n-input-number
                v-model:value="formData.baseQualifiedNum"
                :max="999999"
                :min="0"
                :show-button="false"
                :step="1"
                clearable
                disabled
                placeholder="请输入人数"
                style="width: 220px"
              />
              <span class="ml-[6px]">人</span>
            </n-form-item>
            <n-form-item label="不合格等次人数：">
              <n-input-number
                v-model:value="formData.unqualifiedNum"
                :max="999999"
                :min="0"
                :show-button="false"
                :step="1"
                clearable
                disabled
                placeholder="请输入人数"
                style="width: 220px"
              />
              <span class="ml-[6px]">人</span>
            </n-form-item>
          </div>
        </n-form>
      </div>

      <div v-else class="flex flex-col gap-y-[20px]">
        <div class="flex flex-row py-[20px]">
          <div class="flex flex-row">
            <div class="w-[140px] text-right">
              <span>时间：</span>
            </div>
            <div class="w-[220px]">
              <span>{{ formData.time }}</span>
            </div>
          </div>
          <div class="flex flex-row">
            <div class="w-[140px] text-right">
              <span>地点：</span>
            </div>
            <div class="w-[220px]">
              <span>{{ formData.location }}</span>
            </div>
          </div>
          <div class="flex flex-row">
            <div class="w-[140px] text-right">
              <span>参加评议党员数：</span>
            </div>
            <div class="w-[220px]">
              <span>{{ formData.totalNum }}&emsp;人</span>
            </div>
          </div>
        </div>

        <div class="flex flex-row py-[20px]">
          <div class="flex flex-row">
            <div class="w-[140px] text-right">
              <span>优秀等次人数：</span>
            </div>
            <div class="w-[220px]">
              <span>{{ formData.excellentNum }}&emsp;人</span>
            </div>
          </div>
          <div class="flex flex-row">
            <div class="w-[140px] text-right">
              <span>合格等次人数：</span>
            </div>
            <div class="w-[220px]">
              <span>{{ formData.qualifiedNum }}&emsp;人</span>
            </div>
          </div>
          <div class="flex flex-row">
            <div class="w-[140px] text-right">
              <span>基本合格等次人数：</span>
            </div>
            <div class="w-[220px]">
              <span>{{ formData.baseQualifiedNum }}&emsp;人</span>
            </div>
          </div>
          <div class="flex flex-row">
            <div class="w-[140px] text-right">
              <span>不合格等次人数：</span>
            </div>
            <div class="w-[220px]">
              <span>{{ formData.unqualifiedNum }}&emsp;人</span>
            </div>
          </div>
        </div>
      </div>

      <div>
        <div><span class="font-bold">评议结果</span></div>
        <div>
          <n-data-table
            :columns="tableColumns"
            :data="formData.userEvaluationList"
            class="mt-[20px]"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped></style>
