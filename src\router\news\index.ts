import type { RouteRecordRaw } from 'vue-router'

const NewsRoute: RouteRecordRaw = {
  path: 'newsPage',
  name: 'newsPage',
  meta: { title: '资讯管理', icon: 'notice', category: 'run', order: 2 },
  redirect: { name: 'newsListPage' },
  component: () => import('@/views/main/news/Index.vue'),
  children: [
    {
      path: 'newsListPage',
      name: 'newsListPage',
      component: () => import('@/views/main/news/list/NewsList.vue'),
    },
    // {
    //   path: 'add',
    //   name: 'noticeAdd',
    //   component: () =>
    //     import('@/views/main/news/list/cpns/Detail.vue'),
    // },
  ],
}
export default NewsRoute
