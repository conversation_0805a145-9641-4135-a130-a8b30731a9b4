<template>
  <div>
    <div class="title">
      创建问卷
    </div>
    <!-- <div class="greySplit">
      <div class="subTitle"><span>1</span>基本信息</div>
    </div>
    <NForm
      ref="formRef"
      :model="questionnaire"
      :rules="formRules"
      label-placement="left"
      require-mark-placement="left"
      class="w-[675px]"
    >
      <NFormItem
        class="text-13px text-highlightBlue mt-4 ml-5 mr-4"
        label="问卷名称："
        path="title"
      >
        <NInput
          v-model:value="questionnaire.title"
          class="mx-10px"
          placeholder="请输入评议项名称"
        />
      </NFormItem>

      <NFormItem
        class="text-13px text-highlightBlue mt-4 ml-5 mr-4"
        label="问卷说明："
        path="reviewItemRemark"
      >
        <NInput
          v-model:value="questionnaire.description"
          class="mx-10px"
          placeholder="请输入问卷说明"
          type="textarea"
        />
      </NFormItem>
    </NForm> -->
    <div class="greySplit">
      <div class="subTitle">
        问卷内容
      </div>
    </div>
    <NForm
      :show-feedback="false"
      class="relative"
      label-placement="left"
      :model="questionnaire"
    >
      <NCard
        :bordered="true"
        hoverable
        class="ncard"
        title="题目列表"
        :segmented="{
          content: true,
          footer: 'soft',
        }"
        header-style="font-size:14px;height:48px"
        content-style="padding:0px"
      >
        <NSpin :show="loading">
          <NGrid :cols="8">
            <!-- 预览区域  -->
            <NGi :span="5" class="pr-25px">
              <NCard
                :segmented="{
                  content: true,
                  footer: true,
                }"
                footer-style="font-size:14px;height:48px;display:flex;align-items:center"
                class="h-full"
              >
                <TransitionGroup
                  v-if="questionnaire.subjectList!.length"
                  tag="ul"
                  name="fade"
                >
                  <div
                    v-for="(item, index) in questionnaire.subjectList"
                    :key="index"
                  >
                    <optionItems
                      :key="index"
                      :data="item"
                      :index="index"
                      :total="questionnaire.subjectList!.length"
                      :on-click="
                        () => {
                          clearEditing()
                          item.editing = true
                        }
                      "
                      :on-upward="() => swapItem(index, index - 1)"
                      :on-downward="() => swapItem(index, index + 1)"
                      :on-delete="() => questionnaire.subjectList!.splice(index, 1)
                      "
                      :on-copy="() => copyQuestion(index, item)"
                    />
                  </div>
                </TransitionGroup>
                <div v-else class="flex justify-center p-10">
                  <img src="@/assets/image/vote/emptyQuestion.png">
                </div>
                <template #footer>
                  <n-button size="small" @click="addQuestion()">
                    <template #icon>
                      <n-icon :size="10">
                        <IosAdd />
                      </n-icon>
                    </template>
                    <div class="text-xs">
                      新增题目
                    </div>
                  </n-button>
                </template>
              </NCard>
            </NGi>

            <!-- 编辑区域 -->
            <NGi :span="3" class="px-20px">
              <ConfigForm
                v-if="editingIndex > -1"
                v-model:value="questionnaire.subjectList![editingIndex]"
                class="sticky top-80px"
              />
            </NGi>
          </NGrid>
        </NSpin>
      </NCard>
      <NGrid :cols="8">
        <NGi :span="5">
          <div class="ml-5" />
        </NGi>
      </NGrid>
    </NForm>
    <div class="line" />
    <div
      v-if="
        route.query.questionnaireStatus === QUESTIONNAIRESTATUSMAP.NOPUBLISH
      "
      class="flex ml-9"
    >
      <NButton
        v-if="route.query.action !== 'view'"
        type="primary"
        class="!w-[80px] !h-[30px]"
        @click="save"
      >
        提交
      </NButton>
      <NButton
        class="!ml-4 !mb-4 !w-[80px] !h-[30px]"
        :on-click="
          () => {
            back()
          }
        "
      >
        取消
      </NButton>
    </div>
    <div v-else class="flex ml-9">
      <NButton
        class="!ml-4 !mb-4 !w-[80px] !h-[30px]"
        :on-click="
          () => {
            back()
          }
        "
      >
        返回
      </NButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { NButton } from 'naive-ui'
import { cloneDeep } from 'lodash-es'
// import type { PropType } from 'vue'
// import { Add, AddAlt, Close } from '@vicons/carbon'
import { IosAdd } from '@vicons/ionicons4'
import optionItems from './optionItems.vue'
import ConfigForm from './configForm.vue'
import type {
  Questionnaire,
  QuestionnaireSubj,
} from '@/services/cloud-service/questionnaire/types'
import {
  addQuestionnaireSubject,
  getSubjectListById,
  modifyQuestionnaireSubject,
} from '@/services/cloud-service/questionnaire/index'
// import { formRules } from './config'
import {
  QUESTIONNAIRESTATUSMAP,
  QUESTIONTYPE2 as QUESTIONTYPE,
} from '@/constant'
import { removeIdProperties } from '@/utils/utils'

const loading = ref(false)
const route = useRoute()
const router = useRouter()
// const formRef = ref<InstanceType<typeof NForm>>()
const questionnaire = reactive<Questionnaire>({
  title: '', // 名称
  description: '', // 简介
  coverUrl: '', // 图片
  orgidList: [], // 组织范围
  beginTime: '',
  endTime: '',
  // 题目列表
  subjectList: [],
})

/** 正在编辑的题目索引 */
const editingIndex = computed(() =>
  questionnaire.subjectList!.findIndex(item => item.editing),
)

getDetails()

function getDetails() {
  if (route.query.id) {
    getSubjectListById(route.query.id as string).then((res) => {
      questionnaire.subjectList = cloneDeep(res.subjectList)

      if (route.query.action === 'copy') {
        // 复制新建删除 id
        removeIdProperties(questionnaire.subjectList)
      }
    })
  }
}

/** 排他 */
function clearEditing() {
  questionnaire.subjectList!.forEach(
    (item: QuestionnaireSubj) => (item.editing = false),
  )
}

/** 交换 */
function swapItem(a: number, b: number) {
  const temp = questionnaire.subjectList![a]
  questionnaire.subjectList![a] = questionnaire.subjectList![b]
  questionnaire.subjectList![b] = temp
}

/** 保存 */
async function save() {
  loading.value = true

  // formRef.value?.validate((errors: any) => {
  try {
    // if (!errors) {
    const showTip = false
    // 校验表单并设置sort

    questionnaire.subjectList!.forEach(
      (question: QuestionnaireSubj, qi: number) => {
        if (question.id) {
          // showTip = true
        }
        if (
          question.subjectType === null
          || question.subjectType === ''
          || question.subjectType === undefined
        ) {
          clearEditing()
          setEditing(qi)
          throw new Error('请选择题型')
        }
        if (!question.subjectName.trim()) {
          clearEditing()
          setEditing(qi)
          throw new Error('请输入题目信息')
        }
        if (
          !question.maxWordCount
          && question.subjectType === QUESTIONTYPE.FILLED
        ) {
          clearEditing()
          setEditing(qi)
          throw new Error('请输入字数上限')
        }
        if (
          [QUESTIONTYPE.RADIO, QUESTIONTYPE.MULTI].includes(
            question.subjectType,
          )
          && !question.optionList?.length
        ) {
          clearEditing()
          setEditing(qi)
          throw new Error('请添加选项')
        }
        // nextTick(() => {
        //   emitter.emit('notification-template-evaluation-validate')
        // })
        question.sort = qi + 1
        question.optionList?.forEach((choice, ci) => {
          if (!choice.optionContent.trim()) {
            clearEditing()
            setEditing(qi)
            throw new Error('请输入选项内容')
          }

          choice.sort = ci + 1
        })
      },
    )

    const fn = async() => {
      if (route.query.id && route.query.action === 'modify') {
        await modifyQuestionnaireSubject({
          questionnaireId: route.query.id as string,
          subjectList: questionnaire.subjectList!,
        })
        window.$message.success('保存成功')
      } else {
        await addQuestionnaireSubject({
          questionnaireId: route.query.id as string,
          subjectList: questionnaire.subjectList!,
        })
        window.$message.success('保存成功')
      }

      back()
    }
    if (showTip) {
      window.$dialog.info({
        title: '提示',
        content: '已推送的评价问卷不会被更新，是否继续？',
        positiveText: '是',
        negativeText: '否',
        onPositiveClick: fn,
      })
    } else {
      fn()
    }
    // } else {
    //   window.$message.error('请校验表单必填项')
    // }
  } catch (e) {
    window.$message.error((e as Error).message)
  } finally {
    loading.value = false
  }
  // })
}

/** 复制题目 */
function copyQuestion(index: number, item: QuestionnaireSubj) {
  clearEditing()
  const data = cloneDeep(item)
  data.renderId = `question-${Date.now()}`
  // 移除题目和选项的 id
  delete data.questionnaireId
  data.optionList?.forEach((choice, i) => {
    choice.renderId = `choice-${Date.now()}-${i}`
    delete choice.subjectId
  })
  questionnaire.subjectList!.splice(index + 1, 0, data)
  setEditing(index + 1)
}
function back() {
  router.push({ name: 'questionnaireList' })
}
/** 新增题目 */
function addQuestion() {
  clearEditing()
  questionnaire.subjectList!.push({
    renderId: `question-${Date.now()}`,
    subjectType: '',
    subjectName: '',
    sort: questionnaire.subjectList!.length + 1,
    isRequired: '1',
    // questionDimension: '',
    editing: true,
    // questionObject: null,
  })
}

/** 设置第 n 个题目为编辑中 */
function setEditing(i: number) {
  questionnaire.subjectList![i].editing = true
}
</script>

<style lang="scss" scoped>
.greySplit {
  width: calc(100% - 40px);
  height: 48px;
  background: #f5f6f8;
  margin-left: 20px;
  margin-top: 20px;
  display: flex;
  align-items: center;

  .subTitle {
    position: relative;
    padding-left: 28px;
    font-size: 15px;
    font-weight: 600;
  }

  .subTitle::before {
    content: '';
    /* 创建伪元素 ::before */
    display: block;
    /* 将其显示为块级元素 */
    width: 3px;
    /* 设置竖线的宽度 */
    height: 16px;
    /* 根据需求调整高度（去除上下边距）*/
    background-color: #cb0000;
    /* 设置竖线颜色 */
    position: absolute;
    /* 设置绝对定位 */
    left: 15px;
    top: 3px;
    /* 根据需求调整与文本之间的距离 */
    border-radius: 2px;
  }
}

.title {
  font-weight: 700;
  margin-left: 20px;
  margin-top: 27px;
}

.ncard {
  margin: 20px;
  width: calc(100% - 40px);
}

.line {
  width: 100%;
  border-top: 1px solid #f2f3f6;
  margin: 52px 0 15px 0;
}

:deep(.n-form-item) .n-form-item-label,
:deep(.n-form-item) .n-input {
  font-size: 14px;
}
</style>
