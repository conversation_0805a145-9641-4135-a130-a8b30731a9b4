import type { RequestTypes } from './types'
import { commonReq } from '@/services/request'

// 查询年度工作总结详情
export function queryAnnualSummaryDetail(
  params: RequestTypes.QueryAnnualSummaryDetailType,
) {
  return commonReq.get<RequestTypes.QueryAnnualSummaryDetailResponseType>({
    url: '/party-affairs/work-record-book/annual-summary/detail',
    params,
  })
}

// 编辑年度工作总结记录
export function editAnnualSummary(data: RequestTypes.UpdateAnnualSummaryType) {
  return commonReq.put({
    url: '/party-affairs/work-record-book/annual-summary',
    data,
  })
}

// 新增年度工作总结记录
export function addAnnualSummary(data: RequestTypes.AddAnnualSummaryType) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/annual-summary',
    data,
  })
}
