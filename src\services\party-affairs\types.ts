import type { UploadFileInfo } from 'naive-ui'
/** 党务通分类列表 */
export interface AffairsCategoryList {
  /** 学习得分 */
  studyScore?: number | null
  /** 主键id */
  id?: number | null
  /** 分类名称 */
  name?: null | string
  /** 父级id */
  parentId?: number | null
  /** 排序 */
  sort?: number | null
  /** 简介 */
  description: string
}

/** 新增 / 编辑 党务通分类 */
export interface AddOrEditAffairsCategory {
  /** 学习得分 */
  studyScore?: number | null
  /** 分类名称 */
  name?: null | string
  /** 父级id */
  parentId?: number | null
  /** 排序 */
  sort?: number | null
  /** 主键Id */
  id?: string | null
  description?: string | null
}

/** 移动党务通分类 */
export interface MoveAffairsCategory {
  /** 主键id */
  id: string
  /** 上移 -1、下移 +1, 默认值为0 */
  move: string
  /** 分类名称 */
  name?: null | string
  /** 父级id */
  parentId: number
  /** 排序 */
  sort?: number | null
  studyScore?: number | null
  description?: string | null
}

/** 类别列表 */
export interface AffairsListItem {
  id: string
  categoryId: string
  annexName: string
  annexUrl: string
  fileList?: fileItem[]
  fileIds: string[]
  createTime: string
  isTop: string
  isOutside: string
  linkUrl?: string
  content: string
  pictureUrl: string
}
export interface fileItem {
  fileName: string
  id: string
  original: string
  percentage?: number
  url?: string
  name?: string
}

/** 新增 / 编辑 党务通  */
export interface AddOrEditAffairsItem {
  id?: number | string
  themeName: string
  categoryId: number | string
  annexUrl: string
  pictureUrl: string
  fileList: Array<fileItem>
  content: string
  isTop: string
  isOutside: string
  linkUrl?: string
  createTime?: string
}
export interface uploadFileItem {
  fileId: string
  bucketName: string
  url: string
  fileName: string
}
export interface uploadFileItemNew extends UploadFileInfo {
  fileName?: string | undefined
  original?: string | undefined
}
