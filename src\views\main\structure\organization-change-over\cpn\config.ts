import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  transferType: {
    required: true,
    message: '调动类型不能为空',
    trigger: 'change',
    type: 'number',
  },
  paymentTime: {
    required: true,
    message: '党费缴纳时间不能为空',
    trigger: 'change',
  },
  oldDeptId: {
    required: true,
    message: '当前组织不能为空',
    trigger: 'change',
  },
  userId: {
    required: true,
    message: '调动人选择不能为空',
    trigger: 'change',
  },
  newDeptId: {
    required: true,
    message: '目标组织不能为空',
    trigger: 'change',
  },
  reason: {
    required: true,
    message: '调动原因不能为空',
    trigger: 'input',
  },
  // letterId: {
  //   required: true,
  //   message: '介绍信不能为空',
  //   trigger: 'change',
  // },
  // proveId: {
  //   required: true,
  //   message: '党费缴纳证明不能为空',
  //   trigger: 'change',
  // },
}

export const formRulesForCheck: FormRules = {
  phaseStatus: {
    required: true,
    message: '审核操作不能为空',
    trigger: 'change',
    type: 'number',
  },
  remark: {
    required: true,
    message: '审核说明不能为空',
    trigger: 'input',
  },
}
