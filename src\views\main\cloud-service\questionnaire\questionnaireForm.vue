<template>
  <n-form
    ref="formRef"
    :model="formDataReactive"
    :rules="formRules"
    class="mt-[25px]"
    label-align="right"
    label-placement="left"
    :disabled="props.type === 'view'"
    label-width="130"
    require-mark-placement="left"
  >
    <n-form-item label="问卷名称" path="title" required>
      <n-input
        v-model:value="formDataReactive.title"
        maxlength="100"
        placeholder="请输入问卷名称"
      />
    </n-form-item>
    <n-form-item label="问卷图片：" path="coverUrl">
      <ImgUploader
        v-if="props.type !== 'view'"
        v-model:oldImgUrl="formDataReactive.coverUrl"
        :width="640"
        :height="445"
        :need-cropper="false"
        :is-readonly="
          props.type === 'modify' &&
            (props.status === QUESTIONNAIRESTATUSMAP.ING ||
              props.status === QUESTIONNAIRESTATUSMAP.NOSTARTED ||
              props.status === QUESTIONNAIRESTATUSMAP.ENDED)
        "
        @done="handleCoverDone"
        @delete="handleCoverDelete"
      />
      <n-image
        v-else
        class="w-[250px] h-[150px]"
        :src="baseUrl + formDataReactive.coverUrl"
      />
    </n-form-item>
    <n-form-item label="问卷说明" path="description" required>
      <n-input
        v-model:value="formDataReactive.description"
        type="textarea"
        maxlength="1000"
        placeholder="请输入问卷说明"
      />
    </n-form-item>
    <n-form-item label="问卷管理组织：" path="deptId">
      <n-cascader
        v-model:value="formDataReactive.deptId"
        placeholder="请选择组织"
        :options="(organizationCurrentListTree as any)"
        value-field="deptId"
        label-field="name"
        children-field="children"
        check-strategy="parent"
        :show-path="false"
        clearable
        filterable
        :disabled="
          (props.type === 'modify' &&
            (props.status === QUESTIONNAIRESTATUSMAP.ING ||
              props.status === QUESTIONNAIRESTATUSMAP.NOSTARTED ||
              props.status === QUESTIONNAIRESTATUSMAP.ENDED)) ||
            props.type === 'view'
        "
      />
    </n-form-item>
    <n-form-item label="父子联动：">
      <n-radio-group
        v-model:value="formDataReactive.linkageType"
        :disabled="
          (props.type === 'modify' &&
            (props.status === QUESTIONNAIRESTATUSMAP.ING ||
              props.status === QUESTIONNAIRESTATUSMAP.NOSTARTED ||
              props.status === QUESTIONNAIRESTATUSMAP.ENDED)) ||
            props.type === 'view'
        "
        @change="selectLinkageType"
      >
        <n-radio-button value="0">
          全部
        </n-radio-button>
        <n-radio-button value="1">
          父组织
        </n-radio-button>
        <n-radio-button value="2">
          子组织
        </n-radio-button>
      </n-radio-group>
    </n-form-item>
    <n-form-item label="组织名称：" path="orgidList">
      <n-cascader
        v-model:value="formDataReactive.orgidList"
        placeholder="请选择组织"
        :options="(organizationCurrentListTree as any)"
        value-field="deptId"
        label-field="name"
        children-field="children"
        :check-strategy="checkStrategyMap[formDataReactive.linkageType as '0'|'1'|'2']"
        :show-path="false"
        clearable
        filterable
        multiple
        :disabled="
          (props.type === 'modify' &&
            (props.status === QUESTIONNAIRESTATUSMAP.ING ||
              props.status === QUESTIONNAIRESTATUSMAP.NOSTARTED ||
              props.status === QUESTIONNAIRESTATUSMAP.ENDED)) ||
            props.type === 'view'
        "
      />
    </n-form-item>
    <n-form-item label="开始时间：" path="beginTime">
      <n-date-picker
        v-model:formatted-value="formDataReactive.beginTime"
        value-format="yyyy-MM-dd HH:mm:ss"
        type="datetime"
        style="width: 580px"
        clearable
        placeholder="请选择开始时间"
        :disabled="
          (props.type === 'modify' &&
            (props.status === QUESTIONNAIRESTATUSMAP.ING ||
              props.status === QUESTIONNAIRESTATUSMAP.NOSTARTED ||
              props.status === QUESTIONNAIRESTATUSMAP.ENDED)) ||
            props.type === 'view'
        "
        @clear="() => (formDataReactive.beginTime = null)"
      />
    </n-form-item>
    <n-form-item label="结束时间：" path="endTime">
      <n-date-picker
        v-model:formatted-value="formDataReactive.endTime"
        value-format="yyyy-MM-dd HH:mm:ss"
        type="datetime"
        style="width: 580px"
        clearable
        placeholder="请选择结束时间"
        :disabled="
          (props.type === 'modify' &&
            (props.status === QUESTIONNAIRESTATUSMAP.ING ||
              props.status === QUESTIONNAIRESTATUSMAP.NOSTARTED ||
              props.status === QUESTIONNAIRESTATUSMAP.ENDED)) ||
            props.type === 'view'
        "
        @clear="() => (formDataReactive.endTime = null)"
      />
    </n-form-item>
  </n-form>
</template>
<script setup lang="ts">
import { NForm } from 'naive-ui'
import { formRules } from './config'
import type { Questionnaire } from '@/services/cloud-service/questionnaire/types'
import {
  addQuestionnaire,
  getQuestionnaireById,
  modifyQuestionnaire,
} from '@/services/cloud-service/questionnaire'
import type { uploadFileItem } from '@/services/types'
import { uploadImg } from '@/services'
import { useCurrentOrganizationListOptionsNew } from '@/hooks/use-select-options'
import { QUESTIONNAIRESTATUSMAP } from '@/constant'

interface Props {
  type?: string
  status?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
  status: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<Questionnaire & { linkageType?: string }>({
  coverUrl: '',
  linkageType: '0',
})

const checkStrategy = ref('all')

// watch(
//   () => formDataReactive.linkageType,
//   (val) => {
//     formDataReactive.orgidList = []
//   }
// )

const { organizationCurrentListTree } = useCurrentOrganizationListOptionsNew() // 获取组织列表

const loading = ref(false)
const baseUrl = import.meta.env.VITE_API_BASE

const checkStrategyMap = {
  0: 'all',
  1: 'parent',
  2: 'child',
}

const formRef = ref<InstanceType<typeof NForm>>()
onMounted(async() => {
  loading.value = true
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    try {
      const res = await getQuestionnaireById(props.id)
      Object.assign(formDataReactive, res)

      // 兼容旧数据、父子联动默认展示全部
      if (
        !formDataReactive.linkageType
        || formDataReactive.linkageType === ''
      ) {
        formDataReactive.linkageType = '0'
      }
      loading.value = false
    }
    catch (error) {}
  }
})

function selectLinkageType() {
  formDataReactive.orgidList = []
}
/**
 * 上传图片
 * @param {any} file:File
 */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formDataReactive.coverUrl === '') {
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formDataReactive.coverUrl = data.url
      }
    }
  }
  catch (error) {}
}
/**
 * 删除图片
 */
function handleCoverDelete() {
  formDataReactive.coverUrl = ''
}

// 验证表单,调用接口

async function validateAndSave() {
  loading.value = true
  const errors = await new Promise((resolve) => {
    formRef.value?.validate((errors: any) => {
      resolve(errors)
    })
  })

  if (!errors) {
    const saveFunction
      = props.type === 'modify' && props.id
        ? modifyQuestionnaire
        : addQuestionnaire
    const saveData = { ...formDataReactive }
    try {
      const res = await saveFunction(saveData)
      if (res) {
        window.$message.success('保存成功')
        emits('saved')
      }
      loading.value = false
    }
    catch (error) {}
  }
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>

<style lang="scss" scoped></style>
