import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { PartyBuildingEvaluationTableItemForBranch } from '@/services/publicity/vote/evaluationForBranch/type'

export function getTableColumns(
  optionColumnRenderer: (
    row: PartyBuildingEvaluationTableItemForBranch
  ) => VNodeChild,
): DataTableColumns<PartyBuildingEvaluationTableItemForBranch> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      key: 'title',
      title: '考核名称',
      width: '55%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'evaluationYear',
      title: '考核时间',
      width: '15%',
    },
    {
      key: 'status',
      title: '状态',
      width: '15%',
      render: (row) => {
        return row.status ? row.status : '--'
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '10%',
      render: row => optionColumnRenderer(row),
    },
  ]
}
