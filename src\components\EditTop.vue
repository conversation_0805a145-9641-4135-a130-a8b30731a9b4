<script setup lang="ts"></script>
<template>
  <div
    class="flex items-center justify-between box-border bg-[#fff] py-[18px] pr-[24px] pl-[22px] border-b-[1px] border-[#e6e7e8]"
  >
    <div class="left">
      <slot name="left" />
    </div>
    <div class="mid">
      <slot name="mid" />
    </div>
    <div class="right">
      <slot name="right" />
    </div>
  </div>
</template>
<style lang="scss" scoped>
:deep(.n-button) {
  height: 30px !important;
}

.left,
.right {
  :deep(.n-button:nth-child(n + 2)) {
    margin-left: 8px;
  }
}

.mid {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
}

.right {
  :deep(.n-button) {
    // border-radius: 15px;
    padding: 0 18px;
  }
}
</style>
