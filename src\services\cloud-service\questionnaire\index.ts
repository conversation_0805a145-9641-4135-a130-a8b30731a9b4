import type { Questionnaire, QuestionnaireSubj } from './types'
import { commonReq, downloadReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

// 获取问卷列表
export function getQuestionnaireList(
  params: { title?: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<Questionnaire[]>>({
    url: '/party-affairs/backend/v1/partyaffair_questionnaire/page',
    params,
  })
}

/**
 * 根据问卷id获取问卷详情
 */
export function getQuestionnaireById(id: string) {
  return commonReq.get<Questionnaire>({
    url: `/party-affairs/backend/v1/partyaffair_questionnaire/${id}`,
  })
}

/**
 * 新增问卷
 */
export function addQuestionnaire(data: Questionnaire) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/partyaffair_questionnaire',
    data,
  })
}

/**
 * 修改问卷
 */
export function modifyQuestionnaire(data: Questionnaire) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/partyaffair_questionnaire',
    data,
  })
}

/**
 * 删除问卷
 */
export function delQuestionnaire(data: { id: string }) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/partyaffair_questionnaire',
    params: {
      ids: data.id,
    },
  })
}

/**
 * 发布问卷
 */
export function publishQuestionnaire(data: { id: string }) {
  return commonReq.put({
    url: `/party-affairs/backend/v1/partyaffair_questionnaire/publish?id=${data.id}`,
  })
}

/**
 * 取消发布问卷
 */
export function cancelPublishQuestionnaire(data: { id: string }) {
  return commonReq.put({
    url: `/party-affairs/backend/v1/partyaffair_questionnaire/cancel_publish?id=${data.id}`,
    // data,
  })
}

/**
 * 导出问卷
 */
export function exportQuestionnaire(params: { id: string }) {
  return downloadReq.get({
    url: '/party-affairs/backend/v1/partyaffair_questionnaire/detail/export',
    params,
    responseType: 'blob',
  })
}

/**
 * 根据问卷id获取问卷题目
 */
export function getSubjectListById(id: string) {
  return commonReq.get<Questionnaire>({
    url: `/party-affairs/backend/v1/partyaffair_questionnaire_subject/${id}`,
  })
}

/**
 * 修改问卷题目
 */
export function modifyQuestionnaireSubject(data: {
  questionnaireId: string
  subjectList: QuestionnaireSubj[]
}) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/partyaffair_questionnaire_subject',
    data,
  })
}

/**
 * 新增问卷题目
 */
export function addQuestionnaireSubject(data: {
  questionnaireId: string
  subjectList: QuestionnaireSubj[]
}) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/partyaffair_questionnaire_subject',
    data,
  })
}
