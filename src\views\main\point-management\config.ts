import type { DataTableColumns } from 'naive-ui'
import type { VNodeChild } from 'vue'
import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { PointTableItemType } from '@/services/point-management/types'

// 积分列表
export function getIntegralColumns(
  optionColumnRenderer: (row: PointTableItemType) => VNodeChild,
): TableColumns<PointTableItemType> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
      width: '60',
    },
    {
      key: 'userName',
      title: '姓名',
    },
    {
      key: 'phone',
      title: '手机号',
    },
    {
      key: 'political',
      title: '人员类别',
    },
    {
      key: 'orgName',
      title: '所属党组织',
    },
    {
      key: 'creditSum',
      title: '积分',
    },
    {
      key: 'updateTime',
      title: '更新时间',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '80',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 积分明细列表
export function getIntegralDetailColumns(): DataTableColumns {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
      width: '60',
    },
    {
      key: 'score',
      title: '积分变动',
    },
    {
      key: 'creditSum',
      title: '总积分',
    },
    {
      key: 'recordType',
      title: '模块',
    },
    {
      key: 'newsTitle',
      title: '内容项',
    },
    {
      key: 'updateTime',
      title: '时间',
      width: '160',
    },
  ]
}
