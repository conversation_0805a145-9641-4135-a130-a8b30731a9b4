<!--
 * @Description: 手机预览组件
 * @Author: 朱备 <<EMAIL>>
 * @Date: 2021-11-23 17:15:24
 * @LastEditTime: 2021-11-23 19:27:27
 * @LastEditors: 朱备 <<EMAIL>>
-->
<template>
  <div v-show="show" class="mobile-preview">
    <div v-if="qrcodeUrl.length" class="qrcode">
      <img :src="qrcodeUrl">
      <div class="qrcode-text">
        手机扫码预览
      </div>
    </div>

    <div class="frame">
      <iframe :src="previewUrl" frameborder="0" />

      <div class="close" @click="emits('close')">
        <svg
          style="width: 40px; height: 40px; color: #fff"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 512 512"
        >
          <path
            d="M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192s192-86 192-192z"
            fill="none"
            stroke="currentColor"
            stroke-miterlimit="10"
            stroke-width="32"
          />
          <path
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="32"
            d="M320 320L192 192"
          />
          <path
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="32"
            d="M192 320l128-128"
          />
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  qrcodeUrl: {
    type: String,
    default: '',
  },
  previewUrl: {
    type: String,
    default: '',
  },
  show: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['close'])
</script>

<style lang="scss" scoped>
.mobile-preview {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: rgba($color: #000000, $alpha: 0.47);
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  .qrcode {
    background: #fff;
    margin-right: 80px;
    width: 310px;
    height: 320px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .qrcode-text {
      font-size: 20px;
    }
  }

  .frame {
    width: 375px;
    height: 667px;
    position: relative;

    iframe {
      width: 100%;
      height: 100%;
      border-radius: 28px;
    }

    .close {
      position: absolute;
      top: -5px;
      right: -60px;
      cursor: pointer;
    }
  }
}
</style>
