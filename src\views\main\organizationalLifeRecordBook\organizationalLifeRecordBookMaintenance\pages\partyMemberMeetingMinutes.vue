<script lang="ts" setup>
import { N<PERSON>utton, NForm, NIcon, type TreeSelectOption } from 'naive-ui'
import { AddTwotone, DownloadRound } from '@vicons/material'
import { useRecordBookStore } from '@/store/recordBook'
import { getPartyMemberMinutesColumns } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/config'
import type {
  BranchBasicOrganizationConditionUserType,
  PartyMemberMeetingMinutesItemType,
} from '@/services/recordBookMaintenance/types'
import { partyMemberMinutesFormRules } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/formRules'
import {
  addPartyMemberMinutes,
  deletePartyMemberMinutes,
  editPartyMemberMinutes,
  getPartyMemberMinutesDetail,
  getPartyMemberMinutesList,
  getUserByDepartmentId,
} from '@/services/recordBookMaintenance'

const partyMemberFormRef = ref<InstanceType<typeof NForm>>()

const recordBookStore = useRecordBookStore()
const currentYearOptions = computed(() => {
  return recordBookStore.getYearOptions
})

const fileLoading = ref<boolean>(false)
const drawerTitle = ref<string>('新增')
const actionType = ref<'add' | 'edit' | 'view'>('add')
const showAddPartyMemberModal = ref<boolean>(false)
const pageNum = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const total = ref(0)
const selectedDeptId = ref<string | null>(null)
const selectedYear = ref<string | null>(null)
const tableData = ref([])

const currentDownloadFileType = ref<'word' | 'pdf'>('word')
const showPopConfirm = ref<boolean>(false)

const formDataRef = ref<
PartyMemberMeetingMinutesItemType & { minutesTime?: Array<string> | null }
>({
  title: '',
  startTime: null,
  endTime: null,
  hostId: null,
  attendanceNum: null,
  mainSuggestion: '',
  minutesTime: null,
})

const calcOrganizationListTree = computed(() => {
  return recordBookStore.getOrganizationListTreeList
})

const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})
// 获取当前年份
const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

const hostOptions = computed(() => {
  const options = []
  for (let i = 0; i < 100; i++) {
    options.push({
      label: i,
      value: i,
    })
  }
  return options
})

const userList = ref<BranchBasicOrganizationConditionUserType[]>([])
const userOptions = computed(() => {
  return userList.value.map((item) => {
    return {
      label: item.trueName,
      value: item.userId,
    }
  })
})

const tableColumns = getPartyMemberMinutesColumns((row, index) => {
  return [
    h(
      'div',
      {
        class: 'flex flex-row gap-x-[10px]',
      },
      {
        default: () => [
          h(
            NButton,
            {
              text: true,
              color: '#AC241D',
              onClick: () => {
                actionType.value = 'view'
                drawerTitle.value = '查看'
                getPartyMemberMinutesDetail(row.id!).then((res) => {
                  formDataRef.value = res as any
                  formDataRef.value.minutesTime = [
                    res.startTime as string,
                    res.endTime as string,
                  ]
                })
                showAddPartyMemberModal.value = true
              },
            },
            {
              default: () => '查看',
            },
          ),
          h(
            NButton,
            {
              text: true,
              color: '#AC241D',
              onClick: () => {
                actionType.value = 'edit'
                drawerTitle.value = '编辑'
                getPartyMemberMinutesDetail(row.id!).then((res) => {
                  formDataRef.value = res as any
                  formDataRef.value.minutesTime = [
                    res.startTime as string,
                    res.endTime as string,
                  ]
                })
                showAddPartyMemberModal.value = true
              },
            },
            {
              default: () => '编辑',
            },
          ),
          h(
            NButton,
            {
              text: true,
              color: '#AC241D',
              onClick: () => {
                window.$dialog.create({
                  type: 'default',
                  closable: false,
                  content: '确认删除该条记录？',
                  showIcon: false,
                  positiveText: '确认',
                  negativeText: '取消',
                  onPositiveClick: () => {
                    deletePartyMemberMinutes(row.id!).then(() => {
                      window.$message.success('删除成功！')
                      loadData()
                    })
                  },
                })
              },
            },
            {
              default: () => '删除',
            },
          ),
        ],
      },
    ),
  ]
})

function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  pageNum.value = 1
  selectedDeptId.value = v
  loadData()
  fetchUserList()
  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearSelectedValue(v: string) {
  if (!v) {
    return
  }
  pageNum.value = 1
  selectedYear.value = v
  loadData()
  recordBookStore.setCurrentSelectedYear(v || '')
}

function handleDownLoadFile() {
  fileLoading.value = true
  recordBookStore.fetchDownLoadFile(
    {
      deptId: selectedDeptId.value!,
      year: selectedYear.value!,
      type: currentDownloadFileType.value,
    },
    () => {
      fileLoading.value = false
    },
  )

  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleCancelShowPopConfirm() {
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function initFormData() {
  formDataRef.value = {
    title: '',
    startTime: null,
    endTime: null,
    hostId: null,
    attendanceNum: null,
    mainSuggestion: '',
    minutesTime: null,
  }
}

function handleAddPartyMember() {
  initFormData()
  actionType.value = 'add'
  drawerTitle.value = '新增'
  showAddPartyMemberModal.value = true
}

watch(
  () => pageNum.value,
  () => {
    loadData()
  },
)

watch(
  () => pageSize.value,
  () => {
    pageNum.value = 1
    loadData()
  },
)

function loadData() {
  if (!selectedDeptId.value || !selectedYear.value) {
    return
  }
  loading.value = true
  getPartyMemberMinutesList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
  })
    .then((res) => {
      total.value = res.total || 0
      tableData.value = (res.records as any) || []
    })
    .finally(() => {
      loading.value = false
    })
}

function handleConfirm() {
  partyMemberFormRef.value?.validate((errors: any) => {
    if (!errors) {
      if (actionType.value === 'add') {
        addPartyMemberMinutes({
          ...formDataRef.value,
          deptId: selectedDeptId.value!,
          year: selectedYear.value!,
        }).then(() => {
          window.$message.success('添加成功！')
          loadData()
        })
      }
      else if (actionType.value === 'edit') {
        editPartyMemberMinutes({ ...formDataRef.value }).then(() => {
          window.$message.success('修改成功！')
          loadData()
        })
      }

      partyMemberFormRef.value?.restoreValidation() // 重置验证状态
      showAddPartyMemberModal.value = false
    }
  })
}

function fetchUserList() {
  getUserByDepartmentId({ deptId: selectedDeptId.value! }).then((res) => {
    userList.value = res || []
  })
}

function isDateDisabled(ts: number, type: 'start' | 'end', range: [number, number] | null): boolean {
  const date = new Date(ts)
  const selectedYears = Number(selectedYear.value)

  // 限制只能选指定年份
  if (date.getFullYear() !== selectedYears) {
    return true
  }

  // 如果已有一个日期选中了
  if (range !== null) {
    const [start, end] = range

    // 已选择开始时间，禁用不在同一天的结束时间
    if (type === 'end' && start) {
      const startDate = new Date(start)
      if (date.toDateString() !== startDate.toDateString()) {
        return true
      }
    }

    // 已选择结束时间，禁用不在同一天的开始时间
    if (type === 'start' && end) {
      const endDate = new Date(end)
      if (date.toDateString() !== endDate.toDateString()) {
        return true
      }
    }
  }

  return false
}

function handleUpdateTime(date: any) {}

watch(
  () => formDataRef.value.minutesTime,
  () => {
    if (
      formDataRef.value.minutesTime
      && formDataRef.value.minutesTime.length > 1
    ) {
      const startTime = formDataRef.value.minutesTime[0]
      const endTime = formDataRef.value.minutesTime[1]
      formDataRef.value.startTime = startTime
      formDataRef.value.endTime = endTime
    }
  },
  {
    deep: true,
  },
)

onMounted(() => {
  selectedDeptId.value = currentSelectedDept.value.deptId
  selectedYear.value = currentYear.value
  loadData()
  fetchUserList()
})
</script>

<template>
  <div>
    <table-container
      v-model:page="pageNum"
      v-model:page-size="pageSize"
      :loading="loading"
      :show-delete="false"
      :show-pagination="true"
      :show-toolbar="false"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      default-expand-all
      title="党员议事记录"
    >
      <template #row>
        <div
          class="w-[100%] flex flex-row items-start justify-between gap-[10px] pb-[10px]"
        >
          <div class="flex flex-row gap-x-[10px] justify-start items-start">
            <n-tree-select
              v-model:value="selectedDeptId"
              :options="calcOrganizationListTree"
              :show-path="false"
              check-strategy="all"
              children-field="children"
              filterable
              key-field="id"
              label-field="name"
              placeholder="请选择所属党组织"
              style="width: 400px"
              value-field="id"
              @update:value="handleUpdateTreeSelectedValue"
            />
            <n-select
              v-model:value="selectedYear"
              :options="currentYearOptions"
              placeholder="请选择年份"
              size="medium"
              style="width: 200px"
              @update:value="handleUpdateYearSelectedValue"
            />
          </div>

          <div class="flex flex-row gap-x-[10px] justify-start items-start">
            <n-button type="primary" @click="handleAddPartyMember">
              <template #icon>
                <n-icon size="16">
                  <AddTwotone />
                </n-icon>
              </template>
              新增
            </n-button>
            <n-popconfirm
              :show="showPopConfirm"
              :show-icon="false"
              placement="bottom"
            >
              <template #trigger>
                <n-button
                  :loading="fileLoading"
                  @click="showPopConfirm = !showPopConfirm"
                >
                  <template #icon>
                    <n-icon size="16">
                      <DownloadRound />
                    </n-icon>
                  </template>
                  下载
                </n-button>
              </template>
              <div class="py-[10px]">
                <n-radio-group v-model:value="currentDownloadFileType">
                  <n-radio
                    v-for="(fileType, fileTypeIndex) in [
                      { label: 'word', value: 'word' },
                      { label: 'pdf', value: 'pdf' },
                    ]"
                    :key="fileTypeIndex"
                    :value="fileType.value"
                  >
                    {{ fileType.label }}
                  </n-radio>
                </n-radio-group>
              </div>

              <template #action>
                <div class="w-[100%] flex flex-row justify-center items-center">
                  <n-button
                    size="small"
                    type="primary"
                    @click="handleDownLoadFile"
                  >
                    确定
                  </n-button>
                  <n-button size="small" @click="handleCancelShowPopConfirm">
                    取消
                  </n-button>
                </div>
              </template>
            </n-popconfirm>
          </div>
        </div>
      </template>
    </table-container>

    <n-drawer
      v-model:show="showAddPartyMemberModal"
      :width="502"
      placement="right"
    >
      <n-drawer-content :title="drawerTitle">
        <n-form
          ref="partyMemberFormRef"
          :disabled="actionType === 'view'"
          :model="formDataRef"
          :rules="partyMemberMinutesFormRules"
          label-placement="left"
          label-width="110"
        >
          <n-form-item label="议事主题：" path="title">
            <n-input
              v-model:value="formDataRef.title"
              clearable
              maxlength="10"
              placeholder="请输入议事主题"
              show-count
            />
          </n-form-item>
          <n-form-item label="时间：" path="minutesTime">
            <n-date-picker
              v-model:formatted-value="formDataRef.minutesTime"
              :is-date-disabled="isDateDisabled"
              clearable
              placeholder="请选择时间"
              style="width: 100%"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              @update:value="handleUpdateTime"
            />
          </n-form-item>
          <n-form-item label="主持人：" path="hostId">
            <n-select
              v-model:value="formDataRef.hostId"
              :options="userOptions"
              clearable
              placeholder="请选择主持人"
            />
          </n-form-item>
          <n-form-item label="参加人数：" path="attendanceNum">
            <n-select
              v-model:value="formDataRef.attendanceNum"
              :options="hostOptions"
              clearable
              placeholder="请选择参加人数"
            />
          </n-form-item>

          <n-form-item label="主要意见建议：" path="mainSuggestion">
            <n-input
              v-model:value="formDataRef.mainSuggestion"
              :rows="20"
              maxlength="500"
              placeholder="请输入意见建议"
              show-count
              type="textarea"
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <div
            class="w-[100%] flex flex-row justify-center items-center gap-x-[10px]"
          >
            <n-button @click="showAddPartyMemberModal = false">
              取消
            </n-button>
            <n-button type="primary" @click="handleConfirm">
              确定
            </n-button>
          </div>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<style lang="scss" scoped></style>
