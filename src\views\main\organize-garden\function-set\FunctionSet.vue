<script lang="ts" setup>
import { NSwitch } from 'naive-ui'
import ChangeOrganize from '../cpn/ChangeOrganize.vue'
import { getTableColumns } from './config'
import { useOrganizeGardenStore } from '@/store/organize-garden'

import {
  getFunctionSettingList,
  postFunctionHidden,
} from '@/services/organize-garden'

const { getDeptId } = useOrganizeGardenStore()
const filterReactive = reactive({
  deptId: '',
})
const loading = ref(false)

watch(
  getDeptId,
  async(value) => {
    if (value) {
      loadData(value)
      filterReactive.deptId = value
    }
  },
  {
    immediate: true,
  },
)

const tableData = ref([])

async function loadData(deptId: string) {
  try {
    loading.value = true
    const res = await getFunctionSettingList(deptId)
    tableData.value = res
  }
  catch (e) {
  }
  finally {
    loading.value = false
  }
}

async function switchHidden(value: boolean, data: any) {
  const isShow = value === true ? 1 : 0
  await postFunctionHidden({ ...data, isShow })
  window.$message.success('操作成功')
  loadData(filterReactive.deptId)
}

const tableColumns = getTableColumns(row =>
  h(
    NSwitch,
    {
      onUpdateValue: (value: any) => switchHidden(value, row),
      value: Boolean(Number(row.isShow)),
    },
    {
      checked: '展示',
      unchecked: '不展示',
    },
  ),
)
</script>
<template>
  <div class="w-full px-[20px] py-[20px]">
    <ChangeOrganize />
    <div class="py-[25px]">
      <span class="text-[14px] font-[600] text-[#333] leading-[20px] mb-[26px]">功能设置</span>
    </div>
    <n-data-table
      :columns="tableColumns"
      :data="tableData"
      :loading="loading"
    />
  </div>
</template>
<style lang="scss" scoped></style>
