<template>
  <n-modal
    :loading="loading"
    :mask-closable="maskClosable"
    :show="show"
    :show-icon="false"
    :style="`width: ${width};padding:0;--n-close-size: -6px;`"
    preset="dialog"
    @close="emits('cancel')"
    @update-show="(v:any) => emits('update:show', v)"
  >
    <template #header>
      <slot name="header">
        <div class="dialog-header">
          {{ title }}
        </div>
      </slot>
    </template>

    <template #default>
      <slot></slot>
    </template>

    <template v-if="showAction" #action>
      <slot name="action">
        <div class="dialog-action">
          <n-button size="small" type="primary" @click="emits('confirm')">
            {{ btnTitle }}
          </n-button>
          <n-button size="small" @click="emits('update:show', false)">
            取消
          </n-button>
        </div>
      </slot>
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
import { NButton, NModal } from 'naive-ui'

defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    default: '',
  },
  btnTitle: {
    type: String,
    default: '确定',
  },
  width: {
    type: String,
    default: '600px',
  },
  showAction: {
    type: Boolean,
    default: true,
  },
  maskClosable: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['confirm', 'update:show', 'cancel'])
</script>

<style lang="scss" scoped>
.dialog-header {
  width: 100%;
  height: 50px;
  background: #f2f4f8;
  line-height: 50px;
  padding-left: 29px;
  padding-right: 39px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.dialog-content {
  display: flex;
  justify-content: center;
  padding: 30px 40px;

  :deep(.n-form-item) {
    margin-bottom: 10px;
  }
}

.dialog-action {
  width: 100%;
  height: 60px;
  background: #fcfdfe;
  text-align: right;
  line-height: 60px;
  padding-right: 29px;
  border-top: 1px solid #f2f3f6;

  .n-button {
    width: 64px;
    height: 30px;
    border-radius: 15px;

    &:nth-child(n + 2) {
      margin-left: 8px;
    }
  }
}
</style>
