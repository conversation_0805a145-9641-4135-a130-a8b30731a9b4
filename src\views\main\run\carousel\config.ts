import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { VNodeChild } from 'vue'
import { NImage } from 'naive-ui'
import type { ICarousel } from '@/services/run/carousel/types'

const belongingFunctionEnum = [
  {
    label: '资讯新闻',
    value: 0,
  },
  {
    label: '专题活动新闻',
    value: 1,
  },
  {
    label: '先锋号新闻',
    value: 2,
  },
  {
    label: '专题分类',
    value: 3,
  },
  {
    label: '专题栏目',
    value: 4,
  },
  {
    label: '先锋号',
    value: 5,
  },
  {
    label: '自定义',
    value: 99,
  },
]

// 类型枚举
export const typeEnum = [
  {
    label: '详细文章',
    value: 0,
  },
  {
    label: '目录',
    value: 1,
  },
  {
    label: 'H5链接',
    value: 10,
  },
  {
    label: 'tab模块',
    value: 20,
  },
  {
    label: '应用',
    value: 30,
  },
]

export function getTableColumns(
  optionColumnRenderer: (row: ICarousel) => VNodeChild,
  handleChangeSortRender: (row: ICarousel) => VNodeChild,
  handleMobileIsShowRender: (row: ICarousel) => VNodeChild,
): TableColumns<ICarousel> {
  return [
    {
      title: '序号',
      key: 'id',
      width: '5%',
      align: 'left',
      render: (_, i) => i + 1,
    },
    {
      title: '所属功能',
      width: '100px',
      align: 'center',
      key: 'belongingFunction',
      render: row =>
        belongingFunctionEnum.find(
          item => item.value === row.belongingFunction,
        )?.label,
    },
    {
      title: '类型',
      align: 'center',
      key: 'type',
      // render: row => (row.type === 0 ? '详细文章' : '目录'),
      render: row =>
        typeEnum.find(item => item.value === row.type)?.label || '未知',
    },
    {
      title: '名称',
      key: 'title',
      align: 'left',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '图片',
      align: 'center',
      key: 'coverUrl',
      render: row =>
        h(
          NImage,
          {
            src: import.meta.env.VITE_API_BASE + row.coverUrl,
            width: '100px',
            style: { height: '60px' },
          },
          '',
        ),
    },
    {
      title: '排序',
      align: 'center',
      key: 'sort',
      width: '100px',
      render: handleChangeSortRender,
    },
    {
      title: '移动端展示',
      key: 'isMobile',
      align: 'center',
      render: handleMobileIsShowRender,
    },
    {
      title: '移除轮播池',
      key: 'action',
      align: 'left',
      width: 120,
      render: optionColumnRenderer,
    },
  ]
}
