<!--
 * @Description: 试卷管理-列表页
-->
<template>
  <table-container
    title="试卷管理"
    add-text="创建"
    :loading="loadingRef"
    :total="totalRef"
    :show-delete="false"
    :table-columns="tableColumns"
    :table-data="examinationPaperList"
    @page-change="handlePageChange"
    @click-add="handleClickAdd"
  >
    <template #filters>
      <n-input
        v-model:value="paperNameRef"
        size="small"
        clearable
        @input="handleFilter"
        @clear="handleClearPaperName"
      >
        <template #suffix>
          <n-icon style="cursor: pointer" @click="handleFilter">
            <search />
          </n-icon>
        </template>
      </n-input>
    </template>
  </table-container>

  <custom-dialog
    v-model:show="showPaperMethodsRef"
    style="width: 470px; padding: 0"
    preset="dialog"
    :show-icon="false"
  >
    <template #header>
      <div class="paper-methods-title">
        选择出题方式
      </div>
    </template>
    <div class="paper-methods">
      <div
        v-for="item in paperMethodOptions"
        :key="item.label"
        class="method"
        @click="
          router.push({
            name: 'paperEdit',
            params: { method: item.value, editType: 'add' },
          })
        "
      >
        <div :class="`icon icon-${item.icon}`" />
        <span>{{ item.label }}</span>
      </div>
    </div>
  </custom-dialog>
</template>
<script setup lang="ts">
import { computed, h, ref } from 'vue'
import { useRouter } from 'vue-router'
import type { DataTableColumns } from 'naive-ui'
import { NButton, NIcon, NInput, NPopconfirm, NSwitch } from 'naive-ui'
import { Search } from '@vicons/carbon'
import TableContainer from '../../library/list/TableContainer.vue'
import type {
  IExaminationPaperItem,
  IExaminationPaperQuery,
} from '@/services/examination/index'
import {
  deleteExaminationPaper,
  putExaminationPaper,
} from '@/services/examination/index'
import useExaminationStore from '@/store/examination/index'
import { formatCustomTimeString } from '@/utils/date-time-format'
import useTableContainer from '@/hooks/use-table-container'
import {
  paperMethodOptions,
  paperTypeOptions,
} from '@/views/main/examination/config'

const router = useRouter()
const examinationStore = useExaminationStore()

const examinationPaperList = computed(
  () => examinationStore.examinationPaperList,
)
const paperNameRef = ref('')
const showPaperMethodsRef = ref(false)

const { totalRef, loadingRef, currentPageRef, pageSizeRef, handlePageChange }
  = useTableContainer(loadExaminationPaperList)

// 加载试卷列表
function loadExaminationPaperList() {
  if (!loadingRef.value && currentPageRef.value) {
    loadingRef.value = true
    // 生成参数
    const params: IExaminationPaperQuery = {
      pageNum: currentPageRef.value,
      pageSize: pageSizeRef.value,
    }
    const paperName = paperNameRef.value
    if (paperName.length) {
      params.paperName = paperName
    }
    examinationStore.getExaminationPaperListAction(params).then((res) => {
      totalRef.value = Number(res)
      loadingRef.value = false
    })
  }
}

// 搜索
function handleFilter() {
  currentPageRef.value = 1
  loadExaminationPaperList()
}
// 清空
function handleClearPaperName() {
  paperNameRef.value = ''
  handleFilter()
}

// 点击新增
function handleClickAdd() {
  showPaperMethodsRef.value = true
}

const tableColumns: DataTableColumns<IExaminationPaperItem> = [
  {
    key: 'blank',
    width: 10,
  },
  {
    key: 'name',
    title: '试卷名称',
  },
  {
    key: 'paperType',
    title: '试卷类型',
    width: 100,
    render(row) {
      return (
        paperTypeOptions.find(item => item.value === row.paperType)?.label
        ?? ''
      )
    },
  },
  {
    key: 'nums',
    title: '题量',
    width: 100,
  },
  {
    key: 'total',
    title: '总分',
    width: 100,
  },
  {
    key: 'corp',
    title: '来源',
    width: 100,
  },
  {
    key: 'updatedAt',
    title: '更新时间',
    width: 160,
    render(row) {
      return formatCustomTimeString(row.updatedAt, 'YYYY-MM-DD HH:mm:ss')
    },
  },
  {
    key: 'isOpen',
    title: '开放考试',
    width: 100,
    render(row) {
      return h(NSwitch, {
        checkedValue: 1,
        uncheckedValue: 0,
        defaultValue: row.isOpen,
        onUpdateValue: (v: any) => {
          putExaminationPaper(row.id, v).then(() => {
            window.$message.success('操作成功')
            loadExaminationPaperList()
          })
        },
      })
    },
  },
  {
    key: 'operation',
    title: '操作',
    width: 200,
    render(row) {
      return [
        h(
          NButton,
          {
            text: true,
            type: 'primary',
            style: 'margin-right: 16px',
            onClick: () => {
              const type = row.paperMethod === 5 ? 0 : 1
              router.push({
                name: 'paperEdit',
                params: { method: type, editType: 'modify' },
                query: { id: row.id },
              })
            },
          },
          { default: () => '编辑' },
        ),
        h(
          NPopconfirm,
          {
            positiveText: '确定',
            onPositiveClick: () => {
              deleteExaminationPaper(row.id).then((res) => {
                window.$message.success(res)
                loadExaminationPaperList()
              })
            },
          },
          {
            trigger: () =>
              h(
                NButton,
                { text: true, type: 'primary', style: 'margin-right: 16px' },
                { default: () => '删除' },
              ),
            default: () => '确定删除吗？',
          },
        ),
        // h(
        //   NButton,
        //   { text: true, type: 'primary', style: 'margin-right: 16px' },
        //   { default: () => '查看关联考试' }
        // ),
        // h(
        //   NButton,
        //   { text: true, type: 'primary', style: 'margin-right: 16px' },
        //   { default: () => '导出试题' }
        // ),
        // h(
        //   NButton,
        //   { text: true, type: 'primary', style: 'margin-right: 16px' },
        //   { default: () => '复制' }
        // ),
      ]
    },
  },
]
</script>

<style lang="scss" scoped>
.paper-methods-title {
  height: 50px;
  line-height: 50px;
  background: #f2f4f8;
  width: 100%;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  padding-left: 29px;
}

.paper-methods {
  height: 200px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;

  > .method {
    width: 100px;
    height: 100px;
    border-radius: 10px;
    cursor: pointer;
    user-select: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: box-shadow ease 0.25s;

    &:hover {
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.2) inset;
    }

    > .icon {
      width: 33px;
      height: 33px;
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;

      &.icon-choose {
        background-image: url('@/assets/image/choose.png');
      }

      &.icon-random {
        background-image: url('@/assets/image/random.png');
      }
    }

    > span {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      margin-top: 15px;
    }
  }
}
</style>
