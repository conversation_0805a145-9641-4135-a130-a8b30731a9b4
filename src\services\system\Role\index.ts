import type { RoleItem } from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 获取角色列表 */
export function getRoleList(params: { roleName?: string } & PaginationReq) {
  return commonReq.get<PaginationRes<RoleItem>>({
    url: '/upms/role/page',
    params,
  })
}
/** 添加角色 */
export function addRole(data: RoleItem) {
  return commonReq.post({
    url: '/upms/role',
    data,
  })
}

/** 编辑角色 */
export function editorRole(data: RoleItem) {
  return commonReq.put({
    url: '/upms/role',
    data,
  })
}

/** 删除角色 */
export function delRole(params: { id: string }) {
  return commonReq.delete({
    url: `/upms/role/${params.id}`,
  })
}

/** 批量删除角色 */
export function batchDelRole(params: { ids: string }) {
  return commonReq.delete({
    url: '/upms/role/delete',
    params,
  })
}

/** 获取角色下拉列表 */
export function getRoleSelectList() {
  return commonReq.get({
    url: '/upms/role/list',
  })
}
/** 获取菜单树 */
export function getMenuList() {
  return commonReq.get({
    url: '/upms/menu/tree',
  })
}
/** 给角色赋予资源 */
export function setRoleMenu(data: {
  roleId: string
  menuIds: string
  menuIdsAll: string
}) {
  return commonReq.put({
    url: '/upms/role/menu',
    data,
  })
}
// 获取角色对应资源
export function getRoleMenu(data: { roleId: string }) {
  return commonReq.get({
    url: `/upms/menu/tree/${data.roleId}`,
  })
}
