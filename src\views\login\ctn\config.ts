import type { FormRules, GlobalThemeOverrides } from 'naive-ui'

export const themeOverrides: GlobalThemeOverrides = {
  Input: {
    heightLarge: '50px',
    border: 'none',
    borderRadius: '5px',
  },
}
export const formRules: FormRules = {
  username: {
    required: true,
    message: '请输入账号',
    trigger: 'blur',
  },
  password: {
    required: true,
    message: '请输入密码',
    trigger: 'blur',
  },
  code: {
    required: true,
    message: '请输入验证码',
    trigger: 'blur',
  },
}
