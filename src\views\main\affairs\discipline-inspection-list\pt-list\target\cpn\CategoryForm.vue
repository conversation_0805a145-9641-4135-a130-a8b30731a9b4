<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { formRules } from './config'
import {
  postEvaluationTypes,
  putEvaluationTypes,
} from '@/services/affairs/discipline-inspection-list'

interface Props {
  type?: string
  id?: string
  name?: string
  categoryId?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
  name: '',
  categoryId: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()
const categoryFormRef = ref<InstanceType<typeof NForm>>()

const formDataReactive = reactive({
  inventoryId: '',
  name: '',
  id: '',
})

onMounted(async() => {
  if (props.type === 'modify') {
    formDataReactive.name = props.name
    formDataReactive.id = props.categoryId
  } else {
    formDataReactive.inventoryId = props.id
    formDataReactive.name = props.name
  }
})

// 验证表单,调用接口
async function validateAndSave() {
  const errors = await new Promise((resolve) => {
    categoryFormRef.value?.validate((errors) => {
      resolve(errors)
    })
  })

  if (!errors) {
    const { type } = props
    const { name, id, inventoryId } = formDataReactive
    const saveData = type === 'modify' ? { name, id } : { inventoryId, name }
    const saveFunction
      = type === 'modify' ? putEvaluationTypes : postEvaluationTypes
    try {
      const res = await saveFunction(saveData)
      if (res) {
        window.$message.success('保存成功')
        emits('saved')
      }
    } catch (error) {}
  }
}
// 重置表单
function resetForm() {
  categoryFormRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="categoryFormRef"
    size="small"
    require-mark-placement="left"
    label-width="80"
    label-align="right"
    label-placement="left"
    :rules="formRules"
    :model="formDataReactive"
    class="p-[22px] py-[25px]"
  >
    <n-form-item path="name">
      <n-input
        v-model:value="formDataReactive.name"
        placeholder="请输入考核类别"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
