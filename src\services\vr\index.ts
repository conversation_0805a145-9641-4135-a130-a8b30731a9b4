import { commonReq } from '../request'
import type { PaginationReq } from '../types'
import type { AddOrEditVRListItem } from './types'

/** 获取线上展馆列表 */
export function getVRList(
  params: {
    title?: string
  } & PaginationReq,
) {
  return commonReq.get({
    url: '/sys-operation/backend/v1/red_vr/page',
    params,
  })
}

/** 新增线上展馆 */
export function postVR(data: AddOrEditVRListItem) {
  return commonReq.post({
    url: '/sys-operation/backend/v1/red_vr',
    data,
  })
}

/** 编辑线上展馆 */
export function putVR(data: AddOrEditVRListItem) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/red_vr',
    data,
  })
}

/** 查看线上展馆详情 */
export function getVRDetail(id: string) {
  return commonReq.get<AddOrEditVRListItem>({
    url: `/sys-operation/backend/v1/red_vr/${id}`,
  })
}

/** 删除线上展馆  */
export function deleteVR(ids: string) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/red_vr',
    params: {
      ids,
    },
  })
}

/** 线上展馆是否跳转外部链接 */
export function putVRJumpOutLink(id: string, isOutside: string) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/red_vr/outside',
    params: {
      id,
      isOutside,
    },
  })
}

/** 隐藏线上展馆 */
export function putHideVR(id: string, isHidden: string) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/red_vr/hidden',
    params: {
      id,
      isHidden,
    },
  })
}

/** 置顶线上展馆 */
export function topVR(id: string) {
  return commonReq.put({
    url: `/sys-operation/backend/v1/red_vr/top/${id}`,
    method: 'put',
  })
}
