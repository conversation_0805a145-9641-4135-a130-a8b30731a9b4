<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRulesForCheck"
  >
    <n-form-item label="操作：" path="phaseStatus">
      <n-radio-group
        v-model:value="formDataReactive.phaseStatus"
        name="radiogroup"
      >
        <n-space>
          <n-radio
            v-for="item in options"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </n-radio>
        </n-space>
      </n-radio-group>
    </n-form-item>
    <n-form-item span="24" label="审核说明：" path="remark">
      <n-input
        v-model:value="formDataReactive.remark"
        type="textarea"
        rows="5"
        maxlength="100"
        show-count
      />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { formRulesForCheck } from './config'
import { putUpdateOrganizationChangeOver } from '@/services/structure/organization-change-over'
import type { UpdateChangeOver } from '@/services/structure/organization-change-over/type'
interface Props {
  id?: string
}

const props = withDefaults(defineProps<Props>(), {
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formRef = ref<InstanceType<typeof NForm>>()
const formDataReactive = reactive<UpdateChangeOver>({
  id: props.id,
  phaseStatus: null,
  remark: '',
})
const options = ref([
  {
    label: '审核通过',
    value: 3,
  },
  {
    label: '审核不通过',
    value: 1,
  },
])

// 验证表单,调用接口
const validateAndSave = () => {
  formRef.value?.validate((errors: boolean) => {
    if (!errors) {
      if (formDataReactive.id) {
        // 更新
        putUpdateOrganizationChangeOver(formDataReactive).then(() => {
          window.$message.success('审核成功')
          emits('saved')
        })
      }
    }
  })
}
// 重置表单
const resetForm = () => {
  formRef.value?.restoreValidation()
}

defineExpose({
  validateAndSave,
  resetForm,
})
</script>

<style scoped lang="scss"></style>
