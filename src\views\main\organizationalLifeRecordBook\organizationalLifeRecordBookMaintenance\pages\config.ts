import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import dayjs from 'dayjs'
import type {
  ActivistsMemberRosterType,
  GroupMemberType,
  HandleStatusItemType,
  HeartTalkRecordType,
  ImportantMattersRecordType,
  MeetingRecordType,
  OrganizationalTransferType,
  PartyBranchActivityType,
  PartyMemberActivityType,
  PartyMemberMeetingMinutesItemType,
  PartyMemberType,
  PartyMembersAttendLecturesType,
  PartySituationReport,
  RewardStatusItemType,
  TrainingRequestTypes,
  UserEvaluationList,
} from '@/services/recordBookMaintenance/types'
import type { DuesPayVO } from '@/services/party-dues-payment-records/types/request'

export function getRecordBookTableColumns(
  optionColumnRenderer: (row: MeetingRecordType) => VNodeChild,
): DataTableColumns<MeetingRecordType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'title',
      width: '400px',
      title: '会议主题',
    },
    {
      key: 'meetingTypeName',
      align: 'center',
      title: '会议类型',
    },
    {
      key: 'startTime',
      align: 'center',
      title: '会议时间',
      sorter: true,
      render: row =>
        `${row.startTime || '未知'}~${
          row.endTime ? dayjs(row.endTime).format('HH:mm:ss') : '未知'
        }`,
    },
    {
      key: 'meetingAddr',
      align: 'center',
      title: '会议地点',
    },
    {
      key: 'expectedNum',
      align: 'center',
      title: '应到人数',
    },
    {
      key: 'attendanceNum',
      align: 'center',
      title: '实到人数',
    },
    {
      key: 'host',
      align: 'center',
      title: '主持人',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '80',
      render: row => optionColumnRenderer(row),
    },
  ]
}

export function getOrganizationalTransferTableColumns(
  optionColumnRenderer: (row: OrganizationalTransferType) => VNodeChild,
): DataTableColumns<OrganizationalTransferType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'userName',
      width: '120',
      title: '姓名',
    },
    {
      key: 'transferType',
      width: '160',
      title: '调动类型',
    },
    {
      key: 'transferTime',
      width: '220',
      title: '调动时间',
      sorter: true,
    },
    {
      key: 'oldDeptName',
      title: '原组织',
    },
    {
      key: 'newDeptName',
      title: '接收组织',
    },
    {
      key: 'phaseStatus',
      width: '120',
      title: '状态',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '120',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 党员名册列表字段
export function getPartyMemberColumns(
  sexColumnRenderer: (row: PartyMemberType) => VNodeChild,
  eduColumnRenderer: (row: PartyMemberType) => VNodeChild,
  politicalColumnRenderer: (row: PartyMemberType) => VNodeChild,
  partyDutyColumnRenderer: (row: PartyMemberType) => VNodeChild,
  optionColumnRenderer: (row: PartyMemberType, index: number) => VNodeChild,
): DataTableColumns<PartyMemberType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'trueName',
      width: '120',
      title: '姓名',
    },
    {
      key: 'sex',
      width: '80',
      align: 'center',
      title: '性别',
      render: row => sexColumnRenderer(row),
    },
    {
      key: 'birth',
      width: '120',
      align: 'center',
      title: '出生年月',
    },
    {
      key: 'edu',
      align: 'center',
      title: '学历',
      render: row => eduColumnRenderer(row),
    },
    {
      key: 'political',
      title: '政治面貌',
      render: row => politicalColumnRenderer(row),
    },
    {
      key: 'joinTime',
      title: '入党时间',
    },
    {
      key: 'partyDuty',
      title: '支部内职务',
      render: row => partyDutyColumnRenderer(row),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '120',
      render: (row, index) => optionColumnRenderer(row, index),
    },
  ]
}

// 党员听党课情况列
export function getPartyMembersAttendLecturesColumns(
  optionColumnRenderer: (row: PartyMembersAttendLecturesType) => VNodeChild,
): DataTableColumns<PartyMembersAttendLecturesType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'title',
      width: '200',
      align: 'center',
      title: '会议主题',
    },
    {
      key: 'host',
      width: '160',
      align: 'center',
      title: '主持人',
    },
    {
      key: 'startTime',
      width: '220',
      title: '会议时间',
      align: 'center',
      sorter: true,
      render: row =>
        `${row.startTime || '未知'}~${
          row.endTime ? dayjs(row.endTime).format('HH:mm:ss') : '未知'
        }`,
    },
    {
      key: 'meetingAddr',
      title: '会议地点',
      align: 'center',
    },
    {
      key: 'expectedNum',
      width: '120',
      align: 'center',
      title: '应到人数',
    },
    {
      key: 'attendanceNum',
      width: '120',
      align: 'center',
      title: '实到人数',
    },
    {
      key: 'action',
      title: '操作',
      align: 'center',
      width: '120',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 支部基本状况列
export function getBranchBasicOrganizationalColumns(
  optionColumnRenderer: (row: GroupMemberType) => VNodeChild,
): DataTableColumns<GroupMemberType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'groupDeptName',
      width: '120',
      title: '党小组',
    },
    {
      key: 'userId',
      width: '120',
      title: '小组长名单',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 民主评议党员情况
export function getDemocraticEvaluationColumns(
  resultColumnRenderer: (row: UserEvaluationList) => VNodeChild,
  optionColumnRenderer: (row: UserEvaluationList) => VNodeChild,
): DataTableColumns<UserEvaluationList> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'trueName',
      width: '120',
      title: '姓名',
    },
    {
      key: 'evaluationLevel',
      title: '评议结果',
      width: '120',
      render: row => resultColumnRenderer(row),
    },
    {
      key: 'userName',
      width: '120',
      title: '操作',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 入党积极分子列
export function getActivePartyMemberColumns(
  sexColumnRenderer: (row: ActivistsMemberRosterType) => VNodeChild,
  eduColumnRenderer: (row: ActivistsMemberRosterType) => VNodeChild,
  optionColumnRenderer: (
    row: ActivistsMemberRosterType,
    index: number
  ) => VNodeChild,
): DataTableColumns<ActivistsMemberRosterType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'trueName',
      width: '120',
      title: '姓名',
    },
    {
      key: 'sex',
      width: '80',
      align: 'center',
      title: '性别',
      render: row => sexColumnRenderer(row),
    },
    {
      key: 'birth',
      width: '120',
      align: 'center',
      title: '出生年月',
    },
    {
      key: 'edu',
      align: 'center',
      title: '学历',
      render: row => eduColumnRenderer(row),
    },
    {
      key: 'dutyName',
      title: '职务或职称',
    },
    {
      key: 'contact',
      title: '联系人',
    },
    {
      key: 'confirmTime',
      title: '申请日期',
    },
    {
      key: 'remark',
      title: '备注',
      ellipsis: {
        tooltip: {
          contentStyle: {
            width: '400px',
            'word-break': 'break-all',
            whiteSpace: 'pre-line',
          },
        },
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '120',
      render: (row, index) => optionColumnRenderer(row, index),
    },
  ]
}

// 党员议事记录列
export function getPartyMemberMinutesColumns(
  optionColumnRenderer: (
    row: PartyMemberMeetingMinutesItemType,
    index: number
  ) => VNodeChild,
): DataTableColumns<PartyMemberMeetingMinutesItemType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'title',
      width: '180',
      title: '议事主题',
    },
    {
      key: 'startTime',
      align: 'center',
      title: '时间',
      width: '220',
      render: row =>
        `${row.startTime || '未知'}~${
          row.endTime ? dayjs(row.endTime).format('HH:mm:ss') : '未知'
        }`,
    },
    {
      key: 'host',
      width: '120',
      align: 'center',
      title: '主持人',
    },
    {
      key: 'attendanceNum',
      align: 'center',
      width: '120',
      title: '参加人数',
    },
    {
      key: 'mainSuggestion',
      title: '主要意见建议',
      ellipsis: {
        tooltip: {
          contentStyle: {
            width: '400px',
            'word-break': 'break-all',
            whiteSpace: 'pre-line',
          },
        },
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '120',
      render: (row, index) => optionColumnRenderer(row, index),
    },
  ]
}

// 党内情况通报记录
export function getInternalPartyReportingRecordsColumns(
  optionColumnRenderer: (row: PartySituationReport, index: number) => VNodeChild,
): DataTableColumns<PartySituationReport> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'startTime',
      width: '320',
      title: '时间',
      render: row =>
        `${row.startTime || '未知'}~${
          row.endTime ? dayjs(row.endTime).format('HH:mm:ss') : '未知'
        }`,
    },
    {
      key: 'host',
      width: '140',
      align: 'center',
      title: '主持人',
    },
    {
      key: 'attendanceNum',
      width: '120',
      align: 'center',
      title: '参加人数',
    },
    {
      key: 'mainContent',
      title: '主要内容',
      ellipsis: {
        tooltip: {
          contentStyle: {
            width: '400px',
            'word-break': 'break-all',
            whiteSpace: 'pre-line',
          },
        },
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '120',
      render: (row, index) => optionColumnRenderer(row, index),
    },
  ]
}

// 重要事项征求意见记录
export function getImportantMattersOpinionsRecordsColumns(
  optionColumnRenderer: (
    row: ImportantMattersRecordType,
    index: number
  ) => VNodeChild,
): DataTableColumns<ImportantMattersRecordType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'endTime',
      width: '320',
      title: '时间',
      render: row =>
        `${row.startTime || '未知'}~${
          row.endTime ? dayjs(row.endTime).format('HH:mm:ss') : '未知'
        }`,
    },
    {
      key: 'host',
      width: '140',
      align: 'center',
      title: '主持人',
    },
    {
      key: 'attendanceNum',
      width: '120',
      align: 'center',
      title: '参加人数',
    },
    {
      key: 'mainContent',
      title: '主要内容',
      ellipsis: {
        tooltip: {
          contentStyle: {
            width: '400px',
            'word-break': 'break-all',
            whiteSpace: 'pre-line',
          },
        },
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '120',
      render: (row, index) => optionColumnRenderer(row, index),
    },
  ]
}

// 组织入党积极分子活动情况
export function getPartyMembersActivitiesRecordsColumns(
  optionColumnRenderer: (
    row: PartyMemberActivityType,
    index: number
  ) => VNodeChild,
): DataTableColumns<PartyMemberActivityType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'date',
      width: '220',
      title: '日期',
    },
    {
      key: 'organizer',
      width: '100',
      title: '组织者',
    },
    {
      key: 'content',
      align: 'center',
      title: '活动内容',
      ellipsis: {
        tooltip: {
          contentStyle: {
            width: '400px',
            'word-break': 'break-all',
            whiteSpace: 'pre-line',
          },
        },
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '120',
      render: (row, index) => optionColumnRenderer(row, index),
    },
  ]
}

// 党支部活动情况
export function getPartyBranchActivitiesRecordsColumns(
  optionColumnRenderer: (
    row: PartyBranchActivityType,
    index: number
  ) => VNodeChild,
): DataTableColumns<PartyBranchActivityType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'startTime',
      width: '320',
      title: '时间',
      render: row =>
        `${row.startTime || '未知'}~${
          row.endTime ? dayjs(row.endTime).format('HH:mm:ss') : '未知'
        }`,
    },
    {
      key: 'location',
      width: '240',
      align: 'center',
      title: '地点',
    },
    {
      key: 'attendanceNum',
      width: '100',
      align: 'center',
      title: '出席人数',
    },
    {
      key: 'content',
      align: 'center',
      title: '活动内容',
      ellipsis: {
        tooltip: {
          contentStyle: {
            width: '400px',
            'word-break': 'break-all',
            whiteSpace: 'pre-line',
          },
        },
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '120',
      render: (row, index) => optionColumnRenderer(row, index),
    },
  ]
}

export function getPartyDuesPaymentRecordsColumns(
  monthObjList: {
    label: string
    value: string
    en: string
    field: string
  }[],
  optionColumnRenderer: (
    row: DuesPayVO,
    item: (typeof monthObjList)[number]
  ) => VNodeChild,
): DataTableColumns<{
    id: string
    name: string
    [key: string]: any
  }> {
  // 1-12月
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
      fixed: 'left',
    },
    {
      key: 'userName',
      width: '120',
      title: '姓名',
      fixed: 'left',
    },
    ...(monthObjList.map((item: (typeof monthObjList)[number]) => ({
      key: item.en,
      title: item.label,
      align: 'center',
      width: '180',
      render: (row: DuesPayVO) => optionColumnRenderer(row, item),
    })) as any[]),
  ]
}

// 党组织集中轮训分页查询
export function getPartyPollingListColumns(
  optionColumnRenderer: (
    row: TrainingRequestTypes.RecordsItem,
    index: number
  ) => VNodeChild,
): DataTableColumns<TrainingRequestTypes.RecordsItem> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      title: '时间',
      width: '220',
      key: '',
      render: (row: TrainingRequestTypes.RecordsItem) => {
        const startTime = row?.startTime || ''
        const endTime = row?.endTime || ''
        const yearMonthDay = dayjs(startTime).format('YYYY-MM-DD')
        if (!startTime || !endTime) {
          return '-'
        }
        return `${yearMonthDay} ${dayjs(startTime).format('HH:mm:ss')}~${dayjs(
          endTime,
        ).format('HH:mm:ss')}`
      },
    },
    {
      key: 'addr',
      width: '200',
      title: '地点',
    },
    {
      key: 'studyHour',
      width: '100',
      title: '学时',
    },
    {
      key: 'userName',
      width: '100',
      title: '支部书记',
    },
    {
      key: 'projectContent',
      title: '培训项目',
    },
    {
      key: 'action',
      title: '操作',
      align: 'center',
      width: '150',
      render: (row: TrainingRequestTypes.RecordsItem, index: number) =>
        optionColumnRenderer(row, index),
    },
  ]
}

// 调动类型
export const transferType = [
  {
    label: '系统内调入',
    value: 0,
  },
  {
    label: '系统内调出',
    value: 1,
  },
  {
    label: '调出系统外',
    value: 2,
  },
  {
    label: '系统外调入',
    value: 3,
  },
]

// 主题党日活动情况统计表格
export function getPartyDayActivityTableColumns(
  optionColumnRenderer: (row: MeetingRecordType) => VNodeChild,
): DataTableColumns<MeetingRecordType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      title: '时间',
      key: 'time',
      width: '300',
      render: row =>
        `${row.startTime || '未知'}~${
          row.endTime ? dayjs(row.endTime).format('HH:mm:ss') : '未知'
        }`,
    },
    {
      key: 'meetingAddr',
      width: '240',
      title: '地点',
      ellipsis: {
        tooltip: {
          contentStyle: {
            width: '400px',
            'word-break': 'break-all',
            whiteSpace: 'pre-line',
          },
        },
      },
    },
    {
      key: 'title',
      title: '活动内容',
      ellipsis: {
        tooltip: {
          contentStyle: {
            width: '400px',
            'word-break': 'break-all',
            whiteSpace: 'pre-line',
          },
        },
      },
    },
    {
      key: 'attendanceNum',
      title: '出席人数',
      width: '100',
    },
    {
      key: 'absenteeNum',
      title: '缺席人数',
      width: '100',
    },
    {
      key: 'host',
      width: '200',
      title: '主持人',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '100',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 谈心谈话记录表格
export function getHeartTalkRecordTableColumns(
  optionColumnRenderer: (row: HeartTalkRecordType) => VNodeChild,
): DataTableColumns<HeartTalkRecordType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'talker',
      width: '120',
      title: '谈话人',
    },
    {
      key: 'time',
      width: '300',
      title: '时间',
      render: row =>
        `${row.startTime || '未知'}~${
          row.endTime ? dayjs(row.endTime).format('HH:mm:ss') : '未知'
        }`,
    },
    {
      key: 'intervieweeList',
      width: '400',
      title: '谈话对象',
      render: row =>
        row.intervieweeList
          ? row.intervieweeList.map(item => item.interviewee).join(',')
          : '未知',
    },
    {
      key: 'location',
      title: '地点',
      width: '200',
    },
    {
      key: 'mainSuggestion',
      title: '主要意见建议',
      ellipsis: {
        tooltip: {
          contentStyle: {
            width: '400px',
            'word-break': 'break-all',
            whiteSpace: 'pre-line',
          },
        },
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '150',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 奖励情况表格
export function getRewardStatusTableColumns(
  optionColumnRenderer: (row: RewardStatusItemType) => VNodeChild,
): DataTableColumns<RewardStatusItemType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'userName',
      width: '120',
      title: '姓名',
    },
    {
      key: 'awardDate',
      width: '160',
      title: '时间',
    },
    {
      key: 'awardName',
      width: '220',
      title: '奖励名称',
    },
    {
      key: 'awardStory',
      title: '简要事迹',
      ellipsis: {
        tooltip: {
          contentStyle: {
            width: '400px',
            'word-break': 'break-all',
            whiteSpace: 'pre-line',
          },
        },
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '150',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 处置情况表格
export function getHandleStatusTableColumns(
  optionColumnRenderer: (row: HandleStatusItemType) => VNodeChild,
): DataTableColumns<HandleStatusItemType> {
  return [
    {
      key: 'id',
      title: '序号',
      width: '80',
      render: (_, i) => i + 1,
    },
    {
      key: 'userName',
      width: '120',
      title: '姓名',
    },
    {
      key: 'decisionDate',
      width: '160',
      title: '时间',
    },
    {
      key: 'disciplineType',
      width: '220',
      title: '处分类别',
    },
    {
      key: 'disciplineReason',
      title: '处分（处置）原因',
      ellipsis: {
        tooltip: {
          contentStyle: {
            width: '400px',
            'word-break': 'break-all',
            whiteSpace: 'pre-line',
          },
        },
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '150',
      render: row => optionColumnRenderer(row),
    },
  ]
}
