<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { formRules } from './config'
import {
  getUserInfo,
  toHistoryUserItem,
} from '@/services/system/User'
import type { ToHistoryUserDetailItem } from '@/services/system/User/types'
import { TO_HISTORY_TYPE } from '@/store/dict'

interface Props {
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<ToHistoryUserDetailItem>({
  userId: null,
  username: '',
  trueName: '',
  phone: '',
  identityId: '',
  reasonType: null,
  reasonDescription: '',
})

const formRef = ref<InstanceType<typeof NForm>>()

onBeforeMount(() => {
  getUserInfo(props.id).then((res) => {
    formDataReactive.userId = res.userId
    formDataReactive.username = res.username
    formDataReactive.trueName = res.trueName
    formDataReactive.phone = res.phone
    formDataReactive.identityId = res.identityId
  })
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (formDataReactive.userId) {
        toHistoryUserItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('转至历史成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="130"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <div><span>个人信息</span></div>
    <n-grid :cols="24">
      <n-form-item-gi :span="12" label="用户名：" path="username">
        <n-input
          v-model:value="formDataReactive.username"
          placeholder="请输入用户名"
          maxlength="20"
          disabled
          show-count
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="姓名：" path="trueName">
        <n-input
          v-model:value="formDataReactive.trueName"
          placeholder="请输入姓名"
          maxlength="20"
          disabled
          show-count
          clearable
        />
      </n-form-item-gi>

      <n-form-item-gi :span="12" label="手机号：" path="phone">
        <n-input
          v-model:value="formDataReactive.phone"
          placeholder="请输入手机号"
          disabled
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="身份证：" path="identityId">
        <n-input
          v-model:value="formDataReactive.identityId"
          disabled
          placeholder="请输入身份证号"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="转至历史原因类型：" path="reasonType">
        <n-select
          v-model:value="formDataReactive.reasonType"
          :options="TO_HISTORY_TYPE"
          placeholder="请选择转至历史原因类型"
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi :span="24" label="转至历史原因说明：" path="reasonDescription">
        <n-input
          v-model:value="formDataReactive.reasonDescription"
          rows="5"
          maxlength="200"
          show-count
          type="textarea"
          placeholder="请输入转至历史原因说明"
        />
      </n-form-item-gi>
    </n-grid>
  </n-form>
</template>
<style lang="scss" scoped></style>
