<script lang="ts" setup>
import { GroupAddRound } from '@vicons/material'
import { debounce } from 'lodash-es'
import { NButton } from 'naive-ui'
import { createColumn, formRules } from '../cpns/config'
import { usePagination } from '@/hooks/use-pagination'
import {
  addUser,
  delUser,
  editorUser,
  getOrganizationTree,
  getUserList,
  resetUserPwd,
} from '@/services/system/Admin'
import { getRoleSelectList } from '@/services/system/Role'
import type { managerListType } from '@/services/system/types'
import type { UserItem } from '@/services/system/Admin/types'

const { total, pageNum, pageSize } = usePagination(loadData, 10)
const tableData = ref()
const role = ref()
const username = ref()
const nickName = ref()
const roleOptions = ref()
const newVisible = ref(false)
const formRef = ref()
const deptOptions = ref()
const drawerTitle = ref('')
const loadingFlag = ref(false)
const formData = reactive<UserItem>({
  username: '',
  nickName: '',
  role: '',
  dept: [],
})
const formData2 = reactive<UserItem>({
  username: '',
  nickName: '',
  role: '',
  dept: [],
  userId: '',
})
function resetPassword(id: string) {
  if (!id) {
    window.$message.error('请选择删除项')
    return
  }
  window.$dialog.create({
    type: 'default',
    closable: false,
    content: '确认重置密码吗？',
    showIcon: false,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      resetUserPwd({ id }).then(() => {
        window.$message.success('重置成功！')
        loadData()
      })
    },
  })
}

function deleteFn(id: string) {
  if (!id) {
    window.$message.error('请选择删除项')
    return
  }
  window.$dialog.create({
    type: 'default',
    closable: false,
    content: '确认删除吗？',
    showIcon: false,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      delUser({ id }).then(() => {
        window.$message.success('删除成功！')
        loadData()
      })
    },
  })
}
async function handleGetOriTree() {
  const res = await getOrganizationTree()
  deptOptions.value = res
}
async function getRoleList() {
  const res = await getRoleSelectList()
  roleOptions.value = res
}

const columns = createColumn(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
        },
      },
      [
        h(
          NButton,
          {
            text: true,
            type: 'primary',
            disabled: row.userId === '1',
            onClick: () => updateUser(row),
          },
          { default: () => '编辑' },
        ),
        h(
          NButton,
          {
            text: true,
            type: 'primary',
            disabled: row.userId === '1',
            style: { margin: '0 10px' },
            onClick: () => resetPassword(row.userId),
          },
          { default: () => '重置密码' },
        ),
        h(
          NButton,
          {
            text: true,
            type: 'primary',
            disabled: row.userId === '1',
            onClick: () => deleteFn(row.userId),
          },
          { default: () => '删除' },
        ),
      ],
    )
  },
  (row) => {
    return `${row.roleList.map(item => item.roleName)?.join(',')}`
  },
  (row) => {
    return `${row.deptList.map(item => item.name)?.join(',')}`
  },
)
async function initPageSize() {
  pageNum.value = 1
  loadData()
}
async function loadData() {
  loadingFlag.value = true
  const payload = {
    current: pageNum.value,
    size: pageSize.value,
  }

  if (role.value) {
    Object.assign(payload, { role: role.value })
  }
  if (username.value) {
    Object.assign(payload, { username: username.value })
  }
  if (nickName.value) {
    Object.assign(payload, { nickName: nickName.value })
  }
  const res = await getUserList(payload)
  total.value = res.total
  tableData.value = res.records
  loadingFlag.value = false
}

function addConfirm() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (drawerTitle.value === '添加管理员') {
        const data = {
          ...formData,
          ...{
            role: formData.role ? [formData.role] : undefined,
          },
        }
        addUser(data).then((res) => {
          if (res) {
            window.$message.success('保存成功')
          }
          loadData()
          newVisible.value = false
        })
      }
      else if (drawerTitle.value === '编辑管理员') {
        const data = {
          ...formData2,
          ...{
            role: formData2.role ? [formData2.role] : undefined,
          },
        }
        editorUser(data).then((res) => {
          if (res) {
            window.$message.success('编辑成功')
          }
          loadData()
          newVisible.value = false
        })
      }
    }
  })
}
function createUser() {
  drawerTitle.value = '添加管理员'
  newVisible.value = true
  formData.username = ''
  formData.nickName = ''
  formData.role = ''
  formData.dept = []
}
function updateUser(row: managerListType) {
  drawerTitle.value = '编辑管理员'
  formData2.userId = row.userId ?? '0'
  formData2.username = row.username
  formData2.nickName = row.nickName
  formData2.role = row.roleList[0].roleId
  formData2.dept = row.deptList.map(item => item.deptId)
  newVisible.value = true
}
function updateCheckedKeys(keys: Array<string>) {
  formData.dept = keys
}
function updateCheckedKeys2(keys: Array<string>) {
  formData2.dept = keys
}
function onlyAllowNumberAndLetter(value: string) {
  return !value || /^[A-Za-z0-9]*$/.test(value)
}
onMounted(() => {
  getRoleList()
  handleGetOriTree()
})

watch(username, debounce(initPageSize, 500))
watch(nickName, debounce(initPageSize, 500))
</script>
<template>
  <div class="px-[20px] pt-[25px]">
    <p class="text-[16px] font-semibold">
      管理员列表
    </p>
    <div class="flex justify-between mt-[20px]">
      <div class="flex items-center">
        <n-button type="primary" size="small" @click="createUser">
          <template #icon>
            <n-icon size="16" class="mr-[5px]">
              <group-add-round />
            </n-icon>
          </template>
          添加
        </n-button>
        <div class="w-[300px] flex items-center ml-[30px]">
          <div class="w-[60px] font-semibold">
            角色：
          </div>
          <n-select
            v-model:value="role"
            clearable
            placeholder="请选择角色"
            label-field="roleName"
            value-field="roleId"
            filterable
            :options="roleOptions"
            @update:value="initPageSize"
          />
        </div>
      </div>
      <div>
        <n-input
          v-model:value="nickName"
          class="mr-[10px]"
          placeholder="请输入管理员名称进行搜索"
          style="width: 220px"
          clearable
        />
        <n-input
          v-model:value="username"
          placeholder="请输入账号进行搜索"
          style="width: 220px"
          clearable
        />
      </div>
    </div>
    <n-data-table
      :columns="columns"
      :data="tableData"
      class="mt-[20px]"
      :loading="loadingFlag"
      max-height="calc(100vh - 380px)"
    />
    <div class="flex items-center justify-between mt-[30px] pr-[2px]">
      <span class="text-[#BDBDBD] text-[12px] mr-[30px]">共
        <span class="text-[#262626] mx-[6px]">{{ total }}</span>
        条</span>
      <n-pagination
        v-model:page="pageNum"
        v-model:page-size="pageSize"
        :item-count="total"
        :page-sizes="[5, 10, 20, 30, 50]"
        show-quick-jumper
        show-size-picker
      />
    </div>
  </div>
  <my-drawer
    :confirm-fn="addConfirm"
    :show="newVisible"
    :width="602"
    :title="drawerTitle"
    @change-visible="(res:any)=>newVisible = res"
  >
    <n-form
      v-if="drawerTitle === '添加管理员'"
      ref="formRef"
      :model="formData"
      :rules="formRules"
      class="mt-[25px]"
      label-align="right"
      label-placement="left"
      label-width="100"
      require-mark-placement="left"
    >
      <n-form-item label="管理员名称" path="nickName" required>
        <n-input
          v-model:value="formData.nickName"
          maxlength="20"
          placeholder="请输入管理员名称"
        />
      </n-form-item>
      <n-form-item label="账号" path="username" required>
        <n-input
          v-model:value="formData.username"
          :allow-input="onlyAllowNumberAndLetter"
          maxlength="15"
          placeholder="请输入管理员账号"
        />
      </n-form-item>
      <n-form-item label="初始密码" path="account">
        <n-input maxlength="15" disabled placeholder="xfh@123456#" />
      </n-form-item>
      <div class="mt-[-30px] pl-[100px] text-[#999999] text-[12px]">
        账号创建成功后，初始密码为xfh@123456#，请提醒相关人员及时修改密码
      </div>
      <n-form-item class="mt-[30px]" label="角色" path="role" required>
        <n-select
          v-model:value="formData.role"
          placeholder="请选择角色"
          label-field="roleName"
          value-field="roleId"
          filterable
          :options="roleOptions"
        />
      </n-form-item>
      <n-form-item label="管辖组织" path="dept" required>
        <n-tree
          block-line
          :data="deptOptions"
          key-field="id"
          label-field="name"
          checkable
          multiple
          :cascade="false"
          selectable
          check-strategy="all"
          :default-expand-all="true"
          @update:checked-keys="updateCheckedKeys"
        />
      </n-form-item>
    </n-form>

    <n-form
      v-else-if="drawerTitle === '编辑管理员'"
      ref="formRef"
      label-align="right"
      label-placement="left"
      label-width="100"
      :rules="formRules"
      :model="formData2"
    >
      <n-form-item label="管理员名称" path="nickName" required>
        <n-input
          v-model:value="formData2.nickName"
          maxlength="20"
          placeholder="请输入管理员名称"
        />
      </n-form-item>
      <n-form-item label="账号" path="username" required>
        <n-input
          v-model:value="formData2.username"
          maxlength="15"
          placeholder="请输入管理员账号"
          :allow-input="onlyAllowNumberAndLetter"
        />
      </n-form-item>
      <n-form-item label="角色" path="role" required>
        <n-select
          v-model:value="formData2.role"
          placeholder="请选择角色"
          label-field="roleName"
          value-field="roleId"
          filterable
          :options="roleOptions"
        />
      </n-form-item>
      <n-form-item label="管辖组织" path="dept" required>
        <n-tree
          block-line
          :data="deptOptions"
          key-field="id"
          label-field="name"
          checkable
          multiple
          :cascade="false"
          selectable
          check-strategy="all"
          :default-expand-all="true"
          :default-checked-keys="formData2.dept"
          @update:checked-keys="updateCheckedKeys2"
        />
      </n-form-item>
    </n-form>
  </my-drawer>
</template>
<style lang="scss" scoped></style>
