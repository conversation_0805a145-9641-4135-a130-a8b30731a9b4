<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui'
import EditTop from '@/components/EditTop.vue'
import { getStartup, postStartup } from '@/services/run/start/start'
import type { uploadFileItem } from '@/services/affairs/party-building-list/exam-indicators/types'
import { uploadImg } from '@/services/common'

const fileRef = ref<UploadFileInfo[]>([])
const loading = ref(false)
const imgSizeReactive = reactive<{ width: number; height: number }>({
  width: 750,
  height: 1334,
})
const oldImgUrlRef = ref('')
const fileSizeLimit = 200 * 1000 * 1000
const imgId = ref('')
onBeforeMount(async() => {
  loading.value = true
  const originalStartup = await getStartup()
  if (originalStartup && originalStartup.id) {
    oldImgUrlRef.value = originalStartup.pictureUrl ?? ''
  }
  loading.value = false
})
/** 保存启动页 */
function handleSave() {
  if (!imgId.value) {
    window.$message.error('请上传图片')
  }
  else {
    window.$dialog.warning({
      title: '提示',
      content: '确认保存该启动页?',
      positiveText: '确认',
      negativeText: '取消',
      onPositiveClick: () => {
        loading.value = true
        postStartup(imgId.value).then((res) => {
          window.$message.success('保存成功')
          loading.value = false
        })
      },
    })
  }
}

// 图片裁剪完毕
const handleCoverDone = async(file: File) => {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (oldImgUrlRef.value) {
      return
    }
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      imgId.value = data.fileId || ''
    }
  }
  catch (error) {}
}
// 图片删除
function handleCoverDelete() {
  oldImgUrlRef.value = ''
  imgId.value = ''
}

/** 监听上传文件 */
watch(fileRef, (newV) => {
  if (newV.length > 0) {
    const { file, url } = newV[0]
    if (url === null) {
      if (file) {
        if (file.size > fileSizeLimit) {
          window.$message.error('图片大小超出限制,请重新选择')
          fileRef.value = []
        }
      }
    }
  }
})
</script>
<template>
  <n-spin :show="loading">
    <div class="">
      <edit-top>
        <template #mid>
          启动页管理
        </template>
        <template #right>
          <n-button type="primary" @click="handleSave">
            保存
          </n-button>
        </template>
      </edit-top>
    </div>
    <div class="flex flex-col justify-center items-center">
      <div
        class="flex justify-center items-start gap-[30px] mb-[30px] pt-[8vh] pr-[5vw]"
      >
        <div class="flex flex-col">
          <span class="mb-[15px]">上传：</span>
          <img-uploader
            :need-cropper="false"
            :width="imgSizeReactive.width"
            :height="imgSizeReactive.height"
            :old-img-url="oldImgUrlRef"
            @done="handleCoverDone"
            @delete="handleCoverDelete"
          />
        </div>
      </div>
      <div class="text-[12px] font-[400] text-[#999] leading-[30px] pl-[1vw]">
        单张不超过200M，支持.jpg .jpeg .png格式，推荐上传750*1334分辨率
      </div>
    </div>
  </n-spin>
</template>
<style lang="scss" scoped>
:deep(.n-upload-file-list .n-upload-file.n-upload-file--image-card-type) {
  width: 250px;
  height: 445px;
}
</style>
