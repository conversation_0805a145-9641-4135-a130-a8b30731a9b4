<template>
  <div class="card">
    <NForm ref="formRef" :rules="formRules" :model="value">
      <NFormItem label="题型" path="subjectType">
        <NSelect
          :options="QUESTION_TYPE"
          :value="value.subjectType"
          :on-update-value="updateQuestionType"
        />
      </NFormItem>
      <NFormItem label="题目" path="subjectName">
        <NInput
          type="textarea"
          placeholder="请输入题目信息"
          :value="value!.subjectName"
          :on-update-value="updateQuestionTitle"
        />
      </NFormItem>

      <NFormItem
        v-if="value.subjectType !== QUESTIONTYPE.FILLED"
        label="选项"
        path="optionList"
      >
        <div class="w-full">
          <div v-for="(item, index) in value.optionList" :key="index">
            <div
              :key="index"
              class="flex items-center justify-between gap-x-10px mb-[12px]"
            >
              <NInput
                placeholder="请输入选项内容"
                :value="item.optionContent"
                :on-update-value="(v: any) => updateChoice({ ...item, optionContent: v }, index)
                "
                :disabled="haveTemplate"
              />

              <NButton
                class="!ml-[14px]"
                text
                type="error"
                :on-click="() => deleteChoice(index)"
                :disabled="haveTemplate"
              >
                <NIcon :size="14" class="text-black">
                  <Close />
                </NIcon>
              </NButton>
            </div>
          </div>
          <n-button
            quaternary
            type="info"
            :on-click="addChoice"
            :disabled="haveTemplate"
          >
            <template #icon>
              <n-icon>
                <AddAlt />
              </n-icon>
            </template>
            添加选项
          </n-button>
        </div>
      </NFormItem>

      <NFormItem label="必填">
        <NSwitch
          :checked-value="'1'"
          :unchecked-value="'0'"
          :value="value.isRequired"
          :on-update-value="updateRequired"
        />
      </NFormItem>

      <NFormItem
        v-if="value.subjectType === QUESTIONTYPE.FILLED"
        label="字数上限"
        path="maxWordCount"
      >
        <NInputNumber
          :min="0"
          :precision="0"
          placeholder="请输入字数上限"
          :value="value!.maxWordCount"
          :on-update-value="updateQuestionMaxWordCount"
        />
      </NFormItem>
    </NForm>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable vue/prop-name-casing */
import type { FormRules, NForm } from 'naive-ui'
import {
  NButton,
  NFormItem,
  NIcon,
  NInput,
  NInputNumber,
  NSelect,
  NSwitch,
} from 'naive-ui'
import type { PropType } from 'vue'
import { onUnmounted, ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { AddAlt, Close } from '@vicons/carbon'
import type {
  OptionItem,
  QuestionnaireSubj,
  Template,
} from '@/services/cloud-service/questionnaire/types'
import { QUESTIONTYPE2 as QUESTIONTYPE } from '@/constant'
import { emitter } from '@/utils/event-bus'

const QUESTION_TYPE = [
  { value: QUESTIONTYPE.RADIO, label: '单选题' },
  { value: QUESTIONTYPE.MULTI, label: '多选题' },
  { value: QUESTIONTYPE.FILLED, label: '填空题' },
]

// const EVALUATION_TYPE = [
//   { value: '0', label: '教师' },
//   { value: '1', label: '课程' },
//   { value: '2', label: '项目' },
// ]

const formRules: FormRules = {
  subjectType: [{ required: true, message: '请选择题型', type: 'string' }],
  subjectName: [
    {
      required: true,
      message: '请输入题目信息',
      type: 'string',
    },
  ],
  maxWordCount: [{ required: true, message: '请输入字数上限', type: 'number' }],
  optionList: [{ required: true, message: '请添加选项', type: 'array' }],
  // questionObject: [
  //   {
  //     required: true,
  //     message: '请选择评价指标',
  //     type: 'number',
  //   },
  // ],
  // questionDimension: [
  //   {
  //     required: true,
  //     message: '请输入统计展示名称',
  //     type: 'string',
  //   },
  // ],
}

const props = defineProps({
  value: {
    type: Object as PropType<QuestionnaireSubj>,
    default: () => {
      return {
        renderId: `question-${Date.now()}`,
        subjectType: '',
        subjectName: '',
        sort: 1,
        maxWordCount: 1000,
        isRequired: '1',
        // questionDimension: '',
        editing: true,
        // questionObject: null,
      }
    },
  },
  haveTemplate: {
    type: Boolean,
    default: false,
  },
  templateDetail: {
    type: Object as PropType<Template>,
    default: () => {
      return {
        templateItemList: [],
      }
    },
  },
  'onUpdate:value': {
    type: Function as PropType<(value: QuestionnaireSubj) => void>,
  },
})

watch(
  () => props.value.subjectType,
  (nval) => {
    if (props.haveTemplate && nval === QUESTIONTYPE.RADIO) {
      const data = cloneDeep(props.value!)
      data.optionList = []
      // 有模板且启用了单选
      props.templateDetail.templateItemList.forEach((item) => {
        data.optionList = data.optionList ?? []
        data.optionList.push({
          renderId: `choice-${Date.now()}`,
          sort: data.optionList.length + 1,
          optionContent: item.templateItemName,
          isReason: item.isReason,
        })
      })
      doUpdate(data)
    }
  },
)
function doUpdate(data: QuestionnaireSubj) {
  const { 'onUpdate:value': _updateValue } = props
  _updateValue?.(data)
}
function updateQuestionType(v: string) {
  const data = { ...props.value! }
  data.subjectType = v
  doUpdate(data)
}
function updateQuestionTitle(v: string) {
  const data = { ...props.value! }
  data.subjectName = v
  doUpdate(data)
}
function updateQuestionMaxWordCount(v: number) {
  const data = { ...props.value! }
  data.maxWordCount = v
  doUpdate(data)
}
// function updateDimension(v: string) {
//   const data = { ...props.value! }
//   data.questionDimension = v
//   doUpdate(data)
// }
function updateRequired(v: string) {
  const data = { ...props.value! }
  data.isRequired = v
  doUpdate(data)
}
function addChoice() {
  const data = cloneDeep(props.value!)
  data.optionList = data.optionList ?? []
  data.optionList.push({
    renderId: `choice-${Date.now()}`,
    sort: data.optionList.length + 1,
    optionContent: '',
    isReason: '',
  })
  doUpdate(data)
}
function deleteChoice(i: number) {
  const data = cloneDeep(props.value!)
  data.optionList!.splice(i, 1)
  doUpdate(data)
}
function updateChoice(choice: OptionItem, i: number) {
  const data = cloneDeep(props.value!)
  data.optionList![i] = choice
  doUpdate(data)
}
// function updateType(v: number) {
//   const data = { ...props.value! }
//   data.questionObject = v
//   doUpdate(data)
// }

const formRef = ref<InstanceType<typeof NForm>>()

emitter.on('notification-template-evaluation-validate', () => {
  formRef.value?.validate().catch(() => {})
})

onUnmounted(() => {
  emitter.off('notification-template-evaluation-validate')
})
// onMounted(loadData)
</script>

<style lang="scss" scoped>
.card {
  padding: 17px 24px 24px;

  transition: all 250ms;
  position: relative;
  border-radius: 3px;
  cursor: pointer;

  &.required .questionTitle {
    position: relative;

    &::after {
      content: '*';
      position: absolute;
      left: -12px;
      top: -6px;
      color: #d63434;
    }
  }

  &.active {
    box-shadow: 0px 0px 10px 0px rgba(66, 133, 247, 0.5);

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 2px;
      background: rgb(66, 133, 247);
      border-radius: 3px;
    }
  }

  .btns {
    position: absolute;
    top: 16px;
    right: 19px;
    width: 110px;
    height: 24px;
    background: #f6f7f8;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

:deep(.n-form-item) .n-form-item-label {
  font-weight: 600;
}

:deep(.n-form-item) .n-base-selection,
:deep(.n-form-item) .n-input {
  font-size: 14px;
}
</style>
