<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="缴费统计"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumnsOfPartyPaymentStatistics"
    :table-data="tableData"
    :total="total"
    :show-delete="false"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickExport">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        导出
      </n-button>
      <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        删除
      </n-button>
    </template>
    <template #filters>
      <n-input
        v-model:value="filterReactive.time"
        size="small"
        placeholder="请输入月份"
        clearable
      />
      <n-input
        v-model:value="filterReactive.orgName"
        size="small"
        placeholder="请输入所在组织"
        clearable
      />
      <n-input
        v-model:value="filterReactive.name"
        size="small"
        placeholder="请输入姓名"
        clearable
      />
      <n-input
        v-model:value="filterReactive.orderId"
        size="small"
        placeholder="请输入订单编号"
        clearable
      />
    </template>
  </table-container>
  <!-- 新增明主评议抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <n-form
        ref="formRef"
        class="w-full"
        size="large"
        :model="formData"
        label-placement="left"
      >
        <n-form-item span="24" label="订单号：">
          {{ formData?.orderId }}
        </n-form-item><n-form-item span="24" label="姓名：">
          {{ formData?.name }}
        </n-form-item><n-form-item span="24" label="手机号码：">
          {{ formData?.phone }}
        </n-form-item><n-form-item span="24" label="所在组织：">
          {{ formData?.orgName }}
        </n-form-item><n-form-item span="24" label="月份：">
          {{ formData?.time }}
        </n-form-item><n-form-item span="24" label="应缴金额：">
          {{ formData?.money }}
        </n-form-item><n-form-item span="24" label="缴纳状态：">
          {{ formData?.status }}
        </n-form-item><n-form-item span="24" label="支付方式：">
          {{ formData?.payway }}
        </n-form-item>
      </n-form>
      <!-- <template #footer>
          <div class="flex justify-center w-full gap-[12px]">
            <n-button
              type="primary"
              style="width: 80px"
              @click="handleClickConfirm"
            >
              返回
            </n-button>
          </div>
        </template> -->
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { NButton } from 'naive-ui'
import { columnsOfPaymentStatistics } from './config'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import {
  delTalkAboutTableListItem,
  getTalkAboutTableList,
} from '@/services/publicity/vote/talkAbout'

import { type PaymentRecord } from '@/services/publicity/partypayment/paymentrecords/type'

const filterReactive = ref<PaymentRecord>({})

const formData = ref<PaymentRecord>()

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getTalkAboutTableList, filterReactive, {
  batchDeleteTable: true,
  delApi: delTalkAboutTableListItem,
})

watch(filterReactive.value, () => {
  loadData()
})

const addTalkAboutFormRef = ref()
const { drawerTitle, showEditRef, editTypeRef } = useDrawerEdit(
  '缴费记录',
  handelConfirmEdit,
)

function handleClickExport() {}

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addTalkAboutFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addTalkAboutFormRef.value?.resetForm()
  }
})

// 修改和删除按钮渲染
const tableColumnsOfPartyPaymentStatistics = columnsOfPaymentStatistics(
  (row) => {
    return [
      h(
        NButton,
        {
          onClick: () => {
            editTypeRef.value = 'modify'
            showEditRef.value = true
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        '详情',
      ),
      h(DeleteButton, {
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    ]
  },
)
onMounted(loadData)
</script>

<style lang="scss" scoped></style>
