<script setup lang="ts">
import { NForm } from 'naive-ui'
import type { UploadFileInfo } from 'naive-ui'
import { formRules } from './config'
import {
  // getLatestVersion,
  getVersionInfo,
  postVersionItem,
  putVersionItem,
} from '@/services/system/Version'
import type { VersionForm } from '@/services/system/Version/types'
import type { uploadFileItem } from '@/services/types'
import { uploadImg } from '@/services'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<VersionForm>({
  name: '',
  version: '',
  updateInfo: '',
  downloadAddress: '',
  status: 1,
  type: 0,
  file: null,
})

const uploadProgress = ref(0)

const loading = ref(false)
const formRef = ref<InstanceType<typeof NForm>>()
const originalFileListRef = ref<UploadFileInfo[]>()
onBeforeMount(async() => {
  loading.value = true
  if (props.type === 'modify' && props.id) {
    try {
      const res = await getVersionInfo(props.id)
      formDataReactive.id = res.id
      formDataReactive.name = res.name
      formDataReactive.version = res.version
      formDataReactive.updateInfo = res.updateInfo
      formDataReactive.downloadAddress = res.downloadAddress
      formDataReactive.status = res.status
      formDataReactive.type = 0
      if (res.type === 0) {
        setTimeout(() => {
          originalFileListRef.value = [
            {
              url: res.downloadAddress,
              name: res.apkName ?? (res.name || ''),
              id: `original${res.apkName}`,
              status: 'finished',
              percentage: 100,
            },
          ]
          formDataReactive.file = res.downloadAddress
        }, 0)
      }
      loading.value = false
    }
    catch (error) {}
  }
})
// 文件改变
// function handleFileChange(fileInfoList: UploadFileInfo[]) {
//   if (fileInfoList.length > 0) {
//     formDataReactive.file = fileInfoList[0].file
//     // console.log('formDataReactive.file: ', formDataReactive.file)
//   }
//   else {
//     formDataReactive.file = null
//   }
// }

// async function checkVersion() {
//   if (props.type === 'add') {
//     try {
//       const res = await getLatestVersion()
//       if (formDataReactive.version <= res.version) {
//         window.$message.warning(
//           `版本号必须大于当前最新版本${res.version}`,
//           { duration: 3000, closable: true },
//         )
//         throw new Error('版本号必须大于当前最新版本')
//       }
//       // 如果版本号大于最新版本，可以在这里执行后续的逻辑
//     }
//     catch (error) {
//       console.error('获取最新版本时出错：', error)
//       throw error // 继续抛出错误
//     }
//   }
// }

// 验证表单,调用接口
async function validateAndSave() {
  loading.value = true
  const errors = await new Promise((resolve) => {
    formRef.value?.validate((errors: any) => {
      resolve(errors)
    })
  })

  if (!errors) {
    try {
      // await checkVersion()

      const saveFunction
        = props.type === 'modify' && props.id ? putVersionItem : postVersionItem
      // 处理formData
      const formData = new FormData()

      // 添加常规字段
      formData.append('type', String(formDataReactive.type))
      formData.append('name', formDataReactive.name)
      formData.append('status', String(formDataReactive.status))
      formData.append('updateInfo', formDataReactive.updateInfo)
      formData.append('version', formDataReactive.version)

      // 如果处于编辑状态，添加 id 字段
      if (props.type === 'modify') {
        formData.append('id', String(formDataReactive.id))
      }

      // 根据 type 添加特定字段
      if (formDataReactive.type === 0) {
        if (
          originalFileListRef.value
          && originalFileListRef.value[0]
          && originalFileListRef.value[0].url
        ) {
          formData.append('downloadAddress', originalFileListRef.value[0].url)
        }
        else {
          formData.append('downloadAddress', '') // 或者其他适当的处理
        }
      }
      const res = await saveFunction(formData)
      if (res) {
        window.$message.success('保存成功')
        emits('saved')
      }
      loading.value = false
    }
    catch (error) {
      console.error('验证版本时出错：', error)
      loading.value = false
      // 在出错时停止执行后续代码
    }
  }
}

async function handleUpload(
  file: File,
  options: any,
  callBack: (data: any) => void,
) {
  try {
    const fileData = new FormData()
    const fileItem = options.file.file as Blob
    fileData.append('file', fileItem)
    uploadProgress.value = 0
    const data: uploadFileItem = await uploadImg(
      fileData,
      (progress: number) => {
        if (progress >= 98) {
          uploadProgress.value = 98
        }
        else {
          uploadProgress.value = progress
        }
      },
    )
    formDataReactive.file = data.url as any
    uploadProgress.value = 100
    callBack(data)
  }
  catch (error) {
    window.$message.error('上传失败')
    // 在出错时停止执行后续代码
  }
}

watch(
  () => originalFileListRef.value,
  () => {
    if (originalFileListRef.value && originalFileListRef.value[0]) {
      formDataReactive.file = originalFileListRef.value[0].url as any
    }
    else {
      formDataReactive.file = null
    }
  },
  { deep: true },
)

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="90"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
    style="padding: 20px"
  >
    <n-form-item label="系统类型：" path="type" required>
      <n-radio-group v-model:value="formDataReactive.type">
        <n-radio style="margin-right: 30px" :value="0">
          安卓
        </n-radio>
        <!-- <n-radio :value="1">iOS</n-radio> -->
      </n-radio-group>
    </n-form-item>
    <n-form-item
      v-if="formDataReactive.type === 0"
      label="安装包："
      path="file"
    >
      <!-- @file-list-change="handleFileChange" -->
      <file-uploader-new
        v-model:original-file-list="originalFileListRef"
        need-progress
        :max="1"
        accept=".apk"
        show-tip
        :progress="uploadProgress"
        :upload-method="handleUpload"
      />
    </n-form-item>
    <n-form-item label="版本标题：" path="name">
      <n-input
        v-model:value="formDataReactive.name"
        placeholder="输入版本标题，50字以内"
        :maxlength="50"
        show-count
      />
    </n-form-item>
    <n-form-item label="版本号：" path="version">
      <n-input
        v-model:value="formDataReactive.version"
        placeholder="输入版本号，50字以内"
        :maxlength="50"
        show-count
        :disabled="type === 'modify'"
      />
    </n-form-item>
    <n-form-item label="升级信息：" path="updateInfo">
      <n-input
        v-model:value="formDataReactive.updateInfo"
        type="textarea"
        :rows="4"
        placeholder="输入升级信息，500字以内"
        :maxlength="500"
        show-count
      />
    </n-form-item>
    <!-- <n-form-item
      v-if="formDataReactive.type === 1"
      label="下载地址："
      path="downloadAddress"
    >
      <n-input
        placeholder="输入下载地址："
        v-model:value="formDataReactive.downloadAddress"
      />
    </n-form-item> -->
    <n-form-item label="启用状态：" required>
      <n-switch
        v-model:value="formDataReactive.status"
        :checked-value="1"
        :unchecked-value="0"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
