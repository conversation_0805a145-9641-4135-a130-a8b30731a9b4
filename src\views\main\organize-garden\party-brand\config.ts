import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  brandName: [
    {
      required: true,
      message: '请输入品牌名称',
      trigger: 'input',
    },
  ],
  coverUrl: {
    required: true,
    message: '请上传图片',
    trigger: 'change',
    type: 'string',
  },
  // brandSummary: {
  //   required: true,
  //   message: '请输入品牌简介',
  //   trigger: 'input',
  // },
  // logoDefinition: {
  //   required: true,
  //   message: '请输入LOGO释义',
  //   trigger: 'input',
  // },
  // brandConnotation: {
  //   required: true,
  //   message: '请输入品牌内涵',
  //   trigger: 'input',
  // },
  // featuresAchievements: {
  //   required: true,
  //   message: '请输入创建措施',
  //   trigger: 'input',
  // },
}
