import { commonReq, downloadReq } from '@/services/request'

/** 获取党费统计列表 */
export function getStatisticsList(params: any) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/es_dues_order/getEsDuesOrderStatisticsPage',
    params,
  })
}

/** 导出党费统计列表 */
export function exportStatisticsList(params: any) {
  return downloadReq.get({
    url: '/party-affairs/backend/v1/es_dues_order/export',
    responseType: 'blob',
    params,
  })
}

/** 获取党费基数列表 */
export function getBaseList(params: any) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/es_dues_base/page',
    params,
  })
}

/** 编辑党费基数 */
export function editBaseItem(data: any) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/es_dues_base',
    data,
  })
}

/** 备注党费基数 */
export function remarkBaseItem(data: any) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/es_dues_base/remark',
    params: data,
  })
}

/** 复制新建党费基数 */
export function copyBaseItem(data: any) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/es_dues_base/copy_new',
    params: data,
  })
}

/** 审核党费基数 */
export function examBaseItem(approvalStatus: any, data: any) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/es_dues_base/approval',
    params: { approvalStatus },
    data,
  })
}

/** 下载党费基数模板 */
export function downBaseTemplate() {
  return downloadReq.get({
    url: '/upms/sys-file/local',
    params: {
      fileName: encodeURI('人员党费模板.xlsx'),
    },
    responseType: 'blob',
  })
}

/** 导出党费基数列表 */
export function exportBaseList(params: any) {
  return downloadReq.get({
    url: '/party-affairs/backend/v1/es_dues_base/export',
    responseType: 'blob',
    params,
  })
}

/** 获取缴费记录列表 */
export function getRecordList(params: any) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/es_dues_order/page',
    params,
  })
}

/** 导出缴费记录列表 */
export function exportRecordList(params: any) {
  return downloadReq.get({
    url: '/party-affairs/backend/v1/es_dues_order/export_order',
    responseType: 'blob',
    params,
  })
}

/** 导入缴费记录列表 */
export function importRecordList(file: any, monthDate: any) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/es_dues_base/import_base',
    params: { monthDate },
    data: file,
  })
}
