import type { DataTableColumns, FormRules } from 'naive-ui'
import { NCollapse, NCollapseItem, NEllipsis, NIcon } from 'naive-ui'
import { KeyboardArrowDownSharp } from '@vicons/material'
import type { ExamIndicatorsListItem } from '@/services/affairs/discipline-inspection-list/types'
import { downloadFile } from '@/utils/downloader'

export const tableColumns: DataTableColumns<ExamIndicatorsListItem> = [
  {
    type: 'selection',
    multiple: true,
  },
  {
    key: 'index',
    title: '序号',
    align: 'center',
    width: '5%',
    render: (_, i) => i + 1,
  },
  {
    key: 'title',
    title: '标题',
    width: '17%',
    ellipsis: {
      tooltip: {
        contentStyle: { width: '400px', 'word-break': 'break-all' },
      },
    },
  },
  {
    key: 'matter',
    title: '事项',
    width: '17%',
    ellipsis: {
      tooltip: {
        contentStyle: { width: '400px', 'word-break': 'break-all' },
      },
    },
  },
  {
    key: 'evaluationRequirements',
    title: '工作要求',
    width: '17%',
    render: (row) => {
      return h(
        NEllipsis,
        {
          style: {
            maxWidth: '180px',
          },
          expandTrigger: 'click',
          lineClamp: '1',
          tooltip: true,
        },
        {
          default: () => {
            return h('span', {
              innerHTML: row.evaluationRequirements,
            })
          },
        },
      )
    },
  },
  {
    key: 'evaluationMode',
    title: '考核方式',
    width: '17%',
    render: (row) => {
      return h(
        NEllipsis,
        {
          style: {
            maxWidth: '180px',
          },
          expandTrigger: 'click',
          lineClamp: '1',
          tooltip: true,
        },
        {
          default: () => {
            return h('span', {
              innerHTML: row.evaluationMode,
            })
          },
        },
      )
    },
  },
  {
    key: 'fileList',
    title: '附件',
    width: '17%',
    render: (row) => {
      return row.fileList?.length
        ? h(
          NCollapse,
          {
            arrowPlacement: 'right',
          },
          [
            h(
              NCollapseItem,
              {},
              {
                header: () =>
                  h(
                    'div',
                    {
                      style: {
                        marginBottom: '2px',
                        cursor: 'pointer',
                        color: '#3f7ee8',
                      },
                    },
                    h(
                      'span',
                      {
                        onClick: (e: Event) => {
                          downloadFile(
                            row.fileList?.[0]?.fileName,
                            row.fileList?.[0]?.original,
                          )
                          e.stopPropagation()
                        },
                      },
                      row.fileList?.[0]?.original,
                    ),
                  ),
                arrow: () =>
                  h(
                    NIcon,
                    row.fileList?.length === 1
                      ? ''
                      : () => h(KeyboardArrowDownSharp),
                  ),
                default: () =>
                  row.fileList?.slice(1)
                    && row.fileList?.slice(1).map((item: any) => {
                      return h(
                        'div',
                        {
                          style: {
                            marginBottom: '2px',
                            cursor: 'pointer',
                            color: '#3f7ee8',
                          },
                        },
                        h(
                          'span',
                          {
                            onClick: (e: Event) => {
                              downloadFile(item.fileName, item.original)
                              e.stopPropagation()
                            },
                          },
                          item.original,
                        ),
                      )
                    }),
              },
            ),
          ],
        )
        : h('span', {}, { default: () => '--' })
    },
  },
  {
    key: 'evaluationScore',
    title: '考核分数',
    width: '10%',
  },
]

export const formRules: FormRules = {
  fileList: {
    required: true,
    validator(rule: any, value: any) {
      if (value === null) {
        return new Error('请选择文件')
      }
      return true
    },
    trigger: 'change',
  },
}
