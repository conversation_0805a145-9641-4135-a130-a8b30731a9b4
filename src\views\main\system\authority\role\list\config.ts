import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { VNodeChild } from 'vue'
import type { FormRules } from 'naive-ui'
import type { ICarousel } from '@/services/run/carousel/types'

export function getTableColumns(
  optionColumnRenderer: (row: ICarousel) => VNodeChild,
): TableColumns<ICarousel> {
  return [
    {
      type: 'selection',
      align: 'center',
    },
    {
      title: '角色名称',
      key: 'roleName',
    },
    {
      title: '角色描述',
      key: 'roleDesc',
    },
    {
      title: '包含账号数',
      key: 'accountNum',
    },
    {
      title: '更新时间',
      key: 'updateTime',
    },
    {
      title: '操作',
      key: 'action',
      align: 'left',
      width: 280,
      render: optionColumnRenderer,
    },
  ]
}

export const formRules: FormRules = {
  roleName: {
    required: true,
    message: '请输入角色名称',
    trigger: 'blur',
  },
}

export const mockData = [
  {
    roleName: '系统管理员',
    roleDescription: '系统管理',
    accountNumber: 100,
    updateTime: '2022-10-10',
  },
]
