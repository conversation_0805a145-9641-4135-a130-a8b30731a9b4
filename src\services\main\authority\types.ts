/*
 * @Description: 权限管理相关类型
 */

/**
 * 分页类型
 */
export interface IAuthorityResPagination<T = any> {
  records: T[]
  total: number
  size: number
  current: number
  orders: any[]
  optimizeCountSql: boolean
  searchCount: boolean
  countId?: any
  maxLimit?: any
  pages: number
}

/**
 * 用户列表查询参数
 */
export interface IUserQuery {
  current?: number
  size?: number
}

/**
 * 用户实体类型
 */
export interface IUser {
  userId: number
  username: string
  locked: 0 | 9
  phone: string
  idNumber: string | null
  deptId: number
  deptName: string
  verifyStatus: number
  postId: number | null
  jobTitleId: number | null
  email: string | null
  avatar: string | null
  picCode: string | null
  phoneCode: string | null
  createdAt: string
  updatedAt: string
}

/**
 * 用户表单类型
 */
export interface IUserForm {
  username: string
  companyId: number | null
  deptId: number | null
  jobTitleId: number | null
  postId: number | null
  phone: string
  idNumber: string
  email: string
  role: number[] | null
  lockFlag: '0' | '9'
  postType: number | null
}

/**
 * 分页查询返回的数据类型
 */
export interface IResPagination<T = any> {
  pageNum: number
  pageSize: number
  totalPage: number
  total: string
  list: T[]
}

/**
 * 角色实体类型
 */
export interface IRole {
  roleId: number
  roleName: string
  roleCode: string
  roleDesc: string
  delFlag: string
  createdBy: string | null
  createdAt: string | null
  updatedBy: string | null
  updatedAt: string | null
}

/**
 * 部门树节点类型
 */
export interface IDeptTreeNode {
  id: number
  name: string
  parentId: number
  weight: number
  type: 0 | 1
  createdAt: string
  children: IDeptTreeNode[] | null
  labelId: number[] | null
}

/**
 * 组织编辑表单类型
 */
export interface IDeptForm {
  deptId?: number
  parentId: number | null
  type: 0 | 1 | null
  name: string
  sort: number | null
  labelIdList: number[]
}

export interface IUserQueryByDept {
  pageNo: number
  pageSize: number
  username?: string
}

/**
 * 菜单实体类
 */
export interface IMenu {
  id: number
  parentId: number
  name: string
  path?: string
  icon: string
  sortOrder: number
  type: '0' | '1' | '2'
  children: IMenu[]
  permission?: string
  filePath?: string
  routeName?: string
  visible?: string
  redirect?: string
}

/**
 * 菜单表单类
 */
export interface IMenuForm {
  menuId?: number
  icon?: string
  type: string
  parentId: string | null | number | undefined
  name: string
  path?: string
  sortOrder: number | null
  permission?: string
  filePath?: string
  routeName?: string
  visible?: string
  redirect?: string
}

/**
 * 角色列表查询参数
 */
export interface IRoleQuery {
  pageNo: number
  pageSize: number
  roleName?: string
}

/**
 * 角色表单类型
 */
export interface IRoleForm {
  roleName: string
  roleDesc: string
  roleCode: string
}

/**
 * 审核用户实体类
 */
export interface IVerifyUser {
  createdAt: string
  userId: number
  username: string
  phone: string
  verifyStatus: number
  idNumber: string
  email: string
  companyName: string
  deptName: string
  postName: string
  jobTitleName: string
  delFlag: '1' | '0'
}

/**
 * 审核用户列表查询参数
 */
export interface IVerifyUserQuery {
  pageNo: number
  pageSize: number
  verifyStatus: number
  usernameOrPhone?: string
  startTime?: string
  endTime?: string
}
