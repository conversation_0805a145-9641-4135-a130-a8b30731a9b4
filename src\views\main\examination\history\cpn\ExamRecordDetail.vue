<!--
 * @Description: 考试记录查看页
-->
<template>
  <n-scrollbar style="height: calc(90vh - 120px); padding-right: 20px">
    <div ref="detailElRef" style="font-family: 'YaHei'">
      <question-item
        v-for="(item, index) in questionListRef"
        :key="item.id"
        :index="index"
        :question="item"
      />
    </div>
  </n-scrollbar>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { onBeforeMount, ref } from 'vue'
import { NScrollbar } from 'naive-ui'
import QuestionItem from './QuestionItem.vue'
import jsPDF from './YaHei'
import type { IExamRecord } from '@/services/examination/index'
import { getExamRecordDetail } from '@/services/examination/index'

const props = defineProps({
  row: {
    type: Object as PropType<IExamRecord>,
    required: true,
  },
})

const questionListRef = ref<any[]>([])

onBeforeMount(() => {
  getExamRecordDetail(props.row.recordId).then((res) => {
    questionListRef.value = JSON.parse(res.questionText).subjectList
  })
})

// 导出
const detailElRef = ref()
function handleExport() {
  const { examUser, examName } = props.row
  const filename = `${examUser}的${examName}.pdf`

  jsPDF.html(detailElRef.value, {
    callback(doc) {
      doc.save(filename)
    },
    margin: 30,
    autoPaging: 'text',
  })
}

defineExpose({
  handleExport,
})
</script>
