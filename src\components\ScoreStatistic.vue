<script setup lang="ts">
import { type DataTableColumns, NEllipsis } from 'naive-ui'
import CustomDialog from './CustomDialog.vue'
import type {
  StatisticScoreList,
  TargetItemList,
} from '@/services/affairs/party-building-list/types'
import { getStatisticScoreList } from '@/services/affairs/party-building-list'
import { getStatisticScoreListDiscipline } from '@/services/affairs/discipline-inspection-list'

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  width: {
    type: String,
    default: '1200px',
  },
  showAction: {
    type: Boolean,
    default: true,
  },
  maskClosable: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: '',
  },
  belong: {
    type: String,
  },
})
const emits = defineEmits(['confirm', 'update:show'])

// 点击确定
function handleConfirm() {
  emits('confirm', false)
  emits('update:show', false)
}

const tableColumns: DataTableColumns<TargetItemList> = [
  {
    key: 'index',
    title: '序号',
    align: 'center',
    width: '6%',
    render: (_, i) => i + 1,
  },
  {
    key: 'evaluationRequirements',
    title: '工作要求',
    width: '32%',
    render: (row) => {
      return row.evaluationRequirements
        ? h(
          NEllipsis,
          {
            expandTrigger: 'click',
            lineClamp: '1',
            tooltip: true,
          },
          {
            default: () => {
              return h('div', {
                style: {
                  width: '400px',
                },
                innerHTML: row.evaluationRequirements,
              })
            },
          },
        )
        : '--'
    },
  },
  {
    key: 'evaluationMode',
    title: '考核方式',
    width: '32%',
    render: (row) => {
      return row.evaluationMode
        ? h(
          NEllipsis,
          {
            expandTrigger: 'click',
            lineClamp: '1',
            tooltip: true,
          },
          {
            default: () => {
              return h('div', {
                style: {
                  width: '400px',
                },
                innerHTML: row.evaluationMode,
              })
            },
          },
        )
        : '--'
    },
  },
  {
    key: 'evaluationScore',
    title: '考核得分',
    width: '10%',
    render: row => (row.evaluationScore ? row.evaluationScore : '--'),
  },
  {
    key: 'performance',
    title: '完成情况',
    width: '10%',
    render: row => (row.performance ? row.performance : '--'),
  },
  {
    key: 'score',
    title: '得分',
    width: '10%',
    render: row => (row.score ? row.score : '--'),
  },
]

const scoreList = ref<StatisticScoreList[]>([])
const defaultTab = ref()
function loadScoreList(id: string) {
  if (props.belong === 'discipline') {
    getStatisticScoreListDiscipline(String(id)).then((res: any) => {
      if (res.length > 0) {
        scoreList.value = res
        defaultTab.value = res[0].organization
      }
    })
  } else {
    getStatisticScoreList(String(id)).then((res: any) => {
      if (res.length > 0) {
        scoreList.value = res
        defaultTab.value = res[0].organization
      }
    })
  }
}

defineExpose({
  loadScoreList,
})
</script>

<template>
  <CustomDialog
    :show="show"
    :title="title"
    btn-title="导出"
    width="1200px"
    @update:show="(v) => emits('update:show', v)"
    @confirm="handleConfirm"
  >
    <div class="score-statistic">
      <n-tabs v-model:value="defaultTab" type="line">
        <n-tab-pane
          v-for="(item, index) in scoreList"
          :key="index"
          :name="item.organization"
          :tab="item.organization"
        >
          <n-data-table :columns="tableColumns" :data="item.targetItemList" />
          <div class="total">
            <span>总得分：{{ item.totalScore }} 分</span>
          </div>
        </n-tab-pane>
      </n-tabs>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.dialog-header {
  width: 100%;
  height: 50px;
  background: #f2f4f8;
  line-height: 50px;
  padding-left: 29px;
}

.score-statistic {
  padding: 20px 20px 10px 20px;
  position: relative;
  .total {
    position: absolute;
    bottom: 0px;
    right: 30px;
    color: #cb0000;
  }
}
:deep(.n-tabs .n-tab-pane) {
  padding-bottom: 40px;
}

.dialog-action {
  width: 100%;
  height: 60px;
  background: #fcfdfe;
  text-align: right;
  line-height: 60px;
  padding-right: 29px;
  border-top: 1px solid #f2f3f6;

  .n-button {
    width: 64px;
    height: 30px;
    border-radius: 15px;

    &:nth-child(n + 2) {
      margin-left: 8px;
    }
  }
}
</style>
