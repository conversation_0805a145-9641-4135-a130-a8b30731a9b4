import { commonReq } from '../request'
import type { PaginationReq2 } from '../types'
import type {
  ActivistsMemberRosterType,
  AddHandleItem,
  AddHeartTalkRecordItem,
  AddRewardItem,
  BranchBasicOrganizationConditionType,
  BranchBasicOrganizationConditionUserType,
  CatalogType,
  DemocraticEvaluationType,
  ImportantMattersRecordType,
  MeetingRecordType,
  OrganizationalTransferType,
  PartyBranchActivityType,
  PartyMemberActivityType,
  PartyMemberMeetingMinutesItemType,
  PartyMemberType,
  PartyMembersAttendLecturesType,
  PartySituationReport,
} from '@/services/recordBookMaintenance/types'

/** 获取组织生活记录本目录 */
export function getRecordBookCategoryList() {
  return commonReq.get<CatalogType[]>({
    url: '/party-affairs/work-record-book/catalog/all',
  })
}

// 组织生活记录本------获取会议记录列表
export function getMeetingRecordList(params: {
  pageNum: number
  pageSize: number
  deptId: string
  year: string
  meetingType: string
  startTimeSort: string
}) {
  return commonReq.get<{ records: MeetingRecordType[]; total: number }>({
    url: '/party-affairs/backend/v1/internal-meeting/get_meeting_list_4_work_record_book',
    params,
  })
}

// 组织生活记录本----组织关系转接列表
export function getTransferList(params: {
  pageNum: number
  pageSize: number
  deptId: string
  year: string
  transferType: string
  transferTimeSort: string
}) {
  return commonReq.get<{
    records: OrganizationalTransferType[]
    total: number
  }>({
    url: '/org-construction/backend/v1/transfer/get_org_transfer_list_4_work_record_book',
    params,
  })
}

// 获取党员名册列表
export function getPartyMemberList(params: {
  pageNum: number
  pageSize: number
  deptId: string
  year: string
}) {
  return commonReq.get<{
    records: PartyMemberType[]
    total: number
  }>({
    url: '/party-affairs/work-record-book/party_member_roster/list',
    params,
  })
}

// 获取党员名册详情
export function getPartyMemberDetail(id: string) {
  return commonReq.get<{
    records: OrganizationalTransferType[]
    total: number
  }>({
    url: `/party-affairs/work-record-book/party_member_roster/${id}`,
  })
}

// 新增党员
export function addPartyMemberRoster(data: PartyMemberType) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/party_member_roster',
    data,
  })
}

// 编辑党员详情
export function editPartyMemberRoster(data: PartyMemberType) {
  return commonReq.put({
    url: '/party-affairs/work-record-book/party_member_roster',
    data,
  })
}

// 删除党员
export function deletePartyMemberRoster(id: string) {
  return commonReq.delete({
    url: `/party-affairs/work-record-book/party_member_roster/${id}`,
  })
}

// 交换顺序
export function exchangePartyMemberRoster(data: {
  id: string
  type: 'down' | 'up'
  deptId: string
  year: string
}) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/party_member_roster/swap-sort',
    data,
  })
}

// 党员名册-数据同步
export function syncPartyMemberRoster(params: {
  deptId: string
  syncType: '0' | '1'
}) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/party_member_roster/sync-data',
    params,
  })
}

// 查询同步时间
export function getSyncTime(params: {
  fieldType: string
  deptId: string
  year: string
}) {
  return commonReq.get<{
    syncTime: string
  }>({
    url: '/party-affairs/work-record-book/sync-record/time',
    params,
  })
}

// 查询党员名册是否展示同步弹框
export function getPartyMemberRosterShowSyncDialog(params: {
  deptId: string
  year: string
}) {
  return commonReq.get<boolean>({
    url: '/party-affairs/work-record-book/party_member_roster/check-is-required-tip',
    params,
  })
}

// 查询党员听党课情况
export function getPartyMembersAttendLectures(params: {
  pageNum: number
  pageSize: number
  deptId: string
  year: string
  startTimeSort: string
}) {
  return commonReq.get<{
    records: PartyMembersAttendLecturesType[]
    total: number
  }>({
    url: '/party-affairs/backend/v1/internal-meeting/get_party_lecture_list_4_work_record_book',
    params,
  })
}

// 查询入党积极分子名册列表
export function getPartyMemberRosterList(params: {
  pageNum: number
  pageSize: number
  deptId: string
  year: string
}) {
  return commonReq.get<{
    records: ActivistsMemberRosterType[]
    total: number
  }>({
    url: '/party-affairs/work-record-book/activists_member_roster/list',
    params,
  })
}

// 删除入党积极分子
export function deletePartyMemberActiveRoster(id: string) {
  return commonReq.delete({
    url: `/party-affairs/work-record-book/activists_member_roster/${id}`,
  })
}

// 同步入党积极分子名册数据
export function syncPartyMemberActiveRoster(params: {
  deptId: string
  syncType: '0' | '1'
}) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/activists_member_roster/sync-data',
    params,
  })
}

// 判断入党积极分子同步数据是否展示弹框
export function getPartyMemberActiveRosterShowSyncDialog(params: {
  deptId: string
  year: string
}) {
  return commonReq.get<boolean>({
    url: '/party-affairs/work-record-book/activists_member_roster/check-is-required-tip',
    params,
  })
}

// 查询支部基本组织状况
export function getBranchBasicOrganizationCondition(params: {
  deptId: string
  year: string
}) {
  return commonReq.get<BranchBasicOrganizationConditionType>({
    url: '/party-affairs/work-record-book/basic_dept_condition/detail',
    params,
  })
}

// 编辑支部基本组织状况
export function editBranchBasicOrganizationCondition(
  data: BranchBasicOrganizationConditionType,
) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/basic_dept_condition/edit',
    data,
  })
}

// 根据部门获取人员列表
export function getUserByDepartmentId(params: { deptId: string }) {
  return commonReq.get<BranchBasicOrganizationConditionUserType[]>({
    url: '/upms/portal-user/user/list/dept_id',
    params,
  })
}

// 整体下载成册
export function downloadWorkRecordBook(params: {
  deptName: string
  partyBranchName: string
  deptId: string
  year: string
  type: 'word' | 'pdf'
  yearUpCase: string
}) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/word/all/download',
    params,
    responseType: 'blob',
  })
}

// 整体同步
export function syncWorkRecordBook(params: {
  deptId: string
  year: string
  syncType: '0' | '1' // 同步类型 0-确认保留，1-覆盖所有
}) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/sync-record/all/sync',
    params,
  })
}

// 查询党员议事记录列表
export function getPartyMemberMinutesList(params: {
  pageNum: number
  pageSize: number
  deptId: string
  year: string
}) {
  return commonReq.get<{
    total: number
    records: PartyMemberMeetingMinutesItemType[]
  }>({
    url: '/party-affairs/work-record-book/party_member_discussion/list',
    params,
  })
}

// 查询党员议事详情
export function getPartyMemberMinutesDetail(id: string) {
  return commonReq.get<PartyMemberMeetingMinutesItemType>({
    url: `/party-affairs/work-record-book/party_member_discussion/${id}`,
  })
}

// 新增党员议事记录
export function addPartyMemberMinutes(
  data: PartyMemberMeetingMinutesItemType & {
    deptId: string
    year: string
  },
) {
  return commonReq.post<any>({
    url: '/party-affairs/work-record-book/party_member_discussion',
    data,
  })
}

// 编辑党员议事记录
export function editPartyMemberMinutes(
  data: PartyMemberMeetingMinutesItemType,
) {
  return commonReq.put<any>({
    url: '/party-affairs/work-record-book/party_member_discussion',
    data,
  })
}

// 删除党员议事记录
export function deletePartyMemberMinutes(id: string) {
  return commonReq.delete<any>({
    url: `/party-affairs/work-record-book/party_member_discussion/${id}`,
  })
}

// 查询党内情况通报记录
export function getPartyMemberSituationNoticeList(params: any) {
  return commonReq.get<{ total: number; records: PartySituationReport[] }>({
    url: '/party-affairs/work-record-book/party_situation_report/list',
    params,
  })
}

// 查询党内通报记录详情
export function getPartyMemberSituationDetail(id: string) {
  return commonReq.get<PartySituationReport>({
    url: `/party-affairs/work-record-book/party_situation_report/${id}`,
  })
}

// 新增党内通报记录
export function addPartyMemberSituation(
  data: PartySituationReport & {
    deptId: string
    year: string
  },
) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/party_situation_report',
    data,
  })
}

// 编辑党内通报记录
export function editPartyMemberSituation(data: PartySituationReport) {
  return commonReq.put({
    url: '/party-affairs/work-record-book/party_situation_report',
    data,
  })
}

// 删除党内通报记录
export function deletePartyMemberSituation(id: string) {
  return commonReq.delete({
    url: `/party-affairs/work-record-book/party_situation_report/${id}`,
  })
}

// 查询重要事项征求意见列表
export function getImportantMattersList(params: {
  pageNum: number
  pageSize: number
  deptId: string
  year: string
}) {
  return commonReq.get<{
    total: number
    records: ImportantMattersRecordType[]
  }>({
    url: '/party-affairs/work-record-book/take_advice_4_important_matters/list',
    params,
  })
}

// 查询重要事项详情
export function getImportantMattersDetail(id: string) {
  return commonReq.get<ImportantMattersRecordType>({
    url: `/party-affairs/work-record-book/take_advice_4_important_matters/${id}`,
  })
}

// 新增重要事项记录
export function addImportantMatters(
  data: ImportantMattersRecordType & { deptId: string; year: string },
) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/take_advice_4_important_matters',
    data,
  })
}

// 编辑重要事项记录
export function editImportantMatters(data: ImportantMattersRecordType) {
  return commonReq.put({
    url: '/party-affairs/work-record-book/take_advice_4_important_matters',
    data,
  })
}

// 删除重要事项记录
export function deleteImportantMatters(id: string) {
  return commonReq.delete({
    url: `/party-affairs/work-record-book/take_advice_4_important_matters/${id}`,
  })
}

// 党组织积极分子活动情况列表
export function getPartyMemberActivityList(params: {
  pageNum: number
  pageSize: number
  deptId: string
  year: string
}) {
  return commonReq.get<{ total: number; records: PartyMemberActivityType[] }>({
    url: '/party-affairs/work-record-book/activists_member_activity/list',
    params,
  })
}

// 党组织积极分子活动详情
export function getPartyMemberActivityDetail(id: string) {
  return commonReq.get<any>({
    url: `/party-affairs/work-record-book/activists_member_activity/${id}`,
  })
}

// 新增党组织积极分子活动
export function addPartyMemberActivity(data: any) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/activists_member_activity',
    data,
  })
}

// 编辑党组织积极分子活动
export function editPartyMemberActivity(data: any) {
  return commonReq.put({
    url: '/party-affairs/work-record-book/activists_member_activity',
    data,
  })
}

// 删除党组织积极分子活动
export function deletePartyMemberActivity(id: string) {
  return commonReq.delete({
    url: `/party-affairs/work-record-book/activists_member_activity/${id}`,
  })
}

// 党支部参加党委活动列表
export function getPartyBranchActivityList(params: {
  pageNum: number
  pageSize: number
  deptId: string
  year: string
}) {
  return commonReq.get<{ total: number; records: PartyBranchActivityType[] }>({
    url: '/party-affairs/work-record-book/attend_activity/list',
    params,
  })
}

// 党支部参加党委活动详情
export function getPartyBranchActivityDetail(id: string) {
  return commonReq.get<PartyBranchActivityType>({
    url: `/party-affairs/work-record-book/attend_activity/${id}`,
  })
}

// 新增党支部参加党委活动
export function addPartyBranchActivity(data: any) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/attend_activity',
    data,
  })
}

// 编辑党支部参加党委活动
export function editPartyBranchActivity(data: any) {
  return commonReq.put({
    url: '/party-affairs/work-record-book/attend_activity',
    data,
  })
}

// 删除党支部参加党委活动
export function deletePartyBranchActivity(id: string) {
  return commonReq.delete({
    url: `/party-affairs/work-record-book/attend_activity/${id}`,
  })
}

/** 获取主题党日活动情况登记 */
export function getPartyDayActivityList(
  params: {
    deptId: string
    year: string
  } & PaginationReq2,
) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/internal-meeting/get_theme_party_day_list_4_work_record_book',
    params,
  })
}

/** 获取谈心谈话记录列表 */
export function getHeartTalkRecordList(
  params: {
    deptId: string
    year: string
  } & PaginationReq2,
) {
  return commonReq.get({
    url: '/party-affairs/work-record-book/talk_record/list',
    params,
  })
}

/** 新增谈心谈话记录 */
export function addHeartTalkRecord(
  data: AddHeartTalkRecordItem & { deptId: string; year: string },
) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/talk_record',
    data,
  })
}

/** 编辑谈心谈话记录 */
export function editHeartTalkRecord(data: AddHeartTalkRecordItem) {
  return commonReq.put({
    url: '/party-affairs/work-record-book/talk_record',
    data,
  })
}

/** 谈心谈话记录详情 */
export function getHeartTalkRecordDetail(id: string) {
  return commonReq.get<AddHeartTalkRecordItem>({
    url: `/party-affairs/work-record-book/talk_record/${id}`,
  })
}

/** 删除谈心谈话记录  */
export function deleteHeartTalkRecordItem(id: string) {
  return commonReq.delete({
    url: `/party-affairs/work-record-book/talk_record/${id}`,
  })
}

/** 获取奖励情况列表 */
export function getRewardStatusList(
  params: {
    deptId: string
    year: string
  } & PaginationReq2,
) {
  return commonReq.get({
    url: '/party-affairs/work-record-book/party-award/list',
    params,
  })
}

/** 新增奖励情况 */
export function addRewardStatus(
  data: AddRewardItem & { deptId: string; year: string },
) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/party-award',
    data,
  })
}

/** 编辑奖励情况 */
export function editRewardStatus(data: AddRewardItem) {
  return commonReq.put({
    url: '/party-affairs/work-record-book/party-award',
    data,
  })
}

/** 奖励情况详情 */
export function getRewardStatusDetail(id: string) {
  return commonReq.get<AddRewardItem>({
    url: `/party-affairs/work-record-book/party-award/${id}`,
  })
}

/** 删除奖励情况  */
export function deleteRewardStatusItem(id: string) {
  return commonReq.delete({
    url: `/party-affairs/work-record-book/party-award/${id}`,
  })
}

/** 获取处置情况列表 */
export function getHandleStatusList(
  params: {
    deptId: string
    year: string
  } & PaginationReq2,
) {
  return commonReq.get({
    url: '/party-affairs/work-record-book/disciplinary-decision/list',
    params,
  })
}

/** 新增处置情况 */
export function addHandleStatus(
  data: AddHandleItem & { deptId: string; year: string },
) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/disciplinary-decision',
    data,
  })
}

/** 编辑处置情况 */
export function editHandleStatus(data: AddHandleItem) {
  return commonReq.put({
    url: '/party-affairs/work-record-book/disciplinary-decision',
    data,
  })
}

/**  处置情况详情 */
export function getHandleStatusDetail(id: string) {
  return commonReq.get<AddHandleItem>({
    url: `/party-affairs/work-record-book/disciplinary-decision/${id}`,
  })
}

/** 删除处置情况  */
export function deleteHandleStatusItem(id: string) {
  return commonReq.delete({
    url: `/party-affairs/work-record-book/disciplinary-decision/${id}`,
  })
}

/** 查询民主评议党员情况  */
export function getDemocraticReview(params: { deptId: string; year: string }) {
  return commonReq.get<DemocraticEvaluationType>({
    url: '/party-affairs/work-record-book/democratic_review/detail',
    params,
  })
}

/** 查询民主评议党员情况  */
export function editDemocraticReview(data: any) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/democratic_review/edit',
    data,
  })
}
