<script setup lang="ts">
import { NForm } from 'naive-ui'
import type { SelectOption } from 'naive-ui'
import { reactive } from 'vue'
import { formRulesForOrganization } from './config'
import type {
  AdvanCedPartyOrganizationItem,
  PartyOrganizationTreeItem,
} from '@/services/publicity/advancedShow/type'
import {
  getAdvancedPartyOrganizationItem,
  postInsertAdvancedPartyOrganizationItem,
  putEditorAdvancedPartyOrganizationItem,
} from '@/services/publicity/advancedShow'
import { formatTimeStamp } from '@/utils/format'
import { uploadImg } from '@/services'
import type { uploadFileItem } from '@/services/affairs/party-building-list/exam-indicators/types'
import { useCurrentOrganizationListOptions } from '@/hooks/use-select-options'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const { organizationCurrentListTree } = useCurrentOrganizationListOptions()
const treeObj = reactive<{ treeOptions: PartyOrganizationTreeItem[] }>({
  treeOptions: organizationCurrentListTree as any,
})

const formDataReactive = reactive<AdvanCedPartyOrganizationItem>({
  id: '',
  orgId: '',
  orgName: '',
  epithet: '',
  certificateId: '',
  certificate: '',
  recommandStatus: '0',
  publishTime: '',
  content: '',
})

const formRef = ref<InstanceType<typeof NForm>>()
/** 获取组织ID */
const handleUpdateValue = (v: string, option: SelectOption) => {
  formDataReactive.orgId = v
  formDataReactive.orgName = option.name as string
}

onBeforeMount(() => {
  // partyOrganizationTree()
  if (props.type === 'modify' && props.id) {
    getAdvancedPartyOrganizationItem(props.id).then((res) => {
      formDataReactive.id = res.id || props.id || ''
      formDataReactive.orgId = res.orgId || ''
      formDataReactive.orgName = res.orgName || ''
      formDataReactive.epithet = res.epithet || ''
      formDataReactive.certificateId = res.certificateId || ''
      formDataReactive.certificate = res.certificate || ''
      formDataReactive.recommandStatus = res.recommandStatus
      formDataReactive.publishTime = res.publishTime
      formDataReactive.content = res.content
      handleUpdateValue(res.orgId, { name: res.orgName })
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // 新增或编辑最后确定发布时间
      formDataReactive.publishTime = formatTimeStamp(new Date().getTime())
      if (formDataReactive.id) {
        putEditorAdvancedPartyOrganizationItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
      else {
        postInsertAdvancedPartyOrganizationItem(formDataReactive).then(
          (res) => {
            if (res) {
              window.$message.success('保存成功')
              emits('saved')
            }
          },
        )
      }
    }
  })
}

const handleCertificateDone = async(file: File) => {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      formDataReactive.certificateId = data.fileId
    }
  }
  catch (error) {}
}
const handleCertificateDelete = () => {
  formDataReactive.certificateId = ''
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="140"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRulesForOrganization"
  >
    <n-form-item label="组织名称：" path="orgName">
      <!-- <n-cascader
        v-model:value="formDataReactive.orgId"
        :options="treeObj.treeOptions"
        :default-value="formDataReactive.orgName"
        value-field="id"
        label-field="name"
        children-field="children"
        check-strategy="child"
        :show-path="false"
        clearable
        @update:value="handleUpdateValue"
      /> -->
      <n-tree-select
        v-model:value="formDataReactive.orgId"
        :options="treeObj.treeOptions"
        value-field="id"
        label-field="name"
        key-field="id"
        children-field="children"
        check-strategy="all"
        placeholder="请选择所属党组织"
        :show-path="false"
        clearable
        filterable
        @update:value="handleUpdateValue"
      />
    </n-form-item>
    <n-form-item label="荣誉名称：" path="epithet">
      <n-input
        v-model:value="formDataReactive.epithet"
        placeholder="请输入荣誉名称"
        maxlength="20"
        show-count
        clearable
      />
    </n-form-item>
    <n-form-item span="24" label="奖状：" path="certificateId">
      <ImgUploader
        v-model:oldImgUrl="formDataReactive.certificate"
        :width="380"
        :height="200"
        :need-cropper="false"
        @done="handleCertificateDone"
        @delete="handleCertificateDelete"
      />
    </n-form-item>
    <n-form-item label="是否推荐：" path="recommandStatus">
      <n-switch
        v-model:value="formDataReactive.recommandStatus"
        checked-value="1"
        unchecked-value="0"
      />
    </n-form-item>
    <n-form-item label="先进事迹：" path="content" required>
      <RichEditor
        v-model:value="formDataReactive.content"
        style="width: 100%"
        :rich-height="350"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
