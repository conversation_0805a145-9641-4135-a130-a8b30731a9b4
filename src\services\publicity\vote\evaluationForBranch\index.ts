import type {
  PartyBuildingEvaluationTableItemForBranch,
  TargetDataType,
} from './type'
import { commonReq, downloadReq } from '@/services/request'
import type { PaginationRes } from '@/services/types'

/** 获取党建考核列表（支部） */
export function getPartyBuildingEvaluationTableListForBranch(params: {
  year: string
}) {
  return commonReq.get<
  PaginationRes<PartyBuildingEvaluationTableItemForBranch>
  >({
    url: '/party-affairs/backend/v1/voting-assess/branch',
    params,
  })
}

/** 获取党建考核指标项（支部） */
export function getPartyBuildingEvaluationTargetInfoForBranch(id: string) {
  return commonReq.get<TargetDataType>({
    url: `/party-affairs/backend/v1/voting-assess/target-item/branch/${id}`,
  })
}

/** 获取党建考核指标得分（支部） */
export function getPartyBuildingEvaluationTargetScoreForBranch(id: string) {
  return commonReq.get({
    url: `/party-affairs/backend/v1/voting-assess/target-item/branch/${id}`,
  })
}

/** 导出党建考核指标(支部) */
export function exportPartyBuildingTargetFileForBranch(id: string) {
  return downloadReq.post({
    url: `/party-affairs/backend/v1/voting-assess/target/branch/export/${id}`,
    responseType: 'blob',
  })
}

/** 上传党建考核指标凭证(支部) */
export function uploadPartyBuildingVoucherForBranch(data: {
  id: string
  fileIds: string[]
}) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/voting-assess/target/mark/branch/modify-voucher',
    data,
  })
}

/** 党建考核撤销凭证(支部) */
export function partyBuildingRevocationVoucherForBranch(id: string) {
  return commonReq.post({
    url: `/party-affairs/backend/v1/voting-assess/target/mark/branch/revoke/${id}`,
  })
}
