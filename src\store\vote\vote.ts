import { defineStore } from 'pinia'
import { sessionCache } from '@/utils/cache'

interface VoteStoreType {
  showMenu: boolean
  currentYear: string
  query: {
    reviewId: string | undefined
    year: string | null
    partReviewItemId: string | undefined | null
    branchReviewItemId: string | undefined | null
  }
}

export const useVoteStore = defineStore('vote', {
  state: () => ({
    currentVoteMenuData:
      sessionCache.get('vote_menu') || (null as VoteStoreType | null),
  }),

  getters: {
    getShowMenu(state) {
      return state.currentVoteMenuData
        ? state.currentVoteMenuData.showMenu
        : false
    },
    getCurrentYear(state) {
      return state.currentVoteMenuData
        ? state.currentVoteMenuData.currentYear
        : ''
    },
    getQueryData(state) {
      return state.currentVoteMenuData
        ? state.currentVoteMenuData.query
        : { reviewId: '', year: '' }
    },
  },

  actions: {
    setVoteMenuDataAction(value: VoteStoreType) {
      this.currentVoteMenuData = value || null
      sessionCache.set('vote_menu', value)
    },
    clearVoteMenuDataAction() {
      this.currentVoteMenuData = null
      sessionCache.remove('vote_menu')
    },
  },
})
