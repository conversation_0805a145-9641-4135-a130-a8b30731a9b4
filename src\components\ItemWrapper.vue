<script setup lang="ts">
interface Props {
  itemName: string
  required?: boolean
}
withDefaults(defineProps<Props>(), { required: false })
</script>
<template>
  <div class="my-[30px] flex pr-[40px]">
    <span class="w-[120px] block text-right mr-[10px]"><span v-if="required" class="text-[red] mr-[6px]">*</span><span>{{ itemName }}</span></span>
    <slot />
  </div>
</template>

<style scoped lang="scss"></style>
