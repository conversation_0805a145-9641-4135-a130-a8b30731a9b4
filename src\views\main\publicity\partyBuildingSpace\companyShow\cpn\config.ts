import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  title: {
    required: true,
    message: '公司名称不能为空',
    trigger: 'input',
  },
  topStatus: {
    required: true,
    message: '是否置顶',
    trigger: 'change',
  },
  customAppCover: {
    id: {
      required: true,
      message: '请上传App封面图片',
      trigger: 'change',
      type: 'string',
    },
  },
  customCover: {
    id: {
      required: true,
      message: '请上传PC封面图片',
      trigger: 'change',
    },
  },
  module: {
    '0': {
      moduleName: {
        required: true,
        message: '模块标题不能为空',
        trigger: 'input',
      },
      content: {
        required: true,
        message: '模块介绍不能为空',
        trigger: ['input', 'change'],
      },
    },
  },
}
