import type { DataTableColumns } from 'naive-ui'

import type { BranchPartyMeetingList } from '@/services/affairs/discipline-inspection-list-branch/types'

export const tableColumns: DataTableColumns<BranchPartyMeetingList> = [
  {
    type: 'selection',
    multiple: true,
  },
  {
    key: 'index',
    title: '序号',
    width: '5%',
    align: 'center',
    render: (_, i) => i + 1,
  },
  {
    key: 'title',
    title: '会议主题',
    width: '25%',
    ellipsis: {
      tooltip: {
        contentStyle: { width: '400px', 'word-break': 'break-all' },
      },
    },
  },
  {
    key: 'meetingType',
    title: '会议类型',
    width: '10%',
  },
  {
    key: 'meetingAddr',
    title: '会议地点',
    width: '25%',
    ellipsis: {
      tooltip: {
        contentStyle: { width: '400px', 'word-break': 'break-all' },
      },
    },
  },
  {
    key: 'org',
    title: '所在组织',
    width: '10%',
  },
  {
    key: 'user',
    title: '发起人',
    width: '10%',
  },
  {
    key: 'startTime',
    title: '开始时间',
    width: '15%',
  },
]
