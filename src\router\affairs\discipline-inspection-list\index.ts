import { RouterView } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const disciplineInspectionRoute: RouteRecordRaw = {
  path: 'discipline-inspection-list',
  name: 'disciplineInspectionList',
  meta: { title: '纪检清单', icon: 'authority', category: 'affairs', order: 9 },
  component: () =>
    import(
      '@/views/main/affairs/discipline-inspection-list/partyBuildingList.vue'
    ),
  redirect: { name: 'inspectionList' },
  children: [
    {
      path: 'inspection',
      name: 'inspection',
      meta: { title: '纪检清单', icon: 'role' },
      redirect: { name: 'inspectionList' },
      component: RouterView,
      children: [
        {
          path: 'inspection-list',
          name: 'inspectionList',
          component: () =>
            import(
              '@/views/main/affairs/discipline-inspection-list/pt-list/PtList.vue'
            ),
        },
        {
          path: 'inspection-target/:partyListId',
          name: 'inspectionListTarget',
          component: () =>
            import(
              '@/views/main/affairs/discipline-inspection-list/pt-list/target/Target.vue'
            ),
        },
      ],
    },
    {
      path: 'inspection-exam-indicators',
      name: 'inspectionExamIndicators',
      meta: { title: '考核指标项', icon: 'admin' },
      component: () =>
        import(
          '@/views/main/affairs/discipline-inspection-list/exam-indicators/examList.vue'
        ),
    },
  ],
}
export default disciplineInspectionRoute
