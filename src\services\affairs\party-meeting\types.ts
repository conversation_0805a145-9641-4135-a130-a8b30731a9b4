/** 会议列表 */
export interface PartyMeetingListItem {
  /** 会议id */
  id: string
  /** 党组织id */
  orgId: string
  /** 党组织 */
  org: string
  /** 主持人id */
  hostId: string
  /** 主持人 */
  host: string
  /** 会议地点 */
  meetingAddr: string
  /** 会议状态 */
  meetingStatus: string
  /** 会议类型 */
  meetingType: string
  /** 会议主题 */
  title: string
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime: string
  /** 发起人 */
  user: string
  /** 发起人id */
  userId: string
}
/** 会议列表搜索参数 */
export interface PartyMeetingListParams {
  orgId?: string
  meetingType?: string
  title?: string
}
/** 会议详情-基本信息 */
export interface MeetingDetailBaseInfo {
  memberList: MemberList[]
  id: string
  meetingType: string
  title: string
  user: string
  host: string
  hostPartyIdentity: any
  startTime: string
  endTime: string
  meetingAddr: string
  org: string
  meetingStatus: any
  userNumber: number
  signedNumber: number
  leaveNumber: number
}
/** 参会人员 */
export interface MemberList {
  id: string
  userId: string
  trueName: string
  avatar?: string
  userStatus: string
  leaveType?: string
  leaveReason?: string
  leaveTime: any
  signedType: any
  signedTime: any
}

/** 查询参会人员列表 */
export interface PartyMeetingMemberListParams {
  /** 会议ID */
  meetingId?: string
  /** 用户名 */
  userName?: string
  /** 用户状态 */
  userStatus?: string
}
/** 参会人员 */
export interface PartyMeetingMemberItem {
  id: string
  userId: string
  trueName: string
  avatar: string
  userStatus: string
  leaveType: string
  leaveReason: string
  leaveTime: string
  signedType: string
  signedTime: string
}
/** 会议资料 */
export interface PartyMeetingMaterialItem {
  fileName: string
  id: string
  original: string
  fileSize: string
  uploadTime: string
  fileType: string
  meetingFileId: string
}

/** 心得笔记 */
export interface PartyMeetingNoteItem {
  id: string
  meetingId: string
  title: string
  userId: string
  user: string
  content: string
  orgId: string
  org: string
  uploadTime: string
  createTime: string
}

/** 心得笔记详情 */
export interface PartyMeetingNoteDetail {
  id: string
  meetingId: string
  title: any
  userId: string
  content: string
  fileVOList: FileVolist[]
}

export interface FileVolist {
  fileName: string
  id: string
  original: string
  fileSize: string
  uploadTime: string
  fileType: string
}

/** 会议总结详情 */
export interface PartyMeetingSummaryDetail {
  id: string
  meetingId: string
  title: string
  userId: string
  content: string
  fileVOList: FileVolist[]
}
