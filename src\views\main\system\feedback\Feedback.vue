<script setup lang="ts">
import type { DataTableRowKey } from 'naive-ui'
import { NButton } from 'naive-ui'
import type { RowData } from 'naive-ui/es/data-table/src/interface'
import { getTableColumns } from './config'
import FeedBackForm from './cpn/FeedBackForm.vue'
import { useDrawerEditOrganize } from '@/hooks'

const idEditRef = ref()
const feedbackFormRef = ref()
/** 确定保存 */
const handelConfirmEdit = () => {
  feedbackFormRef.value?.validateAndSave()
}

const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEditOrganize({
  name: '意见详情',
  confirmFn: handelConfirmEdit,
  isNotTips: true,
})

/** 保存成功 */
const handleCarouselSaved = () => {
  showEditRef.value = false
}

/**
 * 当前的tab名称
 */
const currentTabName = ref<string>('unanswered')

/**
 * 分页配置
 * @param {any} page:1
 * @param {any} pageSize:10
 */
const pageConfig = ref({
  page: 1,
  pageSize: 10,
})

/**
 * 表格记录单项删除或多项删除
 */
const delRecord = () => {
  window.$dialog.warning({
    title: '提示',
    content: '删除后无法恢复，确认删除？',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {},
  })
}

/**
 * tab面板切换
 * @param {any} value:string
 */
const tabUpdate = (value: string) => {
  currentTabName.value = value
}

/**
 * 数据表格的列设置
 */
const columns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          showEditRef.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '意见详情',
      },
    ),
    h(
      NButton,
      {
        onClick: () => {
          delRecord()
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '删除',
      },
    ),
  ]
})

/**
 * 表格数据
 */
const unansweredData = ref([
  {
    id: 1,
    pictureUrl: '456123',
  },
])

/**
 * 表格选中单项是绑定的key
 */
const rowKey = (row: RowData) => {
  return row.id
}

/**
 * 表格选中的key
 */
const checkedRowKeysRef = ref<DataTableRowKey[]>([])

/**
 * 表格点击选中该条记录的方法
 */
const handleCheck = (rowKeys: DataTableRowKey[]) => {
  checkedRowKeysRef.value = rowKeys
}

onMounted(() => {
  editTypeRef.value = 'custom'
})
</script>
<template>
  <div class="pt-[20px] pl-[30px] pr-[30px] relative">
    <div class="pb-[26px]">
      <span class="text-[14px] font-[600] text-[#333] leading-[20px]">意见箱</span>
    </div>
    <div class="absolute top-[40px] right-[30px] z-10">
      <n-form class="flex flex-row items-center justify-start gap-[20px]">
        <n-form-item>
          <n-input placeholder="请输入姓名" />
        </n-form-item>
        <n-form-item>
          <n-input placeholder="请输入意见标题" />
        </n-form-item>
      </n-form>
    </div>
    <n-tabs type="card" animated @update:value="tabUpdate">
      <n-tab-pane name="unanswered" tab="未答复">
        <n-data-table
          :columns="columns"
          :data="unansweredData"
          :row-key="rowKey"
          :bordered="false"
          @update:checked-row-keys="handleCheck"
        />
      </n-tab-pane>
      <n-tab-pane name="answered" tab="已答复">
        <n-data-table
          :columns="columns"
          :data="unansweredData"
          :row-key="rowKey"
          :bordered="false"
          @update:checked-row-keys="handleCheck"
        />
      </n-tab-pane>
    </n-tabs>
    <div class="flex flex-row items-start justify-between pt-[20px]">
      <div class="flex flex-row gap-[6px]">
        <span class="text-[12px] leading-[12px]">已选{{ checkedRowKeysRef.length }}条</span>
        <n-button type="primary" text @click="delRecord">
          删除
        </n-button>
      </div>
      <div>
        <n-pagination
          v-model:page="pageConfig.page"
          v-model:page-size="pageConfig.pageSize"
          :page-count="100"
          show-size-picker
          :page-sizes="[10, 20, 30, 40]"
          show-quick-jumper
        />
      </div>
    </div>
  </div>

  <n-drawer v-model:show="showEditRef" :width="600" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <feed-back-form
        :id="idEditRef"
        ref="feedbackFormRef"
        :type="editTypeRef"
        @saved="handleCarouselSaved"
      />
      <template #footer>
        <n-button
          type="primary"
          style="margin-right: 10px"
          @click="handleClickConfirm"
        >
          保存
        </n-button>
        <n-button @click="handleClickCancel">
          取消
        </n-button>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
