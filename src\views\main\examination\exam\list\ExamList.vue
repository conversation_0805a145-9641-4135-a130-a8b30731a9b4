<!--
 * @Description: 考试管理-列表页(新)
-->
<template>
  <table-container
    title="考试管理"
    :show-delete="false"
    add-text="创建"
    :loading="loadingRef"
    :total="totalRef"
    :table-columns="tableColumns"
    :table-data="examList"
    @page-change="handlePageChange"
    @click-add="handleClickAdd"
  >
    <template #filters>
      <div class="item">
        <n-input
          v-model:value="examNameRef"
          size="small"
          placeholder="请输入考试名称"
          clearable
          @input="handleFilter"
          @clear="handleClearExamName"
        >
          <template #suffix>
            <n-icon size="18" style="cursor: pointer" @click="handleFilter">
              <search-round />
            </n-icon>
          </template>
        </n-input>
      </div>
    </template>
  </table-container>
</template>

<script setup lang="ts">
import { computed, h, ref } from 'vue'
import { useRouter } from 'vue-router'
import type { DataTableColumns } from 'naive-ui'
import { NButton, NIcon, NInput, NPopconfirm } from 'naive-ui'
import { SearchRound } from '@vicons/material'
import TableContainer from '../../library/list/TableContainer.vue'
import useTableContainer from '@/hooks/use-table-container'
import useExaminationStore from '@/store/examination/index'
import type {
  IExaminationExam,
  IExaminationExamQuery,
} from '@/services/examination/index'
import {
  deleteExaminationExam,
  publishExaminationExam,
} from '@/services/examination/index'
import { formatCustomTimeString } from '@/utils/date-time-format'

const router = useRouter()
const examinationStore = useExaminationStore()

const examList = computed(() => examinationStore.examList)
const examNameRef = ref('')

const { loadingRef, totalRef, currentPageRef, pageSizeRef, handlePageChange }
  = useTableContainer(loadExamList)

// 加载数据
function loadExamList() {
  if (!loadingRef.value && currentPageRef.value) {
    loadingRef.value = true
    // 生成参数
    const params: IExaminationExamQuery = {
      pageNum: currentPageRef.value,
      pageSize: pageSizeRef.value,
    }
    const examName = examNameRef.value.trim()
    if (examName.length > 0) {
      params.keyWord = examName
    }

    examinationStore.getExamListAction(params).then((res) => {
      totalRef.value = Number(res)
      loadingRef.value = false
    })
  }
}
// 搜索
function handleFilter() {
  currentPageRef.value = 1
  loadExamList()
}
// 清空
function handleClearExamName() {
  examNameRef.value = ''
  handleFilter()
}

// 点击创建
function handleClickAdd() {
  router.push({ name: 'examEdit', params: { type: 'add' } })
}

const tableColumns: DataTableColumns<IExaminationExam> = [
  {
    key: 'blank',
    width: 10,
  },
  {
    key: 'name',
    title: '考试名称',
  },
  {
    key: 'totalScore',
    title: '总分',
  },
  {
    key: 'passScore',
    title: '通过分数',
  },
  {
    key: 'examNumber',
    title: '考试人数',
  },
  {
    key: 'passNumber',
    title: '通过人数',
  },
  {
    key: 'createdAt',
    title: '开放时间',
    render(row) {
      return `${formatCustomTimeString(
        row.startTime,
        'YYYY-MM-DD HH:mm:ss',
        'YYYY.MM.DD HH:mm',
      )} - ${formatCustomTimeString(
        row.endTime,
        'YYYY-MM-DD HH:mm:ss',
        'YYYY.MM.DD HH:mm',
      )}`
    },
  },
  {
    key: 'updatedAt',
    title: '更新时间',
    render(row) {
      return formatCustomTimeString(row.updatedAt ?? '', 'YYYY-MM-DD HH:mm:ss')
    },
  },
  {
    key: 'operation',
    title: '操作',
    render(row) {
      return [
        row.isPublished === 1
          ? null
          : h(
            NButton,
            {
              text: true,
              type: 'primary',
              style: 'margin-right: 16px',
              onClick: () => {
                publishExaminationExam(row.id, 1).then(() => {
                  window.$message.success('发布成功')
                  loadExamList()
                })
              },
            },
            { default: () => '发布' },
          ),
        h(
          NPopconfirm,
          {
            placement: 'bottom-end',
            positiveText: '确定',
            onPositiveClick: () => {
              router.push({
                name: 'examEdit',
                params: { type: 'modify' },
                query: { id: row.id },
              })
            },
          },
          {
            trigger: () =>
              h(
                NButton,
                { text: true, type: 'primary', style: 'margin-right: 16px' },
                { default: () => '编辑' },
              ),
            default: () => '该考试已发布，更改会影响到参考人员，确认编辑？',
          },
        ),
        h(
          NPopconfirm,
          {
            placement: 'bottom-end',
            positiveText: '确定',
            onPositiveClick: () => {
              deleteExaminationExam(row.id).then((res) => {
                window.$message.success(res)
                loadExamList()
              })
            },
          },
          {
            trigger: () =>
              h(
                NButton,
                { text: true, type: 'primary' },
                { default: () => '删除' },
              ),
            default: () => '确定删除吗？',
          },
        ),
      ]
    },
  },
]
</script>
