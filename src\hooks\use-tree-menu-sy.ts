/**
 * @description 树形菜单
 */

export function useTreeMenuSy(options: {
  menuListApi?: (params: any) => Promise<any>
  addNodeApi?: (params: any) => Promise<any>
  delNodeApi?: (params: any) => Promise<any>
  moveNodeApi?: (params: any) => Promise<any>
  modifyNodeApi?: (params: any) => Promise<any>

  // addChildNodeApi?: (params: any) => Promise<any>
  // delChildNodeApi?: (params: any) => Promise<any>
  // modifyChildNodeApi?: (params: any) => Promise<any>
  // moveChildNodeApi?: (params: any) => Promise<any>
  refreshTableApi?: (params: any) => void
  multiLevelKey?: string
  labelField?: string
  childLabelField?: string
  maxLevel?: number
  sessionId?: string
  sessionName?: string
}) {
  const menuList = ref<any>([]) // 菜单列表
  const showModalType = ref('root') // root | sub 添加主类和副类区分开
  const defaultCategoryID = ref('')
  const defaultCategoryName = ref('')
  const commonSessionId = window.sessionStorage.getItem(
    String(options.sessionId),
  )
  const commonSessionName = window.sessionStorage.getItem(
    String(options.sessionName),
  )

  // 初始化时调用菜单列表
  async function init() {
    const res = await options.menuListApi?.({})
    menuList.value = res
    if (options.maxLevel) {
      const data = getDefaultCategoryId(menuList.value)
      defaultCategoryID.value = commonSessionId || data.id
      defaultCategoryName.value = commonSessionName || data.name
    } else {
      defaultCategoryID.value = commonSessionId || menuList.value[0]?.id
      defaultCategoryName.value
        = commonSessionName || menuList.value[0]?.[options.labelField as string]
    }

    return {
      id: defaultCategoryID.value,
      name: defaultCategoryName.value,
    }
  }

  // 取出第一个有资讯的
  function getDefaultCategoryId(arr: any[]) {
    let data = { id: '', name: '' }
    for (let i = 0; i < arr.length; i++) {
      if (
        Array.isArray(arr[i][options.multiLevelKey as string])
        && arr[i][options.multiLevelKey as string].length
      ) {
        data = {
          id: arr[i][options.multiLevelKey as string][0].id,
          name: arr[i][options.multiLevelKey as string][0][
            options.childLabelField as string
          ],
        }
        break
      }
    }

    return data
  }

  // 生成uuid
  function uuid() {
    const s: string[] = []
    const hexDigits = '0123456789abcdef'
    for (let i = 0; i < 36; i++) {
      s[i] = hexDigits.substring(Math.floor(Math.random() * 0x10), 1)
    }
    s[14] = '4'
    s[19] = hexDigits.substring((Number(s[19]) & 0x3) | 0x8, 1)
    s[8] = s[13] = s[18] = s[23] = '-'

    return s.join('')
  }

  // const treeData = computed(() => {
  //   if (options.maxLevel) {
  //     if (options.multiLevelKey) {
  //       return menuList.value && menuList.value.length
  //         ? calcLevelData(menuList.value, true)
  //         : []
  //     } else {
  //       return calcLevelData(menuList.value, true)
  //     }
  //   } else {
  //     return calcLevelData(menuList.value, true)
  //   }
  // })
  const treeData = computed(() => {
    return menuList.value?.length ? calcLevelData(menuList.value, true) : []
  })

  function calcNeedChildBtn(flag: boolean) {
    if (options.maxLevel && options.maxLevel >= 2) {
      // 树形结构有多层的情况需要有添加子集的按钮
      return flag
    } else {
      // 树形结构只有一层的情况不需要有添加子集的按钮 true 代表按钮disabled的值
      return true
    }
  }

  // 递归实现多层级自动组装数据
  function calcLevelData(
    arr: any[],
    currentParentLevel: boolean,
    currentParentID?: string,
  ): any[] {
    return arr.map((item: any, index: number) => {
      return {
        ...item,
        originData: { ...item },
        isChild: !currentParentLevel,
        needAddChildBtn: calcNeedChildBtn(!currentParentLevel),
        parentID: currentParentID || undefined,
        key: uuid(),
        label: currentParentLevel
          ? item[options.labelField as string]
          : item[options.childLabelField as string],
        editing: false,
        canUp: index !== 0,
        canDown: index !== arr.length - 1,
        children:
          item[options.multiLevelKey as string] !== undefined
          && item[options.multiLevelKey as string].length
            ? calcLevelData(
              item[options.multiLevelKey as string],
              false,
              item.id,
            )
            : undefined,
      }
    })
  }

  // 移动节点操作
  async function moveNode(data: any) {
    const moveNum = data.type === 'up' ? '-1' : '+1'
    try {
      // if (data.isChild) {
      //   await options.moveChildNodeApi?.({
      //     id: data.id,
      //     move: moveNum,
      //     parentId: data.parentID,
      //   })
      // } else {
      //   await options.moveNodeApi?.({ id: data.id, move: moveNum, parentId: data.isChild ? data.parentID : '0' })
      // }
      await options.moveNodeApi?.({
        id: data.id,
        move: moveNum,
        parentId: data.isChild ? data.parentID : '0',
      })
      window.$message.success('操作成功')
      await init()
    } catch (error) {
      // 在这里处理可能发生的错误
      console.error('发生错误:', error)
    }
  }

  // async function handleDelResponse() {
  //   try {
  //     if (options.sessionId && options.sessionName) {
  //       window.sessionStorage.removeItem(String(options.sessionId))
  //       window.sessionStorage.removeItem(String(options.sessionName))
  //     }

  //     const result = await init()
  //     console.log('result: ', result)
  //     options.refreshTableApi?.(result)
  //     window.$message.success('操作成功')
  //   } catch (err) {
  //     console.error(err) // Handle error here
  //   }
  // }
  /** 删除节点 */
  async function delNode(data: any) {
    try {
      // if (data.isChild) {
      //   await options.delChildNodeApi?.(data.id) // 删除子节点
      // } else {
      //   await options.delNodeApi?.(data.id) // 删除主节点
      // }
      await options.delNodeApi?.(data.id)
      // await handleDelResponse()
      if (options.sessionId && options.sessionName) {
        window.sessionStorage.removeItem(String(options.sessionId))
        window.sessionStorage.removeItem(String(options.sessionName))
      }
      const result = await init()
      options.refreshTableApi?.(result)
      window.$message.success('操作成功')
    } catch (err) {
      console.error(err)
    }
  }

  // 添加主节点操作
  async function saveNode(data: any) {
    const apiCall = data.id
      ? options.modifyNodeApi?.(data)
      : options.addNodeApi?.(data)
    // if (data.type === 'root') {
    //   apiCall = data.id
    //     ? options.modifyNodeApi?.(data)
    //     : options.addNodeApi?.(data)
    // } else if (data.type === 'sub') {
    //   apiCall = data.id
    //     ? options.modifyChildNodeApi?.(data)
    //     : options.addChildNodeApi?.(data)
    // }

    if (apiCall) {
      try {
        await apiCall
        window.$message.success('操作成功')
        await init()
      } catch (error) {
        // 在这里处理可能发生的错误
        console.error('发生错误:', error)
      }
    }
  }

  return {
    menuList,
    treeData,
    showModalType,
    defaultCategoryID,

    init,
    moveNode,
    delNode,
    saveNode,
  }
}
