<!--
 * @Description: 图片裁剪上传控件，暂时只支持一个文件
-->
<template>
  <div class="img-uploader">
    <n-upload
      v-model:file-list="fileList"
      :accept="accept"
      :max="1"
      list-type="image-card"
      @change="handleFileChange"
    >
      <div>
        <n-icon color="#000" size="25">
          <add-round />
        </n-icon>
        <br>
        <span>添加图片</span>
      </div>
    </n-upload>

    <div style="width: 100%; margin-left: -200px">
      <div>推荐尺寸 {{ props.width }}×{{ props.height }}</div>
      <div style="margin-left: 0">
        支持{{ acceptText }}
      </div>
    </div>

    <n-modal
      v-if="showCropper"
      v-model:show="showCropper"
      preset="dialog"
      title="图片裁剪"
      positive-text="确定"
      :show-icon="false"
      :mask-closable="false"
      @positive-click="handleConfirmCrop"
      @close="handleCloseCropper"
    >
      <div
        :style="{
          margin: '15px 0 20px 0',
          width: props.width + 150 + 'px',
          height: props.height + 150 + 'px',
        }"
      >
        <vue-cropper ref="cropperRef" v-bind="cropperOption" />
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import type { UploadFileInfo } from 'naive-ui'
import { NIcon, NModal, NUpload } from 'naive-ui'
import { AddRound } from '@vicons/material'
import { VueCropper } from 'vue-cropper'
import { dataUrl2File, file2Base64, url2DataUrl } from './file2data'
import 'vue-cropper/dist/index.css'

const fileList = ref<UploadFileInfo[]>()
const showCropper = ref(false)
const cropperRef = ref()
const cropperOption = reactive({
  autoCrop: true, // 是否默认生成截图框
  fixedBox: true, // 固定截图框大小 不允许改变
  canMoveBox: false, // 禁止截图框移动
  centerBox: true, // 限制截图框在图片内
  mode: 'cover',
  img: '',
  autoCropWidth: 0,
  autoCropHeight: 0,
  uid: '',
  fileName: '',
})

const props = defineProps({
  oldImgUrl: {
    type: String,
    default: '',
  },
  width: {
    type: Number,
    required: true,
  },
  height: {
    type: Number,
    required: true,
  },
  needCropper: {
    type: Boolean,
    default: true,
  },
  accept: {
    type: String,
    default: '.jpg,.jpeg,.png',
  },
  acceptText: {
    type: String,
    default: '.jpg,.jpeg,.png',
  },
})
const emits = defineEmits(['done', 'delete'])

// 图片选择完毕
function handleFileChange(options: {
  file: UploadFileInfo
  fileList: Array<UploadFileInfo>
  event?: Event
}) {
  const { fileList } = options
  if (!fileList.length) {
    emits('delete')
    return
  }

  const file = fileList[0]

  file2Base64(file.file).then((data) => {
    cropperOption.fileName = file.name
    if (props.needCropper) {
      // 打开图片裁剪
      cropperOption.img = data as string
      cropperOption.autoCropWidth = props.width
      cropperOption.autoCropHeight = props.height
      cropperOption.uid = file.id
      showCropper.value = true
    } else {
      setFileList(data as string)
    }
  })
}

// 设置fileList
function setFileList(data: string) {
  // 转换为File对象
  const file: File = dataUrl2File(data, cropperOption.fileName)
  fileList.value = [
    {
      id: cropperOption.uid,
      name: file.name,
      file,
      percentage: 100,
      status: 'finished',
    },
  ]

  // 重置cropperOption
  cropperOption.img = ''
  cropperOption.autoCropWidth = 0
  cropperOption.autoCropHeight = 0
  cropperOption.uid = ''
  cropperOption.fileName = ''

  emits('done', file)
}

// 确定裁剪
function handleConfirmCrop() {
  // 获取截图的base64 数据
  cropperRef.value?.getCropData((data: string) => {
    setFileList(data)
  })
}

function handleCloseCropper() {
  setFileList(cropperOption.img)
}

watch(props, (newV) => {
  if (newV.oldImgUrl) {
    url2DataUrl(import.meta.env.VITE_API_BASE + newV.oldImgUrl).then((res) => {
      cropperOption.fileName = 'original.png'
      setFileList(res)
    })
  } else {
    fileList.value = []
  }
})
</script>

<style lang="scss" scoped>
.img-uploader {
  flex-grow: 1;
  display: flex;
  align-items: flex-end;

  > span {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    margin-left: 20px;
  }
}

:deep(.n-upload-file-list.n-upload-file-list--grid) {
  display: flex;
}

:deep(.n-upload-file--image-card-type) {
  border: none !important;
}
</style>
