import type { DataTableSortState } from 'naive-ui'
import type { Ref } from 'vue'
import type { PaginationRes } from '@/services/types'

// 删除配置
interface DeleteConfig<T> {
  /** 是否可批量删除 */
  batchDeleteTable?: boolean
  /** 数据删除接口 */
  delApi?: (ids: string, type?: number) => Promise<any>
  /** rowKey */
  rowKey?: (row: T) => string
  /** 删除类型 */
  delType?: number
}

// 排序配置
interface OrderConfig {
  descend: string | number
  ascend: string | number
  none?: string | number
}

type OrderMap = Record<string, OrderConfig>

export function useMyTable<T>(
  // 接口
  loadApi: (params: any) => Promise<PaginationRes>,
  // 搜索表单参数
  searchForm: Ref<any>,
  // 删除配置
  deleteConfig: DeleteConfig<T>,
  // 组织结构列表
  organizeList?: boolean,
  // 是否清空已选择的表项
  checkClear?: Ref<Boolean>,
  tabParam?: Ref,
  // 排序配置
  orderMap?: OrderMap,
  newPageSize?: number,
  noPagination: boolean = false, // 新增：是否禁用分页
) {
  const { batchDeleteTable, delApi } = deleteConfig
  /** 选中的行id */
  const checkedRowKeys = ref<Array<number | string>>([])
  /** 当前页 */
  const currentPage = ref(1)
  /** 页大小 */
  const pageSize = newPageSize ? ref(newPageSize) : ref(10)
  /** 总条数 */
  const total = ref(0)
  /** 加载状态 */
  const loading = ref(false)
  /** 表格数据 */
  const tableData = ref<T[]>([])
  /** 排序参数 */
  const orderParams = ref<Record<string, unknown>>({})

  /** 加载数据 */
  async function loadData() {
    if (!checkClear?.value) {
      checkedRowKeys.value.length = 0
    }
    loading.value = true
    try {
      let params = {
        ...searchForm?.value,
        ...tabParam?.value,
        ...(noPagination
          ? {} // 不传分页参数
          : {
            pageNum: currentPage.value,
            pageSize: pageSize.value,
          }),
      }

      if (Object.keys(orderParams.value).length > 0) {
        params = { ...params, ...orderParams.value }
      }
      if (organizeList) {
        loadApi(params).then((res) => {
          tableData.value = (res as any) || []
        })
      }
      else {
        const res = await loadApi(params)
        if (noPagination) {
          tableData.value = (res as any) ?? []
          total.value = tableData.value.length
        }
        else {
          const { records, total: t } = res
          tableData.value = records ?? []
          // 已选择的被勾选上
          if (checkClear?.value) {
            checkedRowKeys.value = records
              .filter(item => item.relatedStatus)
              .map(obj => obj.id)
          }
          total.value = t
        }
      }
    }
    catch {
    }
    finally {
      loading.value = false
    }
  }

  // 组件处于激活状态,重新加载数据
  onActivated(() => loadData())

  /** 回到第一页 */
  const backToFirstPage = () => {
    currentPage.value === 1 ? loadData() : (currentPage.value = 1)
  }

  // 搜索参数、页大小发生变化,跳到第一页
  watch([searchForm.value, pageSize], () => {
    backToFirstPage()
  })

  // 当前 Tab 改变，跳到第一页
  if (tabParam) {
    watch(tabParam, () => {
      backToFirstPage()
    })
  }
  watch(currentPage, loadData)

  // 分页变化
  // const onUpdatePage = (page: number) => (currentPage.value = page)
  const onUpdatePage = () => {}
  const onUpdatePageSize = (size: number) => {
    if (size) {
      pageSize.value = size
    }
  }

  /** 表格排序 */
  function onUpdateSorter(options: DataTableSortState | DataTableSortState[]) {
    orderParams.value = {}
    const setOrderParam = (item: DataTableSortState) => {
      const { order, columnKey } = item
      const orderItem = orderMap![columnKey]
      if (order === 'ascend' || order === 'descend') {
        orderParams.value[columnKey] = orderItem[order]
      }
    }
    if (orderMap) {
      if (options instanceof Array) {
        options.forEach((item) => {
          setOrderParam(item)
        })
      }
      else {
        setOrderParam(options)
      }
    }
    loadData()
  }

  /** 行选中 */
  function onUpdateCheckedRowKeys(ids: Array<number | string>) {
    checkedRowKeys.value = ids
  }
  /** 单条删除 */
  async function handleSingleDelete(id: string) {
    try {
      await delApi!(id, deleteConfig.delType)
      loadData()
      window.$message.success('删除成功')
    }
    catch {}
  }

  /** 批量删除 */
  function handleBatchDelete() {
    if (!batchDeleteTable) {
      return
    }
    if (!checkedRowKeys.value.length) {
      return window.$message.warning('请选择需要删除的记录')
    }
    window.$dialog.warning({
      title: '提示',
      content: '删除后无法恢复，确认删除？',
      positiveText: '确认',
      negativeText: '取消',
      onPositiveClick: () => {
        delApi!(checkedRowKeys.value.join(','), deleteConfig.delType)
          .then(() => {
            window.$message.success('删除成功')
          })
          .finally(loadData)
      },
    })
  }
  return {
    checkedRowKeys,
    currentPage,
    pageSize,
    total,
    loading,
    tableData,

    loadData,
    onUpdatePage,
    onUpdatePageSize,
    onUpdateSorter,
    onUpdateCheckedRowKeys,
    handleSingleDelete,
    handleBatchDelete,
  }
}
