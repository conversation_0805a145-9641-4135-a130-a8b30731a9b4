import { base64FromArrayBuffer } from '@/utils/transform'

function usePdfPreview() {
  async function useLoadPdfPreview(pdfAllUrl: string) {
    const encoder = new TextEncoder()
    const data = encoder.encode(pdfAllUrl) // 将字符串转换为Uint8Array
    // console.log('pdfAllUrl.value: ', pdfAllUrl.value)
    const base64Url = await base64FromArrayBuffer(data) // 将Uint8Array转换为Base64编码
    if (import.meta.env.MODE === 'development') {
      window.open(
        `${
          import.meta.env.VITE_PREVIEW
        }/previews/onlinePreview?url=${encodeURIComponent(base64Url)}`,
      )
    }
    else {
      window.open(
        `/previews/onlinePreview?url=${encodeURIComponent(base64Url)}`,
      )
    }
  }

  return {
    useLoadPdfPreview,
  }
}

export default usePdfPreview
