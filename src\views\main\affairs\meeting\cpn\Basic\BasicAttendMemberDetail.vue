<script setup lang="ts">
import { NButton } from 'naive-ui'
import { getTableColumns } from './config'
import {
  getAttendMeetingMemberList,
  putRevokeSign,
  putSign,
} from '@/services/affairs/party-meeting'
import { useMyTable } from '@/hooks'
import { MEETING_ATTEND_STATUS } from '@/store/dict'
interface Props {
  meetingId: string
}
const props = defineProps<Props>()
const filterReactive = ref({
  meetingId: props.meetingId,
  userStatus: null,
  userName: null,
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(
  getAttendMeetingMemberList,
  filterReactive,
  {
    batchDeleteTable: false,
  },
  false,
  ref(true),
)

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () =>
          handleSignOrRevoke(row.userId, row.trueName, row.userStatus),
        type: 'primary',
        text: true,
      },
      {
        default: () => (row.userStatus !== '1' ? '签到' : '取消签到'),
      },
    ),
  ]
})
watch(filterReactive.value, (newV) => {
  if (newV) {
    loadData()
  }
})
function handleSignOrRevoke(id: string, trueName: string, status: string) {
  window.$dialog.info({
    title: '签到状态修改',
    content: `确定要将【${trueName}】的签到状态设为${
      status !== '1' ? '【已签到】' : '【未签到】'
    }吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      loading.value = true
      if (status !== '1') {
        putSign(props.meetingId, id).then(() => {
          window.$message.success('签到成功')
          loadData()
        })
      } else {
        putRevokeSign(props.meetingId, id).then(() => {
          window.$message.success('撤销签到成功')
          loadData()
        })
      }
      loading.value = false
    },
  })
}

onMounted(() => {
  loadData()
})
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :loading="loading"
    class="relation-table"
    :show-title="false"
    :show-add="false"
    :show-delete="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
  >
    <template #filters>
      <n-select
        v-model:value="filterReactive.userStatus"
        style="width: 260px"
        placeholder="请选择参会情况"
        filterable
        :options="MEETING_ATTEND_STATUS"
        clearable
      />
      <n-input
        v-model:value="filterReactive.userName"
        style="width: 260px; height: 32px"
        clearable
        type="text"
        placeholder="请输入姓名"
      />
    </template>
  </table-container>
</template>
<style lang="scss" scoped>
.relation-table:deep(.n-data-table .n-data-table-thead) {
  display: contents;
}
</style>
