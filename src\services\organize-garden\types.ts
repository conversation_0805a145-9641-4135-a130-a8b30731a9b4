import type { UploadFileInfo } from 'naive-ui'
/** 党支部星级 */
export interface OrganizeGardenListItem {
  id: string
  year: string
  star: number
}

/** 组织成员管理 */
export interface OrganizeMemberListItem {
  id: number
  name: string
  partyAge: number
  partyPosition: string
  joinPartyTime: string
}

/** 党建品牌 */
export interface OrganizeBrandFormParams {
  // id: string
  // name: string
  // coverUrl: string
  // brandIntroduce: string
  // logoMeaning: string
  // brandConnotation: string
  // achievement: string
  /** 品牌内涵 */
  brandConnotation?: null | string
  /** 品牌名称 */
  brandName?: null | string
  /** 品牌简介 */
  brandSummary?: null | string
  /** 品牌图片url */
  coverUrl?: null | string
  /** 是否存在数据（0-否 1-是）根据此字段判断修改还是保存表单 */
  existedData?: null | string
  /** 主要特色和取得成就 */
  featuresAchievements?: null | string
  /** 主键ID */
  id?: string | null
  /** LOGO释义 */
  logoDefinition?: null | string
  /** 组织ID */
  orgId?: string | null
  [property: string]: any
}
/** 荣誉墙 */
export interface HonorWallTableItem {
  /** 授予时间 */
  grantTime?: null | string
  /** 更新时间 */
  updateTime?: null | string
  /** 封面图url */
  pictureUrl?: null | string
  /** 主键ID */
  id?: number | null
  /** 分类ID */
  categoryId?: string | null
  /** 组织ID */
  deptId?: string | null
  /** 组织名称 */
  deptName?: string | null
  /** 组织ID或党员Id */
  serviceId?: string | null
  /** 组织名称或党员名称 */
  serviceName?: string | null
  /** 荣誉类型名称 */
  honorType?: null | string
}

/** 党组织工作法 */
export interface OrganizeWorkLawFormParams {
  /** 内容图片url，分隔 */
  contentUrl?: null | string
  /** 内容图片url集合 */
  contentUrlList?: string[] | null
  /** 文件类型数组 */
  fileList: UploadFileInfo[]
  /** 封面图片url */
  coverUrl?: null | string
  /** 组织工作法说明 */
  description?: null | string
  /** 是否存在数据（0-否 1-是）根据此字段判断修改还是保存表单 */
  existedData?: null | string
  /** 主键ID */
  id?: string | null
  /** 组织ID */
  orgId?: string | null
  title: string
  [property: string]: any
}

/** 创新做法列表 */
export interface OrgStyleListItem {
  /** 创新做法图片url */
  coverUrl?: null | string
  /** 主键ID */
  id: string
  /** 是否隐藏 (0-否，1-是) */
  isHidden?: number | null
  /** 是否置顶 (0-否，1-是) */
  isTop?: number | null
  /** 组织ID */
  orgId?: string
  /** 创新做法简介 */
  summary?: null | string
  createTime?: string
  /** 标题 */
  title?: null | string
  [property: string]: any
}
/** 功能设置 */
export interface FunctionSettingFormParams {
  /** 0-正常，1-删除 */
  delFlag?: null | string
  /** 功能id */
  functionId?: null | string
  /** 功能名称 */
  functionName?: null | string
  /** 主键ID */
  id?: string | null
  /** 是否展示 (0-否，1-是) */
  isShow?: number | null
  /** 组织id */
  orgId?: string | null
  [property: string]: any
}
