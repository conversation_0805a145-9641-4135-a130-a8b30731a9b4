<script setup lang="ts">
defineProps<{
  getFn: (value: string, key: string) => void
  needState?: boolean
  defaultButton?: boolean
  needAudit?: boolean
}>()
const organizationOptions = [
  {
    label: '组织1',
    value: '组织1',
  },
  {
    label: '组织2',
    value: '组织2',
  },
  {
    label: '组织3',
    value: '组织3',
  },
  {
    label: '组织4',
    value: '组织4',
  },
]
const stateOptions = [
  {
    label: '已采纳',
    value: 1,
  },
  {
    label: '未采纳',
    value: 2,
  },
]
const auditOptions = [
  {
    label: '未设置',
    value: 0,
  },
  {
    label: '未审核',
    value: 1,
  },
  {
    label: '已通过',
    value: 2,
  },
  {
    label: '被驳回',
    value: 3,
  },
  {
    label: '已缴纳',
    value: 4,
  },
]
</script>
<template>
  <div class="flex justify-between mt-[20px]">
    <div>
      <slot />
      <n-button v-if="defaultButton" style="height: 28px">
        导出
      </n-button>
    </div>
    <div class="flex justify-end flex-1">
      <n-date-picker
        type="month"
        clearable
        style="width: 115px"
        @update:formatted-value="(value:string)=>getFn(value,'months')"
      />
      <n-select
        clearable
        :options="organizationOptions"
        style="width: 184px"
        class="mx-[6px]"
        placeholder="请选择所在组织"
        @update:value="(value:string)=>getFn(value,'organization')"
      />
      <n-select
        v-if="needState"
        :options="stateOptions"
        placeholder="请选择缴纳状态"
        style="width: 151px"
        class="mr-[6px]"
        @update:value="(value:string)=>getFn(value,'state')"
      />
      <n-select
        v-if="needAudit"
        :options="auditOptions"
        placeholder="请选择审核状态"
        style="width: 151px"
        class="mr-[6px]"
        @update:value="(value:string)=>getFn(value,'audit')"
      />
      <n-input
        placeholder="请输入姓名或手机号搜索"
        style="width: 220px"
        @update:value="(value:string)=>getFn(value,'searchValue')"
      />
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
