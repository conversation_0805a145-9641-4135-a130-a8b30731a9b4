<script setup lang="ts">
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import AddPublicItemForm from '../cpn/AddPublicItemForm.vue'
import AddPublicCategoryForm from './AddPublicCategoryForm.vue'
import { getTableColumns } from './config'
import { useDrawerEdit, useMyTable, useTreeMenu } from '@/hooks'
import {
  deletePublic,
  deletePublicCategory,
  getCategoryList,
  getPublicCategoryList,
  movePublicCategory,
  postPublicCategory,
  putPublicCategory,
} from '@/services/party-public'
import DeleteButton from '@/components/DeleteButton.vue'
import { judgePermission } from '@/directive/permission/ifHasPermi'
const { treeData, showModalType, moveNode, delNode, saveNode, init }
  = useTreeMenu({
    menuListApi: getPublicCategoryList,
    moveNodeApi: movePublicCategory,
    delNodeApi: deletePublicCategory,
    addNodeApi: postPublicCategory,
    modifyNodeApi: putPublicCategory,
    refreshTableApi: filterInput,
    labelField: 'name',
    sessionId: 'publicityCategoryId',
    sessionName: 'publicityCategoryLabel',
  })

const showModal = ref(false)
const modalTitle = ref()
const addFirstCategoryRef = ref()

/** 新增子节点 */
async function handleAddChildNode(data: any) {
  showModal.value = true
  modalTitle.value = '新增党务公开类别'
  // addChildNode(data)
  if (data.model === 'modify') {
    modalTitle.value = '修改党务公开类别'
    nextTick(() => {
      addFirstCategoryRef.value.handleSetFormData(data)
    })
  }
}
/** 处理弹框需要保存的数据及校验弹框必填项 */
async function handleFormatterParams() {
  try {
    if (!(await addFirstCategoryRef.value.handleValidate())) {
      return
    }
    const data = addFirstCategoryRef.value.formData
    saveNode({ ...data, type: showModalType.value })
    showModal.value = false
  }
  catch (error) {
    console.error(error)
  }
}

function handleCancel() {
  showModal.value = false
}

// 选中菜单名称
const selectName = ref()

// 筛选项：类别id和资讯标题
const filterRef = ref({
  categoryId: '',
  annexName: null,
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getCategoryList, filterRef, {
  batchDeleteTable: true,
  delApi: deletePublic,
})

// 选中菜单触发的事件
function handleChangeTab(data: any) {
  const { isChild, label } = data
  if (!isChild) {
    filterRef.value.categoryId = data.originData.id
    window.sessionStorage.setItem('publicityCategoryId', data.originData.id)
    window.sessionStorage.setItem('publicityCategoryLabel', label)
    filterRef.value.annexName = null
    selectName.value = label
    currentPage.value = 1
  }
}
// 新增/编辑党务公开抽屉
const idEditRef = ref()
const categoryIdRef = ref()
const addPublicFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('党务公开', handelConfirmEdit)
/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
  categoryIdRef.value = filterRef.value.categoryId
}
function handelConfirmEdit() {
  addPublicFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addPublicFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

/** 列表操作 */
const tableColumns = getTableColumns((row) => {
  return h(
    'div',
    {
      style: {
        color: '#AC241D',
        cursor: 'pointer',
        display: 'flex',
        gap: '15px',
      },
    },
    [
      h(
        'span',
        {
          onClick: () => {
            idEditRef.value = row.id
            editTypeRef.value = 'view'
            showEditRef.value = true
          },
        },
        { default: () => '查看' },
      ),
      h(
        'span',
        {
          onClick: () => {
            idEditRef.value = row.id
            editTypeRef.value = 'modify'
            showEditRef.value = true
          },
        },
        { default: () => '编辑' },
      ),
      h(DeleteButton, {
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    ],
  )
})

const defaultSelectedKeys = ref<string[]>([])
function filterInput(res: { id: string; name: string }) {
  // loadData()
  if (res.id) {
    defaultSelectedKeys.value = [res.id]
    filterRef.value.categoryId = res.id
    selectName.value = res.name
  }
}
onMounted(() => {
  init().then((res: any) => {
    if (res.id) {
      defaultSelectedKeys.value = [res.id]
      filterRef.value.categoryId = res.id
      selectName.value = res.name
    }
  })
})
</script>
<template>
  <layout-container style="height: calc(100vh - 114px)">
    <template #side>
      <SideMenuNew
        v-model:show-modal="showModal"
        title="党务公开"
        :tree-data="treeData"
        :modal-title="modalTitle"
        :default-selected-keys="defaultSelectedKeys"
        @move="moveNode"
        @del-node="delNode"
        @save-tree-node="handleFormatterParams"
        @add-child-node="handleAddChildNode"
        @select-node-key="handleChangeTab"
      />
    </template>
    <template #main>
      <table-container
        v-model:page="currentPage"
        v-model:page-size="pageSize"
        :title="selectName"
        :loading="loading"
        :show-toolbar="false"
        custom-toolbar
        :table-columns="tableColumns"
        :table-data="tableData"
        :total="total"
        :checked-row-keys="checkedRowKeys"
        :show-add="judgePermission('publicity_add_btn')"
        :show-delete="judgePermission('publicity_delete_btn')"
        @click-add="handleClickAdd"
        @click-delete="handleBatchDelete"
        @update-page="onUpdatePage"
        @update-page-size="onUpdatePageSize"
        @update-checked-row-keys="onUpdateCheckedRowKeys"
      >
        <template #btns>
          <n-button size="small" type="primary" @click="handleClickAdd">
            <template #icon>
              <n-icon>
                <plus-round />
              </n-icon>
            </template>
            添加
          </n-button>

          <n-button size="small" @click="handleBatchDelete">
            <template #icon>
              <n-icon>
                <delete-forever-round />
              </n-icon>
            </template>
            删除
          </n-button>
        </template>
        <template #filters>
          <n-input
            v-model:value="filterRef.annexName"
            style="width: 200px"
            size="small"
            placeholder="请输入附件名称"
            clearable
          />
        </template>
      </table-container>
    </template>
  </layout-container>
  <CustomDialog
    :show="showModal"
    :title="modalTitle"
    width="600px"
    @confirm="handleFormatterParams"
    @cancel="handleCancel"
    @update:show="(v: boolean) => (showModal = v)"
  >
    <div class="p-[20px]">
      <AddPublicCategoryForm
        v-show="showModalType === 'root'"
        ref="addFirstCategoryRef"
      />
    </div>
  </CustomDialog>

  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <add-public-item-form
        :id="idEditRef"
        ref="addPublicFormRef"
        :category-id="categoryIdRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template v-if="editTypeRef !== 'view'" #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
