import type { TreeOption } from 'naive-ui'

export interface IArrayElement {
  pid?: number | string
  id: number | string
  label: string
}

/** 将树转为数组 */
export function transformTreeArray(treeData: TreeOption[]) {
  const result: IArrayElement[] = []
  function pushNode(treeData: TreeOption[], pid?: number | string) {
    treeData.forEach((node) => {
      result.push({
        id: node.key!,
        label: node.label!,
        pid,
      })
      if (node.children?.length) {
        pushNode(node.children, node.key)
      }
    })
  }
  pushNode(treeData)
  return result
}

/** 返回过滤节点的根节点 ids */
export function getRootNodes(
  originalArray: IArrayElement[],
  nodeArray: IArrayElement[],
) {
  const rootIds: (number | string)[] = []
  function getRoot(node: IArrayElement) {
    if (node.pid) {
      originalArray
        .filter(sub => sub.id === node.pid)
        .forEach((item) => {
          if (node.pid) {
            getRoot(item)
          } else {
            if (!rootIds.includes(node.id)) {
              rootIds.push(node.id)
            }
          }
        })
    } else {
      if (!rootIds.includes(node.id)) {
        rootIds.push(node.id)
      }
    }
  }
  nodeArray.forEach(node => getRoot(node))
  return rootIds
}
