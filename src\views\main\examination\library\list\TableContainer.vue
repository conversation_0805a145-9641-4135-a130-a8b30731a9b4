<!--
 * @Description: 表格容器
-->
<template>
  <div class="table-container">
    <div v-if="showTitle" class="title">
      {{ props.title }}
    </div>

    <div v-if="showTabs">
      <slot name="tabs" />
    </div>

    <div v-if="showToolbar" class="tool-bar">
      <div class="btns">
        <slot name="btns">
          <n-button
            v-if="showAdd"
            size="small"
            type="primary"
            @click="emits('click-add')"
          >
            <template #icon>
              <n-icon>
                <plus-round />
              </n-icon>
            </template>
            {{ addText }}
          </n-button>
          <n-button
            v-if="showDelete"
            size="small"
            @click="emits('click-delete')"
          >
            <template #icon>
              <n-icon>
                <delete-forever-round />
              </n-icon>
            </template>
            删除
          </n-button>
        </slot>
      </div>

      <div class="filters">
        <slot name="filters" />
      </div>
    </div>

    <div v-else-if="customToolbar" class="tool-bar">
      <div class="btns">
        <slot name="btns" />
      </div>
      <div class="filters">
        <slot name="filters" />
      </div>
    </div>

    <n-data-table
      size="small"
      :style="{ height: tableHeight }"
      :bordered="false"
      :loading="loading"
      :columns="props.tableColumns"
      :data="props.tableData"
      :row-key="rowKey"
      flex-height
      @update-sorter="handleSorterChange"
      @update-checked-row-keys="(rowKeys: any) => {
        emits('row-checked', rowKeys)
      }
      "
    />
    <n-pagination
      v-show="showPagination"
      v-model:page="currentPage"
      v-model:page-size="pageSize"
      class="pagination"
      show-quick-jumper
      show-size-picker
      :item-count="total"
      :page-sizes="pageSizes"
      :prefix="prefixRenderer"
    />
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { onMounted, ref, watch } from 'vue'
import type { DataTableColumns, PaginationInfo } from 'naive-ui'
import { NButton, NDataTable, NIcon, NPagination } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import { sessionCache } from '@/utils/cache'

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  tableColumns: {
    type: Array as PropType<DataTableColumns<any>>,
    default: () => [],
  },
  tableData: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  total: {
    type: Number,
    default: 0,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  addText: {
    type: String,
    default: '添加',
  },
  showAdd: {
    type: Boolean,
    default: true,
  },
  showDelete: {
    type: Boolean,
    default: true,
  },
  showToolbar: {
    type: Boolean,
    default: true,
  },
  showPagination: {
    type: Boolean,
    default: true,
  },
  customToolbar: {
    type: Boolean,
    default: false,
  },
  showTitle: {
    type: Boolean,
    default: true,
  },
  showTabs: {
    type: Boolean,
    default: false,
  },
  pageCacheKey: {
    type: String,
    default: '',
  },
  rowKey: {
    type: Function as PropType<(row: any) => string | number>,
    default: (row: any) => row.id ?? row.nodeId ?? row.trainStartTime,
  },
  tableHeight: {
    type: String,
    default: 'calc(100vh - 320px)',
  },
})
const emits = defineEmits([
  'click-add',
  'click-delete',
  'page-change',
  'sorter-change',
  'row-checked',
])

const currentPage = ref(
  (sessionCache.get(`${props.pageCacheKey}_page_num`) as number) ?? 1,
) // 当前页
const pageSize = ref(
  (sessionCache.get(`${props.pageCacheKey}_page_size`) as number) ?? 10,
) // 每页条数
const pageSizes = [10, 15, 20, 30, 50] // 每页条数范围

function prefixRenderer(info: PaginationInfo) {
  return `共 ${info.itemCount} 条 `
}

onMounted(() => {
  emits('page-change', currentPage.value, pageSize.value)
})

defineExpose({
  currentPage,
})

watch(pageSize, () => (currentPage.value = 1))
watch(
  [currentPage, pageSize],
  () => {
    emits('page-change', currentPage.value, pageSize.value)
    if (props.pageCacheKey) {
      sessionCache.set(`${props.pageCacheKey}_page_num`, currentPage.value)
      sessionCache.set(`${props.pageCacheKey}_page_size`, pageSize.value)
    }
  },
  { immediate: true },
)

function handleSorterChange(sorter: any) {
  emits('sorter-change', sorter)
}
</script>

<style lang="scss" scoped>
.table-container {
  padding: 25px 22px;

  .title {
    height: 20px;
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    line-height: 20px;
    margin-bottom: 26px;
  }

  .tool-bar {
    height: 28px;
    margin: 0 0 20px;
    display: flex;
    justify-content: space-between;

    .btns {
      display: flex;

      :deep(.n-button:nth-child(n + 2)) {
        margin-left: 8px;
      }
    }

    .filters {
      display: flex;

      :deep(.item) {
        display: flex;
        align-items: center;

        &:nth-child(n + 2) {
          margin-left: 14px;
        }

        .n-select,
        .n-input {
          width: 8.5vw;
        }

        .n-date-picker.n-date-picker--range .n-input {
          width: 330px !important;
        }

        .label {
          margin-right: 9px;
          font-size: 12px;
          font-weight: 400;
          color: #333333;
        }
      }
    }
  }

  .rbtns {
    margin-left: 10px;

    .n-button:last-child {
      margin-left: 10px;
    }
  }

  .pagination {
    margin-top: 36px;
    font-size: 12px;
  }

  :deep(.n-data-table-filter) {
    justify-content: flex-start;
  }

  .n-pagination.pagination {
    justify-content: flex-end;
    position: relative;

    :deep(.n-pagination-prefix) {
      position: absolute;
      left: 0;
      line-height: 28px;
    }
  }
}
</style>
