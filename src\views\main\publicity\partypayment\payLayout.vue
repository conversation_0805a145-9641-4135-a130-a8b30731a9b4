<template>
  <slider-menu />
  <layout-container :side-width="200" :show-collapse-trigger="false">
    <template #main>
      <component
        :is="route.matched[route.matched.length - 1].components?.default"
      />
    </template>
  </layout-container>
</template>
<script setup lang="ts">
import SliderMenu from '@/components/SliderMenu.vue'

const route = useRoute()
</script>
<style lang="scss" scoped></style>
