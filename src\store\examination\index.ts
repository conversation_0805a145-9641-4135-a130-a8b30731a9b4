/*
 * @Description: 考试中心相关数据
 */
import { defineStore } from 'pinia'
import type { SelectOption } from 'naive-ui'
import type {
  IExaminationExam,
  IExaminationExamQuery,
  IExaminationLibrary,
  IExaminationLibraryLabel,
  IExaminationLibraryQuery,
  IExaminationPaperItem,
  IExaminationPaperQuery,
  IExaminationQuestion,
  IExaminationQuestionQuery,
  IFilteredExaminationQuestion,
  IFilteredExaminationQuestionQuery,
} from '@/services/examination/index'
import {
  getExaminationExamList,
  getExaminationLibraryLabelList,
  getExaminationLibraryList,
  getExaminationPaperList,
  getExaminationQuestionList,
  getFilteredExaminationQuestionList,
} from '@/services/examination/index'

const useExaminationStore = defineStore('examination', {
  state: () => ({
    examinationLibraryList: [] as IExaminationLibrary[], // 题库列表
    examinationLibraryLabelList: [] as (IExaminationLibraryLabel & {
      editing: boolean
    })[], // 试题标签列表
    examinationQuestionList: [] as IExaminationQuestion[], // 题目列表

    examinationPaperList: [] as IExaminationPaperItem[], // 试卷列表
    filteredExaminationQuestionList: [] as IFilteredExaminationQuestion[], // 题目选择页列表数据

    libraryOptions: [] as SelectOption[], // 所有题库，下拉选项
    labelOptions: [] as SelectOption[], // 所有标签，下拉选项

    examList: [] as IExaminationExam[], // 考试列表
  }),

  actions: {
    async getExaminationLibraryListAction(params: IExaminationLibraryQuery) {
      const res = await getExaminationLibraryList(params)
      this.examinationLibraryList = res.list
      return Promise.resolve({
        total: res.total,
        firstLibraryId: res.list[0]?.libraryId,
      })
    },

    async getExaminationLibraryLabelListAction() {
      const res = await getExaminationLibraryLabelList()
      this.examinationLibraryLabelList = res.map(item => ({
        ...item,
        editing: false,
      }))
    },

    async getExaminationQuestionListAction(params: IExaminationQuestionQuery) {
      const res = await getExaminationQuestionList(params)
      this.examinationQuestionList = res.list
      return Promise.resolve(res.total)
    },

    async getExaminationPaperListAction(params: IExaminationPaperQuery) {
      const res = await getExaminationPaperList(params)
      this.examinationPaperList = res.list
      return Promise.resolve(res.total)
    },

    async getFilteredExaminationQuestionListAction(
      params: IFilteredExaminationQuestionQuery,
    ) {
      const res = await getFilteredExaminationQuestionList(params)
      this.filteredExaminationQuestionList = res.list
      return Promise.resolve(res.total)
    },

    async getLibraryOptionsAction() {
      const res = await getExaminationLibraryList({
        pageNo: 1,
        pageSize: 99999,
      })
      this.libraryOptions = res.list.map(item => ({
        label: item.libraryName,
        value: item.libraryId,
      }))
    },

    async getLabelOptionsAction() {
      const res = await getExaminationLibraryLabelList()
      this.labelOptions = res.map(item => ({
        label: item.name,
        value: item.id,
      }))
    },

    async getExamListAction(params: IExaminationExamQuery) {
      const res = await getExaminationExamList(params)
      this.examList = res.list
      return Promise.resolve(res.total)
    },
  },
})

export default useExaminationStore
