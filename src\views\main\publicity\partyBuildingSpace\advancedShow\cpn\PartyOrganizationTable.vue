<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    class="pt-[0px]"
    title=""
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumnsOfPartyOrganization"
    :table-data="tableData"
    :total="total"
    :loading="loading"
    :show-delete="false"
    :checked-row-keys="checkedRowKeys"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button>
    </template>
    <template #filters>
      <n-input
        v-model:value="filterReactive.orgName"
        size="small"
        placeholder="请输入组织名称"
        clearable
      />
    </template>
  </table-container>
  <!-- 新增优秀党员抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <AddOrganizationForm
        :id="idEditRef"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { NButton, NSwitch } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import { columnsOfPartyOrganization } from '../config'
import AddOrganizationForm from './AddOrganizationForm.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import {
  delAdvancedPartyOrganizationItem,
  getAdvancedPartyOrganizationList,
  putUpdateAdvancedPartyOrganizationItemTop,
} from '@/services/publicity/advancedShow'
const filterReactive = ref<{ orgName?: string; year?: string }>({
  orgName: '',
})
// 有接口后添加：loading,tableData
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getAdvancedPartyOrganizationList, filterReactive, {
  batchDeleteTable: true,
  delApi: delAdvancedPartyOrganizationItem,
})

watch(filterReactive.value, (newVal) => {
  loadData()
})

// 新增/编辑党建清单抽屉
// const showEditRefOfOrganization
const idEditRef = ref()
const addNoticeFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('先进基层党组织', handelConfirmEdit)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addNoticeFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

const tableColumnsOfPartyOrganization = columnsOfPartyOrganization(
  (row) => {
    return [
      h(
        NButton,
        {
          onClick: () => {
            idEditRef.value = row.id
            editTypeRef.value = 'modify'
            showEditRef.value = true
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '编辑',
        },
      ),
      h(DeleteButton, {
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    ]
  },
  (row) => {
    return h(NSwitch, {
      checkedValue: '1',
      uncheckedValue: '0',
      value: row.recommandStatus,
      loading: row.loading,
      onUpdateValue(v) {
        row.loading = true
        putUpdateAdvancedPartyOrganizationItemTop(row.id)
          .then((res) => {
            row.recommandStatus = res
          })
          .catch(() => {})
          .finally(() => {
            loadData()
            row.loading = false
          })
      },
    })
  },
)
onMounted(loadData)
</script>

<style lang="scss" scoped></style>
