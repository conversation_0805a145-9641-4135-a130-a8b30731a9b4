export interface PartyBuildingEvaluationTableItemForBranch {
  id: string
  title: string
  evaluationYear: string
  publishTime: string
  organizations?: any
  targetNum: number
  targetFinishNum: number
  evaluationProgress?: any
  status: string
}

export interface TargetItemListItemType {
  id: string
  matter: string
  title: string
  evaluationMode?: string
  evaluationRequirements?: string
  evaluationScore?: string
  score: string
  dept: string
  relatedStatus: boolean
  fileList: any[]
  delFlag: any
  status: string
  submitStatus: string
}
export interface TargetItemFormType {
  categoryName: string
  targetItemList: TargetItemListItemType[]
  id: string
}

export interface TargetDataType {
  id: string
  targetItemForm: TargetItemFormType[]
  totalScore: string
}
