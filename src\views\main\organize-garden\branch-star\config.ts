import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { OrganizeGardenListItem } from '@/services/organize-garden/types'

export function getTableColumns(
  optionColumnRenderer: (row: OrganizeGardenListItem) => VNodeChild,
  handleDrawStar: (row: OrganizeGardenListItem) => VNodeChild,
): DataTableColumns<OrganizeGardenListItem> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'year',
      title: '年度',
    },
    {
      key: 'star',
      title: '组织星级',
      render: handleDrawStar,
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '10%',
      render: row => optionColumnRenderer(row),
    },

  ]
}
