export function usePagination(loadFn: () => void, size = 100) {
  const total = ref(0)
  const pageNum = ref(1)
  const pageSize = ref(size)
  const jump2First = () => {
    pageNum.value === 1 ? loadFn() : (pageNum.value = 1)
  }

  watch(pageNum, () => {
    loadFn()
  })
  watch(pageSize, () => {
    jump2First()
  })
  onMounted(() => {
    loadFn()
  })
  // onUnmounted(() => {
  // })
  return {
    total,
    pageNum,
    pageSize,
    jump2First,
  }
}
