<!-- 积分管理 -->
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="积分管理"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleExport">
        <n-icon style="margin-right: 6px" size="14">
          <export />
        </n-icon>
        导出
      </n-button>
    </template>
    <template #filters>
      <div class="filter-operate">
        <n-input
          v-model:value="filterReactive.userName"
          size="small"
          placeholder="请输入姓名"
          clearable
        />
        <n-select
          v-model:value="filterReactive.reviewStatus"
          size="small"
          placeholder="请选择党组织"
          :options="MESSAGETYPEOPTIONS"
          clearable
        />
      </div>
    </template>
  </table-container>

  <!-- 明细弹框 -->
  <n-modal
    preset="dialog"
    :show="detailVisible"
    :show-icon="false"
    style="width: 800px"
    @close="handleDetailClose"
  >
    <template #header>
      <slot name="header">
        <div>积分明细</div>
      </slot>
    </template>

    <div>
      <div><span>姓名：</span><span>张三</span></div>
      <div><span>所在组织：</span><span>xxx公司xx支部</span></div>
    </div>

    <table-container
      v-model:page="currentPage"
      v-model:page-size="pageSize"
      title=""
      :show-toolbar="false"
      custom-toolbar
      :table-columns="detailColumns"
      :table-data="tableData"
      :total="total"
      :checked-row-keys="checkedRowKeys"
      :loading="loading"
      @update-page="onUpdatePage"
      @update-page-size="onUpdatePageSize"
      @update-checked-row-keys="onUpdateCheckedRowKeys"
    >
      <template #filters>
        <div class="filter-detail-operate">
          <n-date-picker
            v-model:value="filterReactive.userName"
            type="monthrange"
            clearable
          />
        </div>
      </template>
    </table-container>

    <!-- <template #action>
      <slot name="action">
        <div class="dialog-btn">
          <button class="sure-btn" size="small" @click="hanldleDetailSure">
            确定
          </button>
          <button size="small" class="cancel-btn" @click="handleDetailClose">
            取消
          </button>
        </div>
      </slot>
    </template> -->
  </n-modal>
</template>

<script setup lang="ts">
import { h, onMounted, ref, watch } from 'vue'
import { NButton, NIcon, NInput } from 'naive-ui'
import { PlusRound } from '@vicons/material'
import { Delete, Edit, Export } from '@vicons/carbon'
import {
  MESSAGETYPEOPTIONS,
  getIntegralColumns,
  getIntegralDetailColumns,
} from './config'
import { getStatisticsList } from '@/services/pay-manage'
import { useDrawerEdit, useMyTable } from '@/hooks'
import { downloadArrayBuffer } from '@/utils/downloader'

const filterReactive = ref({
  payYear: new Date().getFullYear(),
  unitId: '',
  userName: '',
})

// 明细弹框
const detailVisible = ref(false)

// 当前操作的row
const currentRow = ref({})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getStatisticsList, filterReactive, {
  // batchDeleteTable: true,
  // delApi: delTalkAboutTableListItem,
})

// 修改和删除按钮渲染
const tableColumns = getIntegralColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          handleDetail(row)
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '明细',
      },
    ),
  ]
})

// 详情列表
const detailColumns = getIntegralDetailColumns()

// 明细
function handleDetail(row: any) {
  currentRow.value = row
  detailVisible.value = true
}

/** 导出 */
async function handleExport() {
  try {
    loading.value = true
    // const res = await postExportTargetFile(String(partyListId.value))
    // downloadArrayBuffer(
    //   res,
    //   `纪检清单-${formatTimeStamp(Date.now(), 'YYYYMMDDHHmmss')}`,
    // )
    loading.value = false
  } catch (error) {}
}

// 明细确认
function hanldleDetailSure() {
  detailVisible.value = false
}

// 明细取消
function handleDetailClose() {
  detailVisible.value = false
}

watch(filterReactive.value, () => {
  loadData()
})

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.operate-btn {
  width: 62px;
  height: 28px;
  background: #ffffff;
  border-radius: 3px;
  border: 1px solid #d8d9da;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
}

:deep(.filter-operate .n-input) {
  width: 220px !important;
  height: 28px;
}

:deep(.filter-operate .n-select) {
  width: 184px !important;
  height: 28px !important;
}

:deep(.filter-operate .n-base-selection) {
  width: 184px !important;
  height: 28px !important;
  box-sizing: border-box !important;
}

:deep(.filter-operate .n-base-selection-label) {
  width: 184px !important;
  height: 28px !important;
  box-sizing: border-box !important;
}

:deep(.filter-operate .n-base-selection__state-border) {
  width: 184px !important;
  height: 28px !important;
  box-sizing: border-box !important;
}

.filter-operate {
  @apply flex gap-x-1;
  width: 410px;
}

.dialog-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;

  .sure-btn {
    width: 80px;
    height: 30px;
    background: #cb0000;
    border-radius: 3px;
    font-size: 14px;
    font-weight: 400;
    color: #ffffff;
  }

  .cancel-btn {
    width: 80px;
    height: 30px;
    background: #ffffff;
    border-radius: 3px;
    border: 1px solid #d8d9da;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    margin-left: 12px;
  }
}

.filter-detail-operate {
  width: 330px;
}
</style>
