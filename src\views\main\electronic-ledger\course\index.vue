<script lang="ts" setup>
import { NButton } from 'naive-ui'
import { debounce } from 'lodash-es'
import { computed, h, onMounted, ref, watch } from 'vue'
import { getTableColumns } from './config'
import { getCourseById, getCourseList } from '@/services/electronic-ledger'
import { useMyTable } from '@/hooks/use-my-table'
import usePdfPreview from '@/hooks/use-pdf-preview'

const baseApi = ref(import.meta.env.VITE_API_BASE)
const { useLoadPdfPreview } = usePdfPreview()

interface rowItemInterface {
  title: string
  content: string
}

interface rowInterface {
  label: string
  value: string | rowItemInterface[]
  fileList?: any[]
}

const detailVisible = ref<boolean>(false)
const filterReactive = ref<{
  orgName: string
  startTime: string | null
  endTime: string | null
}>({
  orgName: '',
  startTime: null,
  endTime: null,
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(getCourseList, filterReactive, {
  batchDeleteTable: false,
})

const timeRangeFormattedValue = computed<[string, string] | null>(() => {
  const { startTime, endTime } = filterReactive.value
  if (startTime && endTime) {
    return [startTime, endTime]
  }
  return null
})

function transformJoinMember(list: any) {
  const length = list.length
  if (length === 0) {
    return '0人'
  }
  let names: any = []
  list.forEach((item: any) => {
    names.push(item.trueName)
  })
  names = names.join('、')
  return `${length}人 (${names})`
}

// 详情数据
const detailRow = ref<rowInterface[]>([
  { label: '会议类型', value: '-' },
  { label: '会议名称', value: '-' },
  { label: '会议时间', value: '-' },
  { label: '发起', value: '-' },
  { label: '主持', value: '-' },
  { label: '参与人员', value: '-' },
  { label: '签到人员', value: '-' },
  { label: '请假人员', value: '-' },
  { label: '心得笔记', value: '-' },
  { label: '会议总结', value: '-', fileList: [] },
])

const handleUpdateMonthRange = (formattedValue: [string, string] | null) => {
  if (formattedValue) {
    filterReactive.value.startTime = formattedValue[0]
    filterReactive.value.endTime = formattedValue[1]
  }
  else {
    filterReactive.value.startTime = ''
    filterReactive.value.endTime = ''
  }
}

/** 图片名称过滤 */
function handleResourceNameChange() {
  currentPage.value = 1
  filterReactive.value.orgName = filterReactive.value.orgName.trim()
  loadData()
}

watch(filterReactive.value, debounce(handleResourceNameChange, 50000))
watch(timeRangeFormattedValue, () => {
  loadData()
})
const detailData = ref<any>({})

// 评价项目列表
const projectList = ref<any>([])

/** 点击查看按钮 */
function getDetail(id: any) {
  getCourseById(id).then((res: any) => {
    detailRow.value = [
      { label: '会议类型', value: res?.meetingTypeName },
      { label: '会议名称', value: res?.title },
      { label: '会议时间', value: `${res?.startTime} 至 ${res?.endTime}` },
      { label: '发起', value: res?.user },
      { label: '主持', value: res?.host },
      { label: '参与人员', value: transformJoinMember(res?.memberList) },
      { label: '签到人员', value: transformJoinMember(res?.signedList) },
      { label: '请假人员', value: transformJoinMember(res?.leaveList) },
      { label: '心得笔记', value: res?.meetingNoteList },
      {
        label: '会议总结',
        value: res?.summary,
        fileList: res.fileVOList || [],
      },
    ]
    detailData.value = res
    // 评价项目数组
    try {
      if (res?.each?.eachUserData && Array.isArray(res.each?.eachUserData)) {
        // 转换数据结构
        let arr = res.each?.eachUserData.slice(0)
        arr = arr.map((item: any) => {
          const keys = Object.keys(item)
          item.title = keys[0]
          item.options = item[keys[0]]
          item.labels = []
          Object.keys(item.options).forEach((optionKey: any) => {
            item.labels.push({
              key: optionKey,
              value: item.options[optionKey],
            })
          })
          return item
        })
        projectList.value = arr
      }
    }
    catch (error) {}
  })
  detailVisible.value = true
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          getDetail(row.id)
        },
        type: 'primary',
        text: true,
      },
      {
        default: () => '查看',
      },
    ),
  ]
})

// 预览文件
function previewFile(fileName: string) {
  const urlAll = `${window.$previewHost}${fileName}`
  useLoadPdfPreview(urlAll)
}

onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :loading="loading"
    :show-delete="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    title="党内会议"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
  >
    <template #btns>
      <div class="flex flex-row justify-start items-center gap-x-[10px]">
        <n-input
          v-model:value="filterReactive.orgName"
          clearable
          placeholder="请输入组织名称"
          size="small"
          style="width: 180px"
        />
        <n-date-picker
          v-model:formatted-value="timeRangeFormattedValue"
          clearable
          format="yyyy-MM"
          style="width: 240px"
          type="monthrange"
          @update:formatted-value="handleUpdateMonthRange"
        />
      </div>
    </template>
    <template #filters>
      <n-button type="primary">
        同步查询结果
      </n-button>
    </template>
  </table-container>

  <!-- 查看详情弹框 -->
  <n-modal
    v-model:show="detailVisible"
    :mask-closable="false"
    :show-icon="false"
    preset="dialog"
    style="width: 1200px"
    title="Dialog"
  >
    <template #header>
      <div>&nbsp;</div>
    </template>
    <div class="h-[600px] overflow-y-auto">
      <div
        class="flex flex-row justify-center items-center mt-[20px] mb-[20px] text-[20px]"
      >
        <span>会议详情</span>
      </div>
      <div class="border-top border-left border-right overflow-auto">
        <!-- 基础信息 -->
        <div
          v-for="(item, index) in detailRow"
          :key="index"
          class="flex flex-row justify-start items-center border-bottom h-[auto] min-h-[40px]"
        >
          <div
            class="w-[120px] flex flex-row justify-center items-center font-bold"
          >
            <span>{{ item.label }}</span>
          </div>
          <div
            v-if="item.label !== '心得笔记' && item.label !== '会议总结'"
            class="flex flex-1 h-[100%] flex-row justify-start items-center box-border border-left px-[20px] min-h-[40px]"
          >
            <span>{{ item.value }}</span>
          </div>

          <div
            v-if="item.label === '心得笔记'"
            class="flex flex-1 h-[100%] flex-row justify-start items-center box-border border-left py-[10px] px-[20px] min-h-[40px]"
          >
            <div v-if="Array.isArray(item.value)" class="w-[100%]">
              <div v-for="(note, key) in item.value" :key="note.id">
                <div>{{ note?.title }}</div>
                <div>{{ note?.content }}</div>
                <div class="pt-[10px]">
                  <div>附件：</div>
                  <div v-for="file in note.fileVOList" :key="file.id">
                    <template
                      v-if="
                        [
                          'pdf',
                          'doc',
                          'docx',
                          'xls',
                          'xlsx',
                          'ppt',
                          'pptx',
                          'txt',
                          'rar',
                          'zip',
                        ].includes(file.fileType.toLowerCase())
                      "
                    >
                      <div>
                        <n-button
                          text
                          type="primary"
                          @click="previewFile(file.fileName)"
                        >
                          {{ file.original }}
                        </n-button>
                      </div>
                    </template>
                    <template
                      v-if="
                        ['jpg', 'jpeg', 'png', 'gif'].includes(
                          file.fileType.toLowerCase()
                        )
                      "
                    >
                      <div>
                        <n-image
                          :show-toolbar="false"
                          :src="baseApi + file.fileName"
                          height="150"
                          width="100"
                        />
                      </div>
                    </template>
                  </div>
                </div>
                <div
                  v-if="key !== item.value.length - 1"
                  class="border-bottom"
                ></div>
              </div>
            </div>
            <div v-else>
              -
            </div>
          </div>

          <div
            v-if="item.label === '会议总结'"
            class="flex flex-1 h-[100%] flex-col justify-start items-start box-border px-[20px] py-[20px] min-h-[40px] border-left"
          >
            <div v-html="item.value"></div>
            <div v-if="item.label === '会议总结' && item.fileList?.length > 0">
              <div class="flex flex-row justify-start items-center mt-[10px]">
                <span class="text-[14px]">附件：</span>
              </div>
              <div
                class="flex flex-col justify-start items-start pt-[10px] gap-[10px]"
              >
                <!-- 处理文件-->
                <template v-for="(file, fileIndex) in item.fileList">
                  <n-button
                    v-if="
                      [
                        'pdf',
                        'doc',
                        'docx',
                        'xls',
                        'xlsx',
                        'ppt',
                        'pptx',
                        'txt',
                        'rar',
                        'zip',
                      ].includes(file.fileType.toLowerCase())
                    "
                    :key="fileIndex"
                    text
                    type="primary"
                    @click="previewFile(file.fileName)"
                  >
                    {{ file.original }}
                  </n-button>
                  <!-- 处理图片-->
                  <template
                    v-if="
                      ['jpg', 'jpeg', 'png', 'gif'].includes(
                        file.fileType.toLowerCase()
                      )
                    "
                  >
                    <div :key="fileIndex">
                      <n-image
                        :show-toolbar="false"
                        :src="baseApi + file.fileName"
                        height="150"
                        width="100"
                      />
                    </div>
                  </template>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </n-modal>
</template>

<style lang="scss" scoped>
.border-bottom {
  border-bottom: 1px solid #f1f1f1;
}

.border-top {
  border-top: 1px solid #f1f1f1;
}

.border-left {
  border-left: 1px solid #f1f1f1;
}

.border-right {
  border-right: 1px solid #f1f1f1;
}

.border-bottom-f5 {
  border-bottom: 1px solid #f1f1f1;
}

.border-right-f5 {
  border-right: 1px solid #f1f1f1;
}
</style>
