import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { VNodeChild } from 'vue'
import type { ICarousel } from '@/services/run/carousel/types'

export function getTableColumns(
  optionColumnRenderer: (row: ICarousel) => VNodeChild,
): TableColumns<ICarousel> {
  return [
    {
      title: '序号',
      key: 'id',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      title: '台账时间',
      key: 'syncTime',
    },
    {
      title: '所属年份',
      key: 'createTime',
      render: (row) => {
        return row.syncTime ? row.syncTime.slice(0, 4) : '-'
      },
    },
    {
      title: '组织名称',
      key: 'orgName',
      width: 380,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '父级组织',
      key: 'orgParentName',
      width: 380,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '操作',
      key: 'action',
      align: 'left',
      width: 120,
      render: optionColumnRenderer,
    },
  ]
}
