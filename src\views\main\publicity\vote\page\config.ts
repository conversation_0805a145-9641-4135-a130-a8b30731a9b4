import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type {
  TalkAboutPartyBranchTableItem,
  TalkAboutPartyMemberTableItem,
} from '@/services/publicity/vote/talkAbout/type'

import finishedIcon from '@/assets/image/vote/rightIcon.png'
import unfinishedIcon from '@/assets/image/vote/wrongIcon.png'
import { QUSETIONNAIREANSWERSTATUS } from '@/constant'
/** 民主评议党员 */
export function talkAboutPartyMemberColumns(
  optionColumnRenderer: (row: TalkAboutPartyMemberTableItem) => VNodeChild,
  additionalData: {
    eachOpen: Boolean
    selfOpen: Boolean
  },
): DataTableColumns<TalkAboutPartyMemberTableItem> {
  const res: any = [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_: any, i: any) => i + 1,
    },
    {
      key: 'commentatorName',
      title: '姓名',
    },
    {
      key: 'orgName',
      title: '所在组织',
    },
    {
      key: 'reviewTitle',
      title: '评议名称',
    },
  ]
  if (additionalData.selfOpen) {
    res.push({
      key: 'questionnaireAnswerStatus',
      title: '党员自评',
      render: (row: any) => {
        return row.questionnaireAnswerStatus
          === QUSETIONNAIREANSWERSTATUS.ANSWERED
          ? h('img', { class: 'w-[20px]', src: finishedIcon })
          : h('img', { class: 'w-[20px]', src: unfinishedIcon })
      },
    })
  }
  if (additionalData.eachOpen) {
    res.push({
      key: 'eachReview',
      title: '党员互评',
      render: (row: any) => {
        return h('span', [
          h(
            'span',
            {
              style: {
                color:
                  row.eachReview === row.eachReviewTotal
                    ? '#00c969'
                    : '#AC241D',
              },
            },
            { default: () => `${row.eachReview}` },
          ),
          h('span', {}, { default: () => '/' }),
          h('span', {}, { default: () => `${row.eachReviewTotal}` }),
        ])
      },
    })
  }
  res.push({
    key: 'action',
    title: '操作',
    align: 'left',
    width: '180',
    render: (row: any) => optionColumnRenderer(row),
  })
  return res
}

// 民主评议党支部班子
export function talkAboutPartyBranchColumns(
  optionColumnRenderer: (row: TalkAboutPartyBranchTableItem) => VNodeChild,
): DataTableColumns<TalkAboutPartyBranchTableItem> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'orgName',
      title: '组织名称',
    },
    {
      key: 'orgTypeName',
      title: '组织性质',
    },

    {
      key: 'branchReview',
      title: '已评人数/党员总人数',
      render: row => `${row.branchReview}/${row.branchReviewTotal}`,
    },

    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
