import type { PaginationReq, PaginationRes } from '../types'
import type { PointDetailTableItemType, PointTableItemType } from './types'
import { commonReq } from '@/services/request'

/** 获取积分列表 */
export function getPointList(
  params: { orgId: string; userName: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<PointTableItemType>>({
    url: '/sys-operation/backend/v1/news_record_statistic/page',
    params,
  })
}

/** 获取积分列表 */
export function getPointDetailList(
  params: { userId: string; startTime: string; endTime: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<PointDetailTableItemType>>({
    url: '/sys-operation/backend/v1/news_record_statistic/detail/page',
    params,
  })
}
