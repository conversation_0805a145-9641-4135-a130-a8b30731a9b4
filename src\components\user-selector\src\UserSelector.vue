<template>
  <custom-dialog
    title="选择人员"
    :show="show"
    width="835px"
    @update:show="(v:any) => emits('update:show', v)"
    @confirm="handleConfirm"
  >
    <div class="user-selector">
      <div class="selector">
        <div class="label">
          选择
        </div>
        <div class="ctn">
          <n-input
            v-model:value="patternRef"
            style="margin-bottom: 10px"
            placeholder="请搜索"
            clearable
          />
          <n-checkbox
            v-model:checked="allCheckedRef"
            style="margin-left: 28px; margin-bottom: 10px"
            :indeterminate="isIndeterminate"
          >
            <n-tooltip trigger="hover">
              <template #trigger>
                <div class="flex flex-row justify-center items-center">
                  <span
                    style="
                      display: inline-block;
                      padding-left: 2px;
                      text-align: center;
                    "
                  >
                    全选
                  </span>
                  <n-icon size="16">
                    <MdInformationCircle />
                  </n-icon>
                </div>
              </template>

              全选时数据量计算较大，请耐心等待！
            </n-tooltip>
          </n-checkbox>
          <n-scrollbar style="max-height: 260px">
            <n-tree
              block-line
              checkable
              check-strategy="parent"
              :checked-keys="selectedKeysRef"
              :pattern="patternRef"
              :filter="handleFilter"
              :data="treeDataRef"
              @update-checked-keys="setCheckedKeys"
            />
          </n-scrollbar>
        </div>
      </div>

      <div class="selected">
        <div class="label">
          选中
        </div>
        <div class="ctn">
          <n-scrollbar
            style="
              max-height: 317px;
              padding-right: 20px;
              box-sizing: border-box;
            "
          >
            <div
              v-for="(item, index) in selectedPersonList"
              :key="item.userId"
              class="user-item"
            >
              <span>{{ item.trueName }}</span>
              <n-button text @click="() => handleDelete(index)">
                <n-icon size="16" color="#A4A4A4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    viewBox="0 0 1024 1024"
                  >
                    <path
                      d="M685.4 354.8c0-4.4-3.6-8-8-8l-66 .3L512 465.6l-99.3-118.4l-66.1-.3c-4.4 0-8 3.5-8 8c0 1.9.7 3.7 1.9 5.2l130.1 155L340.5 670a8.32 8.32 0 0 0-1.9 5.2c0 4.4 3.6 8 8 8l66.1-.3L512 564.4l99.3 118.4l66 .3c4.4 0 8-3.5 8-8c0-1.9-.7-3.7-1.9-5.2L553.5 515l130.1-155c1.2-1.4 1.8-3.3 1.8-5.2z"
                      fill="currentColor"
                    />
                    <path
                      d="M512 65C264.6 65 64 265.6 64 513s200.6 448 448 448s448-200.6 448-448S759.4 65 512 65zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372s372 166.6 372 372s-166.6 372-372 372z"
                      fill="currentColor"
                    />
                  </svg>
                </n-icon>
              </n-button>
            </div>
          </n-scrollbar>
        </div>
      </div>
    </div>
  </custom-dialog>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { computed, ref, watch } from 'vue'
import type { TreeOption } from 'naive-ui'
import { NButton, NCheckbox, NIcon, NInput, NScrollbar, NTree } from 'naive-ui'
import { MdInformationCircle } from '@vicons/ionicons4'
import { getRootNodes, transformTreeToArray } from './utils'
import CustomDialog from '@/components/custom-dialog'

const props = defineProps({
  treeData: {
    type: Array as PropType<TreeOption[]>,
    required: true,
  },
  show: {
    type: Boolean,
    required: true,
  },
})
const emits = defineEmits(['update:show', 'confirm'])

const selectedPersonList = ref<any[]>([])
const isHandleDelete = ref(false)

const patternRef = ref('')
const treeDataRef = ref<TreeOption[]>(props.treeData ?? [])
const originalNodeArr = computed(() => transformTreeToArray(props.treeData))
const selectedKeysRef = ref<(string | number)[]>([])
const allCheckedRef = ref(false)
// const allUserLabels = computed(() =>
//   originalNodeArr.value
//     .map(item => item.id)
//     .filter(item => String(item).includes('&')),
// )

const allUserLabels = computed(() => getAllOrganizationIds(treeDataRef.value))

const isIndeterminate = computed(
  () =>
    selectedKeysRef.value.length > 0
    && selectedKeysRef.value.length < allUserLabels.value.length,
)

function handleFilter(pattern: string, node: TreeOption) {
  isHandleDelete.value = false
  return node.label?.includes(pattern) ?? false
}

// 删除
function handleDelete(index: number) {
  isHandleDelete.value = true // 标记当前操作为删除操作
  // selectedKeysRef.value.splice(index, 1)
  selectedPersonList.value.splice(index, 1)

  // 满足以下场景 若一个部门的下的所有人都被删除 则组织树上相应的组织✔ 应该被取消

  // 获取当前选中人员的唯一组织ID
  const currentOrganizationIds = getUniqueOrganizationIds(
    selectedPersonList.value,
  )

  // 检查哪些组织ID现在应该被取消选中
  const toBeUnselectedIds = selectedPersonList.value
    .filter(id => !currentOrganizationIds.includes(id))
    .map(item => item.deptId)
  selectedKeysRef.value = toBeUnselectedIds
}

// 通过选中那些人，得到一个所在部门的List
function getUniqueOrganizationIds(list: any[]) {
  const organizationIds = new Set()

  // 遍历已选中的人列表，将每个人的组织ID添加到 Set 中
  list.forEach((person) => {
    if (person.deptId) {
      // 确保 person 包含 deptId
      organizationIds.add(person.deptId)
    }
  })

  // 将 Set 转换为数组
  const uniqueOrganizationIdsArray = Array.from(organizationIds)

  return uniqueOrganizationIdsArray
}

// 点击确定
function handleConfirm() {
  const userList = selectedPersonList.value.map(user => ({
    userId: user.userId,
    userName: user.trueName,
    deptId: user.deptId,
  }))
  emits('confirm', userList)
  emits('update:show', false)
}

// 监听选中的部门动态计算该部门下的所有人
watch(
  () => selectedKeysRef.value,
  () => {
    if (selectedKeysRef.value.length && !isHandleDelete.value) {
      selectedPersonList.value = getDeptAllPerson(selectedKeysRef.value)
    }
  },
  { deep: true },
)

// 设置选中的值
function setCheckedKeys(v: Array<number | string>) {
  isHandleDelete.value = false // 标记当前操作为删除操作
  selectedKeysRef.value = v
  // 根据选中的部门id去取部门下的所有人
}

// 主页面通过选中那些人传到这个表单 得到这些人所在的部门idList
function calcCheckedKeys(arr: any[]) {
  selectedKeysRef.value = getUniqueOrganizationIds(arr) as any
}

// 根据选择的部门组合所选中部门下的人
function getDeptAllPerson(deptIdList: any[]): any[] {
  let personList: any[] = []
  deptIdList.forEach((deptId) => {
    const deptData = findDepartmentById(treeDataRef.value, deptId)
    const userList: any[] = Array.isArray(deptData?.userList)
      ? deptData?.userList
      : []
    personList = [...personList, ...userList]
  })
  return personList
}

/**
 * 在树结构中递归查找指定ID的节点。
 * @param tree 树结构数组
 * @param targetId 目标节点ID
 * @returns 找到的节点，如果未找到则返回null
 */
function findDepartmentById(
  tree: TreeOption[],
  targetId: string,
): TreeOption | null {
  for (const node of tree) {
    if (node.key === targetId) {
      return toRaw(node) // 如果找到，立即返回节点
    }
    else {
      if (node.children && node.children.length > 0) {
        const found = findDepartmentById(node.children, targetId) // 递归搜索子节点
        if (found) {
          return toRaw(found) // 如果在子节点中找到，返回找到的节点
        }
      }
    }
  }
  return null // 如果遍历完所有节点都没有找到，则返回null
}

// 取出所有部门的id
function getAllOrganizationIds(tree: TreeOption[]): string[] {
  const ids: string[] = []

  tree.forEach((node) => {
    ids.push(node.key as string) // 添加当前节点的ID
    if (node.children && node.children.length > 0) {
      // 递归地添加子节点的ID
      ids.push(...getAllOrganizationIds(node.children))
    }
  })

  return ids
}

watch(patternRef, (newV) => {
  const rootIds = getRootNodes(
    originalNodeArr.value,
    transformTreeToArray(props.treeData).filter(item =>
      item.label.includes(newV),
    ),
  )
  treeDataRef.value
    = props.treeData?.filter(item => rootIds.includes(item.key!)) ?? []
})

watch(allCheckedRef, (newV) => {
  if (newV) {
    selectedKeysRef.value = allUserLabels.value
  }
  else {
    selectedKeysRef.value = []
    selectedPersonList.value = []
  }
})

watch(selectedKeysRef, (newV) => {
  if (!newV.length) {
    allCheckedRef.value = false
  }
  else if (newV.length === allUserLabels.value.length) {
    allCheckedRef.value = true
  }
})
defineExpose({
  setCheckedKeys,
  calcCheckedKeys,
})
</script>

<style lang="scss" scoped>
.user-selector {
  display: flex;

  .label {
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    margin-bottom: 10px;
  }

  .ctn {
    width: 370px;
    height: 347px;
    box-sizing: border-box;
    background: #f7f9fc;
    border-radius: 3px;
    border: 1px solid #e5e6e7;
    padding: 15px;
  }

  > .selector {
    margin-right: 16px;

    :deep(.n-tree-node-content__text) {
      font-size: 12px;
    }
  }

  > .selected {
    .user-item {
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      line-height: 37px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
}
</style>
