/** url 转 DataUrl */
export function url2DataUrl(url: string): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()
      img.crossOrigin = 'Anonymous'

      // 添加 onerror 处理器
      img.onerror = (err) => {
        reject(new Error(`${err}`))
      }

      img.onload = function() {
        canvas.height = img.height
        canvas.width = img.width
        ctx?.drawImage(img, 0, 0)
        const dataUrl = canvas.toDataURL('image/png')
        resolve(dataUrl)
      }
      img.src = url
    }
    catch (error) {
      reject(error)
    }
  })
}
/** DataUrl 转 文件 */
export function dataUrl2File(dataUrl: string, fileName: string): File {
  const arr = dataUrl.split(',')
  const mime = arr[0].match(/:(.*?);/)?.[1]
  const bstr = atob(arr[1])
  let len = bstr.length
  const u8arr = new Uint8Array(len)
  while (len--) {
    u8arr[len] = bstr.charCodeAt(len)
  }
  return new File([u8arr], fileName, { type: mime })
}

/** 文件转 Base64 */
export function file2Base64(file: any) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}
