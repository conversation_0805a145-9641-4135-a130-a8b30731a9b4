<script setup lang="ts">
import { NButton, NSwitch } from 'naive-ui'
import { debounce } from 'lodash-es'
import { getTableColumns } from './config'
import CarouselForm from './cpn/CarouselForm.vue'
import { useDrawerEdit } from '@/hooks/use-drawer-edit'
import {
  deleteCarousel,
  getCarouselList,
  postMobileIsShow,
  postPublishCarousel,
} from '@/services/run/carousel/carousel'

import { useMyTable } from '@/hooks/use-my-table'
import DeleteButton from '@/components/DeleteButton.vue'

const filterReactive = ref<{
  title: string
}>({
  title: '',
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  handleSingleDelete,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(getCarouselList, filterReactive, {
  batchDeleteTable: true,
  delApi: deleteCarousel,
})

/** 图片名称过滤 */
function handleResourceNameChange() {
  currentPage.value = 1
  filterReactive.value.title = filterReactive.value.title.trim()
  loadData()
}
watch(filterReactive, debounce(handleResourceNameChange, 50000))

// 抽屉
const idEditRef = ref()
const carouselFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('轮播图', handelConfirmEdit)
/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  carouselFormRef.value?.validateAndSave()
}
/** 保存成功 */
function handleCarouselSaved() {
  showEditRef.value = false
  loadData()
}
function handleOuterClickCancel() {
  if (!carouselFormRef.value?.getChangedFlag()) {
    window.$dialog.warning({
      title: '提示',
      content: '数据未发布，关闭后本次编辑内容不保存，是否继续？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        handleClickCancel()
      },
    })
  }
  else {
    handleClickCancel()
  }
}
watch(showEditRef, (newV) => {
  if (!newV) {
    carouselFormRef.value?.resetForm()
  }
})
// 修改和删除按钮渲染
const tableColumns = getTableColumns(
  (row) => {
    return [
      h(
        NButton,
        {
          onClick: () => {
            idEditRef.value = row.id
            editTypeRef.value = 'view'
            showEditRef.value = true
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '查看',
        },
      ),
      h(
        NButton,
        {
          onClick: () => {
            idEditRef.value = row.id
            editTypeRef.value = 'modify'
            showEditRef.value = true
          },
          disabled: row.isRelease === '是',
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '修改',
        },
      ),
      h(DeleteButton, {
        btnDisabled: row.isRelease === '是',
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    ]
  },
  // (row, index) => {
  //   return (currentPage.value - 1) * pageSize.value + index + 1
  // },
  (row) => {
    return h(NSwitch, {
      checkedValue: '是',
      uncheckedValue: '否',
      value: row.isRelease,
      onUpdateValue() {
        const params = {
          id: String(row.id),
          isRelease: row.isRelease === '是' ? 0 : 1,
        }
        postPublishCarousel(params)
          .then((res) => {
            row.isRelease = res
          })
          .catch(() => {})
          .finally(() => {
            loadData()
          })
      },
    })
  },
  (row) => {
    return h(NSwitch, {
      checkedValue: '是',
      uncheckedValue: '否',
      disabled: row.isRelease === '否', // 未发布的不能选择移动端是否显示
      value: row.isMobile,

      onUpdateValue() {
        const params = {
          id: String(row.id),
          isRelease: row.isMobile === '是' ? 0 : 1,
        }
        postMobileIsShow(params)
          .then((res) => {
            row.isMobile = res
          })
          .catch(() => {})
          .finally(() => {
            loadData()
          })
      },
    })
  },
)
onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="轮播图管理"
    :loading="loading"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :show-delete="false"
    @click-add="handleClickAdd"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
  >
    <template #filters>
      <n-form label-placement="left">
        <n-grid :cols="20">
          <n-form-item-gi span="20" label="">
            <n-input
              v-model:value="filterReactive.title"
              clearable
              size="small"
              placeholder="请输入轮播图名称"
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </template>
  </table-container>
  <n-drawer v-model:show="showEditRef" :width="600" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <carousel-form
        :id="idEditRef"
        ref="carouselFormRef"
        :type="editTypeRef"
        @saved="handleCarouselSaved"
      />
      <template #footer>
        <n-button
          type="primary"
          style="margin-right: 10px"
          @click="handleClickConfirm"
        >
          保存
        </n-button>
        <n-button @click="handleOuterClickCancel">
          取消
        </n-button>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
