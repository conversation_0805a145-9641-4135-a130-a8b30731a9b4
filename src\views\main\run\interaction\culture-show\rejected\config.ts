import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { CultureShowListItem } from '@/services/run/interaction/types'

export function getTableColumns(
  optionColumnRenderer: (row: CultureShowListItem) => VNodeChild,
): DataTableColumns<CultureShowListItem> {
  return [
    {
      type: 'selection',
    },
    {
      key: 'index',
      title: '序号',
      width: '100',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'username',
      title: '姓名',
    },
    {
      key: 'orgName',
      title: '组织名称',
    },
    {
      key: 'title',
      title: '标题',
    },
    {
      key: 'content',
      title: '作品描述',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'fileList',
      title: '附件数量',
      render: row => (row.fileList.length ? row.fileList.length : '--'),
    },

    {
      key: 'publishTime',
      title: '发布时间',
    },
    {
      key: 'reason',
      title: '驳回原因',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'rejectedPerson',
      title: '驳回人',
      render: row => (row.rejectedPerson ? row.rejectedPerson : '--'),
    },

    {
      key: 'auditTime',
      title: '驳回时间',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
