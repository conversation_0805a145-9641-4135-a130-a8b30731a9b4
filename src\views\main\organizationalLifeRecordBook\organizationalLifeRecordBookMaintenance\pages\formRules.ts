export const partyMemberFormRules = {
  trueName: {
    required: true,
    message: '请输入姓名',
    trigger: 'input',
  },
  sex: {
    required: true,
    message: '请选择性别',
    trigger: 'change',
  },
  birth: {
    required: true,
    message: '请选择出生日期',
    trigger: 'change',
  },
  edu: {
    required: true,
    message: '请选择学历',
    trigger: 'change',
  },
  // joinTime: {
  //   required: true,
  //   message: '请选择入党时间',
  //   trigger: 'change',
  // },
  political: {
    required: true,
    message: '请选择政治面貌',
    trigger: 'change',
  },
  // partyDuty: {
  //   required: true,
  //   message: '请选择支部内职务',
  //   trigger: 'change',
  // },
}

export const partyMemberMinutesFormRules = {
  title: {
    required: true,
    message: '请输入议事主题',
    trigger: 'input',
  },
  minutesTime: {
    required: true,
    message: '请选择时间',
    trigger: 'change',
    validator: (rule: any, value: any) => {
      if (value == null || value.length === 0) {
        return new Error('请选择时间')
      }
      else {
        return true
      }
    },
  },
  hostId: {
    required: true,
    message: '请选择主持人',
    trigger: 'change',
  },
  attendanceNum: {
    required: true,
    message: '请选择参加人数',
    trigger: 'change',
    type: 'number',
  },
  mainSuggestion: {
    required: true,
    message: '请填写主要意见',
    trigger: 'input',
  },
}

export const trainingFormRules = {
  minutesTime: {
    required: true,
    message: '请选择时间',
    trigger: 'change',
    validator: (rule: any, value: any) => {
      if (value == null || value.length === 0) {
        return new Error('请选择时间')
      }
      else {
        return true
      }
    },
  },
  addr: {
    required: true,
    message: '请填写地点',
    trigger: 'blur',
  },
  studyHour: {
    required: true,
    type: 'number',
    message: '请填写学时',
    trigger: 'blur',
  },
  userId: {
    required: true,
    message: '请填写支部书记',
    trigger: 'blur',
  },
  projectContent: {
    required: true,
    message: '请填写培训项目',
    trigger: 'blur',
  },
}

export const internalPartyReportingRecordsFormRules = {
  minutesTime: {
    required: true,
    message: '请选择时间',
    trigger: 'change',
    validator: (rule: any, value: any) => {
      if (value == null || value.length === 0) {
        return new Error('请选择时间')
      }
      else {
        return true
      }
    },
  },
  hostId: {
    required: true,
    message: '请选择主持人',
    trigger: 'change',
  },
  attendanceNum: {
    required: true,
    message: '请选择参加人数',
    trigger: 'change',
    type: 'number',
  },
  mainContent: {
    required: true,
    message: '请填写主要内容',
    trigger: 'input',
  },
}

// 重要事项征求意见记录表单
export const importantMattersOpinionsRecordsFormRules = {
  minutesTime: {
    required: true,
    message: '请选择时间',
    trigger: 'change',
    validator: (rule: any, value: any) => {
      if (value == null || value.length === 0) {
        return new Error('请选择时间')
      }
      else {
        return true
      }
    },
  },
  hostId: {
    required: true,
    message: '请选择主持人',
    trigger: 'change',
  },
  attendanceNum: {
    required: true,
    message: '请选择参加人数',
    trigger: 'change',
    type: 'number',
  },
  mainContent: {
    required: true,
    message: '请填写主要内容',
    trigger: 'input',
  },
}

export const partyMembersActivitiesRecordsFormRules = {
  date: {
    required: true,
    message: '请选择日期',
    trigger: 'change',
  },
  organizerId: {
    required: true,
    message: '请选择组织者',
    trigger: 'change',
  },
  content: {
    required: true,
    message: '请填写活动内容',
    trigger: 'input',
  },
}

export const partyBranchActivitiesRecordsFormRules = {
  minutesTime: {
    required: true,
    message: '请选择时间',
    trigger: 'change',
    validator: (rule: any, value: any) => {
      if (value == null || value.length === 0) {
        return new Error('请选择时间')
      }
      else {
        return true
      }
    },
  },
  location: {
    required: true,
    message: '请填写地点',
    trigger: 'input',
  },
  content: {
    required: true,
    message: '请填写活动内容',
    trigger: 'input',
  },
  absenteeIdList: {
    required: true,
    message: '请选择缺席人员',
    trigger: 'change',
    validator: (rule: any, value: any) => {
      if (value == null || value.length === 0) {
        return new Error('请选择缺席人员')
      }
      else {
        return true
      }
    },
  },
}
