// 电子台账
import { commonReq } from '../request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 党内会议分页查询 */
export function getCourseList(params: any & PaginationReq) {
  return commonReq.get<PaginationRes<any>>({
    url: '/party-affairs/backend/v1/internal_meeting_book/page',
    params,
  })
}

/** 通过id查询党内会议（党内会议）台账 */
export function getCourseById(id: any) {
  return commonReq.get<PaginationRes<any>>({
    url: `/party-affairs/backend/v1/internal_meeting_book/${id}`,
  })
}

/** 民主评议党员分页查询 */
export function getDemocraticList(params: any & PaginationReq) {
  return commonReq.get<PaginationRes<any>>({
    url: '/party-affairs/backend/v1/democratic_review_book/page',
    params,
  })
}

/** 民主评议支部班子分页查询 */
export function getDemocraticBranchList(params: any & PaginationReq) {
  return commonReq.get<PaginationRes<any>>({
    url: '/party-affairs/backend/v1/democratic_review_book/branch_page',
    params,
  })
}

/** 通过id查询民主评议党员台账 */
export function getDemocraticById(id: any) {
  return commonReq.get<PaginationRes<any>>({
    url: `/party-affairs/backend/v1/democratic_review_book/${id}`,
  })
}

/** 通过id查询民主评议支部台账 */
export function getDemocraticBranchById(params: any) {
  return commonReq.get<PaginationRes<any>>({
    url: '/party-affairs/backend/v1/democratic_review_book/getBranchDetail',
    params,
  })
}

/** 组织生活分页查询 */
export function getOrganizeLifeList(params: any & PaginationReq) {
  return commonReq.get<PaginationRes<any>>({
    url: '/upms/backend/v1/portal_user_book/page',
    params,
  })
}

/** 通过id查询用户表台账 */
export function getOrganizeLifeById(id: any) {
  return commonReq.get<PaginationRes<any>>({
    url: `/upms/backend/v1/portal_user_book/${id}`,
  })
}
