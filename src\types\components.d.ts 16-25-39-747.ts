// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    EditTop: typeof import('./../components/EditTop.vue')['default']
    ImgUploader: typeof import('./../components/ImgUploader.vue')['default']
    NButton: typeof import('naive-ui')['NButton']
    NConfigProvider: typeof import('naive-ui')['NConfigProvider']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NInput: typeof import('naive-ui')['NInput']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
