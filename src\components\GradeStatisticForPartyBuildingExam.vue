<script setup lang="ts">
import type { DataTableColumns } from 'naive-ui'
import {
  NButton,
  NCollapse,
  NCollapseItem,
  NIcon,
  NInputNumber,
} from 'naive-ui'
import { KeyboardArrowDownSharp } from '@vicons/material'
import CustomDialog from './CustomDialog.vue'
import type { RelateMarkListRow } from '@/services/affairs/party-building-list/types'
import { getRelateMarkList } from '@/services/affairs/party-building-list'

import { useEditTable } from '@/hooks/use-edit-table'
import {
  getAssessIssueDetailInfo,
  putAssessIssueGrade,
} from '@/services/publicity/vote/evaluation'
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  width: {
    type: String,
    default: '1200px',
  },
  showAction: {
    type: Boolean,
    default: true,
  },
  maskClosable: {
    type: Boolean,
    default: false,
  },
  belong: {
    type: String,
  },
  examScore: {
    type: Number,
  },
})
const emits = defineEmits(['confirm', 'update:show', 'cancel'])

const markList = ref<RelateMarkListRow[]>([])

async function loadMarkListData(relationId: string) {
  try {
    const params = {
      relationId,
    }
    if (props.belong === 'discipline') {
      markList.value = await getAssessIssueDetailInfo(params)
    } else {
      markList.value = await getRelateMarkList(params)
    }
  } catch (error) {}
}

const { editDisabled, editingData, onClickEdit, onClickCancel, clearEditing }
  = useEditTable(markList)

defineExpose({
  editDisabled,
  clearEditing,
  loadMarkListData,
})

/** 点击确定按钮 */
async function onClickConfirm(index: number) {
  try {
    const { score } = editingData.value!
    if (score === undefined || score === null || Number.isNaN(score)) {
      return window.$message.error('打分不能为空')
    }

    markList.value[index] = editingData.value!
    const data = {
      id: markList.value[index].id,
      score: markList.value[index].score,
    }
    if (props.belong === 'discipline') {
      await putAssessIssueGrade(data)
    } else {
      // await putRelateMark(data)
    }
    markList.value[index].editing = false
    editingData.value = undefined
  } catch (error) {}
}

// 表格渲染
const tableColumns: DataTableColumns<RelateMarkListRow> = [
  {
    key: 'index',
    title: '序号',
    width: '80',
    align: 'center',
    render: (_, i) => i + 1,
  },
  {
    key: 'organizationName',
    title: '组织名称',
    width: '200',
    render: row => (row.organizationName ? row.organizationName : '--'),
  },
  {
    key: 'fileList',
    title: '佐证材料',
    render: (row) => {
      return row.fileList?.length
        ? h(
          NCollapse,
          {
            arrowPlacement: 'right',
          },
          [
            h(
              NCollapseItem,
              {},
              {
                header: () =>
                  h(
                    'div',
                    {
                      style: {
                        marginBottom: '2px',
                        cursor: 'pointer',
                        color: '#3f7ee8',
                      },
                    },
                    row.fileList?.[0].fileName,
                  ),
                arrow: () =>
                  h(
                    NIcon,
                    row.fileList?.length === 1
                      ? ''
                      : () => h(KeyboardArrowDownSharp),
                  ),
                default: () =>
                  row.fileList?.slice(1)
                    && row.fileList?.slice(1).map((item) => {
                      return h(
                        'div',
                        {
                          style: {
                            marginBottom: '2px',
                            cursor: 'pointer',
                            color: '#3f7ee8',
                          },
                        },
                        item.fileName,
                      )
                    }),
              },
            ),
          ],
        )
        : h('div', {}, { default: () => '--' })
    },
  },
  {
    key: 'score',
    title: '打分',
    width: '200',
    render: (row) => {
      return row.editing
        ? h(NInputNumber, {
          max: props.examScore! > 0 ? props.examScore : 0,
          min: props.examScore! < 0 ? props.examScore : -100,
          defaultValue: 1,
          buttonPlacement: 'both',
          size: 'large',
          value: editingData.value!.score,
          onUpdateValue: v => (editingData.value!.score = v!),
        })
        : row.score !== null
          ? row.score
          : '--'
    },
  },
  {
    key: 'options',
    title: '操作',
    width: '10%',
    render: (row, i) => {
      return row.editing
        ? h('div', [
          h(
            NButton,
            {
              text: true,
              type: 'primary',
              style: 'margin-right:20px',
              onClick: () => onClickConfirm(i),
            },
            '确认',
          ),
          h(
            NButton,
            {
              text: true,
              type: 'primary',
              onClick: () => onClickCancel(i),
            },
            '取消',
          ),
        ])
        : h('div', [
          h(
            NButton,
            {
              disabled: editDisabled.value,
              text: true,
              type: 'primary',
              onClick: () => onClickEdit(i),
            },
            '编辑',
          ),
        ])
    },
  },
]
</script>

<template>
  <CustomDialog
    :show="show"
    :title="title"
    width="1500px"
    :show-action="false"
    @cancel="emits('cancel')"
    @update:show="(v) => emits('update:show', v)"
  >
    <div class="score-statistic">
      <n-data-table v-if="markList" :columns="tableColumns" :data="markList" />
      <div v-else class="emptyImg" />
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
:deep() .dialog-header {
  width: 100%;
  height: 50px;
  background: #f2f4f8;
  line-height: 50px;
  padding-left: 29px;
}

.score-statistic {
  padding: 20px;
}

.emptyImg {
  width: 100%;
  height: 500px;
  background-image: url('@/assets/image/emptyImg.png');
  background-position: center center;
  background-repeat: no-repeat;
}
</style>
