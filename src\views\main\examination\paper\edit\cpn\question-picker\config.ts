/*
 * @Description: 选题页面相关配置
 */

import type { DataTableColumns } from 'naive-ui'
import type { IFilteredExaminationQuestion } from '@/services/examination/index'
import { formatCustomTimeString } from '@/utils/date-time-format'

export const tableColumns: DataTableColumns<IFilteredExaminationQuestion> = [
  { type: 'selection' },
  {
    key: 'name',
    title: '题干',
    width: 400,
    ellipsis: { tooltip: { width: 300 } },
  },
  { key: 'blank', width: 30 },
  { key: 'type', title: '题型', width: 100 },
  { key: 'level', title: '难度', width: 100 },
  {
    key: 'labelList',
    title: '标签',
    render(row) {
      return row.labelList.join('、')
    },
    width: 140,
    ellipsis: { tooltip: { width: 200 } },
  },
  {
    key: 'createdAt',
    title: '创建时间',
    width: 180,
    render(row) {
      return formatCustomTimeString(row.createdAt, 'YYYY-MM-DD HH:mm:ss')
    },
  },
]
