import type { VNodeChild } from 'vue'
import { type DataTableColumns } from 'naive-ui'
// import { NImage } from 'naive-ui'
import type { BranchGardenTableItem } from '@/services/structure/branch-garden/types'

export function getTableColumns(
  optionColumnRenderer: (row: BranchGardenTableItem) => VNodeChild,
): DataTableColumns<BranchGardenTableItem> {
  return [
    { type: 'selection' },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      key: 'orgName',
      title: '组织名称',
      width: '25%',
      render: (row) => {
        return h('span', {
          innerHTML: row.orgName,
        })
      },
    },
    // {
    //   key: 'coverUrl',
    //   title: '封面图片',
    //   width: '10%',
    //   render: (row) => {
    //     const url = row.coverUrl || ''
    //     return h(NImage, {
    //       src: import.meta.env.VITE_API_BASE + url,
    //       style: { width: '100px' },
    //     })
    //   },
    // },
    {
      key: 'content',
      title: '简介',
      width: '46%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '800px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'updateTime',
      title: '更新时间',
      width: '12%',
      render: (row) => {
        return h('span', {
          innerHTML: row.updateTime,
        })
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '12%',
      render: row => optionColumnRenderer(row),
    },
  ]
}
