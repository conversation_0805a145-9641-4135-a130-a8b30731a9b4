<script setup lang="ts">
interface Props {
  text: string
  rightText?: string
}
const props = withDefaults(defineProps<Props>(), {
  rightText: '',
})
</script>
<template>
  <div class="flex items-center mb-[30px]">
    <span class="mr-[10px] w-[4px] h-[14px] bg-[#AC241D]"></span>
    <span class="font-600">{{ text }}</span>
    <span v-show="props.rightText" class="font-[400] ml-[30px]">{{
      rightText
    }}</span>
  </div>
</template>
<style lang="scss" scoped></style>
