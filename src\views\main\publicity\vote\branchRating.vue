<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="支部星级评定"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumnsOfPartyMember"
    :table-data="tableData"
    :total="total"
    :show-delete="false"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <!-- <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button> -->
    </template>
    <template #filters>
      <n-input
        v-model:value="filterReactive.title"
        size="small"
        placeholder="请输入评定方案名称"
        clearable
      />
    </template>
  </table-container>
  <!-- 新增支部星级评定抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <addBranchRatingForm
        :id="idEditRef"
        ref="addBranchRatingFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { NButton } from 'naive-ui'
import { PlusRound } from '@vicons/material'
import { columnsOfBranchRating } from './config'
import addBranchRatingForm from './cpns/addBranchRatingForm.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import {
  delBranchRatingListItem,
  getBranchRatingTableList,
  publishRatingResult,
} from '@/services/publicity/vote/branchRating'
const filterReactive = ref<{ title?: string }>({
  title: '',
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getBranchRatingTableList, filterReactive, {
  batchDeleteTable: true,
  delApi: delBranchRatingListItem,
})

watch(filterReactive.value, () => {
  loadData()
})

// 新增/编辑明主评议抽屉
const idEditRef = ref()
const addBranchRatingFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('支部星级评定', handelConfirmEdit)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addBranchRatingFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addBranchRatingFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}
const router = useRouter()
// 修改和删除按钮渲染
const tableColumnsOfPartyMember = columnsOfBranchRating((row) => {
  return [
    ['0', '1', '2'].includes(row.status)
      ? h(
        NButton,
        {
          onClick: () => {
            idEditRef.value = row.id
            editTypeRef.value = 'modify'
            showEditRef.value = true
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => {
            return '编辑'
          },
        },
      )
      : '',
    // 复评中状态
    row.status === '2'
      ? h(
        NButton,
        {
          onClick: () => {
            router.push({
              name: 'branch-rating-reEvaluation',
              query: { starId: row.id, type: 'modify' },
            })
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        { default: () => '复评' },
      )
      : '',
    // 公布结果
    row.status === '2'
      ? h(
        NButton,
        {
          onClick: () => {
            // 申报进度已申报的总数与总申报数一致
            if (
              !row.declarationTotal
                || !row.declaration
                || row.declaration !== row.declarationTotal
            ) {
              window.$message.error('存在支部未复评，请完成复评')
              return
            }
            if (
              row.declarationTotal
                && row.declaration
                && row.declaration === row.declarationTotal
            ) {
              window.$dialog.warning({
                title: '提示',
                content: '确认公布评定结果吗？',
                positiveText: '确定',
                negativeText: '取消',
                onPositiveClick: () => {
                  publishRatingResult(row.id).then((res) => {
                    window.$message.success('公布成功')
                    loadData()
                  })
                },
              })
            }
          },
          type: 'primary',
          text: true,
          disabled: ['0', '1', '3'].includes(row.status),
          style: {
            marginRight: '10px',
          },
        },
        { default: () => '公布结果' },
      )
      : '',
    // 已完成
    row.status === '3'
      ? h(
        NButton,
        {
          onClick: () => {
            router.push({
              name: 'branch-rating-reEvaluation',
              query: { starId: row.id, type: 'view' },
            })
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        { default: () => '查看评定结果' },
      )
      : '',
    h(DeleteButton, {
      handleConfirm: () => handleSingleDelete(String(row.id)),
    }),
  ]
})
onMounted(loadData)
</script>

<style lang="scss" scoped></style>
