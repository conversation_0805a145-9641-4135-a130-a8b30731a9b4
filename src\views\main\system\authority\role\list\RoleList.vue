<script setup lang="ts">
import { NButton, type NForm } from 'naive-ui'
import { AddRound, DeleteForeverRound } from '@vicons/material'
import { debounce } from 'lodash-es'
import authorityDrawer from '../cpn/authority-config/AuthorityDrawer.vue'
import { formRules, getTableColumns } from './config'
import type { ICarousel } from '@/services/run/carousel/types'
import {
  addRole,
  batchDelRole,
  delRole,
  editorRole,
  getMenuList,
  getRoleList,
  getRoleMenu,
  setRoleMenu,
} from '@/services/system/Role'
import { usePagination } from '@/hooks/use-pagination'

const columns = getTableColumns((row) => {
  return h('div', { style: { color: '#AC241D' } }, [
    h(
      NButton,
      {
        text: true,
        type: 'primary',
        onClick: () => distributeAuthority(row),
      },
      { default: () => '赋予权限' },
    ),
    h(
      NButton,
      {
        text: true,
        type: 'primary',
        disabled: row.roleId === '1',
        style: { cursor: 'pointer', margin: '0 20px' },
        onClick: () => updateRole(row),
      },
      { default: () => '编辑' },
    ),
    h(
      NButton,
      {
        text: true,
        type: 'primary',
        disabled: row.roleId === '1',
        style: { cursor: 'pointer' },
        onClick: () => delRoleItemFn(row.roleId, row.accountNum),
      },
      { default: () => '删除' },
    ),
  ])
})
const newVisible = ref(false)
const formData = reactive({ roleName: '', roleDesc: '' })
const formData2 = reactive({ roleName: '', roleDesc: '', roleId: '' })
const formData3 = reactive({ roleId: '' })
const selectMenuTree = ref([])
const formRef = ref<InstanceType<typeof NForm>>()
const drawerTitle = ref('添加角色')
const { total, pageNum, pageSize } = usePagination(loadData, 10)
const roleName = ref()
const tableData = ref()
const menuOptions = ref()
const checkedRowKeysRef = ref()
const menuIds = ref({ menuIds: [], menuIdsAll: [] })
const loadingFlag = ref(false)
const checkedRowKeysFn = (value: string[]) => {
  checkedRowKeysRef.value = value.join(',')
}
async function distributeAuthority(row: ICarousel) {
  selectMenuTree.value = await getRoleMenu({ roleId: row.roleId })
  formData3.roleId = row.roleId
  drawerTitle.value = `“${row.roleName}”赋予权限`
  newVisible.value = true
}
function createRole() {
  drawerTitle.value = '创建角色'
  newVisible.value = true
}
function updateRole(row: ICarousel) {
  drawerTitle.value = '编辑角色'
  formData2.roleId = row.roleId ?? '0'
  formData2.roleName = row.roleName
  formData2.roleDesc = row.roleDesc
  newVisible.value = true
}
/** 表格删除单项逻辑 */
function delRoleItemFn(id: string, num: number) {
  if (!id) {
    window.$message.error('请选择删除项')
    return
  }
  if (num > 0) {
    window.$message.error('请先删除包含账号或修改账号权限')
    return
  }
  window.$dialog.create({
    type: 'default',
    closable: false,
    content: '确认删除该角色？',
    showIcon: false,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      delRole({ id }).then((res) => {
        window.$message.success('删除成功！')
        loadData()
      })
    },
  })
}
/** 批量删除逻辑 */
function delRoleMoreFn(ids: string) {
  if (!ids) {
    window.$message.error('请选择删除项')
    return
  }
  window.$dialog.create({
    type: 'default',
    closable: false,
    content: '确认删除选中角色？',
    showIcon: false,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      batchDelRole({ ids }).then((res) => {
        window.$message.success('删除成功！')
        loadData()
      })
    },
  })
}
function addConfirm() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (drawerTitle.value === '创建角色') {
        addRole(formData).then((res) => {
          if (res) {
            window.$message.success('保存成功')
          }
          newVisible.value = false
          loadData()
        })
      } else if (drawerTitle.value === '编辑角色') {
        editorRole(formData2).then((res) => {
          if (res) {
            window.$message.success('保存成功')
          }
          newVisible.value = false
          loadData()
        })
      } else {
        const data = {
          ...formData3,
          ...{
            menuIds: menuIds.value.menuIds.join(','),
            menuIdsAll: menuIds.value.menuIdsAll
              .concat(menuIds.value.menuIds)
              .join(','),
          },
        }
        if (!data.menuIds && !data.menuIdsAll) {
          // menuIds 和menuIdsAll 不为真直接关闭抽屉
          newVisible.value = false
        } else {
          setRoleMenu(data).then((res) => {
            if (res) {
              window.$message.success('赋予权限成功')
            }
            newVisible.value = false
            loadData()
          })
        }
      }
    }
  })
}

async function initPage() {
  pageNum.value = 1
  loadData()
}
async function loadData() {
  loadingFlag.value = true
  const payload = {
    // current: pageNum.value,
    // size: pageSize.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }

  if (roleName.value) {
    Object.assign(payload, { roleName: roleName.value })
  }
  const res = await getRoleList(payload)
  total.value = res.total
  tableData.value = res.records
  loadingFlag.value = false
}
async function handleGetMenuList() {
  const res = await getMenuList()
  menuOptions.value = removePermitted2(res)
}
interface Item {
  children?: Item[]
  permission?: string
  type?: string
}
// 当前只控制到页面级 将按钮级过滤(存在permission的删除)
function removePermitted(arr: Item[]): Item[] {
  const result: any[] = []

  for (let i = 0; i < arr.length; i++) {
    const item = arr[i]

    if (Array.isArray(item)) {
      result.push(removePermitted(item))
    } else if (item.children) {
      item.children = removePermitted(item.children)
      result.push(item)
    } else {
      if (!item.permission && item.type !== '3') {
        result.push(item)
      }
    }
  }

  return result
}
// 当前只控制到页面级 将按钮级过滤（children长度为0的删除children 不然tree中会展示展开按钮）
function removePermitted2(arr: Item[]): Item[] {
  const result: any[] = []

  for (let i = 0; i < arr.length; i++) {
    const item = arr[i]

    if (Array.isArray(item)) {
      result.push(removePermitted2(item))
    } else if (item.children) {
      if (item.children.length > 0) {
        item.children = removePermitted2(item.children)
        result.push(item)
      } else {
        item.children = undefined
        result.push(item)
      }
    } else {
      result.push(item)
    }
  }

  return result
}
watch(newVisible, () => {
  formData.roleDesc = ''
  formData.roleName = ''
})
watch(roleName, debounce(initPage, 500))
onMounted(() => {
  handleGetMenuList()
})
</script>
<template>
  <div class="px-[20px] pt-[25px]">
    <p class="text-[16px] font-semibold">
      角色配置
    </p>
    <div class="mt-[25px] flex justify-between">
      <div>
        <n-button
          type="primary"
          size="small"
          style="margin-right: 10px"
          @click="createRole"
        >
          <template #icon>
            <n-icon size="20">
              <add-round />
            </n-icon>
          </template>新建
        </n-button>
        <n-button size="small" @click="() => delRoleMoreFn(checkedRowKeysRef)">
          <template #icon>
            <n-icon size="20">
              <delete-forever-round />
            </n-icon>
          </template>删除
        </n-button>
      </div>
      <div class="w-[400px]">
        <n-input
          v-model:value="roleName"
          placeholder="请输入角色名称"
          clearable
        />
      </div>
    </div>
    <n-data-table
      :columns="columns"
      class="mt-[20px]"
      :data="tableData"
      :loading="loadingFlag"
      :row-key="(row: ICarousel) => row.roleId"
      max-height="calc(100vh - 380px)"
      @update:checked-row-keys="checkedRowKeysFn"
    />
    <div class="flex items-center justify-between mt-[30px] pr-[2px]">
      <span class="text-[#BDBDBD] text-[12px] mr-[30px]">共
        <span class="text-[#262626] mx-[6px]">{{ total }}</span>
        条</span>
      <n-pagination
        v-model:page="pageNum"
        v-model:page-size="pageSize"
        :item-count="total"
        :page-sizes="[5, 10, 20, 30, 50]"
        show-quick-jumper
        show-size-picker
      />
    </div>
  </div>
  <my-drawer
    :show="newVisible"
    :confirm-fn="addConfirm"
    :title="drawerTitle"
    @change-visible="(res: any) => newVisible = res"
  >
    <n-form
      v-if="drawerTitle === '创建角色'"
      ref="formRef"
      label-align="right"
      label-placement="left"
      label-width="100"
      :rules="formRules"
      :model="formData"
    >
      <n-form-item
        path="roleName"
        label="角色名称："
        required
        require-mark-placement="left"
      >
        <n-input
          v-model:value="formData.roleName"
          placeholder="请输入，不超过10字"
          maxlength="10"
        />
      </n-form-item>
      <n-form-item path="roleDesc" label="角色描述：">
        <n-input
          v-model:value="formData.roleDesc"
          type="textarea"
          class="h-[150px]"
          placeholder="请输入，不超过20字"
          maxlength="20"
        />
      </n-form-item>
    </n-form>
    <n-form
      v-else-if="drawerTitle === '编辑角色'"
      ref="formRef"
      label-align="right"
      label-placement="left"
      label-width="100"
      :rules="formRules"
      :model="formData2"
    >
      <n-form-item
        path="roleName"
        label="角色名称："
        required
        require-mark-placement="left"
      >
        <n-input
          v-model:value="formData2.roleName"
          placeholder="请输入，不超过10字"
          maxlength="10"
        />
      </n-form-item>
      <n-form-item path="roleDesc" label="角色描述：">
        <n-input
          v-model:value="formData2.roleDesc"
          type="textarea"
          class="h-[150px]"
          placeholder="请输入，不超过20字"
          maxlength="20"
        />
      </n-form-item>
    </n-form>
    <n-form v-else ref="formRef">
      <authority-drawer
        v-model:value="menuIds"
        :menu-options="menuOptions"
        :select-menu-tree="selectMenuTree"
      />
    </n-form>
  </my-drawer>
</template>

<style lang="scss" scoped></style>
