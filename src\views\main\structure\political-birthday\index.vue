<!-- 政治生日管理 -->
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="政治生日"
    :show-other="showDetail"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
    </template>
  </table-container>

  <!-- 新增/编辑抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="600" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <list-form
        :id="idEditRef"
        ref="addListFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template v-if="editTypeRef !== 'view'" #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { h, onMounted, ref } from 'vue'
import { NButton, NIcon, NImage, NSwitch } from 'naive-ui'
import { PlusRound } from '@vicons/material'
import { USER_STATUS, getPoliticalBirthdayColumns } from './cpn/config'
import ListForm from './cpn/ListForm.vue'
import {
  delBirthday,
  getBirthdayList,
  updateBirthday,
} from '@/services/structure/political-birthday/index'
import { useDrawerEdit, useMyTable } from '@/hooks'

// 新增/编辑抽屉
const idEditRef = ref()
const addListFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('政治生日', handelConfirmEdit)

// 查询条件
const filterReactive = ref({})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(getBirthdayList, filterReactive, {})

const showDetail = ref(false)

/** 列表操作 */
const tableColumns = getPoliticalBirthdayColumns(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
          display: 'flex',
          gap: '15px',
        },
      },
      [
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'modify'
              showEditRef.value = true
            },
          },
          { default: () => '编辑' },
        ),
        h(
          'span',
          {
            onClick: () => handleSingleDelete(row),
          },
          { default: () => '删除' },
        ),
      ],
    )
  },
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => handleEditStatus(row, value, row.id),
      // 状态 0-启用 1-禁用
      value: String(row.blessStatus) === USER_STATUS.USE,
    }),
  row =>
    h(NImage, {
      src: import.meta.env.VITE_API_BASE + row.imgUrl,
      width: '62',
      style: { height: '40px' },
    }),
)

// 添加
function handleAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}

// 删除
function handleSingleDelete(row: any) {
  window.$dialog.warning({
    title: '提示',
    content: '确定删除吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      delBirthday(row.id).then((res) => {
        window.$message.success('删除成功')
        loadData()
      })
    },
  })
}

/** 确定保存 */
function handelConfirmEdit() {
  addListFormRef.value?.validateAndSave()
}

/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

// 编辑状态
async function handleEditStatus(row: any, value: boolean, id: string) {
  row.blessStatus = value ? USER_STATUS.USE : USER_STATUS.NOT
  const res = await updateBirthday(row)
  if (res) {
    window.$message.success('操作成功')
    loadData()
  }
}

watch(showEditRef, (newV) => {
  if (!newV) {
    addListFormRef.value?.resetForm()
  }
})

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped></style>
