import type { FormRules } from 'naive-ui'
import { isHttpOrHttpsLink } from '@/utils/utils'

export const newsDetailFormRules: FormRules = {
  title: {
    required: true,
    message: '请输入标题',
    trigger: 'input',
    type: 'string',
  },
  categoryId: {
    required: true,
    message: '请选择所属栏目',
    trigger: 'change',
  },
  sort: {
    required: true,
    message: '请输入排序数值',
    trigger: 'input',
    type: 'number',
  },
  readNum: {
    required: true,
    message: '请输入阅读量',
    trigger: 'input',
    type: 'number',
  },
  likeNum: {
    required: true,
    message: '请输入点赞量',
    trigger: 'input',
    type: 'number',
  },
  coverUrl: {
    required: true,
    message: '请上传图片',
    trigger: 'change',
    type: 'string',
  },
  isTop: {
    required: true,
    message: '请选择是否置顶',
    trigger: 'change',
    type: 'number',
  },
  isRecommend: {
    required: true,
    message: '请选择是否推荐',
    trigger: 'change',
    type: 'number',
  },
  isHidden: {
    required: true,
    message: '请选择是否隐藏',
    trigger: 'change',
    type: 'number',
  },
  content: {
    required: true,
    message: '请输入内容详情',
    trigger: 'input',
    type: 'string',
  },
  linkUrl: {
    required: true,
    // message: '请输入链接地址',
    validator(rule: any, value: any) {
      if (value === null || !isHttpOrHttpsLink(value)) {
        return new Error('请输入合法的链接地址')
      }
      return true
    },
    trigger: 'input',
    type: 'string',
  },
}
