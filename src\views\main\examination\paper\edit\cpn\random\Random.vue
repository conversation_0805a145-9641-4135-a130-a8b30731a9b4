<!--
 * @Description: 试卷内容-随机组卷
-->
<template>
  <n-spin :show="loadingRef">
    <n-form
      ref="formRef"
      class="form"
      require-mark-placement="left"
      label-width="80"
      label-align="right"
      label-placement="left"
      :rules="formRules"
      :model="rulesReactive"
    >
      <n-form-item label="试卷内容：" path="paperRules">
        <div style="width: 100%">
          <div class="rules">
            <div class="top">
              <span class="left">试卷规则</span>
              <span class="right">已添加 <span>{{ questionCount }}</span> 题</span>
            </div>

            <div class="list">
              <div
                v-for="(item, index) in rulesReactive"
                :key="index"
                class="rule"
              >
                <div class="item" style="width: 13%">
                  <span class="label">题库：</span>
                  <n-select
                    size="small"
                    filterable
                    :options="item.libraryList"
                    :value="item.libraryId"
                    @update-value="(v: any) => {
                      item.libraryId = v
                      item.libraryIdRef = v
                      item.type = null
                      item.level = null
                      item.labelId = null
                      item.loadTypeList()
                    }
                    "
                  />
                </div>
                <div class="item" style="width: 12%">
                  <span class="label">题型：</span>
                  <n-select
                    size="small"
                    :disabled="!item.libraryId"
                    :options="item.typeList"
                    :value="item.type"
                    @update-value="(v: any) => {
                      item.type = v
                      item.typeRef = v
                      item.level = null
                      item.labelId = null
                      item.loadLevelList()
                    }
                    "
                  />
                </div>
                <div class="item" style="width: 12%">
                  <span class="label">难度：</span>
                  <n-select
                    size="small"
                    :disabled="!item.type"
                    :options="item.levelList"
                    :value="item.level"
                    @update-value="(v: any) => {
                      item.level = v
                      item.levelRef = v
                      item.labelId = null
                      item.loadLabelList()
                    }
                    "
                  />
                </div>
                <div class="item" style="width: 12%">
                  <span class="label">标签：</span>
                  <n-select
                    v-model:value="item.labelId"
                    size="small"
                    filterable
                    :disabled="!item.level"
                    :options="item.labelList"
                    @update-value="(v: any) => {
                      item.labelId = v
                      item.labelIdRef = v
                      item.loadCount()
                    }
                    "
                  />
                </div>
                <div class="item" style="width: 13%">
                  <span class="label">数量：</span>
                  <n-input-number
                    v-model:value="item.nums"
                    size="small"
                    style="width: 60px"
                    :show-button="false"
                    :min="1"
                    :max="item.countRef"
                  />
                  <span style="width: 32px; margin-left: 5px; flex-shrink: 0">/ {{ item.countRef }}</span>
                </div>
                <div class="item" style="width: 10%">
                  <span class="label">每题分值：</span>
                  <n-input-number
                    v-model:value="item.score"
                    size="small"
                    :show-button="false"
                    :min="0.5"
                  />
                </div>
                <div class="item" style="width: 15%">
                  <template v-if="item.type == 2">
                    <span class="label">漏选得分：</span>
                    <n-select
                      v-model:value="item.missScoreType"
                      size="small"
                      :options="[
                        { label: '一半得分', value: 1 },
                        { label: '不得分', value: 0 },
                      ]"
                    />
                  </template>
                  <n-select
                    v-if="item.type == 3"
                    size="small"
                    :options="[{ label: '整题给分', value: 1 }]"
                    :value="1"
                  />
                </div>
                <div class="item">
                  <n-button
                    text
                    type="error"
                    @click="() => handleDeleteRule(index)"
                  >
                    <n-icon size="18">
                      <delete-outline-round />
                    </n-icon>
                  </n-button>
                </div>
              </div>

              <div class="add" @click="handleAddRule">
                <n-icon size="20" color="#000">
                  <plus-round />
                </n-icon>
                <span>添加规则</span>
              </div>
            </div>
          </div>

          <div class="total">
            总分：{{ totalScore }}分
          </div>
        </div>
      </n-form-item>
    </n-form>

    <div style="height: 120px" />

    <div class="btns">
      <n-button type="primary" @click="handleCommit">
        提交
      </n-button>
      <n-button @click="$router.replace({ name: 'paperList' })">
        取消
      </n-button>
    </div>
  </n-spin>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import type { FormRules, NForm } from 'naive-ui'
import {
  NButton,
  NFormItem,
  NIcon,
  NInputNumber,
  NSelect,
  NSpin,
} from 'naive-ui'
import { DeleteOutlineRound, PlusRound } from '@vicons/material'
import useRuleSelector, { loadingRef } from './use-rule-selector'
import type { IExaminationPaperPostRule } from '@/services/examination/index'
import useExaminationStore from '@/store/examination/index'

const emits = defineEmits(['commit'])

const examinationStore = useExaminationStore()
examinationStore.getLibraryOptionsAction()
examinationStore.getLabelOptionsAction()

const formRef = ref<InstanceType<typeof NForm>>()
const rulesReactive = reactive<
(IExaminationPaperPostRule & Record<string, any>)[]
>([])

/** 题目数量 */
const questionCount = computed(() =>
  rulesReactive.reduce((prev, current) => prev + current.nums, 0),
)
/** 总分 */
const totalScore = computed(() =>
  rulesReactive.reduce(
    (prev, current) => prev + current.score * current.nums,
    0,
  ),
)

/** 添加规则 */
function handleAddRule() {
  rulesReactive.push({
    libraryId: null,
    type: null,
    level: null,
    labelId: null,
    nums: 1,
    score: 1,
    ...useRuleSelector(false),
  })
}

// 点击删除
function handleDeleteRule(index: number) {
  rulesReactive.splice(index, 1)
}

// 点击提交
function handleCommit() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      emits('commit', rulesReactive, totalScore.value)
    }
  })
}

// 设置ruleList
function setRuleList(list: any[]) {
  rulesReactive.length = 0
  rulesReactive.push(
    ...list.map(item => ({
      libraryId: item.libraryId,
      type: item.type,
      level: item.level,
      labelId: item.labelId,
      nums: item.nums,
      score: item.score,
      ...useRuleSelector(true, item),
      missScoreType: (item.missScore === 0 ? 0 : 1) as 0 | 1,
    })),
  )
}

defineExpose({
  setRuleList,
})

const formRules: FormRules = {
  paperRules: {
    required: true,
    validator() {
      if (rulesReactive.length < 1) {
        return new Error('请添加试卷规则')
      } else {
        // 循环判断字段是否有值
        for (const rule of rulesReactive) {
          for (const k in rule) {
            if (
              rule[k] == null
              || rule.countRef < 1
              || rule.nums < 1
              || (rule.missScoreType === undefined && rule.type === 2)
              || rule.countRef < rule.nums
            ) {
              return new Error('试卷规则有误')
            }
          }
        }
        return true
      }
    },
    trigger: 'blur',
  },
}
</script>

<style lang="scss" scoped>
.form {
  padding: 25px 0 0 44px;

  .rules {
    width: 100%;
    background: #ffffff;
    border-radius: 3px;
    border: 1px solid #dfe0e1;

    > .top {
      line-height: 48px;
      border-bottom: 1px solid #dfe0e1;
      padding: 0 32px 0 18px;
      font-size: 14px;
      font-weight: 400;
      display: flex;
      justify-content: space-between;

      > .left {
        color: #333;
      }

      > .right {
        color: #999;

        > span {
          color: #333;
        }
      }
    }

    > .list {
      padding: 26px 21px 21px;

      > .rule {
        display: flex;
        margin-bottom: 20px;
        justify-content: space-between;

        &:nth-last-child(2) {
          margin-bottom: 45px;
        }

        > .item {
          flex-grow: 0;
          flex-shrink: 0;
          display: flex;
          align-items: center;

          > .label {
            flex-shrink: 0;
            font-size: 12px;
            font-weight: 400;
            color: #999999;
          }
        }
      }

      > .add {
        height: 40px;
        background: #ffffff;
        border-radius: 3px;
        border: 1px dashed #999999;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        user-select: none;

        > span {
          margin-left: 5px;
        }

        &:hover {
          border-color: #006fff;
          color: #006fff !important;

          > .n-icon {
            color: #006fff !important;
          }
        }
      }
    }
  }

  .total {
    margin: 20px 0 0;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
  }
}

.btns {
  width: 100%;
  padding: 40px 0 40px 132px;
  background: #fff;
  position: fixed;
  bottom: 0;

  > button {
    width: 80px;
    height: 30px;
    border-radius: 3px;

    &:nth-child(n + 2) {
      margin-left: 12px;
    }
  }
}
</style>
