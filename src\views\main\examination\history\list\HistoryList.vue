<!--
 * @Description: 考试记录列表页
-->
<template>
  <div class="examination-history">
    <div class="left">
      <awesome-tree
        ref="treeRef"
        max-level="infinite"
        :tree-data="treeData"
        :can-modify="false"
        max-height="calc(100% - 35px)"
        @select-node="handleSelectDept"
      />
    </div>
    <div class="right">
      <table-container
        table-height="calc(100vh - 350px)"
        :show-title="false"
        :show-toolbar="false"
        custom-toolbar
        show-tabs
        :loading="loadingRef"
        :total="totalRef"
        :table-columns="tableColumns"
        :table-data="examRecordListRef"
        @page-change="handlePageChange"
      >
        <template #tabs>
          <n-tabs
            v-model:value="currentTabRef"
            style="margin-bottom: 30px"
            type="line"
          >
            <n-tab
              v-for="item in EXAMINATION_TYPE"
              :key="item.value"
              :name="item.value"
            >
              {{ item.label }}
            </n-tab>
          </n-tabs>
        </template>

        <template #filters>
          <div class="item">
            <span class="label">姓名</span>
            <n-input
              v-model:value="filterReactive.username"
              size="small"
              clearable
              @input="handleFilter"
              @clear="
                () => {
                  filterReactive.username = ''
                  handleFilter()
                }
              "
            />
          </div>
          <div class="item">
            <span class="label">手机号</span>
            <n-input
              v-model:value="filterReactive.phone"
              size="small"
              clearable
              @input="handleFilter"
              @clear="
                () => {
                  filterReactive.phone = ''
                  handleFilter()
                }
              "
            />
          </div>
          <div class="item">
            <span class="label">考试时间</span>
            <n-date-picker
              type="datetimerange"
              size="small"
              clearable
              :value="filterReactive.timeRange"
              @update-value="(v: any) => {
                filterReactive.timeRange = v
                handleFilter()
              }
              "
            />
          </div>
        </template>
      </table-container>

      <!-- 查看页 -->
      <custom-dialog
        v-model:show="showDetailRef"
        width="60vw"
        :show-action="false"
        mask-closable
      >
        <template #header>
          <div class="detail-header">
            <span class="exam-name">
              {{ recordRef?.examUser + '的' + recordRef?.examName }}
            </span>
            <n-button type="primary" size="small" @click="handleClickExport">
              导出
            </n-button>
          </div>
        </template>
        <exam-record-detail v-if="recordRef" ref="detailRef" :row="recordRef" />
      </custom-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, h, reactive, ref, shallowRef, watch } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import { NButton, NDatePicker, NInput, NTab, NTabs } from 'naive-ui'
import { EXAMINATION_TYPE } from '../config'
import ExamRecordDetail from '../cpn/ExamRecordDetail.vue'
import TableContainer from '../../library/list/TableContainer.vue'
import AwesomeTree from '@/components/awesome-tree'
import useDeptTree from '@/hooks/use-dept-tree'
import useTableContainer from '@/hooks/use-table-container'
import type {
  IExamRecord,
  IExamRecordQuery,
} from '@/services/examination/index'
import { getExamRecordList } from '@/services/examination/index'
import { formatTimeStamp } from '@/utils/date-time-format'
import CustomDialog from '@/components/custom-dialog'

const { treeRef, treeData, currentDeptIdRef, handleSelectDept } = useDeptTree()

const { loadingRef, totalRef, currentPageRef, pageSizeRef, handlePageChange }
  = useTableContainer(loadExamRecordList)

// 当前tab
const currentTabRef = ref(EXAMINATION_TYPE[0].value)

const examRecordListRef = shallowRef<IExamRecord[]>([])
const filterReactive = reactive<{
  username: string
  phone: string
  timeRange: [number, number] | null
}>({
  username: '',
  phone: '',
  timeRange: null,
})
// 加载考试记录
function loadExamRecordList() {
  if (!loadingRef.value && currentPageRef.value && currentDeptIdRef.value) {
    loadingRef.value = true
    // 生成参数
    const params: IExamRecordQuery = {
      deptId: currentDeptIdRef.value,
      examinationType: currentTabRef.value,
      pageNo: currentPageRef.value,
      pageSize: pageSizeRef.value,
      from: 'admin',
    }
    const { username, phone, timeRange } = filterReactive
    if (username.length) {
      params.username = username
    }
    if (phone.length) {
      params.phone = phone
    }
    if (timeRange) {
      const [start, end] = timeRange
      params.startTime = formatTimeStamp(start, 'YYYY-MM-DD HH:mm:ss')
      params.endTime = formatTimeStamp(end, 'YYYY-MM-DD HH:mm:ss')
    }
    getExamRecordList(params).then((res) => {
      examRecordListRef.value = res.list
      totalRef.value = Number(res.total)
      loadingRef.value = false
    })
  }
}
// 过滤
function handleFilter() {
  currentPageRef.value = 1
  loadExamRecordList()
}

watch([currentTabRef, currentDeptIdRef], () => {
  loadExamRecordList()
})

const tableColumns = computed<DataTableColumns<IExamRecord>>(() => {
  return [
    { key: 'blank', width: 10 },
    {
      key: 'order',
      title: '序号',
      width: 60,
      render(row, index) {
        return (currentPageRef.value - 1) * pageSizeRef.value + index + 1
      },
    },
    { key: 'examName', title: '考试名称' },
    { key: 'examUser', title: '姓名', width: 100 },
    { key: 'phone', title: '手机号', width: 140 },
    // 分值和得分，只在大于0时展示
    {
      key: 'totalScore',
      width: currentTabRef.value > 0 ? 100 : 0,
      title: currentTabRef.value > 0 ? '分值' : undefined,
      render(row) {
        return currentTabRef.value > 0 ? row.totalScore : null
      },
    },
    {
      key: 'score',
      width: currentTabRef.value > 0 ? 100 : 0,
      title: currentTabRef.value > 0 ? '得分' : undefined,
      render(row) {
        return currentTabRef.value > 0 ? row.score : null
      },
    },
    // 获得积分，只在小于0且不等于-4时展示
    {
      key: 'points',
      title:
        currentTabRef.value < 0 && currentTabRef.value !== -4
          ? '获得积分'
          : undefined,
      width: currentTabRef.value < 0 && currentTabRef.value !== -4 ? 100 : 0,
      render(row) {
        return currentTabRef.value < 0 && currentTabRef.value !== -4
          ? row.points
          : null
      },
    },
    // 考试结果，只在大于0时展示
    {
      key: 'examResult',
      title: currentTabRef.value > 0 ? '考试结果' : undefined,
      width: currentTabRef.value > 0 ? 130 : 0,
      render(row) {
        return currentTabRef.value > 0 ? row.examResult : ''
      },
    },
    { key: 'createAt', title: '考试时间', width: 180 },
    {
      key: 'operation',
      title: '操作',
      width: 100,
      render(row) {
        return [
          h(
            NButton,
            {
              text: true,
              type: 'primary',
              onClick: () => {
                handleClickDetail(row)
              },
            },
            { default: () => '查看' },
          ),
        ]
      },
    },
  ]
})

/* 查看页相关 */
const showDetailRef = ref(false)
const recordRef = ref<IExamRecord>()
const detailRef = ref()
// 点击查看
function handleClickDetail(row: IExamRecord) {
  showDetailRef.value = true
  recordRef.value = row
}
// 点击导出
function handleClickExport() {
  detailRef.value?.handleExport()
}
</script>

<style lang="scss" scoped>
.examination-history {
  height: calc(100vh - 98px);
  display: flex;

  > .left {
    box-sizing: border-box;
    width: 220px;
    padding: 22px 0 22px 19px;
    border-right: 1px solid #e6e7e8;
    flex-shrink: 0;

    :deep(.n-tree-node-wrapper .n-tree-node .n-tree-node-content) {
      height: 28px;
    }

    :deep(.n-tree-node-switcher) {
      width: 12px;
    }

    :deep(.n-tree-node-indent) {
      flex: 0 0 10px !important;
    }
  }

  > .right {
    flex-grow: 1;
  }
}

.detail-header {
  color: #333;
  font-size: 16px;
  font-weight: 500;
  height: 60px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  > button {
    position: absolute;
    right: 60px;
  }
}
</style>
