<script lang="ts" setup>
import { DownloadRound } from '@vicons/material'
import type { TreeSelectOption } from 'naive-ui'
import { NSelect } from 'naive-ui'
import { getBranchBasicOrganizationalColumns } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/config'
import { useRecordBookStore } from '@/store/recordBook'
import {
  editBranchBasicOrganizationCondition,
  getBranchBasicOrganizationCondition,
  getUserByDepartmentId,
} from '@/services/recordBookMaintenance'
import type {
  BranchBasicOrganizationConditionType,
  BranchBasicOrganizationConditionUserType,
  GroupMemberType,
} from '@/services/recordBookMaintenance/types'

const recordBookStore = useRecordBookStore()
const showPopConfirm = ref<boolean>(false)
const currentDownloadFileType = ref<'word' | 'pdf'>('word')

const userList = ref<BranchBasicOrganizationConditionUserType[]>([])
const userOptions = computed(() => {
  return userList.value.map((item) => {
    return {
      label: item.trueName,
      value: item.userId,
    }
  })
})

const originalData = ref<BranchBasicOrganizationConditionType>({})

const formDataRef = ref<BranchBasicOrganizationConditionType>({
  id: '',
  branchMemberList: [],
  groupMemberList: [],
  electionTime: null,
})

// 书记
const secretaryList = ref<string[]>([])
// 副书记
const viceSecretaryList = ref<string[]>([])
// 组织委员
const organizingCommittee = ref<string[]>([])
// 宣传委员
const promotionalCommittee = ref<string[]>([])
// 纪检委员
const inspectionCommittee = ref<string[]>([])

const cacheFormData = ref<Record<string, any>>({})
const selectedDeptId = ref<string | null>(null)
const selectedYear = ref<string | null>(null)
const isCanEdit = ref<boolean>(false)
const tableColumns = computed(() => {
  return getBranchBasicOrganizationalColumns((row) => {
    return isCanEdit.value
      ? h(
        NSelect,
        {
          value: row.userId,
          placeholder: '请选择小组长',
          clearable: true,
          options: userOptions.value,
          onUpdateValue: (value) => {
            row.userId = value
          },
        },
        {},
      )
      : h('span', {}, { default: () => row.userName })
  })
})
const tableData = ref<GroupMemberType[]>([])

const calcOrganizationListTree = computed(() => {
  return recordBookStore.getOrganizationListTreeList
})
const currentYearOptions = computed(() => {
  return recordBookStore.getYearOptions
})
const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})
// 获取当前年份
const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

function handleDownLoadFile() {
  recordBookStore.fetchDownLoadFile({
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
    type: currentDownloadFileType.value,
  })

  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleCancelShowPopConfirm() {
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  selectedDeptId.value = v
  loadData()
  fetchUserList()
  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearSelectedValue(v: string) {
  if (!v) {
    return
  }
  selectedYear.value = v
  loadData()
  recordBookStore.setCurrentSelectedYear(v || '')
}

function handleEdit() {
  isCanEdit.value = true
  cacheFormData.value = { ...formDataRef.value }
}

function handleCancelEdit() {
  isCanEdit.value = false
  loadData()
}

function handleSaveEdit() {
  isCanEdit.value = false
  updateBranchOrganizationalData()
}

function fetchUserList() {
  getUserByDepartmentId({ deptId: selectedDeptId.value! }).then((res) => {
    userList.value = res || []
  })
}

function loadData() {
  getBranchBasicOrganizationCondition({
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
  }).then((res) => {
    // 支部委员会名单 处理
    originalData.value = res
    // 书记
    secretaryList.value
      = res.branchMemberList.find((item: any) => item.position === '书记')
        ?.userIdList || []
    // 副书记
    viceSecretaryList.value
      = res.branchMemberList.find((item: any) => item.position === '副书记')
        ?.userIdList || []
    // 组织委员
    organizingCommittee.value
      = res.branchMemberList.find((item: any) => item.position === '组织委员')
        ?.userIdList || []
    // 宣传委员
    promotionalCommittee.value
      = res.branchMemberList.find((item: any) => item.position === '宣传委员')
        ?.userIdList || []
    // 纪检委员
    inspectionCommittee.value
      = res.branchMemberList.find((item: any) => item.position === '纪检委员')
        ?.userIdList || []
    // 党小组数及小组长名单
    tableData.value = res.groupMemberList || []
    // 本届支部选举时间
    formDataRef.value.electionTime = res.electionTime
  })
}

function calcUserList(position: string): string[] {
  const positionMap: Record<string, string[]> = {
    书记: toRaw(secretaryList.value),
    副书记: toRaw(viceSecretaryList.value),
    组织委员: toRaw(organizingCommittee.value),
    宣传委员: toRaw(promotionalCommittee.value),
    纪检委员: toRaw(inspectionCommittee.value),
  }

  const targetList = positionMap[position]
  return targetList || []
}

function updateBranchOrganizationalData() {
  const branchMemberType = [
    '书记',
    '副书记',
    '组织委员',
    '宣传委员',
    '纪检委员',
  ]
  const branchMemberList = branchMemberType.map((item) => {
    return {
      id:
        originalData.value.branchMemberList.find(
          branchItem => branchItem.position === item,
        )?.id || '',
      position: item,
      userIdList: calcUserList(item),
    }
  })

  const data = {
    id: originalData.value.id || '',
    branchMemberList,
    groupMemberList: tableData.value.map((groupItem) => {
      return {
        id: groupItem.id,
        userId: groupItem.userId,
        groupDeptId: groupItem.groupDeptId,
      }
    }),
    electionTime: formDataRef.value.electionTime,
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
  }

  editBranchBasicOrganizationCondition(data as any).then((res) => {
    window.$message.success('修改成功')
    loadData()
  })
}

onMounted(() => {
  selectedDeptId.value = currentSelectedDept.value.deptId
  selectedYear.value = currentYear.value
  loadData()
  fetchUserList()
})
</script>

<template>
  <div class="p-[20px]">
    <div><span class="font-bold">支部基本组织状况</span></div>
    <div class="flex flex-row justify-between items-center py-[20px]">
      <div class="flex flex-row gap-x-[10px] justify-start items-start">
        <n-tree-select
          v-model:value="selectedDeptId"
          :options="calcOrganizationListTree"
          :show-path="false"
          check-strategy="all"
          children-field="children"
          filterable
          key-field="id"
          label-field="name"
          placeholder="请选择所属党组织"
          style="width: 400px"
          value-field="id"
          @update:value="handleUpdateTreeSelectedValue"
        />
        <n-select
          v-model:value="selectedYear"
          :options="currentYearOptions"
          placeholder="请选择年份"
          size="medium"
          style="width: 200px"
          @update:value="handleUpdateYearSelectedValue"
        />
      </div>
      <div class="flex flex-row gap-x-[10px]">
        <n-button v-if="!isCanEdit" type="primary" @click="handleEdit">
          开启编辑
        </n-button>
        <div v-if="isCanEdit" class="flex flex-row gap-x-[10px]">
          <n-button @click="handleCancelEdit">
            取消编辑
          </n-button>
          <n-button type="primary" @click="handleSaveEdit">
            保存编辑
          </n-button>
        </div>
        <n-popconfirm
          :show="showPopConfirm"
          :show-icon="false"
          placement="left"
        >
          <template #trigger>
            <n-button @click="showPopConfirm = !showPopConfirm">
              <template #icon>
                <n-icon size="16">
                  <DownloadRound />
                </n-icon>
              </template>
              下载
            </n-button>
          </template>
          <div class="py-[10px]">
            <n-radio-group v-model:value="currentDownloadFileType">
              <n-radio
                v-for="(fileType, fileTypeIndex) in [
                  { label: 'word', value: 'word' },
                  { label: 'pdf', value: 'pdf' },
                ]"
                :key="fileTypeIndex"
                :value="fileType.value"
              >
                {{ fileType.label }}
              </n-radio>
            </n-radio-group>
          </div>

          <template #action>
            <div class="w-[100%] flex flex-row justify-center items-center">
              <n-button
                size="small"
                type="primary"
                @click="handleDownLoadFile(item)"
              >
                确定
              </n-button>
              <n-button size="small" @click="handleCancelShowPopConfirm">
                取消
              </n-button>
            </div>
          </template>
        </n-popconfirm>
      </div>
    </div>
    <div class="flex flex-row justify-between items-center">
      <div><span class="font-bold">支部委员会名单</span></div>
    </div>
    <div v-if="isCanEdit">
      <n-form label-placement="left" label-width="100">
        <div class="flex flex-row py-[20px]">
          <n-form-item label="书记：">
            <n-select
              v-model:value="secretaryList"
              :options="userOptions"
              clearable
              multiple
              placeholder="请选择书记"
              style="width: 220px"
            />
          </n-form-item>
          <n-form-item label="副书记：">
            <n-select
              v-model:value="viceSecretaryList"
              :options="userOptions"
              clearable
              multiple
              placeholder="请选择副书记"
              style="width: 220px"
            />
          </n-form-item>
        </div>
        <div class="flex flex-row py-[20px]">
          <n-form-item label="组织委员：">
            <n-select
              v-model:value="organizingCommittee"
              :options="userOptions"
              multiple
              placeholder="请选择组织委员"
              style="width: 220px"
            />
          </n-form-item>
          <n-form-item label="宣传委员：">
            <n-select
              v-model:value="promotionalCommittee"
              :options="userOptions"
              clearable
              multiple
              placeholder="请选择宣传委员"
              style="width: 220px"
            />
          </n-form-item>
          <n-form-item label="纪检委员：">
            <n-select
              v-model:value="inspectionCommittee"
              :options="userOptions"
              clearable
              multiple
              placeholder="请选择纪检委员"
              style="width: 220px"
            />
          </n-form-item>
        </div>
      </n-form>
    </div>

    <div v-else class="flex flex-col gap-y-[20px]">
      <div class="flex flex-row py-[20px]">
        <div class="flex flex-row">
          <div class="w-[100px] text-right">
            <span>书记：</span>
          </div>
          <div class="w-[220px]">
            <span>{{
              originalData?.branchMemberList?.find(
                (item: any) => item.position === '书记'
              )?.userNames || ''
            }}</span>
          </div>
        </div>
        <div class="flex flex-row">
          <div class="w-[100px] text-right">
            <span>副书记：</span>
          </div>
          <div class="w-[220px]">
            <span>{{
              originalData?.branchMemberList?.find(
                (item: any) => item.position === '副书记'
              )?.userNames || ''
            }}</span>
          </div>
        </div>
      </div>

      <div class="flex flex-row py-[20px]">
        <div class="flex flex-row">
          <div class="w-[100px] text-right">
            <span>组织委员：</span>
          </div>
          <div class="w-[220px]">
            <span>{{
              originalData?.branchMemberList?.find(
                (item: any) => item.position === '组织委员'
              )?.userNames || ''
            }}</span>
          </div>
        </div>
        <div class="flex flex-row">
          <div class="w-[100px] text-right">
            <span>宣传委员：</span>
          </div>
          <div class="w-[220px]">
            <span>{{
              originalData?.branchMemberList?.find(
                (item: any) => item.position === '宣传委员'
              )?.userNames || ''
            }}</span>
          </div>
        </div>
        <div class="flex flex-row">
          <div class="w-[100px] text-right">
            <span>纪检委员：</span>
          </div>
          <div class="w-[220px]">
            <span>{{
              originalData?.branchMemberList?.find(
                (item: any) => item.position === '纪检委员'
              )?.userNames || ''
            }}</span>
          </div>
        </div>
      </div>
    </div>

    <div>
      <div><span class="font-bold">下设党小组数及小组长名单</span></div>
      <div>
        <n-data-table
          :columns="tableColumns"
          :data="tableData"
          class="mt-[20px]"
        />
      </div>
    </div>

    <div class="flex flex-row py-[20px]">
      <span class="font-bold">本届支部选举时间：</span>
      <n-date-picker
        v-if="isCanEdit"
        v-model:formatted-value="formDataRef.electionTime"
        clearable
        placeholder="请选择本届支部选举时间"
        style="width: 220px"
        type="date"
        value-format="yyyy-MM-dd"
      />
      <span v-else>{{ formDataRef.electionTime }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
