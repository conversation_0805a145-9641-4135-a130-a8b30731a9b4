<script lang="ts" setup>
import { DownloadRound } from '@vicons/material'
import { NButton, type TreeSelectOption } from 'naive-ui'
import { getRecordBookTableColumns } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/config'
import { useRecordBookStore } from '@/store/recordBook'
import { MEETING_TYPE } from '@/store/dict'
import { getMeetingRecordList } from '@/services/recordBookMaintenance'
import type { MeetingRecordType } from '@/services/recordBookMaintenance/types'

const recordBookStore = useRecordBookStore()
const router = useRouter()

const calcMeetingType = computed(() => {
  return MEETING_TYPE.filter(
    item => !['党课', '主题党日'].includes(item.label),
  )
})

const pageNum = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const total = ref(0)
const selectedDeptId = ref<string | null>(null)
const selectedYear = ref<string | null>(null)
const selectedMeetingType = ref<string | null>(null)
const startTimeSort = ref<'' | '0' | '1'>('')
const currentDownloadFileType = ref<'word' | 'pdf'>('word')
const showPopConfirm = ref<boolean>(false)

const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})

// 获取当前年份
const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

const calcOrganizationListTree = computed(() => {
  return recordBookStore.getOrganizationListTreeList
})

const tableColumns: any = getRecordBookTableColumns((row) => {
  return [
    h(
      'div',
      {
        class: 'flex flex-row gap-x-[10px]',
      },
      {
        default: () => [
          h(
            NButton,
            {
              text: true,
              color: '#AC241D',
              onClick: () => {
                router.push({
                  name: 'meetingManage',
                  query: {
                    id: String(row.id),
                    status: row.meetingStatus || '已结束',
                  },
                })
              },
            },
            {
              default: () => '查看',
            },
          ),
        ],
      },
    ),
  ]
})
const tableData = ref<MeetingRecordType[]>([])

const currentYearOptions = computed(() => {
  return recordBookStore.getYearOptions
})

function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  pageNum.value = 1
  loadData(v, selectedYear.value!)
  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  pageNum.value = 1
  loadData(selectedDeptId.value!, v)
  recordBookStore.setCurrentSelectedYear(option.value as string)
}

function handleUpdateMeetingTypeSelectedValue(v: string) {
  pageNum.value = 1
  selectedMeetingType.value = v
  recordBookStore.setCurrentSelectedMeetingType(v as string)
  loadData(selectedDeptId.value!, selectedYear.value!)
}

watch(
  () => pageNum.value,
  () => {
    loadData(selectedDeptId.value!, currentYear.value)
  },
)

watch(
  () => pageSize.value,
  () => {
    pageNum.value = 1
    loadData(selectedDeptId.value!, currentYear.value)
  },
)

function loadData(deptId: string, year: string) {
  if (!deptId || !year) {
    return
  }
  loading.value = true
  getMeetingRecordList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    deptId,
    // deptId: '1865009482200268801',
    year,
    meetingType: selectedMeetingType.value as string,
    startTimeSort: startTimeSort.value,
  })
    .then((res) => {
      tableData.value = res.records
      total.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}

function handleUpdateSorter({
  columnKey,
  order,
}: {
  columnKey: string
  order: 'ascend' | 'descend' | false
}) {
  if (columnKey === 'startTime') {
    if (order === 'descend') {
      startTimeSort.value = '0'
    }
    else if (order === 'ascend') {
      startTimeSort.value = '1'
    }
    else {
      startTimeSort.value = ''
    }
  }
  loadData(selectedDeptId.value!, selectedYear.value!)
}

function handleCancelShowPopConfirm() {
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleDownLoadFile() {
  recordBookStore.fetchDownLoadFile({
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
    startTimeSort: startTimeSort.value,
    meetingType: (selectedMeetingType.value as string) || '',
    type: currentDownloadFileType.value,
  })

  nextTick(() => {
    showPopConfirm.value = false
  })
}

onMounted(() => {
  selectedDeptId.value = currentSelectedDept.value.deptId
  selectedYear.value = currentYear.value
  selectedMeetingType.value = recordBookStore.getCurrentSelectedMeetingType
  loadData(currentSelectedDept.value.deptId, currentYear.value)
})
</script>

<template>
  <div>
    <table-container
      v-model:page="pageNum"
      v-model:page-size="pageSize"
      :loading="loading"
      :show-delete="false"
      :show-pagination="true"
      :show-toolbar="false"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      custom-toolbar
      default-expand-all
      title="会议记录"
      @update-sorter="handleUpdateSorter"
    >
      <template #filters>
        <div class="">
          <n-popconfirm
            :show="showPopConfirm"
            :show-icon="false"
            placement="left"
          >
            <template #trigger>
              <n-button @click="showPopConfirm = !showPopConfirm">
                <template #icon>
                  <n-icon size="16">
                    <DownloadRound />
                  </n-icon>
                </template>
                下载
              </n-button>
            </template>
            <div class="py-[10px]">
              <n-radio-group v-model:value="currentDownloadFileType">
                <n-radio
                  v-for="(fileType, fileTypeIndex) in [
                    { label: 'word', value: 'word' },
                    { label: 'pdf', value: 'pdf' },
                  ]"
                  :key="fileTypeIndex"
                  :value="fileType.value"
                >
                  {{ fileType.label }}
                </n-radio>
              </n-radio-group>
            </div>

            <template #action>
              <div class="w-[100%] flex flex-row justify-center items-center">
                <n-button
                  size="small"
                  type="primary"
                  @click="handleDownLoadFile(item)"
                >
                  确定
                </n-button>
                <n-button size="small" @click="handleCancelShowPopConfirm">
                  取消
                </n-button>
              </div>
            </template>
          </n-popconfirm>
        </div>
      </template>
      <template #btns>
        <div class="flex flex-row items-center justify-between gap-[10px]">
          <n-tree-select
            v-model:value="selectedDeptId"
            :options="calcOrganizationListTree"
            :show-path="false"
            check-strategy="all"
            children-field="children"
            filterable
            key-field="id"
            label-field="name"
            placeholder="请选择所属党组织"
            size="medium"
            style="width: 400px"
            value-field="id"
            @update:value="handleUpdateTreeSelectedValue"
          />
          <n-select
            v-model:value="selectedYear"
            :options="currentYearOptions"
            placeholder="请选择年份"
            size="medium"
            style="width: 200px"
            @update:value="handleUpdateYearSelectedValue"
          />
          <n-select
            v-model:value="selectedMeetingType"
            :options="calcMeetingType"
            clearable
            placeholder="请选择会议类型"
            size="medium"
            style="width: 200px"
            @update:value="handleUpdateMeetingTypeSelectedValue"
          />
        </div>
      </template>
    </table-container>
  </div>
</template>

<style lang="scss" scoped></style>
