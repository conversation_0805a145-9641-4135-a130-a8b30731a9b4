import type {
  CommonParamsOfCompanyAndRegulation,
  CompanySHowItem,
  InsertCommonParamsOfCompanyAndRegulation,
} from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 获取公司展示列表 */
export function getCompanyShowList(
  params: CommonParamsOfCompanyAndRegulation & PaginationReq,
) {
  return commonReq.get<PaginationRes<CompanySHowItem>>({
    url: '/propaganda/backend/v1/party/list',
    params,
  })
}
/** 获取公司展示列表元素单项 */
export function getCompanyShowListItem(params: { id: string; type: number }) {
  return commonReq.get<InsertCommonParamsOfCompanyAndRegulation>({
    url: '/propaganda/backend/v1/party',
    params,
  })
}

/** 新建公司展示单项 */
export function postInsertCompanyShowListItem(
  data: InsertCommonParamsOfCompanyAndRegulation,
) {
  return commonReq.post({
    url: '/propaganda/backend/v1/party',
    data,
  })
}

/** 编辑公司展示单项 */
export function putEditorCompanyShowListItem(
  data: InsertCommonParamsOfCompanyAndRegulation,
) {
  return commonReq.put<InsertCommonParamsOfCompanyAndRegulation>({
    url: '/propaganda/backend/v1/party',
    data,
  })
}

/** 更新置顶公司展示 */
export function putUpdateCompanyShowItemTop(params: { id: string }) {
  return commonReq.put({
    url: '/propaganda/backend/v1/party/top',
    params,
  })
}

/**
 * @delCompanyShowItem
 * type: 0：公司展示 1：法规制度 2：近期活动 3：先进党组织 4：优秀党员
 * 删除公司展示单项
 *  */
export function delCompanyShowItem(ids: string, type: number | undefined) {
  return commonReq.delete({
    url: '/propaganda/backend/v1/party',
    params: { ids, type },
  })
}
