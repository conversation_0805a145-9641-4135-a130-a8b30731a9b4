import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  title: [
    {
      required: true,
      message: '清单标题不能为空',
      trigger: 'input',
    },
  ],
  matter: [
    {
      required: true,
      message: '考核指标不能为空',
      trigger: 'input',
    },
  ],
  evaluationRequirements: [
    {
      required: true,
      message: '请输入计分标准',
      trigger: 'change',
    },
  ],
  evaluationMode: [
    {
      required: true,
      message: '请输入检查材料',
      trigger: 'change',
    },
  ],
  evaluationScore: [
    {
      required: true,
      validator(rule: any, value: any) {
        if (value !== null && value >= -10 && value <= 100) {
          return true
        }
        else {
          return new Error('请输入分值')
        }
      },
      trigger: 'change',
    },
  ],
}
