/*
 * @Description: 表格容器通用逻辑
 */
import { ref } from 'vue'

/**
 * 返回表格容器通用逻辑
 * @param loadFn 加载数据的函数
 * @param deleteFn 删除数据的函数
 * @returns
 */
function useTableContainer(
  loadFn: () => void,
  deleteFn?: (ids: string) => Promise<any>,
) {
  const idsChecked = ref<number[]>([]) // 选中的行ids
  const currentPageRef = ref(0) // 当前页
  const pageSizeRef = ref(0) // 页大小
  const totalRef = ref(0) // 总条数
  const loadingRef = ref(false) // 表格加载状态

  // 行选中
  function handleRowChecked(ids: number[]) {
    idsChecked.value = ids
  }

  // 分页变化
  function handlePageChange(currentPage: number, pageSize: number) {
    currentPageRef.value = currentPage
    pageSizeRef.value = pageSize

    loadFn()
  }

  // 排序变化
  function handleSorterChange() {
    // console.log(sorter)
  }

  // 点击批量删除
  function handleClickDelete() {
    if (!idsChecked.value.length) {
      window.$message.warning('请选择需要删除的记录')
      return
    }
    window.$dialog.warning({
      title: '提示',
      content: '确定删除吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        if (deleteFn) {
          deleteFn(idsChecked.value.join(',')).then(() => {
            window.$message.success('删除成功')
            loadFn()
          })
        }
      },
    })
  }

  return {
    idsChecked,
    currentPageRef,
    pageSizeRef,
    totalRef,
    loadingRef,

    handleRowChecked,
    handlePageChange,
    handleSorterChange,
    handleClickDelete,
  }
}

export default useTableContainer
