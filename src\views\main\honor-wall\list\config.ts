import type { VNodeChild } from 'vue'
import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import { NImage } from 'naive-ui'
import type { HonorWallTableItem } from '@/services/honor-wall/types'
import { HONORTYPE } from '@/constant'
import { HONOR_LEVEL, getDictLabelByValue } from '@/store/dict'

export function getTableColumns(
  operationRender: (row: HonorWallTableItem) => VNodeChild,
  honorType: string,
): TableColumns<HonorWallTableItem> {
  let columns: TableColumns<HonorWallTableItem> = []
  columns = columns.concat([
    {
      type: 'selection',
      align: 'center',
    },
    {
      title: '荣誉图',
      key: 'img',
      render: row =>
        h(NImage, {
          src: import.meta.env.VITE_API_BASE + row.pictureUrl,
          width: '62',
          style: { height: '40px' },
        }),
    },
    {
      title: '荣誉标题',
      key: 'title',
    },

  ])
  if (honorType === HONORTYPE.PERSONHONOR) {
    columns.push({
      title: '党员名称',
      key: 'serviceName',
    })
  }
  columns.push({
    title: '组织名称',
    key: honorType === HONORTYPE.ORGHONOR ? 'serviceName' : 'deptName',
  })
  columns = columns.concat([
    {
      title: '荣誉级别',
      key: 'level',
      render: (row) => {
        return getDictLabelByValue(HONOR_LEVEL, row.level)
      },
    },
    {
      title: '授予时间',
      key: 'grantTime',
      render: row => row.grantTime ?? '-',
    },
    {
      title: '更新时间',
      key: 'updateTime',
      render: row => row.updateTime ?? '-',
    },
    {
      title: '排序',
      key: 'sort',
    },
    {
      title: '操作',
      key: 'operation',
      width: '12%',
      render: operationRender,
    },
  ])
  return columns
}
