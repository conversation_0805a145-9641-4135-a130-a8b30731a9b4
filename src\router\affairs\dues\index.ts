import type { RouteRecordRaw } from 'vue-router'

const duesRoute: RouteRecordRaw = {
  path: 'dues',
  name: 'dues',
  meta: { title: '党费管理', icon: 'dues', category: 'affairs', order: 1 },
  component: () => import('@/views/main/affairs/dues/Index.vue'),
  redirect: { name: 'dues-record' },
  children: [
    {
      path: 'record',
      name: 'dues-record',
      meta: { title: '缴费记录', icon: 'pay-record' },
      component: () => import('@/views/main/affairs/dues/record/Index.vue'),
    },
    {
      path: 'statistics',
      name: 'dues-statistics',
      meta: { title: '党费统计', icon: 'statistics' },
      component: () => import('@/views/main/affairs/dues/statistics/Index.vue'),
    },
    {
      path: 'cardinality',
      name: 'dues-cardinality',
      meta: { title: '党费基数设定', icon: 'cardinality' },
      component: () =>
        import('@/views/main/affairs/dues/cardinality/Index.vue'),
    },
  ],
}
export default duesRoute
