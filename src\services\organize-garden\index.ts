import type {
  OrgStyleListItem,
  OrganizeBrandFormParams,
  OrganizeGardenListItem,
  OrganizeMemberListItem,
  OrganizeWorkLawFormParams,
} from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 获取当前组织 */
export function getCurrentOrganize() {
  return commonReq.get({
    url: '/org-construction/orgconstruction_change/cur_user_manage_depts_top',
  })
}

/** 通过id查询组织 */
export function getOrganizeById(deptId: string) {
  return commonReq.get({
    url: `/org-construction/orgconstruction_change/${deptId}`,
  })
}

/** 修改组织简介 */
export function putOrganizeIntroduce(data: {
  deptId: string
  summary: string
}) {
  return commonReq.put({
    url: '/org-construction/orgconstruction_change/update_summary',
    data,
  })
}

/** 获取党建品牌 */
export function getOrganizeBrand(orgId: string) {
  return commonReq.get<OrganizeBrandFormParams>({
    url: '/org-construction/backend/v1/party_building_brand/get_party_building_brand',
    params: {
      orgId,
    },
  })
}

/** 新增党建品牌 */
export function postOrganizeBrand(data: OrganizeBrandFormParams) {
  return commonReq.post({
    url: '/org-construction/backend/v1/party_building_brand',
    data,
  })
}

/** 修改党建品牌 */
export function putOrganizeBrand(data: OrganizeBrandFormParams) {
  return commonReq.put({
    url: '/org-construction/backend/v1/party_building_brand',
    data,
  })
}

/** 获取党组织工作法 */
export function getOrganizeMethod(orgId: string) {
  return commonReq.get<OrganizeWorkLawFormParams>({
    url: '/org-construction/backend/v1/work_law/get_work_law',
    params: {
      orgId,
    },
  })
}

/** 新增党组织工作法 */
export function postOrganizeMethod(data: OrganizeWorkLawFormParams) {
  return commonReq.post({
    url: '/org-construction/backend/v1/work_law',
    data,
  })
}

/** 修改党组织工作法 */
export function putOrganizeMethod(data: OrganizeWorkLawFormParams) {
  return commonReq.put({
    url: '/org-construction/backend/v1/work_law',
    data,
  })
}

/** 获取创新做法列表 */
export function getOrgStyleList(
  params: {
    title?: string
  } & PaginationReq,
) {
  return commonReq.get({
    url: '/org-construction/orgconstructionStyle/page',
    params,
  })
}

/** 新增创新做法 */
export function postOrgStyle(data: OrgStyleListItem) {
  return commonReq.post({
    url: '/org-construction/orgconstructionStyle',
    data,
  })
}

/** 编辑创新做法 */
export function putOrgStyle(data: OrgStyleListItem) {
  return commonReq.put({
    url: '/org-construction/orgconstructionStyle',
    data,
  })
}

/** 查看创新做法详情 */
export function getOrgStyleDetail(id: string) {
  return commonReq.get<OrgStyleListItem>({
    url: `/org-construction/orgconstructionStyle/${id}`,
  })
}

/** 删除创新做法  */
export function deleteOrgStyle(ids: string) {
  return commonReq.delete({
    url: '/org-construction/orgconstructionStyle',
    params: {
      ids,
    },
  })
}

/** 置顶创新做法 */
export function putOrgStyleTop(id: string) {
  return commonReq.put({
    url: `/org-construction/orgconstructionStyle/top/${id}`,
  })
}

/** 隐藏创新做法 */
export function postOrgStyleHidden(id: string) {
  return commonReq.post({
    url: `/org-construction/orgconstructionStyle/hidden/${id}`,
  })
}

/** 查询组织园地设置 */
export function getFunctionSettingList(deptId: string) {
  return commonReq.get({
    url: '/org-construction/backend/v1/garden_set/get_garden_set_list',
    params: {
      deptId,
    },
  })
}

/** 修改组织园地设置
 *
 *  以前的参数  id: string, isShow: number
 * */
export function postFunctionHidden(data: any) {
  return commonReq.put({
    url: '/org-construction/backend/v1/garden_set/update_garden_set',
    data,
  })
}

/** 查询组织星级列表 */
export function getOrganizeStarList(
  params: PaginationReq & {
    deptId: string
    year: number
  },
) {
  return commonReq.get<PaginationRes<OrganizeGardenListItem>>({
    url: '/party-affairs/backend/v1/star-dept-result',
    params,
  })
}

/** 保存党支部星级评定结果 */
export function postOrganizeStarResult(data: {
  id: string
  deptId: string
  year: number | null | string
  star: number | null
}) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/star-dept-result',
    data,
  })
}

/** 编辑党支部星级评定结果 */
export function putOrganizeStarResult(data: {
  id: string
  deptId: string
  year: number
  star: number
}) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/star-dept-result',
    data,
  })
}

/** 删除党支部星级评定结果 */
export function deleteOrganizeStarResult(id: string) {
  return commonReq.delete({
    url: `/party-affairs/backend/v1/star-dept-result/${id}`,
  })
}

/** 获取党支部星级评定详情 */
export function getOrganizeStarResultDetail(id: string) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/star-dept-result/get-one',
    params: {
      id,
    },
  })
}

/** 给支部评级 */
export function postOrganizeStar(data: {
  id: string
  star: number
  deptId: string
}) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/star-dept-result',
    data,
  })
}

/** 查询组织成员列表 */
export function getOrganizeMemberList(params: PaginationReq) {
  return commonReq.get<PaginationRes<OrganizeMemberListItem>>({
    url: '/party-affairs/backend/v1/party_activity/page',
    params,
  })
}

/** 获取书记项目列表 */
export function getProjectSecretaryList(
  params: {
    title?: string
  } & PaginationReq,
) {
  return commonReq.get({
    url: '/org-construction/orgconstructionStyle/secretary-project/page',
    params,
  })
}

/** 新增书记项目 */
export function postProjectSecretary(data: OrgStyleListItem) {
  return commonReq.post({
    url: '/org-construction/orgconstructionStyle/secretary-project',
    data,
  })
}

/** 编辑书记项目 */
export function putProjectSecretary(data: OrgStyleListItem) {
  return commonReq.put({
    url: '/org-construction/orgconstructionStyle/secretary-project',
    data,
  })
}

/** 查看书记项目详情 */
export function getProjectSecretaryDetail(id: string) {
  return commonReq.get<OrgStyleListItem>({
    url: `/org-construction/orgconstructionStyle/secretary-project/${id}`,
  })
}

/** 删除书记项目  */
export function deleteProjectSecretary(ids: string) {
  return commonReq.delete({
    url: '/org-construction/orgconstructionStyle/secretary-project',
    params: {
      ids,
    },
  })
}

/** 置顶书记项目 */
export function putProjectSecretaryTop(id: string) {
  return commonReq.put({
    url: `/org-construction/orgconstructionStyle/secretary-project/top/${id}`,
  })
}

/** 隐藏书记项目 */
export function postProjectSecretaryHidden(id: string) {
  return commonReq.post({
    url: `/org-construction/orgconstructionStyle/secretary-project/hidden/${id}`,
  })
}
/** 获取党建创新案例列表 */
export function getPbCaseList(
  params: {
    title?: string
  } & PaginationReq,
) {
  return commonReq.get({
    url: '/org-construction/orgconstructionStyle/innovation-case/page',
    params,
  })
}

/** 新增党建创新案例 */
export function postPbCase(data: OrgStyleListItem) {
  return commonReq.post({
    url: '/org-construction/orgconstructionStyle/innovation-case',
    data,
  })
}

/** 编辑党建创新案例 */
export function putPbCase(data: OrgStyleListItem) {
  return commonReq.put({
    url: '/org-construction/orgconstructionStyle/innovation-case',
    data,
  })
}

/** 查看党建创新案例详情 */
export function getPbCaseDetail(id: string) {
  return commonReq.get<OrgStyleListItem>({
    url: `/org-construction/orgconstructionStyle/innovation-case/${id}`,
  })
}

/** 删除党建创新案例  */
export function deletePbCase(ids: string) {
  return commonReq.delete({
    url: '/org-construction/orgconstructionStyle/innovation-case',
    params: {
      ids,
    },
  })
}

/** 置顶党建创新案例 */
export function putPbCaseTop(id: string) {
  return commonReq.put({
    url: `/org-construction/orgconstructionStyle/innovation-case/top/${id}`,
  })
}

/** 隐藏党建创新案例 */
export function postPbCaseHidden(id: string) {
  return commonReq.post({
    url: `/org-construction/orgconstructionStyle/innovation-case/hidden/${id}`,
  })
}
