<script setup lang="ts">
import { NButton } from 'naive-ui'
import { Flowchart24Regular } from '@vicons/fluent'
import { PlusRound } from '@vicons/material'
import { getTableColumns } from './config'
import DevelopForm from './cpn/DevelopForm.vue'
import DevDraw from './cpn/dev/DevDraw.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import CustomDialog from '@/components/CustomDialog.vue'
import {
  deletePartyMember,
  getPartyDevelopmentList,
  putCancelCurrentStage,
} from '@/services/structure/party-development'
import { DEVELOPMENT_STAGE } from '@/store/dict'
import partyDevelopmentIMG from '@/assets/image/partyDevelopmentImg.png'

const filterReactive = ref({
  name: null,
  sort: null,
})
// 有接口后添加：loading,tableData
const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getPartyDevelopmentList, filterReactive, {
  batchDeleteTable: false,
  delApi: deletePartyMember,
})

const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('入党申请', handelConfirmEdit)

// 新增/编辑党建清单抽屉
const idEditRef = ref()
const addPartyMemberFormRef = ref()
/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addPartyMemberFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addPartyMemberFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

// 发展
const devFormRef = ref()
const stageRef = ref()
const showDevRef = ref(false)

const activeTab = ref()
const currentTab = ref()
const handleCurrentTab = (v: string) => {
  currentTab.value = v
  activeTab.value = v
}
const handelChangeTab = (v: string) => {
  activeTab.value = v
}

// const activeAndCurrent = computed(() => activeTab.value === currentTab.value)

// 点击保存按钮
function handleDevClickConfirm() {
  devFormRef.value?.validateAndSave()
}

// 保存成功
function handleDevSaved() {
  showDevRef.value = false
  loadData()
}

// 点击取消按钮
function handleDevClickCancel() {
  showDevRef.value = false
}

/** 撤销此阶段 */
function handleCancelCurrentStage(id: string) {
  window.$dialog.warning({
    title: '提示',
    content: '该操作无法撤回，确认撤销该发展阶段？',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      const params = {
        id,
      }
      putCancelCurrentStage!(params)
        .then(() => {
          window.$message.success('撤销成功')
        })
        .finally(loadData)
    },
  })
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          idEditRef.value = row.id
          stageRef.value = row.sort
          showDevRef.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '发展',
      },
    ),
    Number(row.sort) === 0
      && h(DeleteButton, {
        style: {
          marginRight: '10px',
        },
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    Number(row.sort) !== 0
      ? h(
        NButton,
        {
          onClick: () => handleCancelCurrentStage(String(row.id)),
          type: 'primary',
          text: true,
        },
        {
          default: () => '撤销此阶段',
        },
      )
      : '',
  ]
})

// 流程查看
const showFlow = ref(false)
const handelImportFn = () => {
  showFlow.value = true
}

onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :loading="loading"
    title="党员发展"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <!-- <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button> -->
      <n-button size="small" @click="handelImportFn">
        <template #icon>
          <n-icon>
            <Flowchart24Regular />
          </n-icon>
        </template>
        流程查看
      </n-button>
    </template>
    <template #filters>
      <n-select
        v-model:value="filterReactive.sort"
        style="width: 200px"
        placeholder="请选择发展阶段"
        size="small"
        :options="DEVELOPMENT_STAGE"
        clearable
      />
      <n-input
        v-model:value="filterReactive.name"
        style="width: 200px"
        size="small"
        placeholder="请输入搜索内容"
        clearable
      />
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <develop-form
        :id="idEditRef"
        ref="addPartyMemberFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>

  <n-drawer v-model:show="showDevRef" :width="860" :mask-closable="false">
    <n-drawer-content title="发展党员" closable>
      <dev-draw
        :id="idEditRef"
        ref="devFormRef"
        :current-stage="stageRef"
        @saved="handleDevSaved"
        @tab-change="handelChangeTab"
        @current-tab="handleCurrentTab"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleDevClickConfirm"
          >
            保存
            <!-- {{ activeTab === '预备党员转正' ? '保存' : '保存并进入下一阶段' }} -->
          </n-button>

          <!-- <n-button
            v-else
            type="primary"
            style="width: 80px"
            @click="handleDevClickConfirm"
          >
            保存信息
          </n-button> -->

          <n-button style="width: 80px" @click="handleDevClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>

  <custom-dialog
    v-model:show="showFlow"
    :show-action="false"
    width="1200px"
    title="流程查看"
  >
    <div class="p-[20px]">
      <n-image style="height: 617px" :src="partyDevelopmentIMG" />
    </div>
  </custom-dialog>
</template>
<style lang="scss" scoped></style>
