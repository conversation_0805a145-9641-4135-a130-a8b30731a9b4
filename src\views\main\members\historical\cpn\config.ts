import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  username: {
    required: true,
    validator(rule: any, value: any) {
      if (value === '' || !value) {
        return new Error('用户名不能为空')
      } else if (/^[A-Za-z0-9]+$/.test(value)) {
        return true
      } else {
        return new Error('用户名仅支持英文或数字')
      }
    },
    trigger: 'input',
  },
  trueName: {
    required: true,
    validator(rule: any, value: any) {
      if (value === '' || !value) {
        return new Error('姓名不能为空')
      } else if (/^[\u4E00-\u9FA5]+$/.test(value)) {
        return true
      } else {
        return new Error('姓名仅支持中文')
      }
    },
    trigger: 'input',
  },
  phone: {
    required: true,
    validator(rule: any, value: any) {
      if (value === '' || !value) {
        return new Error('手机号码不能为空')
      } else if (/^1(3|4|5|7|8|9)\d{9}$/.test(value)) {
        return true
      } else {
        return new Error('请输入正确的手机号码')
      }
    },
    trigger: 'input',
  },
  identityId: {
    required: true,
    validator(rule: any, value: any) {
      if (value === '' || !value) {
        return new Error('身份证号码不能为空')
      } else if (
        /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
          value,
        )
      ) {
        return true
      } else {
        return new Error('请输入正确的身份证号码')
      }
    },
    trigger: 'input',
  },
  sex: {
    required: true,
    message: '性别不能为空',
    trigger: 'change',
  },
  customEthnic: {
    required: true,
    message: '民族不能为空',
    trigger: 'change',
  },
  edu: {
    required: true,
    message: '学历不能为空',
    trigger: 'change',
  },
  jobTime: {
    required: true,
    message: '参加工作时间不能为空',
    trigger: 'change',
  },
  departmentId: {
    required: true,
    message: '部门不能为空',
    trigger: 'input',
  },
  political: {
    required: true,
    message: '政治面貌不能为空',
    trigger: 'change',
  },
  deptId: {
    required: true,
    message: '所属党组织不能为空',
    trigger: 'change',
  },
  partyIdentity: {
    required: true,
    message: '人员类别不能为空',
    trigger: 'change',
  },
  reasonType: {
    required: true,
    message: '转至历史原因类型不能为空',
    trigger: 'change',
    type: 'number',
  },
  reasonDescription: {
    required: true,
    message: '转至历史原因说明不能为空',
    trigger: 'input',
  },
}
