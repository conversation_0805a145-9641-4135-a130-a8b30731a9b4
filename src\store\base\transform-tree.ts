// import type { TreeOption } from 'naive-ui'
// import { defineStore } from 'pinia'
// const useTreeStore = defineStore('tree', {
//   state: () => ({
//     userAuthList: [] as TreeOption[],
//     allUserLabels: [] as string[],
//   }),

//   actions: {
//     async getUserAuthListAction() {
//       const res = await getUserAuthList()

//       // 将返回结果转为naive树结构
//       const { userLabels, treeOptions } = transformToTreeOptions(res)
//       this.allUserLabels = userLabels
//       this.userAuthList = treeOptions
//     },
//   },
// })

// function transformToTreeOptions(data: any[]) {
//   const userLabels: string[] = []
// }

// export default useTreeStore
