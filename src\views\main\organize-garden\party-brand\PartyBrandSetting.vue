<template>
  <div
    class="w-[800px] px-[20px] py-[20px] flex flex-col justify-start items-center"
  >
    <n-form
      ref="brandSettingFormRef"
      :model="formDataReactive"
      require-mark-placement="left"
      label-placement="left"
      label-width="160px"
    >
      <div v-for="(item, index) in formDataReactive.value" :key="index">
        <n-form-item :label="`第${numList[index]}模块分类名称：`" path="title">
          <n-input
            v-model:value="item.title"
            style="width: 580px"
            maxlength="50"
            show-count
            clearable
            rows="1"
            :placeholder="`请输入第${numList[index]}模块分类名称`"
          />
        </n-form-item>
        <n-form-item :label="`是否展示${numList[index]}级：`" path="onShow">
          <n-switch
            v-model:value="item.onShow"
            :checked-value="true"
            :unchecked-value="false"
          >
            <template #checked>
              是
            </template>
            <template #unchecked>
              否
            </template>
          </n-switch>
        </n-form-item>
        <n-form-item label="展示组织：" path="deptIdList">
          <n-tree-select
            v-model:value="item.deptIdList"
            :options="(treeData.value as any)"
            value-field="id"
            label-field="name"
            key-field="id"
            children-field="children"
            check-strategy="all"
            placeholder="请选择组织"
            :show-path="false"
            clearable
            multiple
            filterable
            style="width: 580px"
            @update:value="(v:any) => (item.deptIdList = v)"
          />
        </n-form-item>
      </div>
    </n-form>
    <div
      v-if="formDataReactive.value.length"
      class="flex justify-center items-center gap-[20px]"
    >
      <n-button size="large">
        取消
      </n-button>
      <n-button size="large" type="primary" @click="handleConfirm">
        保存
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getPartyBuildingBrand,
  getPartyBuildingBrandTreeList,
  putPartyBuildingBrandSetting,
} from '@/services/party-brand'
import type {
  PartyBrandSettingType,
  TreeDataType,
} from '@/services/party-brand/types'
const formDataReactive = reactive<{ value: PartyBrandSettingType[] }>({
  value: [],
})
const treeData = reactive<{ value: TreeDataType[] }>({ value: [] })
const numList = ref(['一', '二', '三'])

function getSettingDetailInfo() {
  getPartyBuildingBrand({ id: '' }).then((res) => {
    formDataReactive.value = res || []
  })
}

function getTreeData() {
  getPartyBuildingBrandTreeList().then((res) => {
    treeData.value = res || []
  })
}

// 保存
function handleConfirm() {
  putPartyBuildingBrandSetting(formDataReactive.value).then((res) => {
    window.$message.success('保存成功')
    getSettingDetailInfo()
    getTreeData()
  })
}

onMounted(() => {
  getSettingDetailInfo()
  getTreeData()
})
</script>

<style scoped lang="scss"></style>
