<script setup lang="ts">
import dayjs from 'dayjs'
import { newsDetailFormRules } from './config'
import { uploadImg } from '@/services'
import { getNewsDetail, postNews, putNews } from '@/services/news'
import type { AddOrEditNewListItem } from '@/services/news/types'
import type { uploadFileItem } from '@/services/types'
const route = useRoute()
const router = useRouter()
const newsDetailFormRef = ref()

const categoryId = ref((route.query.categoryId as string) || '')
const enterType = computed(() => route.query.model ?? null)
const title = ref()
if (enterType.value === 'modify') {
  title.value = '编辑资讯'
}
else if (enterType.value === 'add') {
  title.value = '添加资讯'
}
else {
  title.value = '查看资讯'
}

const formData = reactive<{ data: AddOrEditNewListItem }>({
  data: {
    title: '',
    shortTitle: '',
    author: '',
    publishTime: null,
    content: '',
    coverUrl: '',
    categoryId: categoryId.value,
    // isTop: 0,
    // isRecommend: 0,
    isHidden: 0,
    readNum: null,
    commentNum: 0,
    likeNum: 0,
    sort: 0,
    reviewed: 0,
    reviewedUser: '',
    reviewedTime: '',
    isOutside: '0',
    linkUrl: '',
  },
})

const newID = (route.query.id as string) || null
const getNewsDetailFn = async() => {
  const data = await getNewsDetail(newID as string)
  formData.data = data
}
onMounted(() => {
  if (newID) {
    getNewsDetailFn()
  }
})

/**
 * 上传图片
 * @param {any} file:File
 */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formData.data.coverUrl === '' || formData.data.coverUrl === null) {
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formData.data.coverUrl = data.url
      }
    }
  }
  catch (error) {}
}
/**
 * 删除图片
 */
function handleCoverDelete() {
  formData.data.coverUrl = ''
}

const isViewModel = computed(() => {
  if (enterType.value === 'view') {
    return true
  }
  else {
    return false
  }
})

// const viewImageSrc = computed(
//   () => import.meta.env.VITE_API_BASE + formData.data.coverUrl,
// )

async function publishConfirm() {
  newsDetailFormRef.value?.validate((errors: any) => {
    if (!errors) {
      formData.data.content = `<div style="font-size: 18px; font-family: '微软雅黑'; line-height: 2;">${formData.data.content}</div>`
      // 新增或编辑
      if (formData.data.id) {
        putNews({ ...formData.data }).then((res) => {
          window.$message.success('编辑成功')
          router.push({ name: 'newsListPage' })
        })
      }
      else {
        // 发布时间改为非必填项了，若没有填发布时间，则取发布时最新时间
        if (!formData.data.publishTime) {
          formData.data.publishTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
        }
        postNews({ ...formData.data }).then((res) => {
          window.$message.success('添加成功')
          router.push({ name: 'newsListPage' })
        })
      }
    }
  })
}
</script>
<template>
  <div>
    <DetailHeader
      :header-title="title"
      :is-show-confirm-btn="enterType === 'view' ? false : true"
      back-name="newsListPage"
      :release="publishConfirm"
      :need-show-dialog="isViewModel ? false : true"
    />
    <div class="pl-[200px] pt-[20px]">
      <n-form
        ref="newsDetailFormRef"
        :model="formData.data"
        :rules="newsDetailFormRules"
        require-mark-placement="left"
        label-placement="left"
        label-width="120px"
        :disabled="isViewModel"
      >
        <n-form-item label="新闻标题：" path="title">
          <n-input
            v-model:value="formData.data.title"
            style="width: 580px"
            maxlength="80"
            type="textarea"
            rows="2"
            show-count
            clearable
            placeholder="请输入新闻标题"
          />
        </n-form-item>

        <n-form-item label="短标题：">
          <n-input
            v-model:value="formData.data.shortTitle"
            style="width: 580px"
            placeholder="请输入短标题"
          />
        </n-form-item>
        <n-form-item label="作者：">
          <n-input
            v-model:value="formData.data.author"
            style="width: 580px"
            placeholder="请输入作者"
          />
        </n-form-item>

        <n-form-item label="发表时间：">
          <n-date-picker
            v-model:formatted-value="formData.data.publishTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            style="width: 580px"
            clearable
            placeholder="请选择发表时间"
            @clear="() => (formData.data.publishTime = null)"
          />
        </n-form-item>

        <n-form-item label="阅读数量：">
          <n-input-number
            v-model:value="formData.data.readNum"
            clearable
            :min="0"
            :precision="0"
            style="width: 580px"
            placeholder="请输入阅读数量"
          />
        </n-form-item>

        <n-form-item label="图片：">
          <ImgUploader
            v-model:oldImgUrl="formData.data.coverUrl"
            :width="220"
            :height="150"
            :need-cropper="false"
            :is-readonly="isViewModel"
            @done="handleCoverDone"
            @delete="handleCoverDelete"
          />
        </n-form-item>

        <!-- <n-form-item label="是否置顶：" path="isTop">
          <n-switch
            v-model:value="formData.data.isTop"
            :checked-value="1"
            :unchecked-value="0"
          />
        </n-form-item>
        <n-form-item label="是否推荐：" path="isRecommend">
          <n-switch
            v-model:value="formData.data.isRecommend"
            :checked-value="1"
            :unchecked-value="0"
          />
        </n-form-item> -->
        <n-form-item label="是否隐藏：" path="isHidden">
          <n-switch
            v-model:value="formData.data.isHidden"
            :checked-value="1"
            :unchecked-value="0"
          />
        </n-form-item>
        <n-form-item label="是否外链：" path="isTop">
          <n-switch
            v-model:value="formData.data.isOutside"
            checked-value="1"
            unchecked-value="0"
          />
        </n-form-item>
        <n-form-item
          v-if="formData.data.isOutside === '1'"
          label="链接地址："
          path="linkUrl"
        >
          <n-input
            v-model:value="formData.data.linkUrl"
            placeholder="请输入链接地址"
            style="width: 80%"
            clearable
          />
        </n-form-item>

        <n-form-item
          v-if="formData.data.isOutside === '0'"
          label="正文："
          path="content"
        >
          <RichEditor
            v-model:value="formData.data.content"
            :disabled="isViewModel"
          />
        </n-form-item>
      </n-form>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
