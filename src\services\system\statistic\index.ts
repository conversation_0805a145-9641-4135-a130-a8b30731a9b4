import type { LoginStatisticListItem } from './types'
import { commonReq, downloadReq } from '@/services/request'
import type { PaginationRes } from '@/services/types'

/** 获取登录统计列表 */
export function getLoginStatisticList(params: {
  userName: string
  phone: string
  deptId: string
  size: number
  current: number
}) {
  return commonReq.get<PaginationRes<LoginStatisticListItem>>({
    url: '/upms/backend/v1/login',
    params,
  })
}

/** 获取登录统计Tips */
export function getLoginStatisticTips(params: {
  userName: string
  phone: string
  deptId: string
}) {
  return commonReq.get({
    url: '/upms/backend/v1/login/statistics',
    params,
  })
}

/** 获取登录统计export */
export function exportLoginStatisticList(params: {
  userName: string
  phone: string
  deptId: string
}) {
  return downloadReq.get({
    url: '/upms/backend/v1/login/export',
    responseType: 'blob',
    params,
  })
}
