/*
 * @Description: 菜单编辑表单相关配置
 */

import type { FormRules } from 'naive-ui'
import { MENU_DICT, VISIBLE_DICT } from '../../config'
import type { IMenuForm } from '../../../../../../../services'

// 菜单规则
export const menuFormRules: FormRules = {
  type: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请选择菜单类型')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  name: { required: true, message: '请输入菜单名称', trigger: 'change' },
  icon: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请选择图标')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  path: { required: true, message: '请输入路由地址', trigger: 'change' },
  permission: { required: true, message: '请输入权限字符', trigger: 'change' },
  filePath: { required: true, message: '请输入文件路径', trigger: 'change' },
  routeName: { required: true, message: '请输入路由名称', trigger: 'change' },

  sortOrder: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请输入排序')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  parentId: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请选择上级菜单')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  visible: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请选择是否显示')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
}

// 目录规则
export const directoryRules: FormRules = {
  type: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请选择菜单类型')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  parentId: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请选择上级菜单')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  name: { required: true, message: '请输入菜单名称', trigger: 'change' },
  icon: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请选择图标')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  path: { required: true, message: '请输入路由地址', trigger: 'change' },
  sortOrder: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请输入排序')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  visible: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请选择是否显示')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
}

// 按钮规则
export const btnRules: FormRules = {
  type: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请选择菜单类型')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  parentId: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请选择上级菜单')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  name: { required: true, message: '请输入菜单名称', trigger: 'change' },
  permission: { required: true, message: '请输入权限字符', trigger: 'change' },

  sortOrder: {
    required: true,
    validator(rule: any, value: any) {
      if (value == null) {
        return new Error('请输入排序')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
}

export function getFormData(): IMenuForm {
  return {
    icon: '',
    name: '',
    parentId: null,
    path: '',
    sortOrder: null,
    type: MENU_DICT.MENU,
    visible: VISIBLE_DICT.SHOW,
    permission: '',
    filePath: '',
    routeName: '',
    redirect: '',
  }
}
