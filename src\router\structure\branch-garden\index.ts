import type { RouteRecordRaw } from 'vue-router'

const branchGardenRoute: RouteRecordRaw = {
  path: 'branch-garden',
  name: 'branchGarden',
  meta: { title: '支部园地', icon: 'life', category: 'structure', order: 1 },
  component: () => import('@/views/main/structure/branch-garden/index.vue'),
  redirect: { name: 'branchGardenIndex' },
  children: [
    {
      path: 'branch-garden-index',
      name: 'branchGardenIndex',
      meta: { title: '支部园地', icon: 'AppleOutlined' },
      component: () =>
        import('@/views/main/structure/branch-garden/branchGardenPage.vue'),
    },
    {
      path: 'add-branch-garden/:id',
      name: 'addBranchGardenIndex',
      meta: { title: '添加支部园地', icon: 'AppleOutlined' },
      component: () =>
        import(
          '@/views/main/structure/branch-garden/branch-show/BranchShow.vue'
        ),
      // component: () =>
      //   import('@/views/main/structure/branch-garden/addBranchGarden.vue'),
    },
    {
      path: 'my-branch-show',
      name: 'myBranchShow',
      meta: { title: '支部展示', icon: 'AppleOutlined' },
      component: () =>
        import(
          '@/views/main/structure/branch-garden/branch-show/BranchShow.vue'
        ),
    },
  ],
}
export default branchGardenRoute
