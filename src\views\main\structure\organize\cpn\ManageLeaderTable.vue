<template>
  <div class="w-[100%]">
    <div class="flex flex-row justify-between items-center pb-[10px]">
      <span class="text-[14px] font-bold">{{ title }}</span><n-button type="primary" text @click="handleAddManageLeader">
        {{ rightBtnText }}
      </n-button>
    </div>
    <div>
      <n-data-table
        :columns="calcColumnList"
        :data="manageLeaderTableDataCalc"
        :paginate-single-page="false"
      />
    </div>
  </div>
  <!-- 添加领导班子弹框 -->
  <n-modal
    :show="addManageLeaderVisible"
    :show-icon="false"
    preset="dialog"
    style="width: 592px"
    @close="handleClose"
  >
    <template #header>
      <slot name="header">
        <div>添加成员</div>
      </slot>
    </template>
    <div>
      <n-form
        ref="addManageLeaderRef"
        :model="addModel"
        :rules="addManageLeaderRules"
        label-width="100px"
        label-placement="left"
        require-mark-placement="left"
      >
        <n-form-item label="姓名：" path="userId">
          <n-select
            v-model:value="addModel.userId"
            filterable
            placeholder="搜索成员"
            :options="userList"
            clearable
            @update:value="handleUserSelectChange"
          />
        </n-form-item>
        <n-form-item label="所属党组织：" path="deptId">
          <n-tree-select
            v-model:value="addModel.deptId"
            :options="calcOrgList"
            value-field="deptId"
            label-field="name"
            key-field="deptId"
            children-field="children"
            check-strategy="all"
            placeholder="请选择所属党组织"
            :show-path="false"
            clearable
            filterable
            size="small"
            @update:value="(v:any) => addModel.deptId = v"
          />
        </n-form-item>
        <n-form-item label="职务名称：" path="position">
          <n-input
            v-model:value="addModel.position"
            clearable
            maxlength="30"
            show-count
            placeholder="请输入职务名称"
          />
        </n-form-item>
      </n-form>
    </div>
    <template #action>
      <slot name="action">
        <div
          class="w-[100%] flex flex-row justify-center items-center gap-x-[20px]"
        >
          <n-button
            type="primary"
            :loading="confirmLoading"
            @click="handleConfirm"
          >
            确定
          </n-button>
          <n-button class="cancel-btn" @click="handleCancel">
            取消
          </n-button>
        </div>
      </slot>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { NButton, NPopconfirm } from 'naive-ui'
import { cloneDeep } from 'lodash-es'
import { addManageLeaderRules, getManageLeaderTableColumns } from './config'
import type { ManageLeaderDto } from '@/services/organization/types'
import { deleteLeader } from '@/services/organization'

interface Props {
  title: string
  rightBtnText?: string
  tableColumns?: Array<any>
  orgList: Array<any>
  tableData: Array<any>
  total?: number
  currentOrgId: string
  currentPartyIdentity?: string
  partyIdentityTypeOptions?: Array<any>
  userList: Array<any>
}

const props = withDefaults(defineProps<Props>(), {
  title: '书记',
  rightBtnText: '添加成员',
  tableColumns: () => [],
  orgList: () => [],
  tableData: () => [],
  total: 0,
  currentOrgId: '',
  currentPartyIdentity: '',
  partyIdentityTypeOptions: () => [],
  userList: () => [],
})

const addManageLeaderRef = ref<any>(null)
const addManageLeaderVisible = ref<boolean>(false)
const addModel = ref<ManageLeaderDto>({
  userId: null,
  trueName: '',
  position: '',
  deptId: props.currentOrgId,
  deptName: '',
})
const confirmLoading = ref<boolean>(false)
const currentEditModel = ref<'add' | 'edit'>('add')
const currentEditIndex = ref<number | null>(null)
// 保存原始数据备份，用于取消时恢复
const originalTableData = ref<any[]>([])

// 添加成员中的 所属党组织 应包含当前选中的组织及以下组织是可以选中的
const calcOrgList = computed(() => {
  const cloneDeepOrgList = cloneDeep(props.orgList)

  // 递归函数，用于遍历并设置 disabled 属性
  const setDisabled = (
    orgList: any[],
    currentOrgId: string,
    parentDisabled: boolean = true,
  ) => {
    return orgList.map((org) => {
      if (!parentDisabled) {
        org.disabled = false
        if (org.children && org.children.length > 0) {
          org.children = setDisabled(org.children, currentOrgId, false)
        }
      }
      else if (org.deptId === currentOrgId) {
        // 如果当前节点的 deptId 等于 currentOrgId，设置 disabled 为 false
        org.disabled = false
        // 递归处理子节点
        if (org.children && org.children.length > 0) {
          org.children = setDisabled(org.children, currentOrgId, false)
        }
      }
      else {
        // 否则设置 disabled 为 true
        org.disabled = true
        // 递归处理子节点
        if (org.children && org.children.length > 0) {
          org.children = setDisabled(org.children, currentOrgId)
        }
      }
      return org
    })
  }

  // 调用递归函数处理 cloneDeepOrgList
  const result = setDisabled(cloneDeepOrgList, props.currentOrgId)

  return result
})

const manageLeaderTableData = ref(props.tableData || [])

watch(
  () => props.tableData,
  () => {
    manageLeaderTableData.value = props.tableData || []
  },
  { deep: true },
)
// 成员列表
const manageLeaderTableDataCalc = computed({
  get() {
    return manageLeaderTableData.value || []
  },
  set(val) {
    manageLeaderTableData.value = val || []
  },
})

const calcColumnList = computed(() => {
  return props.tableColumns.length
    ? props.tableColumns
    : getManageLeaderTableColumns((row, index) => {
      return [
        h(
          NButton,
          {
            type: 'primary',
            text: true,
            size: 'small',
            onClick: () => {
              currentEditModel.value = 'edit'
              currentEditIndex.value = index
              // 保存原始数据备份
              originalTableData.value = cloneDeep(manageLeaderTableData.value)
              addManageLeaderVisible.value = true
              addModel.value = cloneDeep(row as any)
            },
          },
          { default: () => '修改' },
        ),

        h(
          NPopconfirm,
          {
            onPositiveClick: () => {
              if (row.id) {
                // 调用接口进行删除
                handleDeleteManageLeader(row.id, index)
              }
              else {
                // 直接删除
                manageLeaderTableData.value.splice(index, 1)
                window.$message.success('删除成功')
              }
            },
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              h(
                NButton,
                {
                  type: 'primary',
                  text: true,
                  size: 'small',
                  style: {
                    marginLeft: '10px',
                  },
                  onClick: () => {
                    currentEditIndex.value = index
                  },
                },
                {
                  default: () => '删除',
                },
              ),
            default: () => '你确定要删除吗？',
          },
        ),
      ]
    }, props.title === '党务工作者')
})

// 执行删除领导班子成员
function handleDeleteManageLeader(userId: string, index: number) {
  deleteLeader(userId).then((res) => {
    manageLeaderTableData.value.splice(index, 1)
    window.$message.success('删除成功')
  })
}

function handleAddManageLeader() {
  currentEditModel.value = 'add'
  // 保存原始数据备份
  originalTableData.value = cloneDeep(manageLeaderTableData.value)
  addManageLeaderVisible.value = true
  addModel.value.position = props.title // 职务名称 默认取 当前的title 即（书记，副书记，委员）
}
function handleClose() {
  // 恢复到原始数据状态
  manageLeaderTableData.value = cloneDeep(originalTableData.value)
  // 清空原始数据备份
  originalTableData.value = []
  addManageLeaderVisible.value = false
  resetForm()
}

// 添加成员弹窗点确定
function handleConfirm() {
  addManageLeaderRef.value?.validate((errors: boolean) => {
    if (!errors) {
      confirmLoading.value = true

      // 基于原始数据进行操作，而不是当前显示的数据
      const newData = cloneDeep(originalTableData.value)

      if (currentEditModel.value === 'add') {
        newData.push({
          ...addModel.value,
          updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        })
      }
      else {
        // 如果是编辑，替换掉原来的数据
        newData.splice(
          currentEditIndex.value as number,
          1,
          {
            ...addModel.value,
            updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          },
        )
      }

      // 确认后才更新表格数据
      manageLeaderTableData.value = newData

      confirmLoading.value = false
      addManageLeaderVisible.value = false
      // 清空原始数据备份
      originalTableData.value = []
      resetForm()
    }
  })
}
function handleCancel() {
  // 恢复到原始数据状态
  manageLeaderTableData.value = cloneDeep(originalTableData.value)
  // 清空原始数据备份
  originalTableData.value = []
  resetForm()
  addManageLeaderRef.value.restoreValidation()
  addManageLeaderVisible.value = false
}

function resetForm() {
  addModel.value = {
    userId: null,
    trueName: '',
    position: '',
    deptId: props.currentOrgId,
    deptName: '',
  }
}

// 执行选人更新操作
function handleUserSelectChange(val: any) {
  const findUserData = props.userList.find((item: any) => {
    return item.value === val
  })

  if (findUserData) {
    addModel.value.trueName = findUserData.trueName
    addModel.value.deptName = findUserData.deptName
    addModel.value.deptId = findUserData.deptId
    // addModel.value.position = findUserData.partyIdentityLabel
  }
}

// 获取当前的列表，想父组件发送列表
function getManageLeaderUserList() {
  return cloneDeep(manageLeaderTableData.value || [])
}

defineExpose({
  getManageLeaderUserList,
})
</script>

<style scoped lang="scss"></style>
