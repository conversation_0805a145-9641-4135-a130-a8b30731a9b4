<!--
 * @Description: 试卷选择组件
-->
<template>
  <div class="paper-selector">
    <n-popover :width="300" placement="bottom" trigger="click">
      <template #trigger>
        <n-button size="small" class="select-btn">
          点击选择
        </n-button>
      </template>

      <n-select
        v-model:value="paperIdsRef"
        multiple
        filterable
        :options="paperOptionsRef"
      />
    </n-popover>

    <template v-if="papersRef && papersRef.length">
      <div v-for="paper in papersRef" :key="paper.value" class="paper">
        <span class="paper-bg">试卷</span>
        <span
          class="paper-name"
        >{{ showPaperName(paper.label as string) }}</span>

        <n-button
          size="large"
          class="paper-remove"
          text
          @click="handleRemovePaper(Number(paper.value))"
        >
          <template #icon>
            <n-icon>
              <delete-outlined />
            </n-icon>
          </template>
        </n-button>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { onBeforeMount, ref, watch } from 'vue'
import type { SelectOption } from 'naive-ui'
import { NButton, NIcon, NPopover, NSelect } from 'naive-ui'
import { DeleteOutlined } from '@vicons/material'
import { getAllOpenedPapers } from '@/services/examination/index'

const props = defineProps({
  originalPaperIds: {
    type: Array as PropType<number[]>,
    default: () => [],
  },
})
const emits = defineEmits(['paper-change'])

const papersRef = ref<SelectOption[]>([])
const paperIdsRef = ref<any>()
const paperOptionsRef = ref<SelectOption[]>([])

onBeforeMount(() => {
  // 加载所有开放考试的试卷
  getAllOpenedPapers().then((res) => {
    paperOptionsRef.value = res.map(item => ({
      label: `${item.name} —— ${item.total}分`,
      value: item.id,
      score: item.total,
    }))
  })
})

function handleRemovePaper(value: number) {
  paperIdsRef.value = paperIdsRef.value?.filter((item: any) => item !== value)
}

// 展示试卷名，删除末尾的分数
function showPaperName(str: string) {
  const arr = str.split(' —— ')
  arr.pop()
  return arr.join(' —— ')
}

watch(paperIdsRef, (newV) => {
  let score // 试卷分数
  if (newV && newV.length) {
    papersRef.value = paperOptionsRef.value.filter((item) => {
      if (newV.includes(item.value)) {
        score = item.score
        return true
      } else {
        return false
      }
    })

    // 判断选项是否禁用
    paperOptionsRef.value.forEach((item) => {
      if (
        papersRef.value?.findIndex(paper => paper.score === item.score) < 0
      ) {
        item.disabled = true
      }
    })
  } else {
    papersRef.value = []
    paperOptionsRef.value.forEach((item) => {
      item.disabled = false
    })
  }

  // 将试卷idList和试卷分数传递出去
  emits('paper-change', newV, score)
})

watch(props, (newV) => {
  if (newV.originalPaperIds.length) {
    paperIdsRef.value = newV.originalPaperIds
  }
})

watch(paperOptionsRef, (newV) => {
  if (newV.length && props.originalPaperIds.length) {
    paperIdsRef.value = props.originalPaperIds
  }
})
</script>

<style lang="scss" scoped>
.paper-selector {
  .select-btn {
    width: 76px;
    height: 30px;
    background: linear-gradient(180deg, #ffffff 0%, #f5f5f5 100%);
    margin-bottom: 15px;
  }

  .paper {
    width: 435px;
    height: 61px;
    background: #ffffff;
    border: 1px solid #dcddde;

    &:nth-child(n + 3) {
      border-top: none;
    }

    display: flex;
    align-items: center;
    padding-left: 11px;
    position: relative;

    .paper-bg {
      width: 78px;
      height: 44px;
      background: #ee5757;
      opacity: 0.7;
      line-height: 44px;
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      color: #ffffff;
    }

    .paper-name {
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      margin-left: 10px;
    }

    .paper-remove {
      position: absolute;
      right: 20px;
    }
  }
}
</style>
