<script setup lang="ts">
import { NButton } from 'naive-ui'
import { getTableColumns } from './config'
import UnReviewedForm from './cpn/UnReviewedForm.vue'
import type { CultureShowListItem } from '@/services/run/interaction/types'
import {
  getCultureShowList,
  postInteractionSingleDataAudit,
} from '@/services/run/interaction'
import { useMyTable } from '@/hooks'
import Scan from '@/views/main/run/interaction/cpn/Scan.vue'

interface Props {
  secondMenuStatus: string
}
const props = defineProps<Props>()

const filterRef = ref({
  title: null,
  username: null,
  status: props.secondMenuStatus,
  type: 2,
})

const idRef = ref()
const showScanDialog = ref(false)
function handleScan(id: string) {
  idRef.value = id
  showScanDialog.value = true
}
const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getCultureShowList, filterRef, {
  batchDeleteTable: false,
})

const tableColumns = getTableColumns(
  // (row: CultureShowListItem) => {
  //   return h(NSwitch, {
  //     checkedValue: '1',
  //     uncheckedValue: '0',
  //     value: row.topStatus,
  //     onUpdateValue(v) {
  //       row.topStatus = v
  //       putInteractionSingleDataTop(row.id)
  //         .then((res) => {
  //           loadData()
  //         })
  //         .catch(() => {})
  //     },
  //   })
  // },
  (row: CultureShowListItem) => {
    return [
      h(
        NButton,
        {
          onClick: () => handleScan(row.id),
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '查看',
        },
      ),
      h(
        NButton,
        {
          onClick: () => handlePass(row.id),
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '通过',
        },
      ),
      h(
        NButton,
        {
          onClick: () => handleUnReviewed(row.id),
          type: 'primary',
          text: true,
        },
        {
          default: () => '驳回',
        },
      ),
    ]
  },
)

async function handlePass(id: string) {
  const params = {
    id,
    status: '1',
  }
  window.$dialog.info({
    title: '提示',
    content: '确认内容无误，审核通过？',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      loading.value = true
      postInteractionSingleDataAudit(params)
        .then(() => {
          window.$message.success('审核已通过')
        })
        .finally(() => {
          loading.value = false
          loadData()
        })
    },
  })
}

const unReviewedFormRef = ref()
const showUnReviewedDialog = ref(false)
function handleUnReviewed(id: string) {
  idRef.value = id
  showUnReviewedDialog.value = true
}

function handleUnReviewedReason() {
  showUnReviewedDialog.value = false
  loadData()
}

function handleConfirm() {
  unReviewedFormRef.value?.validateAndSave()
}

onMounted(() => {
  loadData()
})
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title=""
    :show-title="false"
    :loading="loading"
    :show-toolbar="true"
    :show-add="false"
    :show-delete="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #filters>
      <n-input
        v-model:value="filterRef.username"
        style="width: 200px; margin-right: 10px"
        size="small"
        placeholder="请输入姓名"
        clearable
      />
      <n-input
        v-model:value="filterRef.title"
        style="width: 200px"
        size="small"
        placeholder="请输入主题"
        clearable
      />
    </template>
  </table-container>
  <custom-dialog
    v-model:show="showScanDialog"
    :show-action="false"
    width="800px"
    title="查看"
  >
    <scan
      :id="idRef"
      :show-content="false"
      title-text="主题"
      description-text="学习心得"
    />
  </custom-dialog>

  <custom-dialog
    v-model:show="showUnReviewedDialog"
    title="驳回原因"
    @confirm="handleConfirm"
  >
    <un-reviewed-form
      :id="idRef"
      ref="unReviewedFormRef"
      @saved="handleUnReviewedReason"
    />
  </custom-dialog>
</template>
<style lang="scss" scoped></style>
