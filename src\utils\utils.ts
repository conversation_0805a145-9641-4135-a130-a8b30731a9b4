export function removeIdProperties<T>(obj: T) {
  if (obj instanceof Array) {
    obj.forEach((item) => {
      removeIdProperties(item)
    })
  }
  else if (typeof obj === 'object' && obj !== null) {
    for (const prop in obj) {
      if (prop === 'id') {
        delete obj[prop]
      }
      else if (typeof obj[prop] === 'object') {
        removeIdProperties(obj[prop])
      }
    }
  }
}

export function getUnitTreeNode(list: any[], uid: string) {
  for (let i = 0; i < list.length; i += 1) {
    if (list[i].id === uid) {
      return list[i]
    }
    else {
      if (list[i].children && list[i].children.length !== 0) {
        const res: any = getUnitTreeNode(list[i].children, uid)
        if (res) {
          return res
        }
      }
    }
  }
}

// 等于情况不算重合
export function checkOverlapIntervals(pairs: Array<Array<number>>) {
  pairs.sort((a, b) => a[0] - b[0])

  for (let i = 1; i < pairs.length; i++) {
    if (pairs[i][0] < pairs[i - 1][1]) {
      return true // 有重合区间
    }
  }

  return false // 没有重合区间
}

// 判断是不是一个合法的http链接或https链接
export function isHttpOrHttpsLink(inputString: string) {
  // 通用正则表达式匹配合法的链接
  // const pattern = /^(https?:\/\/)?([\da-zA-Z.-]+)\.([a-zA-Z]{2,})([\/\w .-]*)*\/?(\?[\/\w .-=&%]*)?(#[\w-]*)?$/

  // 修改后的正则表达式，仅检查字符串是否以http://或https://开头
  const pattern = /^(https?:\/\/)/

  // 测试输入字符串是否匹配该模式
  return pattern.test(inputString)
}

/**
 * 根据身份证号码判断性别
 * @param idCard 身份证号码，可以是15位或18位，允许空格和短横线
 * @returns 返回性别，'男'、'女'或'未知'
 */
export function getGenderFromIdCard(
  idCard: string | null | undefined,
): '男' | '女' | '未知' {
  // 参数校验
  if (!idCard || typeof idCard !== 'string') {
    return '未知'
  }

  // 去除可能的空格和特殊字符
  const cleanedIdCard = idCard.trim().replace(/[\s-]/g, '')

  // 验证身份证长度（15位或18位）和格式
  if (!/^\d{15}$|^\d{17}[\dXx]$/.test(cleanedIdCard)) {
    return '未知'
  }

  try {
    // 获取性别位数字
    let genderDigit: number
    if (cleanedIdCard.length === 15) {
      // 15位身份证：第15位是性别位
      genderDigit = parseInt(cleanedIdCard.charAt(14), 10)
    }
    else {
      // 18位身份证：第17位是性别位
      genderDigit = parseInt(cleanedIdCard.charAt(16), 10)
    }

    // 检查数字是否有效
    if (isNaN(genderDigit)) {
      return '未知'
    }

    // 判断性别（奇数男，偶数女）
    return genderDigit % 2 === 1 ? '男' : '女'
  }
  catch (error) {
    console.error('身份证解析错误:', error)
    return '未知'
  }
}

export function numberToChinese(num: number): string {
  if (isNaN(num) || num < 0 || num > 9999) {
    return '不支持的数字'
  }

  if (num === 0) {
    return '零'
  }

  const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const units = ['', '十', '百', '千']

  let result = ''
  const numStr = num.toString()
  const length = numStr.length

  for (let i = 0; i < length; i++) {
    const digit = parseInt(numStr[i])
    const unit = units[length - i - 1]

    if (digit === 0) {
      // 避免重复的零，如 1001 -> 一千零一，而不是一千零零一
      if (result[result.length - 1] !== '零') {
        result += '零'
      }
    }
    else {
      // 处理十位数的特殊情况，如 12 -> 十二，不是一十二
      if (!(i === 0 && digit === 1 && unit === '十' && length > 1)) {
        result += digits[digit]
      }
      result += unit
    }
  }

  // 清理末尾的零
  result = result.replace(/零+$/, '')

  // 处理全零的情况
  if (result === '') {
    return '零'
  }

  return result
}
