import type { VNodeChild } from 'vue'
import { type DataTableColumns } from 'naive-ui'
import { type PaymentRecord } from '@/services/publicity/partypayment/paymentrecords/type'

/** 缴费记录表格 */
export function columnsOfPayment(
  optionColumnRenderer: (row: PaymentRecord) => VNodeChild,
): DataTableColumns<PaymentRecord> {
  return [
    {
      type: 'selection',
    },
    {
      key: 'index',
      title: '序号',
      width: '50',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'orderId',
      title: '订单号',
    },
    {
      key: 'name',
      title: '姓名',
    },
    {
      key: 'phone',
      title: '手机号码',
    },
    {
      key: 'orgName',
      title: '所在组织',
    },
    {
      key: 'time',
      title: '月份',
    },
    {
      key: 'money',
      title: '应缴金额（元）',
    },
    {
      key: 'status',
      title: '缴纳状态',
    },
    {
      key: 'payway',
      title: '支付方式',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}

/** 缴费记录表格 */
export function columnsOfPaymentStatistics(
  optionColumnRenderer: (row: PaymentRecord) => VNodeChild,
): DataTableColumns<PaymentRecord> {
  return [
    {
      type: 'selection',
    },
    {
      key: 'index',
      title: '序号',
      width: '50',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'orderId',
      title: '订单号',
    },
    {
      key: 'name',
      title: '姓名',
    },
    {
      key: 'phone',
      title: '手机号码',
    },
    {
      key: 'orgName',
      title: '所在组织',
    },
    {
      key: 'time',
      title: '月份',
    },
    {
      key: 'money',
      title: '应缴金额（元）',
    },
    {
      key: 'status',
      title: '缴纳状态',
    },
    {
      key: 'payway',
      title: '支付方式',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}

/** 缴费记录表格 */
export function columnsOfPaymentBaseSetting(
  optionColumnRenderer: (row: PaymentRecord) => VNodeChild,
): DataTableColumns<PaymentRecord> {
  return [
    {
      type: 'selection',
    },
    {
      key: 'index',
      title: '序号',
      width: '50',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'orderId',
      title: '订单号',
    },
    {
      key: 'name',
      title: '姓名',
    },
    {
      key: 'phone',
      title: '手机号码',
    },
    {
      key: 'orgName',
      title: '所在组织',
    },
    {
      key: 'time',
      title: '月份',
    },
    {
      key: 'money',
      title: '应缴金额（元）',
    },
    {
      key: 'status',
      title: '缴纳状态',
    },
    {
      key: 'payway',
      title: '支付方式',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
