import type { RouteRecordRaw } from 'vue-router'

const noticeRoute: RouteRecordRaw = {
  path: 'notice',
  name: 'news',
  meta: { title: '资讯管理', icon: 'notice', category: 'run', order: 1 },
  redirect: { name: 'noticeList' },
  component: () => import('@/views/main/run/notice/Index.vue'),
  children: [
    {
      path: 'list',
      name: 'noticeList',
      component: () => import('@/views/main/run/notice/cpns/Notice.vue'),
    },
    {
      path: 'add',
      name: 'noticeAdd',
      component: () => import('@/views/main/run/notice/cpns/Detail.vue'),
    },
  ],
}
export default noticeRoute
