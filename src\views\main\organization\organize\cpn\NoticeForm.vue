<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { formRules } from './config'
import type { addAndEditParams } from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
import RichEditor from '@/components/RichEditor.vue'
import {
  getViewPartyBuildingExamIndIcatorsItem,
  postPartyBuildingExamAdd,
  putEditExamIndIcatorsIdsItem,
} from '@/services/affairs/discipline-inspection-list/exam-indicators'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<addAndEditParams>({
  id: '',
  title: '',
  evaluationRequirements: '',
  evaluationMode: '',
  evaluationScore: 0,
  matter: '',
  fileIds: [],
  fileList: [],
})

const formRef = ref<InstanceType<typeof NForm>>()
const fileIdObj = reactive<{ fileIdsArr: Array<string> }>({ fileIdsArr: [] })

onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getViewPartyBuildingExamIndIcatorsItem(props.id).then((res) => {
      formDataReactive.evaluationMode = res.evaluationMode
      formDataReactive.evaluationRequirements = res.evaluationRequirements
      formDataReactive.evaluationScore = res.evaluationScore
      formDataReactive.fileIds = res.fileList?.map(item => item.id) || []
      formDataReactive.fileList = res.fileList || []
      formDataReactive.matter = res.matter
      formDataReactive.title = res.title
      formDataReactive.id = props.id
      formDataReactive.relatedStatus = res.relatedStatus
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (formDataReactive.id) {
        putEditExamIndIcatorsIdsItem(formDataReactive).then((res) => {
          if (res) {
            fileIdObj.fileIdsArr = []
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      } else {
        postPartyBuildingExamAdd(formDataReactive).then((res) => {
          if (res) {
            fileIdObj.fileIdsArr = []
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :disabled="formDataReactive.relatedStatus"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item v-if="type === 'modify'" label="上级组织：" path="title">
      <n-input
        v-model:value="formDataReactive.title"
        placeholder="请输入上级组织"
        disabled
        clearable
      />
    </n-form-item>
    <n-form-item label="党组织名称：" path="matter">
      <n-input
        v-model:value="formDataReactive.matter"
        placeholder="请输入党组织名称"
        clearable
      />
    </n-form-item>

    <n-form-item label="党组织简称：">
      <n-input
        v-model:value="formDataReactive.matter"
        placeholder="请输入党组织简称"
        clearable
      />
    </n-form-item>

    <n-form-item label="党组织类别：" path="matter">
      <n-select
        v-model:value="formDataReactive.matter"
        placeholder="请选择党组织类别"
        clearable
      />
    </n-form-item>

    <n-form-item label="组织编号：">
      <n-input
        v-model:value="formDataReactive.matter"
        placeholder="请输入组织编号"
        clearable
      />
    </n-form-item>

    <n-form-item span="24" label="描述说明：">
      <RichEditor
        v-model:value="formDataReactive.evaluationMode"
        style="width: 100%"
        :rich-height="350"
        :disabled="formDataReactive.relatedStatus"
      />
    </n-form-item>
    <n-form-item span="24" label="排序：" path="evaluationScore">
      <n-input-number
        v-model:value="formDataReactive.evaluationScore"
        :min="-10"
        :show-button="true"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
