{"name": "xfh-admin", "version": "1.0.0", "description": "智慧党建云平台", "private": true, "scripts": {"dev": "cross-env SYSTEMROOT='C:\\Windows' vite --open", "build": "vite build", "preview": "vite preview", "prepare": "husky install", "prettier": "prettier --write .", "lint": "eslint .", "lint:fix": "eslint --fix .", "commit": "cz"}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["prettier --write", "eslint --fix"]}, "dependencies": {"@easestrategy/ease-request": "^1.1.0", "@tinymce/tinymce-vue": "^4.0.5", "@vicons/carbon": "^0.12.0", "@vicons/fa": "^0.12.0", "@vicons/fluent": "^0.12.0", "@vicons/ionicons4": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "@vicons/material": "^0.12.0", "@vueuse/core": "^9.4.0", "crypto-js": "^4.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.6", "html2canvas": "^1.4.1", "jsencrypt": "3.3.1", "json-bigint": "^1.0.0", "jspdf": "^2.5.1", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "qs": "^6.11.1", "tinymce": "http://************/repository/npm-group/tinymce/-/tinymce-5.10.2.tgz", "vue": "^3.2.45", "vue-router": "^4.1.6", "vue3-video-play": "1.3.1-beta.6"}, "devDependencies": {"@commitlint/cli": "^16.2.1", "@commitlint/config-conventional": "^16.2.1", "@easestrategy/eslint-config-vue3": "~2", "@types/crypto-js": "^4.1.1", "@types/json-bigint": "^1.0.4", "@types/lodash-es": "^4.17.7", "@types/node": "^17.0.21", "@types/qs": "^6.9.7", "@vitejs/plugin-vue": "^4.6.2", "autoprefixer": "^10.4.12", "buffer": "^6.0.3", "commitizen": "^4.2.4", "cropperjs": "^1.5.12", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.11.0", "husky": "^7.0.4", "lint-staged": "^12.4.1", "naive-ui": "^2.42.0", "postcss": "^8.4.18", "prettier": "^2.6.0", "sass": "^1.55.0", "tailwindcss": "^3.2.1", "typescript": "^5.8.3", "unplugin-auto-import": "^0.11.4", "unplugin-vue-components": "^0.22.9", "vite": "^4.3.9", "vite-plugin-html": "^3.2.0", "vite-svg-loader": "^3.6.0", "vue-cropper": "^1.0.5"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "eslintConfig": {"extends": ["@easestrategy/vue3"], "ignorePatterns": ["public", "dist"]}}