import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { LoginStatisticListItem } from '@/services/system/statistic/types'

export function getTableColumns(): TableColumns<LoginStatisticListItem> {
  return [
    {
      key: 'index',
      title: '序号',
      render: (_, i) => i + 1,
    },
    {
      title: '所属组织',
      key: 'orgName',
    },
    {
      title: '姓名',
      key: 'userName',
    },
    {
      title: '手机号',
      key: 'phone',
    },
    {
      title: '是否登录',
      key: 'isLogin',
    },
    {
      title: '登录次数',
      key: 'loginCount',
    },
  ]
}
