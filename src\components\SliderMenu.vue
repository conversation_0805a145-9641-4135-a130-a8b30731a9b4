<script setup lang="ts">
import { useRoute } from 'vue-router'
const route = useRoute()
const parentRouter = route.matched[route.matched.length - 2]

console.log('parentRouter.children: ', parentRouter.children)
parentRouter.children = (parentRouter.children ?? []).filter(
  item => item.type !== '3' && item.visible !== '1',
)
</script>
<template>
  <layout-container :side-width="200" :show-collapse-trigger="false">
    <template #side>
      <side-menu-routes :menu-route="parentRouter" :has-children="true" />
    </template>
    <template #main>
      <router-view />
    </template>
  </layout-container>
</template>
<style lang="scss" scoped></style>
