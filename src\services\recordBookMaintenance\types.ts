export interface CatalogType {
  title: string
  sort: number
  value: string | null
  type?: string
  fileName?: string
  showPopconfirm?: boolean
}

export interface MeetingRecordType {
  id: string
  title: string
  orgId: string
  org?: any
  startTime: string
  endTime: string
  hostId: string
  host: string
  hostPartyIdentity?: any
  hostAvatar?: any
  meetingType: string
  meetingTypeName: string
  meetingAddr: string
  userId: string
  user: string
  userPartyIdentity?: any
  userAvatar?: any
  meetingStatus?: any
  userStatus?: any
  leaveReason?: any
  description: string
  requirement?: any
  summary: boolean
  createBy: string
  content?: any
  expectedNum: number
  attendanceNum: number
}

export interface OrganizationalTransferType {
  id: string
  userName: string
  transferType: string
  oldDeptName?: any
  newDeptName: string
  reason?: any
  phaseStatus: string
  remark?: any
  joinTime?: any
  paymentTime?: any
  letterId?: any
  letterName?: any
  proveId?: any
  proveName?: any
  transferTime: string
  continueApprovalStatus?: any
  approvalId?: any
  fileList?: any
  approvalNodes?: any
}

export interface PartyMemberType {
  id?: any
  trueName: string
  sex: string | null
  birth: string | null
  edu: string | null
  joinTime: string | null
  political: string | null
  partyDuty: string | null
  remark?: string
  deptId: string
  year: string
  sort?: number
}

export interface PartyMembersAttendLecturesType {
  id: string
  title: string
  host: string
  startTime: string
  endTime: string
  meetingAddr: string
  meetingStatus: string
  expectedNum: number
  attendanceNum: number
}

export interface ActivistsMemberRosterType {
  id: string
  memberId: string
  developId: string
  trueName: string
  sex: string
  birth: string
  edu: string
  dutyName?: any
  contact?: any
  confirmTime: string
  remark?: any
}

export interface BranchMemberType {
  id?: any
  position: string
  userIdList?: any
  userNames?: any
}

export interface GroupMemberType {
  id?: any
  userId?: any
  groupDeptId: string
  groupDeptName: string
  userName?: any
}

export interface BranchBasicOrganizationConditionType {
  id?: string
  branchMemberList: BranchMemberType[]
  groupMemberList: GroupMemberType[]
  electionTime?: string | null
  deptId?: string
  year?: string
}

export interface BranchBasicOrganizationConditionUserType {
  userId: string
  deptId: string
  trueName: string
  partyIdentity: string
  deptName?: any
}

export interface PartyMemberMeetingMinutesItemType {
  id?: string
  title: string
  startTime: string | null
  endTime: string | null
  hostId: string | null
  host?: string
  attendanceNum?: number | null
  mainSuggestion: string
}

export interface PartySituationReport {
  id?: string
  startTime: string | null
  endTime: string | null
  hostId: string | null
  host?: string
  attendanceNum: number | null
  mainContent: string
}

export interface ImportantMattersRecordType {
  id?: string
  startTime: string | null
  endTime: string | null
  hostId: string | null
  host?: string
  attendanceNum: number | null
  mainContent: string
}

// 党员积极分子活动情况
export interface PartyMemberActivityType {
  id?: string
  date: string | null
  organizerId: string | null
  organizer?: string
  content: string
}

export interface PartyBranchActivityType {
  id?: string | null
  startTime?: string | null
  endTime?: string | null
  location?: string | null
  attendanceNum?: number | null
  content?: string
  absenteeList: Array<AbsenteeList>
}

export interface AbsenteeList {
  userId?: string
  absentee?: string
  reason?: string
}

/** 主题党日活动情况登记列表项 */
export interface PartyDayActivityType {
  id: string
  title: string
  startTime: string
  endTime: string
  hostId: string
  host: string
  hostPartyIdentity?: any
  hostAvatar?: any
  meetingType: string
  meetingTypeName: string
  meetingAddr: string
  userId: string
  user: string
  userPartyIdentity?: any
  userAvatar?: any
  meetingStatus?: any
}

/** 谈心谈话记录列表项 */
export interface HeartTalkRecordType {
  /** 结束时间 */
  endTime?: null | string
  /** 主键id */
  id?: number | null
  /** 谈话对象 */
  intervieweeList?: TalkRecordIntervieweeVO[] | null
  /** 地点 */
  location?: null | string
  /** 主要意见建议 */
  mainSuggestion?: null | string
  /** 开始时间 */
  startTime?: null | string
  /** 谈话人 */
  talker?: null | string
  /** 谈话人ID */
  talkerId?: number | null

  [property: string]: any
}

export interface TalkRecordIntervieweeVO {
  /** 谈话对象 */
  interviewee?: null | string
  /** 谈话对象ID */
  userId?: number | null

  [property: string]: any
}

/** 新增谈心谈话记录参数 */
export interface AddHeartTalkRecordItem {
  /** 组织id */
  deptId: string
  /** 结束时间 */
  endTime: null | string
  /** 主键id */
  id?: string | number | null
  /** 谈话对象 */
  intervieweeIdList: number[] | null
  /** 地点 */
  location: null | string
  /** 主要意见建议 */
  mainSuggestion: null | string
  /** 开始时间 */
  startTime: null | string
  /** 谈话人ID */
  talkerId: string | number | null
  /** 年份 */
  year: string
  /** 创建时间 */
  timeRange: string[] | null

  [property: string]: any
}

/** 奖励情况列表项 */
export interface RewardStatusItemType {
  /** 时间 */
  awardDate?: string
  /** 奖励名称 */
  awardName?: string
  /** 简要事迹 */
  awardStory?: string
  /** 0-正常，1-删除 */
  delFlag?: string
  /** 支部id */
  deptId?: number
  /** 主键id */
  id?: number
  /** 用户id */
  userId?: number
  /** 姓名 */
  userName?: string
  /** 年份 */
  year?: string

  [property: string]: any
}

/** 处置情况列表项 */
export interface HandleStatusItemType {
  /** 时间 */
  decisionDate?: string
  /** 0-正常，1-删除 */
  delFlag?: string
  /** 支部id */
  deptId?: number
  /** 处分原因 */
  disciplineReason?: string
  /** 处分类别 */
  disciplineType?: string
  /** 主键id */
  id?: number
  /** 用户id */
  userId?: number
  /** 姓名 */
  userName?: string
  /** 年份 */
  year?: string

  [property: string]: any
}

/** 新增奖励情况参数 */
export interface AddRewardItem {
  /** 时间 */
  awardDate?: string
  /** 奖励名称 */
  awardName?: string
  /** 简要事迹 */
  awardStory?: string
  /** 0-正常，1-删除 */
  delFlag?: string
  /** 支部id */
  deptId: string
  /** 主键id */
  id?: string
  /** 用户id */
  userId?: string | null | undefined
  /** 姓名 */
  userName?: string | null | undefined
  /** 年份 */
  year: string

  [property: string]: any
}

/** 新增处置情况参数 */
export interface AddHandleItem {
  /** 时间 */
  decisionDate?: string
  /** 0-正常，1-删除 */
  delFlag?: string
  /** 支部id */
  deptId: string
  /** 处分原因 */
  disciplineReason?: string
  /** 处分类别 */
  disciplineType?: string
  /** 主键id */
  id?: string
  /** 用户id */
  userId?: string
  /** 姓名 */
  userName?: string
  /** 年份 */
  year: string

  [property: string]: any
}

export interface DemocraticEvaluationType {
  id?: string | null
  time?: string | null
  location?: string | null
  totalNum: number | null
  excellentNum: number | null
  qualifiedNum: number | null
  baseQualifiedNum: number | null
  unqualifiedNum: number | null
  userEvaluationList: Array<UserEvaluationList>
}

export interface UserEvaluationList {
  id?: string
  userId: string
  trueName: string
  evaluationLevel: string
  reviewItemId: string
  reviewId: string
}

export { RequestTypes as TrainingRequestTypes } from '../training-for-party-secretary/types'
