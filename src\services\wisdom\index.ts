import { commonReq } from '../request'
import type { PaginationReq2, PaginationRes } from '../types'
import type {
  AddOrEditNewListItem,
  AddOrEditNewsCategory,
  CarousePoolType,
  MoveNewsCategory,
  NewsCategoryList,
} from './types'

/** 获取智云课堂分类列表及其子类 */
export function getNewsCategoryList() {
  return commonReq.get<NewsCategoryList[]>({
    url: '/sys-operation/backend/v1/zhiyun/category',
  })
}

/** 新增智云课堂分类 */
export function postNewsCategory(data: AddOrEditNewsCategory) {
  return commonReq.post({
    url: '/sys-operation/backend/v1/zhiyun/category',
    data,
  })
}

/** 编辑智云课堂分类 */
export function putNewsCategory(data: AddOrEditNewsCategory) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/zhiyun/category',
    data,
  })
}

/** 移动智云课堂分类 */
export function moveNewsCategory(data: MoveNewsCategory) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/zhiyun/category/move',
    data,
  })
}

/** 删除智云课堂分类 */
export function deleteNewsCategory(id: string) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/zhiyun/category',
    params: {
      id,
    },
  })
}

/** 校验智云课堂类别下是否有挂载资讯 */
export function checkCategoryHaveNews(id: string) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/zhiyun/category/try-delete',
    params: {
      id,
    },
  })
}

/** 获取智云课堂列表 */
export function getNewsList(
  params: {
    categoryId: string
    title?: string
  } & PaginationReq2,
) {
  return commonReq.get({
    url: '/sys-operation/backend/v1/zhiyun',
    params,
  })
}

/** 新增智云课堂 */
export function postNews(data: AddOrEditNewListItem) {
  return commonReq.post({
    url: '/sys-operation/backend/v1/zhiyun',
    data,
  })
}

/** 编辑智云课堂 */
export function putNews(data: AddOrEditNewListItem) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/zhiyun',
    data,
  })
}

/** 查看智云课堂详情 */
export function getNewsDetail(id: string) {
  return commonReq.get<AddOrEditNewListItem>({
    url: `/sys-operation/backend/v1/zhiyun/${id}`,
  })
}

/** 删除智云课堂  */
export function deleteNews(ids: string) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/zhiyun',
    params: {
      ids,
    },
  })
}

/** 置顶智云课堂 */
export function putNewsTop(id: string) {
  return commonReq.put({
    url: `/sys-operation/backend/v1/zhiyun/top/${id}`,
  })
}

/** 推荐智云课堂 */
export function putNewsRecommend(id: string) {
  return commonReq.put({
    url: `/sys-operation/backend/v1/zhiyun/recommend/${id}`,
  })
}

/** 隐藏智云课堂 */
export function postHideNews(id: string) {
  return commonReq.post({
    url: `/sys-operation/backend/v1/zhiyun/hidden/${id}`,
  })
}

/** 审核智云课堂 */
export function postNewsAudit(id: string, reviewed: string) {
  return commonReq.post({
    url: `/sys-operation/backend/v1/zhiyun/reviewed/${id}?reviewed=${reviewed}`,
  })
}

// 改造后新增的接口 是否加入轮播池
export function isJoinCarouselPool(params: { id: string; type: string }) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/zhiyun/enter_carousel_pool',
    params,
  })
}

// 获取轮播图列表

export function getCarouselPoolList(params: {
  pageSize: number
  pageNum: number
}) {
  return commonReq.get<PaginationRes<CarousePoolType>>({
    url: '/sys-operation/backend/v1/zhiyun_class_carousel_pool/list_carousel_pool',
    params,
  })
}

// 置顶轮播图
export function updateCarouselPoolTop(params: { id: string }) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/zhiyun_class_carousel_pool/top',
    params,
  })
}

// 删除轮播图
export function deleteCarouselPool(params: { id: string }) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/zhiyun_class_carousel_pool/remove',
    params,
  })
}

// 轮播图排序
export function sortCarouselPool(params: { id: string; sort?: number }) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/zhiyun_class_carousel_pool/sort',
    params,
  })
}

// 智云课堂列表排序
// 轮播图排序
export function sortZhiYunNewsList(params: { id: string; sort?: number }) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/zhiyun/sort',
    params,
  })
}
