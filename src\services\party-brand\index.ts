import type { PartyBrandSettingType, TreeDataType } from './types'
import { commonReq } from '@/services/request'
import type { PaginationRes } from '@/services/types'

/** 获取党建品牌 */
export function getPartyBuildingBrand(params: { id: string }) {
  return commonReq.get<PartyBrandSettingType[]>({
    url: '/org-construction/backend/v1/party_building_brand/config/list',
    params,
  })
}

/** 修改党建品牌设置 */
export function putPartyBuildingBrandSetting(data: any) {
  return commonReq.put<PaginationRes<any>>({
    url: '/org-construction/backend/v1/party_building_brand/config',
    data,
  })
}

/** 党建品牌组织树接口 */
export function getPartyBuildingBrandTreeList() {
  return commonReq.get<TreeDataType[]>({
    url: '/org-construction/app/v1/orgconstruction_garden/branch_org_tree',
  })
}
