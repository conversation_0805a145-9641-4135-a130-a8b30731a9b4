import type { ProblemClues } from './types'
import { commonReq } from '@/services/request'
import type { PaginationRes } from '@/services/types'

/** 获取问题线索列表 */
export function getProblemList(params: any) {
  return commonReq.get<PaginationRes<ProblemClues>>({
    url: '/party-affairs/backend/v1/question-clues',
    params,
  })
}

/** 删除问题线索 */
export function deleteProblem(ids: string) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/question-clues/deleteByIds',
    params: {
      ids,
    },
  })
}
