<!-- 组织及个人详情 -->
<script setup lang="ts">
import { NButton } from 'naive-ui'
import { debounce } from 'lodash-es'
import { computed, h, onMounted, ref, watch } from 'vue'
import { getTableColumns } from './config'
import { useMyTable } from '@/hooks/use-my-table'
import {
  getOrganizeLifeById,
  getOrganizeLifeList,
} from '@/services/electronic-ledger/index'

// 详情数据
const detailRow = ref([
  { label: '台账时间', value: '' },
  { label: '组织名称', value: '' },
  { label: '父级组织名称', value: '' },
  { label: '姓名', value: '' },
  { label: '状态', value: '' },
  { label: '党内职务', value: '' },
  { label: '手机号', value: '' },
  { label: '入党时间', value: '' },
  { label: '转正时间', value: '' },
  { label: '工作部门', value: '' },
  { label: '学历', value: '' },
  { label: '家庭住址', value: '' },
])

const detailData = ref<any>({})

const detailVisible = ref<boolean>(false)
const filterReactive = ref<{
  orgName: string
  startTime: string | null
  endTime: string | null
}>({
  orgName: '',
  startTime: null,
  endTime: null,
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(getOrganizeLifeList, filterReactive, {
  batchDeleteTable: true,
})

const timeRangeFormattedValue = computed<[string, string] | null>(() => {
  const { startTime, endTime } = filterReactive.value
  if (startTime && endTime) {
    return [startTime, endTime]
  }
  return null
})

const handleUpdateMonthRange = (formattedValue: [string, string] | null) => {
  if (formattedValue) {
    filterReactive.value.startTime = formattedValue[0]
    filterReactive.value.endTime = formattedValue[1]
  }
  else {
    filterReactive.value.startTime = ''
    filterReactive.value.endTime = ''
  }
}

/** 图片名称过滤 */
function handleResourceNameChange() {
  currentPage.value = 1
  filterReactive.value.orgName = filterReactive.value.orgName.trim()
  loadData()
}
watch(filterReactive.value, debounce(handleResourceNameChange, 50000))

/** 点击添加按钮 */
function handleClickAdd() {}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          getDetail(row)
        },
        type: 'primary',
        text: true,
      },
      {
        default: () => '查看',
      },
    ),
  ]
})

// 人员状态 （0：正常 1：死亡 2：离退休 3：失联）
function transformMemberStatus(status: string) {
  let statusText = ''
  switch (status) {
  case '0':
    statusText = '正常'
    break
  case '1':
    statusText = '离退休'
    break
  case '2':
    statusText = '失联'
    break
  case '3':
    statusText = '正常'
    break
  default:
    statusText = '-'
    break
  }
  return statusText
}

/** 点击查看按钮 */
function getDetail(row: any) {
  getOrganizeLifeById(row?.userId).then((res: any) => {
    detailRow.value = [
      { label: '台账时间', value: row?.syncTime },
      { label: '组织名称', value: res?.deptName },
      { label: '父级组织名称', value: row?.orgParentName },
      { label: '姓名', value: res?.trueName },
      {
        label: '状态',
        value: transformMemberStatus(String(res?.memberStatus)),
      },
      { label: '党内职务', value: row?.partyIdentity },
      { label: '手机号', value: res?.phone },
      { label: '入党时间', value: res?.joinTime },
      { label: '转正时间', value: res?.regularTime },
      { label: '工作部门', value: res?.departmentName },
      { label: '学历', value: res?.eduDes },
      { label: '家庭住址', value: res?.address },
    ]
    detailData.value = res
  })
  detailVisible.value = true
}

onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="组织及党员"
    :loading="loading"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :show-delete="false"
    @click-add="handleClickAdd"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
  >
    <template #btns>
      <div class="flex flex-row justify-start items-center gap-x-[10px]">
        <n-input
          v-model:value="filterReactive.orgName"
          clearable
          size="small"
          placeholder="请输入组织名称"
          style="width: 180px"
        />
        <n-date-picker
          v-model:formatted-value="timeRangeFormattedValue"
          style="width: 240px"
          type="monthrange"
          format="yyyy-MM"
          clearable
          @update:formatted-value="handleUpdateMonthRange"
        />
      </div>
    </template>
    <template #filters>
      <n-button type="primary">
        同步查询结果
      </n-button>
    </template>
  </table-container>

  <!-- 查看详情弹框 -->
  <n-modal
    v-model:show="detailVisible"
    preset="dialog"
    title="Dialog"
    :show-icon="false"
    :mask-closable="false"
    style="width: 1200px"
  >
    <template #header>
      <div>&nbsp;</div>
    </template>
    <div class="h-[600px] overflow-y-auto">
      <div
        class="flex flex-row justify-center items-center mt-[20px] mb-[20px] text-[20px]"
      >
        <span>组织及个人详情</span>
      </div>
      <div class="border-top border-left border-right">
        <!-- 基础信息 -->
        <div
          v-for="(item, index) in detailRow"
          :key="index"
          class="flex flex-row justify-start items-center h-[auto] min-h-[40px] border-bottom"
        >
          <div
            class="w-[120px] h-[auto] min-h-[40px] flex flex-row justify-center items-center border-right-f5 font-bold"
          >
            <span>{{ item.label }}</span>
          </div>
          <div
            class="flex flex-1 h-[100%] flex-row justify-start items-center box-border px-[20px]"
          >
            <span>{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>
  </n-modal>
</template>
<style lang="scss" scoped>
.border-bottom {
  border-bottom: 1px solid #f1f1f1;
}

.border-top {
  border-top: 1px solid #f1f1f1;
}

.border-left {
  border-left: 1px solid #f1f1f1;
}

.border-right {
  border-right: 1px solid #f1f1f1;
}

.border-bottom-f5 {
  border-bottom: 1px solid #f1f1f1;
}

.border-right-f5 {
  border-right: 1px solid #f1f1f1;
}
</style>
