import type {
  AllTargetListBranch,
  BranchPartyMeetingList,
  BranchVoteScoreList,
  CredentialsData,
  PartyBuildingListBranchItem,
} from './types'
import { commonReq, downloadReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 获取党建清单列表 */
export function getPartyBuildListBranch(
  params: { startTime: string; endTime: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<PartyBuildingListBranchItem>>({
    url: '/party-affairs/backend/v1/building-inventory/branch',
    params,
  })
}

/** 结果确认 */
export function putResultConfirm(id: string) {
  return commonReq.put({
    url: `/party-affairs/backend/v1/building-inventory/branch/confirm/${id}`,
  })
}

/** 查看清单下的所有指标 */
export function getAllTargetListBranch(id: string) {
  return commonReq.get<AllTargetListBranch>({
    url: `/party-affairs/backend/v1/building-inventory/target/branch/${id}`,
  })
}

/** 党支部-党内会议列表 */
export function getPartyMeetingList(
  params: {
    startTime: string | null | undefined
    meetingType: string | null | undefined
  } & PaginationReq,
) {
  return commonReq.get<PaginationRes<BranchPartyMeetingList>>({
    url: '/party-affairs/backend/v1/building-inventory/target/mark/branch/meeting',
    params,
  })
}

/** 党支部-投票打分列表 */
export function getVoteScoreList(
  params: {
    year: string | null | undefined
    voteType: string | null | undefined
  } & PaginationReq,
) {
  return commonReq.get<PaginationRes<BranchVoteScoreList>>({
    url: '/party-affairs/backend/v1/building-inventory/target/mark/branch/vote',
    params,
  })
}

/** 党支部-上传修改凭证 */
export function putCredentials(data: CredentialsData) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/building-inventory/target/mark/branch',
    data,
  })
}

/** 党支部-查看凭证 */
export function getCredentialsDetail(id: string) {
  return commonReq.get({
    url: `/party-affairs/backend/v1/building-inventory/target/mark/branch/${id}`,
  })
}

/** 党支部-撤销凭证 */
export function putRevokeCredentials(id: string) {
  return commonReq.put({
    url: `/party-affairs/backend/v1/building-inventory/target/mark/branch/revoke/${id}`,
  })
}

/** 导出指标 */
export function postBranchExportTargetFile(id: string) {
  return downloadReq.post({
    url: `/party-affairs/backend/v1/building-inventory/target/branch/export/${id}`,
    responseType: 'blob',
  })
}
