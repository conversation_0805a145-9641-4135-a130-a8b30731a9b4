import { AES, enc, mode, pad } from 'crypto-js'
export const encryption = (params: any) => {
  const { data, type, param } = params
  const result = JSON.parse(JSON.stringify(data))
  if (type === 'Base64') {
    param.forEach((ele: any) => {
      result[ele] = btoa(result[ele])
    })
  } else {
    let key = params.key

    param.forEach((ele: any) => {
      const data = result[ele]
      key = enc.Latin1.parse(key)
      const iv = key
      const encrypted = AES.encrypt(data, key, {
        iv,
        mode: mode.CFB,
        padding: pad.NoPadding,
      })
      result[ele] = encrypted.toString()
    })
  }
  return result
}
