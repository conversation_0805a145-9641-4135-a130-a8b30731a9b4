<script lang="ts" setup>
import { NButton } from 'naive-ui'
import { PlusRound } from '@vicons/material'
import { getTableColumns, statusOptions, transferType } from './config'
import NoticeForm from './cpn/NoticeForm.vue'
import viewNoticeForm from './cpn/viewNoticeForm.vue'
import ApproverSetting from './cpn/ApproverSetting.vue'
import { useDrawerEditOrganize } from '@/hooks'
import {
  getPartyMemberTransferSponsorTableList,
  revocationPartyMemberTransfer,
} from '@/services/transfer'
import type { TransferTableItemType } from '@/services/transfer/types'

const userNameRef = ref('')
const transferTypeRef = ref<number | null>(null)
const phaseStatusRef = ref<null | string>(null)
const pageNum = ref(1)
const pageSize = ref(10)
const loading = ref(false)

const currentFormStatus = ref('add')

const uploadFileLoading = ref(false)

const total = ref(0)
const tableData = ref<TransferTableItemType[]>([])
const approverSettingRef = ref(false)
const approverSettingFormRef = ref()

/** 获取由我发起列表 */
const getTableList = () => {
  loading.value = true
  const params = {
    userName: userNameRef.value,
    transferType: transferTypeRef.value,
    phaseStatus: phaseStatusRef.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }
  getPartyMemberTransferSponsorTableList(params)
    .then((res: any) => {
      total.value = res.total || 0
      tableData.value = res.records || []
    })
    .finally(() => {
      loading.value = false
    })
}

// 新增/查看党组织转接抽屉
const idEditRef = ref()
const idEditParentID = ref()
const idEditParentName = ref()
const idEditLevel = ref()
const addNoticeFormRef = ref()
const checkFormRef = ref()

/**
 * 保存操作
 */
const handelConfirmEdit = () => {
  if (['check'].includes(currentFormStatus.value)) {
    checkFormRef.value?.validateAndSave()
  }
  else {
    addNoticeFormRef.value?.validateAndSave()
  }
}

const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  editTitle,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEditOrganize({
  name: '组织关系转接',
  confirmFn: handelConfirmEdit,
  isNotTips: true,
})

/**
 * @description 添加组织关系转接
 */
const addOrganizationChangeOver = () => {
  editTitle.value = '组织关系转接'
  editTypeRef.value = 'add'
  currentFormStatus.value = 'add'
  showEditRef.value = true
}

// 审批人设置
// function approverSetting() {
//   approverSettingRef.value = true
// }

watch(showEditRef, (newV) => {
  if (!newV) {
    if (['check'].includes(currentFormStatus.value)) {
      checkFormRef.value?.resetForm()
    }
    else {
      addNoticeFormRef.value?.resetForm()
    }
  }
})

/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  getTableList()
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          window.$dialog.warning({
            title: '提示',
            content: '撤销后无法恢复，确认撤销？',
            positiveText: '确认',
            negativeText: '取消',
            onPositiveClick: () => {
              const data = {
                id: row.id,
              }
              revocationPartyMemberTransfer(data).then((res) => {
                window.$message.success('撤销成功')
                getTableList()
              })
            },
          })
        },
        type: 'primary',
        disabled: ['已驳回', '已撤销', '已完成'].includes(row.phaseStatus),
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '撤销',
      },
    ),
    h(
      NButton,
      {
        onClick: () => {
          editTitle.value = '组织关系转接'
          editTypeRef.value = 'view'
          currentFormStatus.value = 'view'
          showEditRef.value = true
          idEditRef.value = row.id
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '查看',
      },
    ),
  ]
})

watch(
  () => pageNum.value,
  () => {
    getTableList()
  },
)

watch(
  () => pageSize.value,
  () => {
    pageNum.value = 1
    getTableList()
  },
)

// 审批人设置确定按钮
function handleClickApproveSettingConfirm() {
  approverSettingFormRef.value.saveApprover()
  approverSettingRef.value = false
}

// 审批人设置取消按钮
function handleClickApproveSettingCancel() {
  approverSettingRef.value = false
}

// 过滤条件筛选时
function handelFilterFn() {
  pageNum.value = 1
  getTableList()
}

onMounted(() => {
  getTableList()
})
</script>
<template>
  <table-container
    v-model:page="pageNum"
    v-model:page-size="pageSize"
    :loading="loading"
    :show-delete="false"
    :show-pagination="true"
    :show-toolbar="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    custom-toolbar
    default-expand-all
    title="组织关系转接"
    @click-add="addOrganizationChangeOver"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="addOrganizationChangeOver">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <!-- <n-button size="small" type="primary" @click="approverSetting">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        审批人设置
      </n-button> -->
    </template>
    <template #filters>
      <div
        class="flex flex-row items-center justify-between gap-[10px] w-[600px]"
      >
        <n-select
          v-model:value="transferTypeRef"
          :options="transferType"
          clearable
          placeholder="请选择调动类型"
          size="medium"
          @update:value="handelFilterFn"
        />
        <n-select
          v-model:value="phaseStatusRef"
          :options="statusOptions"
          clearable
          placeholder="请选择状态"
          size="medium"
          @update:value="handelFilterFn"
        />
        <n-input
          v-model:value="userNameRef"
          clearable
          placeholder="请输入党员姓名"
          size="medium"
          @input="handelFilterFn"
        />
      </div>
    </template>
  </table-container>

  <!-- 新增组织关系转接 -->
  <n-drawer v-model:show="showEditRef" :mask-closable="false" :width="700">
    <n-drawer-content :title="drawerTitle" closable>
      <!-- <notice-form
        v-show="['add', 'view'].includes(currentFormStatus)"
        :id="idEditRef"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        :transfer-type-prop="transferType"
        :parent-id="idEditParentID"
        :parent-name="idEditParentName"
        :level="idEditLevel"
        @saved="handleListSaved"
      /> -->
      <template v-if="['view'].includes(currentFormStatus)">
        <viewNoticeForm
          :id="idEditRef"
          ref="addNoticeFormRef"
          :level="idEditLevel"
          :parent-id="idEditParentID"
          :parent-name="idEditParentName"
          :transfer-type-prop="transferType"
          type="view"
          @saved="handleListSaved"
        />
      </template>
      <template v-if="['add'].includes(currentFormStatus)">
        <notice-form
          :id="idEditRef"
          ref="addNoticeFormRef"
          v-model:upload-file-loading="uploadFileLoading"
          :level="idEditLevel"
          :parent-id="idEditParentID"
          :parent-name="idEditParentName"
          :transfer-type-prop="transferType"
          :type="editTypeRef"
          @saved="handleListSaved"
        />
      </template>
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            :loading="uploadFileLoading"
            style="width: 80px"
            type="primary"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>

  <!-- 审批人设置 -->
  <n-drawer
    v-model:show="approverSettingRef"
    :mask-closable="false"
    :width="700"
  >
    <n-drawer-content closable title="审批人设置">
      <ApproverSetting ref="approverSettingFormRef" @saved="handleListSaved" />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            style="width: 80px"
            type="primary"
            @click="handleClickApproveSettingConfirm"
          >
            确定
          </n-button>
          <n-button
            style="width: 80px"
            @click="handleClickApproveSettingCancel"
          >
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
