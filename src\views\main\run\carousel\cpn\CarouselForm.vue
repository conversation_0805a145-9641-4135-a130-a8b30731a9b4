<script setup lang="ts">
import { NForm } from 'naive-ui'
import { formRules } from './config'
import type { ICarouselNewType } from '@/services/run/carousel/types'
import {
  addCarousel,
  getCarouselDetail,
  updateCarousel,
} from '@/services/run/carousel/carousel'
import { uploadImg } from '@/services/common'
import type { uploadFileItem } from '@/services/affairs/party-building-list/exam-indicators/types'
import {
  CAROUSEL_LINK_TYPE,
  TAB_MODULE_ENTRY,
  TAB_MODULE_ROUTE,
} from '@/store/dict'

const emits = defineEmits<{
  (e: 'saved'): void
}>()
interface Props {
  type?: string
  id?: number
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: 0,
})
// 移动端模块选择列表
const oriFormDataReactive = reactive<ICarouselNewType>({
  type: 10,
  title: '',
  coverUrl: '',
  linkUrl: null,
  sort: 0,
  isMobile: 0,
})
const formDataReactive = reactive<ICarouselNewType>({
  type: 10,
  title: '',
  coverUrl: '',
  linkUrl: null,
  sort: 0,
  isMobile: 0,
})
const imgSizeReactive = reactive<{ width: number; height: number }>({
  width: 590,
  height: 350,
})
const formRef = ref<InstanceType<typeof NForm>>()
const oldImgUrlRef = ref('')
const oldImgUrlFirstFlag = ref(true)
// 图片裁剪完毕
const handleCoverDone = async(file: File) => {
  // 修改的时候 传入旧图片会触发done 此时不能上传 否则与原图id不同 会被视为更改了图片
  if (
    (!oldImgUrlFirstFlag.value && props.type === 'modify')
    || props.type === 'add'
  ) {
    const imgFileData = new FormData()
    imgFileData.append('file', file)
    try {
      if (formDataReactive.coverUrl) {
        return
      }
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formDataReactive.coverUrl = data.url || ''
      }
    }
    catch (error) {}
  }
  oldImgUrlFirstFlag.value = false
}
// 图片删除
function handleCoverDelete() {
  formDataReactive.coverUrl = ''
}
// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
// 关闭时判断是否存在改变了的数据
function getChangedFlag() {
  return deepEqual(oriFormDataReactive, formDataReactive)
}
function deepEqual(obj1: ICarouselNewType, obj2: ICarouselNewType) {
  if (Object.keys(obj1).length !== Object.keys(obj2).length) {
    return false
  }

  for (const key in obj1) {
    if (!Object.prototype.hasOwnProperty.call(obj2, key)) {
      return false
    }
    if (obj1[key] !== obj2[key]) {
      return false
    }
  }

  return true
}
// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (props.id === 0 || props.type === 'add') {
        addCarousel(formDataReactive).then((res) => {
          window.$message.success('保存成功')
          emits('saved')
        })
      }
      else {
        updateCarousel(formDataReactive).then((res) => {
          window.$message.success('保存成功')
          emits('saved')
        })
      }
    }
  })
}

const calcLinkUrlOptions = computed(() => {
  return formDataReactive.type === 20 ? TAB_MODULE_ROUTE : TAB_MODULE_ENTRY
})

function changeTypeOption() {
  formDataReactive.linkUrl = null
}

function beforeUpload({ file }: any) {
  const fileSize = file.file.size || 0
  if (fileSize > 1024 * 1024 * 5) {
    window.$message.error('图片大小不能超过5M')
    return false
  }
  return true
}

defineExpose({
  validateAndSave,
  resetForm,
  getChangedFlag,
})
onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getCarouselDetail(props.id).then((res) => {
      for (const k in formDataReactive) {
        formDataReactive[k] = res[k]
        oriFormDataReactive[k] = res[k]
      }
      formDataReactive.id = props.id
      oriFormDataReactive.id = props.id
      oldImgUrlRef.value = res.coverUrl ?? ''
    })
  }
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
    :disabled="props.type === 'view'"
  >
    <n-grid>
      <n-form-item-gi span="24" label="轮播图名称：" path="title">
        <n-input
          v-model:value="formDataReactive.title"
          placeholder="请输入轮播图名称"
          clearable
          maxlength="30"
          show-count
        />
      </n-form-item-gi>
      <n-form-item-gi span="14" label="轮播图图片" path="coverUrl" required>
        <img-uploader
          :need-cropper="false"
          :is-readonly="props.type === 'view'"
          :width="imgSizeReactive.width"
          :height="imgSizeReactive.height"
          :old-img-url="oldImgUrlRef"
          :before-upload="beforeUpload"
          :size="5"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="排序：" path="sort">
        <n-input-number
          v-model:value="formDataReactive.sort"
          style="width: 100px"
          :min="0"
          :max="99"
          :step="1"
          :precision="0"
          placeholder="排序"
          clearable
        />
      </n-form-item-gi>

      <n-form-item-gi span="24" label="是否在APP展示：" required>
        <n-switch
          v-model:value="formDataReactive.isMobile"
          :checked-value="'1'"
          :unchecked-value="'0'"
        />
      </n-form-item-gi>

      <n-form-item-gi span="24" label="类型：" required>
        <n-select
          v-model:value="formDataReactive.type"
          style="width: 100%"
          placeholder="请选择轮播图跳转类型"
          filterable
          :options="CAROUSEL_LINK_TYPE"
          clearable
          @update:value="changeTypeOption"
        />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="跳转地址：" path="linkUrl">
        <n-input
          v-if="formDataReactive.type === 10"
          v-model:value="formDataReactive.linkUrl"
          placeholder="请输入跳转地址"
          clearable
        />
        <n-select
          v-else
          v-model:value="formDataReactive.linkUrl"
          style="width: 100%"
          placeholder="请选择跳转地址"
          filterable
          :options="calcLinkUrlOptions"
          clearable
        />
      </n-form-item-gi>
    </n-grid>
  </n-form>
</template>
<style lang="scss" scoped></style>
