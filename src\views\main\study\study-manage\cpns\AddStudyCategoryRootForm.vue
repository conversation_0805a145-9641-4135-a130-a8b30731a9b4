<template>
  <div>
    <n-form
      ref="studyRootCategoryRef"
      require-mark-placement="left"
      label-placement="left"
      label-width="90px"
      :model="formData"
      :rules="formRules"
    >
      <n-form-item label="类别名称：" path="name">
        <n-input
          v-model:value="formData.name"
          placeholder="请输入类别名称"
          clearable
        />
      </n-form-item>
      <n-form-item label="学习类别：" path="studyType">
        <n-radio-group v-model:value="formData.studyType">
          <n-radio label="课程" value="1" />
          <n-radio label="书库" value="2" />
        </n-radio-group>
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import type { FormRules } from 'naive-ui'
const formRules: FormRules = {
  name: {
    required: true,
    // validator(rule: any, value: any) {
    //   if (value === '' || !value) {
    //     return new Error('资讯类别不能为空')
    //   } else if (/^[\u4E00-\u9FA5]+$/.test(value)) {
    //     return true
    //   } else {
    //     return new Error('资讯类别仅支持中文')
    //   }
    // },
    message: '类别名称不能为空',
    trigger: 'input',
    type: 'string',
  },
  studyType: {
    required: true,
    message: '学习类别不能为空',
    trigger: 'change',
    type: 'string',
  },
}
const studyRootCategoryRef = ref()
const formData = ref({
  name: '',
  studyType: '1',
})

function handleValidate() {
  return new Promise((resolve, reject) => {
    studyRootCategoryRef.value?.validate((errors: any) => {
      if (!errors) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  })
}

function handleSetFormData(data: any) {
  formData.value = data
}

defineExpose({
  formData,
  handleValidate,
  handleSetFormData,
})
</script>

<style scoped lang="scss"></style>
