<script setup lang="ts">
import { useData } from '../cpns/use-data'
import OptionsBar from '../cpns/OptionsBar.vue'
import ModalContent from './ModalContent.vue'
import { createColumns } from './config'
const { getData, total, pageNum, pageSize } = useData(apiFn)
function apiFn() {}
const columns = createColumns((row) => {
  switch (row.auditStatus) {
    case 1:
      return 1

    default:
      break
  }
})
const showModal = ref(false)
function closeModal() {
  showModal.value = false
}
</script>
<template>
  <div class="pt-[25px] px-[20px]">
    <p class="text-[14px] font-[600] leading-[20px]">
      党费基数设定
    </p>
    <OptionsBar :get-fn="getData" :need-audit="true">
      <n-button style="height: 28px" type="primary" @click="showModal = true">
        导入数据
      </n-button>
      <n-button style="height: 28px; margin-left: 10px">
        批量审核
      </n-button>
    </OptionsBar>

    <n-data-table :columns="columns" class="mt-[20px]" />
    <div class="flex items-center justify-between mt-[30px] pr-[2px]">
      <span class="text-[#BDBDBD] text-[12px] mr-[30px]">共
        <span class="text-[#262626] mx-[6px]">{{ total }}</span>
        条</span>
      <n-pagination
        v-model:page="pageNum"
        v-model:page-size="pageSize"
        :page-sizes="[5, 10, 20, 30, 50]"
        show-quick-jumper
        show-size-picker
        :item-count="total"
      />
    </div>
    <n-modal
      v-model:show="showModal"
      :mask-closable="false"
      class="custom-card"
      preset="card"
      title="导入党费"
      size="huge"
      style="width: 795px"
      :bordered="false"
    >
      <ModalContent :close="closeModal" />
    </n-modal>
  </div>
</template>

<style scoped lang="scss"></style>
