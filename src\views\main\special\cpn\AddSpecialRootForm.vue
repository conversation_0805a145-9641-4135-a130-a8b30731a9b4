<script setup lang="ts">
import { specialRootFormRules } from './ruleConfig'
import type { uploadFileItem } from '@/services/types'
import { uploadImg } from '@/services'
const specialRootRef = ref()
const formData = reactive({
  data: {
    name: '',
    coverUrl: '',
    categoryDesc: '',
    // configStudyScore: '',
    // configCommentScore: '',
    sliderFlag: 0, // 是否加入首页轮播池
  },
})

/**
 * 上传图片
 * @param {any} file:File
 */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formData.data.coverUrl === '' || formData.data.coverUrl === null) {
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formData.data.coverUrl = data.url
      }
    }
  }
  catch (error) {}
}
/**
 * 删除图片
 */
function handleCoverDelete() {
  formData.data.coverUrl = ''
}

function handleValidate() {
  return new Promise((resolve, reject) => {
    specialRootRef.value?.validate((errors: any) => {
      if (!errors) {
        resolve(true)
      }
      else {
        resolve(false)
      }
    })
  })
}

function handleSetFormData(data: any) {
  formData.data = data
}

defineExpose({
  formData,
  handleValidate,
  handleSetFormData,
})
</script>

<template>
  <div>
    <n-form
      ref="specialRootRef"
      label-placement="left"
      label-width="110px"
      require-mark-placement="left"
      :model="formData.data"
      :rules="specialRootFormRules"
    >
      <n-form-item label="专题分类名称：" path="name">
        <n-input v-model:value="formData.data.name" clearable />
      </n-form-item>
      <n-form-item label="专题分类图片：" path="coverUrl">
        <ImgUploader
          v-model:oldImgUrl="formData.data.coverUrl"
          :width="57"
          :height="57"
          :need-cropper="false"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </n-form-item>
      <!-- <n-form-item label="学习学分：" path="configStudyScore">
        <n-input-number
          v-model:value="formData.data.configStudyScore"
          clearable
        />
      </n-form-item>
      <n-form-item label="评论学分：" path="configCommentScore">
        <n-input-number
          v-model:value="formData.data.configCommentScore"
          clearable
        />
      </n-form-item> -->
      <n-form-item label="是否加入首页轮播池" path="sliderFlag">
        <n-switch
          v-model:value="formData.data.sliderFlag"
          :checked-value="1"
          :unchecked-value="0"
          :rubber-band="false"
        />
      </n-form-item>
      <n-form-item label="专题分类简介：" path="categoryDesc">
        <n-input
          v-model:value="formData.data.categoryDesc"
          type="textarea"
          maxlength="500"
          show-count
          clearable
        />
      </n-form-item>
    </n-form>
  </div>
</template>

<style scoped lang="scss"></style>
