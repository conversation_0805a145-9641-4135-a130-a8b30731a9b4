/** 公司展示列表item */
export interface CompanySHowItem {
  id: string
  title: string
  module: string
  publishTime: string
  topStatus: string
  cover: CoverItem
  appCover: CoverItem
  fileIds?: string[]
  fileList: fileItem[]
  location?: string
  activityMember?: string[]
  loading?: boolean
  value?: boolean
  locked?: boolean
}

/** 通用公司展示和法规制度列表通用接口 */
export interface CommonParamsOfCompanyAndRegulation {
  title?: string
  content?: string
  categoryId?: string
  type: string
}

/** 模块数据模型 */
export interface ModuleList {
  id: string
  moduleName: string
  content: string
}
/** 封面数据模型 */
export interface CoverItem {
  id: string
  url: string
}

export interface fileItem {
  fileName: string
  id: string
  original: string
  url?: string
}
/** 新增通用接口入参数据模型 */
export interface InsertCommonParamsOfCompanyAndRegulation {
  id: string
  title: string
  module: ModuleList[]
  publishTime: string
  topStatus: string
  cover?: CoverItem
  appCover: CoverItem
  categoryId: string
  location: string
  fileIds?: string[]
  fileList: Array<fileItem>
  activityMemberId: string[]
  type: string
  loading?: boolean
  value?: boolean
  locked?: boolean
  customCover?: CoverItem
  customAppCover?: CoverItem
}
