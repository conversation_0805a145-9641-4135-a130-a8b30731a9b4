<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="公司展示"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :loading="loading"
    :show-delete="false"
    :checked-row-keys="checkedRowKeys"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button>
    </template>
    <template #filters>
      <n-input
        v-model:value="filterReactive.title"
        size="small"
        placeholder="请输入搜索内容"
        clearable
      />
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <AddCompanyShowForm
        :id="idEditRef"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
  <!-- 预览 -->
  <mobile-preview
    :show="showPreviewRef"
    :preview-url="previewUrlRef"
    :qrcode-url="qrcodeUrlRef"
    @close="handleClosePreview"
  />
</template>

<script setup lang="ts">
import { NButton, NSwitch } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import { getTableColumns } from './config'
import AddCompanyShowForm from './cpn/AddCompanyShowForm.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import type { CommonParamsOfCompanyAndRegulation } from '@/services/publicity/companyShow/types'
import {
  delCompanyShowItem,
  getCompanyShowList,
  putUpdateCompanyShowItemTop,
} from '@/services/publicity/companyShow'
import router from '@/router'
const filterReactive = ref<CommonParamsOfCompanyAndRegulation>({
  title: '',
  content: '',
  categoryId: '',
  type: '0',
})
/** 预览 */
const previewUrlRef = ref('')
const showPreviewRef = ref(false)
const qrcodeUrlRef = ref('')
/** 关闭预览 */
const handleClosePreview = () => {
  showPreviewRef.value = false
  previewUrlRef.value = ''
}
// 有接口后添加：loading,tableData
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getCompanyShowList, filterReactive, {
  batchDeleteTable: true,
  delApi: delCompanyShowItem,
  delType: 0,
})

watch(filterReactive.value, (newVal) => {
  loadData()
})

// 新增/编辑党建清单抽屉
const idEditRef = ref()
const addNoticeFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('公司展示', handelConfirmEdit)

/** 点击添加按钮 */
function handleClickAdd() {
  router.push({ name: 'add-company-show', query: { id: '-1' } })
}
/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addNoticeFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns(
  (row) => {
    return [
      h(
        NButton,
        {
          onClick: () => {
            router.push({ name: 'add-company-show', query: { id: row.id } })
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '编辑',
        },
      ),
      h(DeleteButton, {
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    ]
  },
  (row) => {
    return h(NSwitch, {
      checkedValue: '1',
      uncheckedValue: '0',
      value: row.topStatus,
      loading: row.loading,
      onUpdateValue(v: any) {
        row.loading = true
        putUpdateCompanyShowItemTop({ id: row.id })
          .then((res) => {
            row.topStatus = res
          })
          .catch(() => {})
          .finally(() => {
            loadData()
            row.loading = false
          })
      },
    })
  },
)

onMounted(loadData)
</script>

<style lang="scss" scoped></style>
