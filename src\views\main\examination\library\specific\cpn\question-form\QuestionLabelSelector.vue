<!--
 * @Description: 题目标签选择组件
-->
<template>
  <n-select
    style="width: 308px"
    multiple
    filterable
    placement="top"
    max-tag-count="responsive"
    :value="value"
    :options="labelOptions"
    @update-value="(v: any) => emits('update:value', v)"
  />
  <n-popconfirm
    :show-icon="false"
    placement="bottom-end"
    positive-text="确定"
    @positive-click="handleAddLabel"
  >
    <template #trigger>
      <n-button text type="primary">
        <n-icon style="margin: 0 6px 0 10px" size="14">
          <add-alt />
        </n-icon>
        新增标签
      </n-button>
    </template>

    <n-input
      v-model:value="labelAddingRef"
      size="small"
      placeholder="输入标签名称"
    />
  </n-popconfirm>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { computed, ref } from 'vue'
import { NButton, NIcon, NInput, NPopconfirm, NSelect } from 'naive-ui'
import { AddAlt } from '@vicons/carbon'

import { postExaminationLibraryLabel } from '@/services/examination/index'
import useExaminationStore from '@/store/examination/index'

defineProps({
  value: {
    type: Array as PropType<number[] | null>,
    required: true,
  },
})
const emits = defineEmits(['update:value'])

const examinationStore = useExaminationStore()
examinationStore.getExaminationLibraryLabelListAction()
const labelOptions = computed(() =>
  examinationStore.examinationLibraryLabelList
    .filter(item => item?.name)
    .map(item => ({
      value: item.id,
      label: item.name,
    })),
)

// 添加标签
const labelAddingRef = ref('')
function handleAddLabel() {
  const labelName = labelAddingRef.value.trim()
  if (!labelName.length) {
    window.$message.warning('请输入标签名称')
    return
  }
  postExaminationLibraryLabel(labelName).then((res) => {
    window.$message.success(res)
    examinationStore.getExaminationLibraryLabelListAction()
  })
}
</script>

<style lang="scss" scoped>
:deep(.n-tag) {
  height: 24px;
}
</style>
