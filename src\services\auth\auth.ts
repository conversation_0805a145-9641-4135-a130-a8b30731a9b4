import { <PERSON><PERSON><PERSON> } from 'buffer'
import qs from 'qs'
import { authReq, commonReq } from '../request'
import type { ILoginReq, ILoginRes } from './types'
/** 获取验证码 */
export function getVerCode(randomStr: string) {
  return authReq.get({
    url: '/code',
    params: { randomStr },
    responseType: 'arraybuffer',
    interceptors: {
      responseInterceptor(res) {
        // 处理字节流
        const buffer = Buffer.from(res, 'base64')
        const codeUrl = `data:image/png;base64,${buffer.toString('base64')}`
        return codeUrl
      },
    },
  })
}
/** 登录 */
export function login(
  data: ILoginReq,
  randomStr: string,
  code: string,
  grant_type = 'password',
  scope = 'server',
) {
  // const formData = new FormData()
  // for (const k in data) {
  //   formData.append(k, data[k])
  // }
  return authReq.post<{ data: ILoginRes }>({
    url: '/auth/oauth2/token',
    params: { randomStr, code, grant_type, scope },
    data: qs.stringify(data),
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;',
    },
  })
}

/** 退出登录 */
export function loginOut() {
  return commonReq.delete({
    url: '/auth/token/logout',
  })
}

/** 获取用户菜单权限 */
export function getMenus() {
  return commonReq.get({
    url: '/upms/menu',
  })
}

/** 修改密码 */
export function updatePassword(data: any) {
  return commonReq.put({
    url: '/upms/admin-user/password',
    data,
  })
}
