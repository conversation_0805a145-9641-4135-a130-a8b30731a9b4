<script setup lang="ts">
import { ArrowBackIosNewRound } from '@vicons/material'
import { Export } from '@vicons/carbon'
import { NButton, NIcon } from 'naive-ui'

import { getTableColumns } from './config'
import type {
  AllTargetItemListRowBranch,
  AllTargetListBranch,
} from '@/services/affairs/discipline-inspection-list-branch/types'
import {
  getAllTargetListBranch,
  postBranchExportTargetFile,
  putRevokeCredentials,
} from '@/services/affairs/discipline-inspection-list-branch'
import { downloadArrayBuffer } from '@/utils/downloader'
import { formatTimeStamp } from '@/utils/format'

const route = useRoute()
const router = useRouter()
const title = route.query.title
const examTime = computed(
  () => `${String(route.query.examTime).replace('-', '年')}月`,
)
const status = computed(() => route.query.status)

const partyListId = computed(() => route.query.partyListId)
const targetListRef = ref<AllTargetListBranch>()
const allTargetItemList = ref<AllTargetItemListRowBranch[]>([])

const loading = ref(false)
/** 加载指标数据 */
function loadTargetListData() {
  loading.value = true
  getAllTargetListBranch(String(partyListId.value)).then((res: any) => {
    if (res) {
      targetListRef.value = res
      allTargetItemList.value = res.targetItemForm?.flatMap(
        (item: any) => item.targetItemList,
      )
    }
  })
  loading.value = false
}
/** 撤销 */
async function handleRevokeCredentials(id: string, isScore: boolean) {
  if (isScore) {
    window.$message.warning('党委管理员已完成该项打分，不可撤销！')
  } else {
    window.$dialog.create({
      type: 'default',
      closable: false,
      content: '确认撤销吗？',
      showIcon: false,
      positiveText: '确认',
      negativeText: '取消',
      onPositiveClick: () => {
        putRevokeCredentials(id).then((res) => {
          window.$message.success('撤销成功！')
          loadTargetListData()
        })
      },
    })
  }
}

/** 导出指标 */
async function handleExport() {
  try {
    loading.value = true
    const res = await postBranchExportTargetFile(String(partyListId.value))
    downloadArrayBuffer(
      res,
      `纪检清单-${formatTimeStamp(Date.now(), 'YYYYMMDDHHmmss')}`,
    )
    loading.value = false
  } catch (error) {}
}

const tableColumns = getTableColumns((row: AllTargetItemListRowBranch) => {
  return status.value === '考核中'
    ? h('div', [
      row.submitStatus === '未提交'
          && h(
            NButton,
            {
              text: true,
              type: 'primary',
              onClick: () => {
                router.push({
                  name: 'uploadCredentialsDiscipline',
                  query: {
                    id: row.id,
                    type: 'add',
                  },
                })
              },
            },
            '填写完成情况',
          ),
      row.submitStatus === '已提交'
          && h('div', [
            h(
              NButton,
              {
                text: true,
                type: 'primary',
                style: 'margin-right:10px',
                onClick: () => {
                  router.push({
                    name: 'uploadCredentialsDiscipline',
                    query: {
                      id: row.id,
                      type: 'view',
                    },
                  })
                },
              },
              '查看完成情况',
            ),
            h(
              NButton,
              {
                text: true,
                type: 'primary',
                style: 'margin-right:10px',
                onClick: () => handleRevokeCredentials(row.id, row.isScore),
              },
              '撤销',
            ),
          ]),
      row.submitStatus === '已撤销'
          && h(
            NButton,
            {
              text: true,
              type: 'primary',
              onClick: () => {
                router.push({
                  name: 'uploadCredentialsDiscipline',
                  query: {
                    id: row.id,
                    type: 'modify',
                  },
                })
              },
            },
            '填写完成情况',
          ),
    ])
    : h(
      NButton,
      {
        text: true,
        type: 'primary',
        style: 'margin-right:10px',
        onClick: () => {
          router.push({
            name: 'uploadCredentialsDiscipline',
            query: {
              id: row.id,
              type: 'view',
            },
          })
        },
      },
      '查看完成情况',
    )
})

const showTableColumns = computed(() => {
  if (status.value === '考核中') {
    return tableColumns.filter(
      (item: any) => item.key !== 'performance' && item.key !== 'score',
    )
  } else {
    return tableColumns.filter((item: any) => item.key !== 'submitStatus')
  }
})

onMounted(loadTargetListData)
</script>
<template>
  <edit-top>
    <template #left>
      <n-button
        size="small"
        @click="() => router.replace({ name: 'inspectionBranch' })"
      >
        <n-icon size="16">
          <arrow-back-ios-new-round />
        </n-icon>
        返回
      </n-button>
    </template>
    <template #mid>
      {{ title }}-{{ examTime }}
    </template>
    <template #right>
      <n-button
        v-if="status === '已完成' || status === '待确认'"
        @click="handleExport"
      >
        <n-icon style="margin-right: 6px" size="14">
          <export />
        </n-icon>
        导出
      </n-button>
    </template>
  </edit-top>

  <div class="px-[20px] py-[23px]">
    <div
      class="bg-[#fafafa] px-[20px] text-center leading-[42px] text-[12px] font-[500] mb-[11px] text-[#333] grid grid-cols-[4%,14%,14%,14%,14%,8%,8%,14%,10%]"
      :class="{
        '!grid-cols-[4%,14%,14%,14%,14%,8%,8%,7%,7%,10%]':
          status === '已完成' || status === '待确认',
      }"
    >
      <span>序号</span>
      <span>事项</span>
      <span>工作要求</span>
      <span>考核方式</span>
      <span>附件</span>
      <span>考核得分</span>
      <span>截止日期</span>
      <span v-show="status === '考核中'">状态</span>
      <span v-show="status === '已完成' || status === '待确认'">完成情况</span>
      <span v-show="status === '已完成' || status === '待确认'">得分</span>
      <span>操作</span>
    </div>
    <n-scrollbar style="width: 100%; height: calc(100vh - 330px)">
      <div
        v-for="(item, index) in targetListRef?.targetItemForm"
        :key="index"
        class="bg-[#fafafa] px-[20px] py-[22px] mb-[20px]"
      >
        <div class="flex justify-between items-center mb-[22px]">
          <div class="flex items-center gap-[20px]">
            <span
              class="text-[12px] text-[#CB0000] font-[500] leading-[17px] ml-[10px]"
            >类别：{{ item.categoryName }}</span>
          </div>
        </div>
        <div>
          <n-data-table
            id="targetTable"
            :bordered="true"
            :single-line="false"
            :columns="showTableColumns"
            :data="item.targetItemList"
          />
        </div>
      </div>
    </n-scrollbar>

    <div class="sticky bottom-0 z-1 bg-[#fff]">
      <div
        class="bg-[#fafafa] px-[20px] leading-[42px] text-[12px] font-[500] text-[#cb0000]"
      >
        总分：{{ targetListRef?.totalScore }}分
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
:deep(.n-data-table .n-data-table-thead) {
  display: none;
}

.emptyImg {
  width: 100%;
  height: 500px;
  background-image: url('@/assets/image/emptyImg.png');
  background-position: center center;
  background-repeat: no-repeat;
}
</style>
