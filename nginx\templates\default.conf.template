server {
  listen      80;
  listen      [::]:80;
  server_name _;
  root        /var/www/public;

  # security
  include     nginxconfig.io/security.conf;

  # index.html fallback
  location / {
      try_files $uri $uri/ /index.html;
  }

  # api gateway
  location /api {
    rewrite ^\/api\/(.*) /$1 break;
    proxy_pass http://${API_GATEWAY_HOST}:${API_GATEWAY_PORT};
    include    nginxconfig.io/proxy.conf;
  }

  location ~* /api/.*\.(?:css(\.map)?|js(\.map)?|jpe?g|png|gif|ico|cur|heic|webp|tiff?|mp3|m4a|aac|ogg|midi?|wav|mp4|mov|webm|mpe?g|avi|ogv|flv|wmv)$ {
    rewrite ^\/api\/(.*) /$1 break;
    proxy_pass http://${API_GATEWAY_HOST}:${API_GATEWAY_PORT};
    include    nginxconfig.io/proxy.conf;
  }

  location ~* /api/.*\.(?:svgz?|ttf|ttc|otf|eot|woff2?)$ {
    rewrite ^\/api\/(.*) /$1 break;
    proxy_pass http://${API_GATEWAY_HOST}:${API_GATEWAY_PORT};
    include    nginxconfig.io/proxy.conf;
  }

# kk fileview
  location /previews {
    proxy_set_header X-Base-Url http://$http_host/previews;
    proxy_pass http://${FILE_GATEWAY_HOST}:${FILE_GATEWAY_PORT};
    include    nginxconfig.io/proxy.conf;
  }

  location ~* /previews/.*\.(?:css(\.map)?|js(\.map)?|jpe?g|png|gif|ico|cur|heic|webp|tiff?|mp3|m4a|aac|ogg|midi?|wav|mp4|mov|webm|mpe?g|avi|ogv|flv|wmv)$ {
    proxy_set_header X-Base-Url http://$http_host/previews;
    proxy_pass http://${FILE_GATEWAY_HOST}:${FILE_GATEWAY_PORT};
    include    nginxconfig.io/proxy.conf;
  }

  location ~* /previews/.*\.(?:svgz?|ttf|ttc|otf|eot|woff2?)$ {
    proxy_set_header X-Base-Url http://$http_host/previews;
    proxy_pass http://${FILE_GATEWAY_HOST}:${FILE_GATEWAY_PORT};
    include    nginxconfig.io/proxy.conf;
  }


  # additional config
  include nginxconfig.io/general.conf;

}
