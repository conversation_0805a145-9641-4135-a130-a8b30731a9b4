<script setup lang="ts">
import type { CultureShowListItemDetail } from '@/services/run/interaction/types'
import { getCulSingleDataDetail } from '@/services/run/interaction'
interface Props {
  id: string
  showTitle?: boolean
  showDescription?: boolean
  showContent?: boolean
  showReason?: boolean
  titleText?: string
  descriptionText?: string
  contentText?: string
  reasonText?: string
}
const props = withDefaults(defineProps<Props>(), {
  showTitle: true,
  showDescription: true,
  showContent: true,
  showReason: false,
  titleText: '标题',
  descriptionText: '作品描述',
  contentText: '内容',
  reasonText: '驳回原因',
})
const loading = ref(false)
const singleData = ref<CultureShowListItemDetail>()

// const isVideo = ref(false)
// const videoOptions = ref({
//   width: '650px', // 播放器高度
//   height: '410px', // 播放器高度
//   color: '#409eff', // 主题色
//   title: singleData.value?.fileList[0].original, // 视频名称
//   src: 'https://sh1a.qingstor.com/jchcweb/sjk/281fd491-9c3a-4c08-a487-f48f24994d8c.mp4?access_key_id=MLVUDJUERXAVZLETHEFB&expires=1984288867&signature=k4n1MZ8VY5HPiE3Jrus8KRGHa4g9d8TkdljfbppFvd8%3D', // 视频源
//   muted: false, // 静音
//   webFullScreen: false,
//   speedRate: ['0.75', '1.0', '1.25', '1.5', '2.0'], // 播放倍速
//   autoPlay: false, // 自动播放
//   loop: false, // 循环播放
//   mirror: false, // 镜像画面
//   lightOff: false, // 关灯模式
//   volume: 0.3, // 默认音量大小
//   control: true, // 是否显示控制
//   controlBtns: [
//     'audioTrack',
//     'quality',
//     'speedRate',
//     'volume',
//     'setting',
//     'pip',
//     'pageFullScreen',
//     'fullScreen',
//   ], // 显示所有按钮,
// })
// const reg
//   = /(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/

watchEffect(() => {
  loading.value = true
  getCulSingleDataDetail(props.id).then((res: CultureShowListItemDetail) => {
    singleData.value = res
    // isVideo.value = /<video.+<\/video>/.test(singleData.value.fileList[0].url)
    // if (isVideo.value) {
    //   videoOptions.value.src = reg.exec(singleData.value.fileList[0].url)![0]
    // }
    loading.value = false
  })
})

function getFullImageUrl(fileName: string) {
  return `${import.meta.env.VITE_API_HOST}${fileName}`
}
</script>
<template>
  <div class="p-[20px] flex flex-col">
    <div v-show="showTitle" class="every-row">
      <span class="title-column">{{ titleText }}：</span>
      <span>{{ singleData?.title }}</span>
    </div>
    <div v-show="showDescription" class="every-row">
      <span class="title-column">{{ descriptionText }}：</span>
      <span>{{ singleData?.content }}</span>
    </div>
    <div v-show="showReason" class="every-row">
      <span class="title-column">{{ reasonText }}：</span>
      <span>{{ singleData?.reason ?? '--' }}</span>
    </div>
    <div v-show="showContent" class="flex text-[16px] mb-[10px]">
      <span class="title-column">{{ contentText }}：</span>
      <div class="flex flex-col">
        <!-- <div class="mb-[20px]">
          <vue3VideoPlay
            :poster="`https://sh1a.qingstor.com/jchcweb/sjk/Snipaste_2023-01-09_15-16-15.png?access_key_id=MLVUDJUERXAVZLETHEFB&expires=1984288607&signature=LCp45J87CJPT1IUMODm4s1EwdrEn10FW44VQsQMlUS0%3D`"
            v-bind="videoOptions"
          />
        </div> -->
        <div class="flex flex-wrap justify-between gap-[10px]">
          <n-image-group>
            <n-image
              v-for="item in singleData?.fileList"
              :key="item.id"
              lazy
              class="border-radius-[10px]"
              show-toolbar-tooltip
              object-fit="fill"
              style="width: 200px; height: 200px; border-radius: 4px"
              :src="getFullImageUrl(item.fileName)"
            />
          </n-image-group>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.every-row {
  @apply flex items-start text-[16px] mb-[20px];
}
.title-column {
  @apply w-[100px] text-right mr-[10px] flex-none;
}
</style>
