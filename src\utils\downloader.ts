/**
 * 根据流下载文件
 * @param arrayBuffer 流
 * @param fileName 文件名
 * @param type 文件类型，默认 xlsx
 */
export function downloadArrayBuffer(
  arrayBuffer: ArrayBuffer,
  fileName: string,
  type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
) {
  const aEl = document.createElement('a')
  aEl.style.display = 'none'
  aEl.href = URL.createObjectURL(
    new Blob([arrayBuffer], {
      type,
    }),
  )
  aEl.download = fileName
  document.body.appendChild(aEl)
  aEl.click()
  URL.revokeObjectURL(aEl.href)
  document.body.removeChild(aEl)
}

export function downloadFile(
  URL: string | undefined,
  fileName: string | undefined,
) {
  const url = import.meta.env.VITE_API_BASE + URL
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', fileName as string)
  link.click()
}

export async function fetchDownloadFile(
  url: string,
  filename?: string,
  params?: any,
) {
  try {
    // 1. 使用fetch获取文件blob
    const queryString = new URLSearchParams(params).toString()
    const urlWithParams = `${url}?${queryString}`
    const response = await fetch(
      import.meta.env.VITE_API_BASE + urlWithParams,
      {
        headers: {
          Authorization: `Bearer ${sessionStorage.getItem('access_token')}`,
          responseType: 'blob',
        },
        method: 'POST',
      },
    )

    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`)
    }

    // 2. 获取blob数据
    const blob = await response.blob()

    // 3. 从响应头或URL中提取文件名（如果没有提供filename参数）
    const finalFilename = `${filename}`

    // 4. 创建临时链接并触发下载
    const blobUrl = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = blobUrl
    a.download = finalFilename
    document.body.appendChild(a)
    a.click()

    // 5. 清理
    setTimeout(() => {
      document.body.removeChild(a)
      window.URL.revokeObjectURL(blobUrl)
    }, 100)

    return true
  }
  catch (error) {
    console.error('文件下载失败:', error)
    return false
  }
}
