<!-- 党费统计 -->
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="缴费记录"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <button class="operate-btn" @click="handleExport">
        <n-icon>
          <Export class="mt-[1px]" />
        </n-icon>
        导出
      </button>
    </template>
    <template #filters>
      <div class="filter-operate">
        <n-date-picker
          v-model:value="payYear"
          type="year"
          clearable
          placeholder="请输入年份"
          @change="handleYearChange"
        />
        <n-tree-select
          v-model:value="filterReactive.unitId"
          :options="transformTreeData(organizeData)"
          placeholder="请选择所在组织"
          value-field="deptId"
          key-field="deptId"
          clearable
        />
        <n-input
          v-model:value="filterReactive.userName"
          size="small"
          placeholder="请输入姓名"
          clearable
        />
      </div>
    </template>
  </table-container>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { NIcon } from 'naive-ui'
import { Export } from '@vicons/carbon'
import { getPayStatisticsColumns } from '../config'
import { exportStatisticsList, getStatisticsList } from '@/services/pay-manage'
import { useMyTable } from '@/hooks'
import { getCurrentPartyOrganizationByUser } from '@/services/data-permission'
import { downloadArrayBuffer } from '@/utils/downloader'

const payYear = ref(new Date().getTime())

const filterReactive = ref({
  payYear: new Date().getFullYear(),
  unitId: '',
  userName: '',
})

// 组织数据
const organizeData = ref([])

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getStatisticsList, filterReactive, {
  // batchDeleteTable: true,
  // delApi: delTalkAboutTableListItem,
})

// 修改和删除按钮渲染
const tableColumns = getPayStatisticsColumns()

function handleYearChange(value: string | number) {
  const year = new Date(value)?.getFullYear()
  filterReactive.value.payYear = year
}

// 转化data为树状结构
function transformTreeData(data: any) {
  if (Array.isArray(data)) {
    data.forEach((item) => {
      item.key = item.id
      item.label = item.name
      item.value = item.id

      if (item.children) {
        item.children.forEach((child: any) => {
          transformTreeData(child)
        })
      }
    })
  } else {
    data.key = data.id
    data.label = data.name
    data.value = data.id

    if (data.children) {
      data.children.forEach((child: any) => {
        transformTreeData(child)
      })
    }
  }
  return data
}

/** 获取列表 */
const getOrganizeList = () => {
  getCurrentPartyOrganizationByUser().then((res: any) => {
    organizeData.value = res || []
  })
}

function handleExport() {
  loading.value = true
  exportStatisticsList({ ...filterReactive.value })
    .then((res) => {
      downloadArrayBuffer(
        res,
        '订单导出.xlsx',
        'application/octet-stream; charset=UTF-8',
      )
    })
    .finally(() => {
      loading.value = false
    })
}

watch(filterReactive.value, () => {
  loadData()
})

onMounted(() => {
  loadData()
  getOrganizeList()
})
</script>

<style lang="scss" scoped>
.operate-btn {
  width: 62px;
  height: 28px;
  background: #ffffff;
  border-radius: 3px;
  border: 1px solid #d8d9da;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
}

:deep(.n-date-picker) {
  width: 115px !important;
  height: 28px;
}

:deep(.filter-operate .n-input) {
  width: 220px !important;
  height: 28px;
}

.filter-operate {
  @apply flex gap-x-1;
  width: 522px;
}
</style>
