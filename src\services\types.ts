/** 响应体类型 */
export interface IResponse<T = any> {
  code: 0 | 1
  msg: string | null
  data: T
}

/** 分页响应类型 */
export interface PaginationRes<T = any> {
  records: T[]
  total: number
  size: number
  current: number
  additionalData: any
}

/** 分页请求参数 */
export interface PaginationReq {
  pageNum: number
  pageSize: number
}

/** 分页请求参数 */
export interface PaginationReq2 {
  current: number
  size: number
}
/** 树节点类型 */
export interface ITreeNode {
  nodeId: number
  nodeName: string
  children: ITreeNode[] | null
  pid: number
  sort?: number | null
}
/** 文件上传 */
export interface uploadFileItem {
  id?: string
  fileId: string
  bucketName: string
  url: string
  fileName: string
}

/** 侧边分类树自定义字段 */
export interface ICustomCategoryTree {
  key: string
  label: string
  editing: boolean
  renderId: string
  parentId: string
  canUp: boolean
  canDown: boolean
  isChild: boolean
}
