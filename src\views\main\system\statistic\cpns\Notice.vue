<script lang="ts" setup>
import { NButton } from 'naive-ui'
import { Export } from '@vicons/carbon'
import LeftMenu from './LeftMenu.vue'
import { getTableColumns } from './config'
import {
  exportLoginStatisticList,
  getLoginStatisticList,
  getLoginStatisticTips,
} from '@/services/system/statistic'
import type { LoginStatisticListItem } from '@/services/system/statistic/types'
import { downloadArrayBuffer } from '@/utils/downloader'
import { formatTimeStamp } from '@/utils/format'
// 部门id
const deptId = ref('')
const size = ref(10)
const current = ref(1)
const total = ref(0)
const loading = ref(false)
const tableData = ref<LoginStatisticListItem[]>([])
const statisticTipsData = reactive<{ data: any }>({ data: {} })

const filterRef = ref({
  deptId: deptId.value || '',
  phone: '',
  userName: '',
})

const getCurrentDeptId = (id: string) => {
  deptId.value = id
  current.value = 1
  size.value = 10
  filterRef.value.phone = ''
  filterRef.value.userName = ''
}

const getLoginStatisticListFunc = () => {
  const params = {
    size: size.value,
    current: current.value,
    deptId: deptId.value,
    phone: filterRef.value.phone,
    userName: filterRef.value.userName,
  }
  getLoginStatisticList(params)
    .then((res) => {
      total.value = res.total
      tableData.value = res.records
    })
    .finally(() => {
      loading.value = false
    })
}

const getLoginStatisticTipsFunc = () => {
  const params = {
    deptId: deptId.value,
    phone: filterRef.value.phone,
    userName: filterRef.value.userName,
  }
  return getLoginStatisticTips(params).then((res) => {
    statisticTipsData.data = res
  })
}

const exportFile = () => {
  loading.value = true
  const params = {
    deptId: deptId.value,
    phone: filterRef.value.phone,
    userName: filterRef.value.userName,
  }
  exportLoginStatisticList(params)
    .then((res) => {
      downloadArrayBuffer(
        res,
        `登录统计-${formatTimeStamp(Date.now(), 'YYYYMMDDHHmmss')}`,
      )
    })
    .finally(() => {
      loading.value = false
    })
}

const loadData = () => {
  loading.value = true
  Promise.all([
    getLoginStatisticListFunc(),
    getLoginStatisticTipsFunc(),
  ]).finally(() => {
    loading.value = false
  })
}

const tableColumns = getTableColumns()

watch(
  () => [deptId.value, filterRef.value.phone, filterRef.value.userName],
  () => {
    loadData()
  },
)
watch(
  () => [current.value, size.value],
  () => {
    loadData()
  },
)
</script>
<template>
  <div>
    <LeftMenu title="登录统计" @get-current-dept-id="getCurrentDeptId" />
    <div class="pl-[280px] pt-[25px] pr-[30px]">
      <table-container
        v-model:page="current"
        v-model:page-size="size"
        show-tabs
        title="登录统计"
        :loading="loading"
        :show-toolbar="true"
        :show-add="false"
        custom-toolbar
        :table-columns="tableColumns"
        :table-data="tableData"
        :total="total"
      >
        <template #tabs>
          <div
            class="h-[60px] border-[#fee999] mb-[20px] flex flex-row items-center justify-start box-border pl-[20px] bg-[#fefce8]"
          >
            当前共包含 {{ statisticTipsData.data.userTotal }}个用户，总登录
            {{ statisticTipsData.data.loginTotal }}人，总登录
            {{ statisticTipsData.data.countTotal }} 人次
          </div>
        </template>
        <template #btns>
          <n-button size="small" type="primary" @click="exportFile">
            <template #icon>
              <n-icon style="margin-right: 6px" size="14">
                <export />
              </n-icon>
            </template>
            导出
          </n-button>
        </template>
        <template #filters>
          <n-input
            v-model:value="filterRef.userName"
            style="width: 200px"
            size="small"
            placeholder="请输入姓名"
            clearable
          />
          <n-input
            v-model:value="filterRef.phone"
            style="width: 200px"
            size="small"
            placeholder="请输入手机号"
            clearable
          />
        </template>
      </table-container>
    </div>
  </div>
</template>
<style lang="scss" scoped></style>
