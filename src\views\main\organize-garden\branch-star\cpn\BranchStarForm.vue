<script setup lang="ts">
import { formRules } from './config'
import {
  getOrganizeStarResultDetail,
  postOrganizeStarResult,
} from '@/services/organize-garden'
interface Props {
  id: string
  deptId: string
  enterType: string
}

interface FormData {
  id: string
  deptId: string
  year: number | null | string
  star: number | null
}

const props = defineProps<Props>()

const branchStarFormRef = ref()

const formData = reactive<{ data: FormData }>({
  data: {
    id: '',
    deptId: props.deptId,
    year: null,
    star: null,
  },
})

const isViewModel = computed(() => {
  if (props.enterType === 'view') {
    return true
  }
  else {
    return false
  }
})

const getNewsDetailFn = async() => {
  const res = await getOrganizeStarResultDetail(props.id)
  formData.data.id = res.id
  // 确保year是字符串格式
  formData.data.year = String(res.year)
  formData.data.star = res.star
}
onBeforeMount(() => {
  if (
    (props.enterType === 'modify' || props.enterType === 'view')
    && props.id
  ) {
    getNewsDetailFn()
  }
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()
async function validateAndSave() {
  branchStarFormRef.value?.validate((errors: any) => {
    if (!errors) {
      postOrganizeStarResult({ ...formData.data }).then((res) => {
        window.$message.success('保存成功')
        emits('saved')
      })
    }
  })
}
// 重置表单
function resetForm() {
  branchStarFormRef.value?.restoreValidation()
}

// 年份禁用逻辑：禁用当前年份之后的年份
const handleYearDisable = (timestamp: number): boolean => {
  const currentYear = new Date().getFullYear()
  const selectedYear = new Date(timestamp).getFullYear()

  // 如果选择的年份大于当前年份，则禁用
  return selectedYear > currentYear
}

defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <div>
    <n-form
      ref="branchStarFormRef"
      :model="formData.data"
      :rules="formRules"
      require-mark-placement="left"
      label-placement="left"
      label-width="90px"
      :disabled="isViewModel"
    >
      <n-form-item label="请选择年份" path="year">
        <n-date-picker
          v-model:formatted-value="formData.data.year"
          style="width: 100%"
          placeholder="请选择年份"
          clearable
          type="year"
          :is-date-disabled="handleYearDisable"
          @update:formatted-value="
            (v:any) => (formData.data.year = v)
          "
        />
      </n-form-item>
      <n-form-item label="请选择星级" path="star">
        <n-rate
          v-model:value="formData.data.star"
          :read-only="isViewModel"
          @update:value="
            (v:any) => (formData.data.star = v)
          "
        />
      </n-form-item>
    </n-form>
  </div>
</template>

<style scoped lang="scss"></style>
