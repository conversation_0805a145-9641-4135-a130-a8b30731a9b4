<script setup lang="ts">
defineProps<{ close: () => void }>()
const time = ref()
</script>
<template>
  <div>
    <p
      class="bg-[#FFFAE1] rounded-[3px] text-[12px] font-[400] py-[11px] pl-[13px] text-[#7C5125] border-[1px] border-[#F5DD8C]"
    >
      提示：根据固定模板，直接将党员每月的党费导入，选择当前方式，不按比例核算党费。
    </p>
    <div
      class="bg-[#F5F6F8] border-[#DCDFE6] border-[1px] mt-[16px] py-[25px] px-[23px] border-dashed"
    >
      <div class="flex text-[12px] font-[400] leading-[16px]">
        <div class="flex items-center">
          <span>选择月份：</span>
          <n-date-picker
            v-model:formatted-value="time"
            type="month"
            clearable
            style="width: 130px"
          />
        </div>
        <div class="ml-[14px] flex items-center">
          <span>选择组织：</span>
          <n-select placeholder="请选择所在组织" style="width: 210px" />
        </div>
      </div>

      <div
        class="mt-[34px] text-[12px] leading-[17px] text-[#333333] font-[600]"
      >
        <div>
          <span class="text-[#EE0000] text-[14px] leading-[19px] mr-[21px]">1</span><span>直接导入固定党费，下载</span><span class="text-[#006FFF] font-[500] mx-[10px] cursor-pointer">全部人员模板</span><span class="text-[#006FFF] font-[500] cursor-pointer">部分人员模板</span>
        </div>
        <div class="w-[1px] h-[38px] bg-[#D0D5D8] ml-[3px] my-[2px]" />
        <div>
          <span class="text-[#EE0000] text-[14px] leading-[19px] mr-[21px]">2</span><span>上传填写好的应缴党费模板并导入</span>
        </div>
      </div>
      <div
        class="flex items-center font-[400] leading-[16px] text-[12px] mt-[32px]"
      >
        <span>选择文件：</span>
        <n-select placeholder="请选择上传文件" style="width: 210px" />
      </div>
    </div>
    <div class="flex justify-end mt-[35px]">
      <n-button style="width: 80px; height: 30px" type="primary">
        提交
      </n-button>
      <n-button
        style="width: 80px; height: 30px; margin-left: 12px"
        @click="close"
      >
        取消
      </n-button>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
