import { type DataTableColumns, NEllipsis, NIcon } from 'naive-ui'
import type { VNodeChild } from 'vue'
import { EyeOffOutline, EyeOutline } from '@vicons/ionicons5'
import type { PartyMeetingMemberItem } from '@/services/affairs/party-meeting/types'
import { MEETING_ATTEND_STATUS, getDictLabelByValue } from '@/store/dict'

const showLeaveReason = ref(false)
export function getTableColumns(
  optionColumnRenderer: (row: PartyMeetingMemberItem) => VNodeChild,
): DataTableColumns<PartyMeetingMemberItem> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '10%',
      render: (_, i) => i + 1,
    },
    {
      width: '10%',
      key: 'trueName',
      title: '姓名',
    },
    {
      key: 'userStatus',
      title: '参会情况',
      width: '40%',
      render: (row) => {
        const status = getDictLabelByValue(
          MEETING_ATTEND_STATUS,
          row.userStatus,
        )

        if (status === '未签到' || status === '已签到') {
          return status
        } else {
          return h(
            'div',
            {
              class: 'flex items-center justify-start gap-x-[10px]',
            },
            {
              default: () => [
                h(
                  'span',
                  {
                    class: 'flex items-center justify-center',
                  },
                  {
                    default: () => status,
                  },
                ),
                h(
                  NIcon,
                  {
                    onClick: () =>
                      (showLeaveReason.value = !showLeaveReason.value),
                  },
                  {
                    default: () =>
                      showLeaveReason.value ? h(EyeOutline) : h(EyeOffOutline),
                  },
                ),
                showLeaveReason.value
                  && h(
                    NEllipsis,
                    {
                      style: {
                        maxLength: '240px',
                      },
                    },
                    { default: () => `(${row.leaveReason})` },
                  ),
              ],
            },
          )
        }
      },
    },
    {
      key: 'signedType',
      width: '15%',
      title: '签到方式',
      render: row => row.signedType ?? '--',
    },
    {
      key: 'signedTime',
      width: '15%',
      title: '签到时间',
      render: row => row.signedTime ?? '--',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '10%',
      render: row => optionColumnRenderer(row),
    },
  ]
}
