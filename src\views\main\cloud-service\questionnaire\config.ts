import type { DataTableColumns, FormRules } from 'naive-ui'
import type { VNodeChild } from 'vue'
import { QUESTIONNAIRESTATUS } from '@/constant'

export const formRules: FormRules = {
  title: [
    {
      required: true,
      message: '问卷名称不能为空',
      trigger: 'input',
    },
  ],
  description: [
    {
      required: true,
      message: '问卷说明不能为空',
      trigger: 'input',
    },
  ],
  beginTime: [
    {
      required: true,
      message: '问卷开始时间不能为空',
      trigger: 'change',
    },
  ],
  endTime: [
    {
      required: true,
      message: '问卷结束时间不能为空',
      trigger: 'change',
    },
  ],
  orgidList: [
    {
      required: true,
      message: '组织不能为空',
      trigger: 'change',
      type: 'array',
    },
  ],
  deptId: [
    {
      required: true,
      message: '组织不能为空',
      trigger: 'change',
      type: 'string',
    },
  ],
  coverUrl: [
    {
      required: true,
      message: '问卷图片不能为空',
      trigger: 'change',
    },
  ],
}

export function createColumn(
  operationRender: (row: any) => VNodeChild,
): DataTableColumns {
  return [
    {
      key: 'blank',
      width: 20,
    },
    {
      title: '问卷名称',
      key: 'title',
      width: '20%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      title: '问卷说明',
      key: 'description',
      width: '20%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      title: '开始时间',
      key: 'beginTime',
      width: '10%',
    },
    {
      title: '结束时间',
      key: 'endTime',
      width: '10%',
    },
    {
      title: '状态',
      key: 'questionnaireStatus',
      width: '8%',
      render: row =>
        QUESTIONNAIRESTATUS[
          row.questionnaireStatus as keyof typeof QUESTIONNAIRESTATUS
        ],
    },
    {
      title: '参与人数统计',
      key: 'participantsNum',
      width: '7%',
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: '10%',
    },
    {
      title: '操作',
      key: 'operation',
      width: '15%',
      render: row => operationRender(row),
    },
  ]
}
