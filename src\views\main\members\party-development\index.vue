<script lang="ts" setup>
import { NButton } from 'naive-ui'
import { Flowchart24Regular } from '@vicons/fluent'
import { PlusRound } from '@vicons/material'
import { getTableColumns } from './config'
import DevelopForm from './cpn/DevelopForm.vue'
import DevDraw from './cpn/dev/DevDraw.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import CustomDialog from '@/components/CustomDialog.vue'
import {
  deletePartyMember,
  getPartyDevelopmentList,
  getStageListAndBaseInfo,
  putCancelCurrentStage,
} from '@/services/structure/party-development'
import { DEVELOPMENT_STAGE } from '@/store/dict'
import partyDevelopmentIMG from '@/assets/image/partyDevelopmentImg.png'
import { useOrganizationListOptionsNew } from '@/hooks/use-select-options'

const filterReactive = ref({
  name: null,
  sort: null,
  deptId: '',
})
// 有接口后添加：loading,tableData
const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getPartyDevelopmentList, filterReactive, {
  batchDeleteTable: false,
  delApi: deletePartyMember,
})

const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('入党申请', handelConfirmEdit)

const { organizationListTree } = useOrganizationListOptionsNew()

// 新增/编辑党建清单抽屉
const idEditRef = ref()
const addPartyMemberFormRef = ref()
const route = useRoute()
const currentTypeRef = ref<'develop' | 'activeMember'>('develop')

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}

/** 确定保存 */
function handelConfirmEdit() {
  addPartyMemberFormRef.value?.validateAndSave()
}

watch(showEditRef, (newV) => {
  if (!newV) {
    addPartyMemberFormRef.value?.resetForm()
  }
})

/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

// 发展
const devFormRef = ref()
const stageRef = ref()
const showDevRef = ref(false)

const activeTab = ref()
const currentTab = ref()
const handleCurrentTab = (v: string) => {
  currentTab.value = v
  activeTab.value = v
}
const handelChangeTab = (v: string) => {
  activeTab.value = v
}

// const activeAndCurrent = computed(() => activeTab.value === currentTab.value)

// 点击保存按钮
function handleDevClickConfirm() {
  devFormRef.value?.validateAndSave()
}

// 保存成功
function handleDevSaved() {
  showDevRef.value = false
  loadData()
}

// 点击取消按钮
function handleDevClickCancel() {
  showDevRef.value = false
}

/** 撤销此阶段 */
function handleCancelCurrentStage(id: string) {
  window.$dialog.warning({
    title: '提示',
    content: '该操作无法撤回，确认撤销该发展阶段？',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      const params = {
        id,
      }
      putCancelCurrentStage!(params)
        .then(() => {
          window.$message.success('撤销成功')
        })
        .finally(loadData)
    },
  })
}

const handleUpdateValue = (v: string) => {
  filterReactive.value.deptId = v
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          idEditRef.value = row.id
          stageRef.value = row.sort
          currentTypeRef.value = 'develop'
          showDevRef.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '发展',
      },
    ),
    Number(row.sort) === 0
      && h(DeleteButton, {
        style: {
          marginRight: '10px',
        },
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    Number(row.sort) !== 0
      ? h(
        NButton,
        {
          onClick: () => handleCancelCurrentStage(String(row.id)),
          type: 'primary',
          text: true,
        },
        {
          default: () => '撤销此阶段',
        },
      )
      : '',
  ]
})

// 流程查看
const showFlow = ref(false)
const handelImportFn = () => {
  showFlow.value = true
}

onMounted(() => {
  if (route.query.id) {
    currentTypeRef.value = 'activeMember'
    getStageListAndBaseInfo(String(route.query.id)).then((res) => {
      stageRef.value = res.sort - 1
      idEditRef.value = route.query.id
      showDevRef.value = true
    })
  }
  loadData()
})
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    :show-toolbar="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    custom-toolbar
    title="党员发展"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <!-- <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button> -->
      <n-button size="small" @click="handelImportFn">
        <template #icon>
          <n-icon>
            <Flowchart24Regular />
          </n-icon>
        </template>
        流程查看
      </n-button>
    </template>
    <template #filters>
      <n-tree-select
        v-model:value="filterReactive.deptId"
        :options="organizationListTree"
        :show-path="false"
        check-strategy="all"
        children-field="children"
        clearable
        filterable
        key-field="deptId"
        label-field="name"
        placeholder="请选择接收党组织"
        size="small"
        style="width: 200px"
        value-field="deptId"
        @update:value="handleUpdateValue"
      />
      <n-select
        v-model:value="filterReactive.sort"
        :options="DEVELOPMENT_STAGE"
        clearable
        placeholder="请选择发展阶段"
        size="small"
        style="width: 200px"
      />
      <n-input
        v-model:value="filterReactive.name"
        clearable
        placeholder="请输入姓名"
        size="small"
        style="width: 200px"
      />
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :mask-closable="false" :width="700">
    <n-drawer-content :title="drawerTitle" closable>
      <develop-form
        :id="idEditRef"
        ref="addPartyMemberFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            style="width: 80px"
            type="primary"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>

  <n-drawer v-model:show="showDevRef" :mask-closable="false" :width="860">
    <n-drawer-content closable title="发展党员">
      <dev-draw
        :id="idEditRef"
        ref="devFormRef"
        :current-stage="stageRef"
        :current-type="currentTypeRef"
        @saved="handleDevSaved"
        @tab-change="handelChangeTab"
        @current-tab="handleCurrentTab"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            style="width: 80px"
            type="primary"
            @click="handleDevClickConfirm"
          >
            保存
            <!-- {{ activeTab === '预备党员转正' ? '保存' : '保存并进入下一阶段' }} -->
          </n-button>

          <!-- <n-button
            v-else
            type="primary"
            style="width: 80px"
            @click="handleDevClickConfirm"
          >
            保存信息
          </n-button> -->

          <n-button style="width: 80px" @click="handleDevClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>

  <custom-dialog
    v-model:show="showFlow"
    :show-action="false"
    title="流程查看"
    width="1200px"
  >
    <div class="p-[20px]">
      <n-image :src="partyDevelopmentIMG" style="height: 617px" />
    </div>
  </custom-dialog>
</template>
<style lang="scss" scoped></style>
