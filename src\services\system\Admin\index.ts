import type { DictItem, UserItem, UserItem2 } from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq2, PaginationRes } from '@/services/types'

/** 获取角色列表 */
export function getUserList(
  params: { username?: string; nickName?: string } & PaginationReq2,
) {
  return commonReq.get<PaginationRes<UserItem>>({
    url: '/upms/admin-user/page',
    params,
  })
}
/** 添加角色 */
export function addUser(data: UserItem2) {
  return commonReq.post({
    url: '/upms/admin-user',
    data,
  })
}

/** 编辑角色 */
export function editorUser(data: UserItem2) {
  return commonReq.put({
    url: '/upms/admin-user',
    data,
  })
}

/** 删除管理员 */
export function delUser(data: { id: string }) {
  return commonReq.delete({
    url: `/upms/admin-user/${data.id}`,
  })
}

/** 重置管理员密码 */
export function resetUserPwd(data: { id: string }) {
  return commonReq.put({
    url: `/upms/admin-user/${data.id}`,
  })
}

/** 管辖组织列表 */
export function getOrganizationTree() {
  return commonReq.get({
    url: '/org-construction/organization/tree',
  })
}

/** 管辖组织列表（新） */
export function getOrganizationTreeByUser() {
  return commonReq.get({
    url: '/org-construction/orgconstruction_change/cur_user_manage_depts',
  })
}

export function getDicts(type: String) {
  return commonReq.get({
    url: `/upms/dict/type/${type}`,
    method: 'get',
  })
}

export function delDicts(ids: Object) {
  return commonReq.delete({
    url: '/upms/dict',
    data: ids,
  })
}

export function getDictList(query: any) {
  return commonReq.get({
    url: '/upms/dict/list',
    method: 'get',
    params: query,
  })
}

export function refreshDictCache() {
  return commonReq.put({
    url: '/upms/dict/sync',
    method: 'put',
  })
}

export function addDict(obj: any) {
  return commonReq.post({
    url: '/upms/dict',
    method: 'post',
    data: obj,
  })
}

export function getDict(id: string) {
  return commonReq.get({
    url: `/upms/dict/details/${id}`,
    method: 'get',
  })
}

export function putDict(obj: any) {
  return commonReq.put({
    url: '/upms/dict',
    method: 'put',
    data: obj,
  })
}

export function validateDictType(
  rule: any,
  value: any,
  callback: any,
  isEdit: boolean,
) {
  if (isEdit) {
    return callback()
  }

  getDictDetails({ dictType: value }).then((response) => {
    const result = response.data
    if (result !== null) {
      callback(new Error('字典类型已经存在'))
    }
    else {
      callback()
    }
  })
}

export function getDictDetails(obj: object) {
  return commonReq.get({
    url: '/upms/dict/details',
    method: 'get',
    params: obj,
  })
}

export function delItemDict(id: string) {
  return commonReq.delete({
    url: `/upms/dict/item/${id}`,
    method: 'delete',
  })
}

export function fetchItemDictList(query: any) {
  return commonReq.get<PaginationRes<DictItem>>({
    url: '/upms/dict/item/page',
    method: 'get',
    params: query,
  })
}

export function getItemDict(id: string) {
  return commonReq.get({
    url: `/upms/dict/item/details/${id}`,
    method: 'get',
  })
}

export function addItemDict(obj: any) {
  return commonReq.post({
    url: '/upms/dict/item',
    method: 'post',
    data: obj,
  })
}

export function putItemDict(obj: any) {
  return commonReq.put({
    url: '/upms/dict/item',
    method: 'put',
    data: obj,
  })
}

export function validateDictItemLabel(
  rule: any,
  value: any,
  callback: any,
  type: string,
  isEdit: boolean,
) {
  if (isEdit) {
    return callback()
  }

  getItemDictDetails({ dictType: type, label: value }).then((response) => {
    const result = response.data
    if (result !== null) {
      callback(new Error('标签已经存在'))
    }
    else {
      callback()
    }
  })
}

export function getItemDictDetails(obj: object) {
  return commonReq.get({
    url: '/upms/dict/item/details',
    method: 'get',
    params: obj,
  })
}

/** 管辖组织列表（新） */
// export function getOrganizationTreeByUser() {
//   return commonReq.get({
//     url: '/org-construction/orgconstruction_change/cur_user_manage_depts',
//   })
// }
