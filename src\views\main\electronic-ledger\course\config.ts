import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { VNodeChild } from 'vue'
import type { ICarousel } from '@/services/run/carousel/types'

export function getTableColumns(
  optionColumnRenderer: (row: ICarousel) => VNodeChild,
): TableColumns<ICarousel> {
  return [
    {
      title: '序号',
      key: 'id',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      title: '主题',
      key: 'title',
      width: 280,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '台账时间',
      key: 'syncTime',
    },
    {
      title: '发布者',
      key: 'createBy',
    },
    {
      title: '组织名称',
      key: 'orgName',
    },
    {
      title: '操作',
      key: 'action',
      align: 'left',
      width: 120,
      render: optionColumnRenderer,
    },
  ]
}
