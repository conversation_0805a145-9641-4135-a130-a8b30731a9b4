<script lang="ts" setup>
import { DownloadRound } from '@vicons/material'
import { NButton, type TreeSelectOption } from 'naive-ui'
import { getPartyDayActivityTableColumns } from './config'
import { useRecordBookStore } from '@/store/recordBook'
import { getPartyDayActivityList } from '@/services/recordBookMaintenance'
import { useMyTable } from '@/hooks'

const recordBookStore = useRecordBookStore()

const router = useRouter()

const fileLoading = ref<boolean>(false)

const transferTimeSort = ref<'' | '0' | '1'>('')
const currentDownloadFileType = ref<'word' | 'pdf'>('word')
const showPopConfirm = ref<boolean>(false)

// 筛选项：组织id和年份
const filterRef = ref({
  deptId: '',
  year: '',
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(getPartyDayActivityList, filterRef, {
  batchDeleteTable: true,

})

const calcOrganizationListTree = computed(() => {
  return recordBookStore.getOrganizationListTreeList
})
const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})

// 获取当前年份
const currentYearOptions = computed(() => {
  return recordBookStore.getYearOptions
})

const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

const tableColumns: any = getPartyDayActivityTableColumns((row) => {
  return [
    h(
      'div',
      {
        class: 'flex flex-row gap-x-[10px]',
      },
      {
        default: () => [
          h(
            NButton,
            {
              text: true,
              color: '#AC241D',
              onClick: () => {
                router.push({
                  name: 'meetingManage',
                  query: {
                    id: String(row.id),
                    status: row.meetingStatus,
                  },
                })
              },
            },
            {
              default: () => '查看',
            },
          ),
        ],
      },
    ),
  ]
})

function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  currentPage.value = 1
  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  currentPage.value = 1

  recordBookStore.setCurrentSelectedYear(option.value as string)
}

function handleDownLoadFile() {
  fileLoading.value = true
  recordBookStore.fetchDownLoadFile(
    {
      deptId: filterRef.value.deptId,
      year: filterRef.value.year!,
      type: currentDownloadFileType.value,
    },
    () => {
      fileLoading.value = false
    },
  )
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleCancelShowPopConfirm() {
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleUpdateSorter({
  columnKey,
  order,
}: {
  columnKey: string
  order: 'ascend' | 'descend' | false
}) {
  if (columnKey === 'transferTime') {
    if (order === 'descend') {
      transferTimeSort.value = '0'
    }
    else if (order === 'ascend') {
      transferTimeSort.value = '1'
    }
    else {
      transferTimeSort.value = ''
    }
  }
  loadData()
}

// function loadData() {
//   if (!selectedDeptId.value || !selectedYear.value) {
//     return
//   }
//   loading.value = true
//   getTransferList({
//     pageNum: pageNum.value,
//     pageSize: pageSize.value,
//     deptId: selectedDeptId.value!,
//     year: selectedYear.value!,
//     transferTimeSort: transferTimeSort.value as string,
//   })
//     .then((res) => {
//       total.value = res.total || 0
//       tableData.value = res.records || []
//     })
//     .finally(() => {
//       loading.value = false
//     })
// }

onMounted(() => {
  filterRef.value.deptId = currentSelectedDept.value.deptId
  filterRef.value.year = currentYear.value
})
</script>

<template>
  <div>
    <table-container
      v-model:page="currentPage"
      v-model:page-size="pageSize"
      :loading="loading"
      :show-delete="false"
      :show-pagination="true"
      :show-toolbar="false"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      custom-toolbar
      default-expand-all
      title="主题党日活动情况登记"
      @update-page="onUpdatePage"
      @update-page-size="onUpdatePageSize"
    >
      <template #filters>
        <div class="">
          <n-popconfirm
            :show="showPopConfirm"
            :show-icon="false"
            placement="left"
          >
            <template #trigger>
              <n-button
                :loading="fileLoading"
                @click="showPopConfirm = !showPopConfirm"
              >
                <template #icon>
                  <n-icon size="16">
                    <DownloadRound />
                  </n-icon>
                </template>
                下载
              </n-button>
            </template>
            <div class="py-[10px]">
              <n-radio-group v-model:value="currentDownloadFileType">
                <n-radio
                  v-for="(fileType, fileTypeIndex) in [
                    { label: 'word', value: 'word' },
                    { label: 'pdf', value: 'pdf' },
                  ]"
                  :key="fileTypeIndex"
                  :value="fileType.value"
                >
                  {{ fileType.label }}
                </n-radio>
              </n-radio-group>
            </div>

            <template #action>
              <div class="w-[100%] flex flex-row justify-center items-center">
                <n-button
                  size="small"
                  type="primary"
                  @click="handleDownLoadFile()"
                >
                  确定
                </n-button>
                <n-button size="small" @click="handleCancelShowPopConfirm">
                  取消
                </n-button>
              </div>
            </template>
          </n-popconfirm>
        </div>
      </template>
      <template #btns>
        <div class="flex flex-row items-center justify-between gap-[10px]">
          <n-tree-select
            v-model:value="filterRef.deptId"
            :options="calcOrganizationListTree"
            :show-path="false"
            check-strategy="all"
            children-field="children"
            filterable
            key-field="id"
            label-field="name"
            placeholder="请选择所属党组织"
            style="width: 400px"
            value-field="id"
            @update:value="handleUpdateTreeSelectedValue"
          />
          <n-select
            v-model:value="filterRef.year"
            :options="currentYearOptions"
            placeholder="请选择年份"
            size="medium"
            style="width: 200px"
            @update:value="handleUpdateYearSelectedValue"
          />
        </div>
      </template>
    </table-container>
  </div>
</template>

<style lang="scss" scoped></style>
