<script setup lang="ts">
import { NButton } from 'naive-ui'
import { getTableColumns } from './config'
import HistoryForm from './cpn/HistoryForm.vue'
import { useMyTable } from '@/hooks'
import { useOrganizationListOptionsNew } from '@/hooks/use-select-options'
import ifHasPermi from '@/directive/permission/ifHasPermi'
import {
  deleteUsers,
  getHistoryUserList,
  putRecover,
} from '@/services/members/historical'

const filterReactive = ref<{ trueName: string; deptId: string | null }>({
  trueName: '',
  deptId: null,
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getHistoryUserList, filterReactive, {
  batchDeleteTable: false,
  delApi: deleteUsers,
})
const { organizationListTree } = useOrganizationListOptionsNew()
const handleUpdateValue = (v: string) => {
  filterReactive.value.deptId = v
}

const historyFormRef = ref()
const showHistoryRef = ref(false)
const idEditRef = ref()

function handleRecover(id: string) {
  window.$dialog.warning({
    title: '提示',
    content: '确定恢复该党员吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      putRecover(id).then(() => {
        window.$message.success('已恢复')
        loadData()
      })
    },
  })
}
// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          idEditRef.value = row.id
          showHistoryRef.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
          display: ifHasPermi('portal_user_edit_btn'),
        },
      },
      {
        default: () => '查看',
      },
    ),
    h(
      NButton,
      {
        onClick: () => handleRecover(String(row.id)),
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
          display: ifHasPermi('portal_user_edit_btn'),
        },
      },
      {
        default: () => '恢复',
      },
    ),
  ]
})

onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="历史党员管理"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :loading="loading"
    :show-delete="false"
    :checked-row-keys="checkedRowKeys"
    default-expand-all
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <div
        class="w-full flex flex-row justify-start items-center gap-[10px]"
      >
        <!-- <n-cascader
          v-model:value="filterReactive.deptId"
          size="small"
          :options="organizationListTree"
          value-field="id"
          label-field="name"
          children-field="children"
          check-strategy="all"
          :show-path="false"
          clearable
          placeholder="请选择所属党组织"
          @update:value="handleUpdateValue"
        /> -->
        <!-- <n-tree-select
          v-model:value="filterReactive.deptId"
          :options="organizationListTree"
          value-field="id"
          label-field="name"
          key-field="id"
          children-field="children"
          check-strategy="all"
          placeholder="请选择所属党组织"
          :show-path="false"
          clearable
          filterable
          size="small"
          @update:value="handleUpdateValue"
        /> -->

        <n-tree-select
          v-model:value="filterReactive.deptId"
          style="width: 400px"
          :options="organizationListTree"
          :show-path="false"
          check-strategy="all"
          children-field="children"
          clearable
          filterable
          key-field="deptId"
          label-field="name"
          placeholder="请选择所属党组织"
          size="small"
          value-field="deptId"
          @update:value="handleUpdateValue"
        />
        <n-input
          v-model:value="filterReactive.trueName"
          style="width: 200px"
          size="small"
          placeholder="请输入姓名"
          clearable
        />
      </div>
    </template>
  </table-container>
  <n-drawer v-model:show="showHistoryRef" :width="700" :mask-closable="false">
    <n-drawer-content title="查看历史党员" closable>
      <history-form :id="idEditRef" ref="historyFormRef" />
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
