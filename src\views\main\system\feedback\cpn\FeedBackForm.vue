<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { formRules, imgSizes } from './config'
import type { ICarousel } from '@/services/run/carousel/types'
import {
  getCarouselDetail,
  // postCarousel,
} from '@/services/run/carousel/carousel'

interface Props {
  type?: string
  id?: number
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: 0,
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

// 移动端模块选择列表

const formDataReactive = reactive<ICarousel>({
  resourceName: '', // 图片名称
  layout: null, // 图片位置
  sortOrder: null, // 排序
  jumpUrl: null, // 跳转地址
  resourceStatus: 1, // 是否显示 1显示 0不显示
  file: null,
  jumpType: null, // 跳转类型
})
const imgSizeReactive = reactive<{ width: number; height: number }>({
  width: 0,
  height: 0,
})
const formRef = ref<InstanceType<typeof NForm>>()
const oldImgUrlRef = ref('')

onBeforeMount(() => {
  if (props.type === 'modify' && props.id) {
    getCarouselDetail(props.id).then((res) => {
      for (const k in formDataReactive) {
        if (k === 'jumpUrl') {
          formDataReactive[k] = Number(res[k])
          continue
        }
        formDataReactive[k] = res[k]
      }
      formDataReactive.id = props.id
      handleLayoutChange(res.layout!)
      oldImgUrlRef.value = res.pictureUrl ?? ''
    })
  }
})
// 位置改变
function handleLayoutChange(v: number) {
  const { width, height } = imgSizes.find(item => item.id === v)!
  imgSizeReactive.width = width
  imgSizeReactive.height = height
}
// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      const formData = new FormData()
      for (const k in formDataReactive) {
        formData.append(k, formDataReactive[k])
      }
      if (formDataReactive.jumpType === 1) {
        formData.append('jumpUrl', '')
      }
      // postCarousel(formData).then((res) => {
      //   if (res) {
      //     window.$message.success('保存成功')
      //     emits('saved')
      //   }
      // })
    }
  })
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="90"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-grid>
      <n-form-item-gi span="24" label="发起人：" path="resourceName">
        <n-input v-model:value="formDataReactive.resourceName" clearable />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="发起时间：" path="layout">
        <n-input v-model:value="formDataReactive.layout" clearable />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="意见标题：" path="sortOrder">
        <n-input v-model:value="formDataReactive.sortOrder" clearable />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="意见内容：" path="jumpType">
        <n-input
          v-model:value="formDataReactive.jumpType"
          type="textarea"
          :autosize="{
            minRows: 5,
            maxRows: 7,
          }"
        />
      </n-form-item-gi>
    </n-grid>
  </n-form>
</template>
<style lang="scss" scoped></style>
