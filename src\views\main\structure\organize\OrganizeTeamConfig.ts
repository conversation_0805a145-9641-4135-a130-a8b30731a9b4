import type { VNodeChild } from 'vue'
import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type {
  PartyTeamListType,
  QuotaList,
} from '@/services/structure/organize/types'
export function getTableColumns(
  operationRender: (row: PartyTeamListType) => VNodeChild,
): TableColumns<PartyTeamListType> {
  return [
    {
      type: 'selection',
      align: 'center',
    },
    {
      title: '组织名称',
      key: 'organizationName',
      width: '300px',
    },
    {
      title: '届期名称',
      key: 'appointedName',
      width: '280px',
    },
    {
      title: '届期开始时间',
      key: 'appointedStartTime',
      width: '200px',
    },
    {
      title: '届期结束时间',
      key: 'appointedEndTime',
      width: '200px',
    },
    {
      title: '状态',
      key: 'teamTransitionStatus',
      width: '100px',
      render: (row) => {
        const arr = ['--', '往届', '当届']
        return row.teamTransitionStatus
          ? h(
            'span',
            {
              class: row.teamTransitionStatus === '2' ? 'text-[red]' : '',
            },
            {
              default: () => arr[Number(row.teamTransitionStatus)],
            },
          )
          : '--'
      },
    },
    {
      title: '班子简介',
      key: 'teamIntroduction',
      minWidth: '200px',
    },
    {
      title: '操作',
      key: 'operation',
      width: '180px',
      render: operationRender,
    },
  ]
}

// 新增班子职务表格
export function getQuotaColumns(
  selectionRender: (row: QuotaList, index: number) => VNodeChild,
  operationRender: (row: QuotaList, index: number) => VNodeChild,
): TableColumns<QuotaList> {
  return [
    {
      title: '序号',
      key: 'index',
      width: 60,
      render: (_, i) => i + 1,
    },
    {
      title: '职务编号',
      key: 'jobId',
      width: 100,
    },
    {
      title: '职务名称',
      key: 'jobName',
      width: 100,
    },
    {
      title: '用户名',
      key: 'userName',
      render: selectionRender,
    },
    {
      title: '姓名',
      key: 'trueName',
      width: 80,
    },
    {
      title: '操作',
      key: '',
      render: operationRender,
      width: 60,
    },
  ]
}
