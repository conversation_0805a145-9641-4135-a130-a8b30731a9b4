<script lang="ts" setup>
import ContentBox from '../ContentBox.vue'
import BasicAttendMemberDetail from './BasicAttendMemberDetail.vue'
import { getMeetingDetail } from '@/services/affairs/party-meeting'
import type { MeetingDetailBaseInfo } from '@/services/affairs/party-meeting/types'
import defaultAvatar from '@/assets/image/avatarIcon.png'
import leave from '@/assets/image/meeting/leave.png'

interface Props {
  id: string
}

const props = defineProps<Props>()
const baseApi = ref(import.meta.env.VITE_API_BASE)
const loading = ref(false)

const meetingDetail = ref<MeetingDetailBaseInfo>()

async function loadMeetingDetail() {
  try {
    loading.value = true
    meetingDetail.value = await getMeetingDetail(String(props.id))
  }
  catch (e) {
  }
  finally {
    loading.value = false
  }
}

const formattedTime = computed(() => {
  if (!meetingDetail.value) {
    return ''
  }
  const { startTime, endTime } = meetingDetail.value
  const formattedStartTime = startTime ? startTime.slice(0, 16) : ''
  const formattedEndTime = endTime ? endTime.slice(11, 16) : ''
  return `${formattedStartTime || '未知'} ~ ${formattedEndTime || '未知'}`
})

/** 参会详情 */
const attendeeDialogRef = ref()
const attendDialogVisible = ref(false)

function handleAttendDetail() {
  attendDialogVisible.value = true
}

onMounted(() => {
  loadMeetingDetail()
})
</script>
<template>
  <content-box title="基本信息">
    <div class="flex flex-row justify-start items-start">
      <div class="meeting-info w-[120px]">
        <span class="left-span-item">会议主题：</span>
        <span class="left-span-item">发起人：</span>
        <span class="left-span-item">主持人：</span>
        <span class="left-span-item">会议时间：</span>
        <span class="left-span-item">会议地点：</span>
        <span class="left-span-item">所在组织：</span>
        <span class="left-span-item">参会人：</span>
      </div>
      <div class="meeting-info w-[80%]">
        <span class="right-span-item">{{ meetingDetail?.title || '--' }}</span>
        <span class="right-span-item">{{ meetingDetail?.user || '--' }}</span>
        <span class="right-span-item">{{ meetingDetail?.host || '--' }}</span>
        <span class="right-span-item">{{ formattedTime }}</span>
        <span class="right-span-item">{{
          meetingDetail?.meetingAddr || '--'
        }}</span>
        <span class="right-span-item">{{ meetingDetail?.org || '--' }}</span>
        <div>
          <span class="right-span-item mr-[10px]">参会人数：{{ meetingDetail?.userNumber }} 人，签到人数：{{
            meetingDetail?.signedNumber
          }}
            人，请假人数：{{ meetingDetail?.leaveNumber }} 人</span>
          <n-button text type="info" @click="handleAttendDetail">
            查看详情
          </n-button>
        </div>

        <div class="grid grid-cols-10 flex-wrap gap-[20px]">
          <div
            v-for="(item, index) in meetingDetail?.memberList"
            :key="index"
            class="flex flex-col justify-start items-center gap-y-[8px]"
          >
            <div class="relative">
              <n-avatar
                :class="{ 'grayscale-[100%]': item.userStatus === '0' }"
                :size="48"
                :src="item.avatar ? baseApi + item.avatar : defaultAvatar"
                round
              />
              <div
                v-show="item.userStatus === '2'"
                class="absolute bottom-[6px] right-0"
              >
                <img :src="leave" alt="请假图标" class="w-[15px] h-[15px]" />
              </div>
            </div>
            <span class="text-[#818181]">{{ item.trueName }}</span>
          </div>
        </div>
      </div>
    </div>
  </content-box>

  <custom-dialog
    ref="attendeeDialogRef"
    v-model:show="attendDialogVisible"
    :show-action="false"
    title="参会详情"
    width="1200px"
    @cancel="() => loadMeetingDetail()"
  >
    <basic-attend-member-detail
      ref="correlateTableRef"
      :meeting-id="props.id"
    />
  </custom-dialog>
</template>
<style lang="scss" scoped>
.meeting-info {
  @apply flex flex-col items-start;
}

.left-span-item {
  @apply inline-block py-[20px] text-[#818181];
}

.right-span-item {
  @apply inline-block py-[20px] text-[#000];
}
</style>
