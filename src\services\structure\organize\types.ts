export interface PartyTeamListType {
  id: string
  organization_id?: any
  organizationName: string
  appointedName: string
  appointedStartTime: string
  appointedEndTime: string
  teamTransitionStatus: string
  teamIntroduction: string
  quotaList?: any
}

// 新增班子数据类型
export interface QuotaList {
  id?: string
  teamTransitionId: string
  jobId: string
  jobName: string
  userId: string | null
  userName: string
  trueName: string
}

export interface PartyTeamInfoType {
  organizationId: string
  organizationName: string
  appointedName: string | null
  appointedStartTime: string | null
  appointedEndTime: string | null
  teamTransitionStatus: string
  teamIntroduction: string
  quotaList: QuotaList[]
}

// 组织下的人员类型
export interface organizationPersonType {
  userId: string
  username: string
  nickName: string
  trueName: string
  phone: string
  avatarId: string
  deptId: string
  lockFlag: string
  lastLoginTime?: any
  lastLoginIp?: any
  identityId: string
  signImgId?: any
  sex: string
  birthday: string
  edu: string
  origin: string
  address: string
  postId?: any
  departmentId: string
  partyIdentity: string
  partyIdentityName: string
  jobTime: string
  joinTime?: any
  regularTime?: any
  memberStatus: string
  loginCount: number
  employeeId?: any
  political: string
  lastLoginSystem: number
  lastLoginVersion: string
}
