import type { RouteRecordRaw } from 'vue-router'

/**
 * @description 组织架构->组织关系转换路由
 */
const organizationChangeOverRoute: RouteRecordRaw = {
  path: 'organization-change-over',
  name: 'organizationChangeOver',
  meta: {
    title: '组织关系转接',
    icon: 'organize',
    category: 'structure',
    order: 1,
  },
  component: () =>
    import('@/views/main/structure/organization-change-over/index.vue'),
}
export default organizationChangeOverRoute
