export interface UserItem {
  userId: string
  trueName: string
  phone: string
  deptName: string
  partyIdentity: string
  updateTime: string
  lockFlag: string
  isLoading?: boolean
  political?: string | null
}

export interface UserDetailItem {
  userId?: string | null
  username: string
  trueName: string
  sex: string | null
  ethnic: number | null
  customEthnic?: string | null
  edu: string | null
  jobTime: string | null
  origin: string | null
  departmentId?: string | null
  address: string
  memberStatus: string | null
  phone: string
  identityId: string
  partyIdentity: string | null
  joinTime: string | null
  regularTime: string | null
  deptId: string | null
  avatarId: string
  avatarUrl?: string
  partyMember?: string | null
  political?: string | null
  dutyName?: string | null
  workDepartment: string | null
  postCategory: string | null
  partyProcessStatus?: string | null
  partyDuty?: string | null

  oldDeptName?: string | null
  paymentTime?: string | null
  transferTime?: string | null

}

export interface ToHistoryUserDetailItem {
  userId?: string | null
  username: string
  trueName: string
  phone: string
  identityId: string
  reasonType: string | null
  reasonDescription: string
}
