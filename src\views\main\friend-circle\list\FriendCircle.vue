<script setup lang="ts">
import { NButton, NSwitch } from 'naive-ui'
import { getTableColumns } from './config'
import type { CultureShowListItem } from '@/services/run/interaction/types'
import {
  deleteInteractionSingleData,
  getCultureShowList,
} from '@/services/run/interaction'
import { useMyTable } from '@/hooks'
import DeleteButton from '@/components/DeleteButton.vue'
import Scan from '@/views/main/run/interaction/cpn/Scan.vue'
import { postPublishCarousel } from '@/services/run/carousel/carousel'
interface Props {
  secondMenuStatus: string
}
defineProps<Props>()

const filterRef = ref({
  content: null,
  username: null,
  type: 1,
})
const idRef = ref()
const showScanDialog = ref(false)
function handleScan(id: string) {
  idRef.value = id
  showScanDialog.value = true
}

const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getCultureShowList, filterRef, {
  batchDeleteTable: true,
  delApi: deleteInteractionSingleData,
})

const tableColumns = getTableColumns(
  (row: CultureShowListItem) => {
    return [
      h(
        NButton,
        {
          onClick: () => handleScan(row.id),
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '查看',
        },
      ),
      h(DeleteButton, {
        style: {
          marginRight: '10px',
        },
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    ]
  },
  (row) => {
    return h(NSwitch, {
      checkedValue: '是',
      uncheckedValue: '否',
      value: row.isRelease,
      loading: row.loading,
      onUpdateValue() {
        row.loading = true
        const params = {
          id: String(row.id),
          isRelease: row.isRelease === '是' ? 0 : 1,
        }
        postPublishCarousel(params)
          .then((res) => {
            row.isRelease = res
          })
          .catch(() => {})
          .finally(() => {
            loadData()
            row.loading = false
          })
      },
    })
  },
)

onMounted(() => {
  loadData()
})
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="朋友圈管理"
    :loading="loading"
    :show-toolbar="true"
    :show-add="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #filters>
      <n-input
        v-model:value="filterRef.username"
        style="width: 200px"
        size="small"
        placeholder="请输入姓名"
        clearable
      />
      <n-input
        v-model:value="filterRef.content"
        style="width: 200px"
        size="small"
        placeholder="请输入内容"
        clearable
      />
    </template>
  </table-container>

  <custom-dialog
    v-model:show="showScanDialog"
    :show-action="false"
    width="800px"
    title="查看"
  >
    <scan
      :id="idRef"
      :show-title="false"
      description-text="内容"
      content-text="图片 / 视频"
    />
  </custom-dialog>
</template>
<style lang="scss" scoped></style>
