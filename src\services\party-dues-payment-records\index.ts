import type { RequestTypes } from './types'
import { commonReq } from '@/services/request'

// 查询党费缴纳
export function getPayFeeList(
  params: RequestTypes.QueryPartyDuesPaymentRecordsType,
) {
  return commonReq.get<RequestTypes.QueryPartyDuesPaymentRecordsResponseType>({
    url: '/party-affairs/work-record-book/dues_pay/list',
    params,
  })
}

// 编辑党费缴纳
export function editPayFee(
  data: RequestTypes.UpdatePartyDuesPaymentRecordsType,
) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/dues_pay/edit',
    data,
  })
}
