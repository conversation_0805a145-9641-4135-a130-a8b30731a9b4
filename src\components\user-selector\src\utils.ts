/*
 * @Description: 工具函数
 * @Author: 朱备 <z<PERSON><PERSON>@easestrategy.com>
 * @Date: 2021-11-25 16:10:56
 * @LastEditTime: 2021-11-26 15:14:21
 * @LastEditors: 朱备 <zhu<PERSON>@easestrategy.com>
 */

import type { TreeOption } from 'naive-ui'

export interface IArrayElement {
  pid?: number | string
  id: number | string
  label: string
}

/**
 * 将树转成数组
 */
export function transformTreeToArray(treeData: TreeOption[]) {
  const result: IArrayElement[] = []

  function pushNode(treeData: TreeOption[], pid?: number | string) {
    treeData.forEach((node) => {
      result.push({ id: node.key!, label: node.label!, pid })
      if (node.children?.length) {
        pushNode(node.children, node.key)
      }
    })
  }

  pushNode(treeData)

  return result
}

/**
 * 返回过滤节点的根节点ids
 */
export function getRootNodes(
  originalArray: IArrayElement[],
  nodeArray: IArrayElement[],
) {
  // 获取数组所有元素的根节点
  const rootIds: (number | string)[] = []
  function getRoot(node: IArrayElement) {
    if (node.pid) {
      originalArray
        .filter(sub => sub.id === node.pid)
        .forEach((sub) => {
          if (node.pid) {
            getRoot(sub)
          } else {
            if (!rootIds.includes(node.id)) {
              rootIds.push(node.id)
            }
          }
        })
    } else {
      if (!rootIds.includes(node.id)) {
        rootIds.push(node.id)
      }
    }
  }
  nodeArray.forEach(node => getRoot(node))

  return rootIds
}
