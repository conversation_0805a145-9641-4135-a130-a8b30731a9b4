<!--
 * @Description: 试卷内容-从题库选择
-->
<template>
  <div class="choose">
    <!-- 左侧统计 -->
    <div v-if="showCount" class="count">
      <div v-if="statisticsReactive.单选题.count > 0" class="cate">
        <div>单选题 x {{ statisticsReactive.单选题.count }}</div>
        <div class="score">
          每题<n-input-number
            v-model:value="statisticsReactive.单选题.score"
            size="small"
            :min="0.5"
            :show-button="false"
          />分
        </div>
      </div>
      <div v-if="statisticsReactive.多选题.count > 0" class="cate">
        <div>多选题 x {{ statisticsReactive.多选题.count }}</div>
        <div class="score">
          每题<n-input-number
            v-model:value="statisticsReactive.多选题.score"
            size="small"
            :min="0.5"
            :show-button="false"
          />分
        </div>
      </div>
      <div v-if="statisticsReactive.填空题.count > 0" class="cate">
        <div>填空题 x {{ statisticsReactive.填空题.count }}</div>
        <div class="score">
          每题<n-input-number
            v-model:value="statisticsReactive.填空题.score"
            size="small"
            :min="0.5"
            :show-button="false"
          />分
        </div>
      </div>
      <div v-if="statisticsReactive.判断题.count > 0" class="cate">
        <div>判断题 x {{ statisticsReactive.判断题.count }}</div>
        <div class="score">
          每题<n-input-number
            v-model:value="statisticsReactive.判断题.score"
            size="small"
            :min="0.5"
            :show-button="false"
          />分
        </div>
      </div>
    </div>

    <!-- 右侧试题列表 -->
    <div class="list">
      <div class="top">
        <span class="left">试题列表</span>
        <span v-if="showCount" class="right">
          已添加
          <span>{{ questionListRef.length }}</span>
          题，总分
          <span>{{ totalScore }}</span>
          分
        </span>
      </div>

      <div class="questions">
        <!-- 题目列表 -->
        <question-item
          v-for="(item, index) in questionListRef"
          :key="item.id"
          :index="index + 1"
          :question="item"
          :can-down="index < questionListRef.length - 1"
          :can-up="index > 0"
          @down="() => handleDown(index)"
          @up="() => handleUp(index)"
          @delete="() => handleDelete(index)"
        />

        <n-button size="small" @click="handleClickChoose">
          从题库选题
        </n-button>
        <question-picker
          v-model:show="showQuestionPickerRef"
          v-model:checked-list="questionListRef"
        />
      </div>
    </div>
  </div>

  <!-- 底部按钮 -->
  <div class="btns" :class="{ margin: showCount }">
    <n-button type="primary" @click="handleCommit">
      提交
    </n-button>
    <n-button @click="$router.replace({ name: 'paperList' })">
      取消
    </n-button>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { NButton, NInputNumber } from 'naive-ui'
import QuestionPicker from '../question-picker/QuestionPicker.vue'
import QuestionItem from '../question-item/QuestionItem.vue'
import type {
  IExaminationPaperPostQuestion,
  IFilteredExaminationQuestion,
} from '@/services/examination/index'

const emits = defineEmits(['commit'])

const showQuestionPickerRef = ref(false)
const questionListRef = ref<IFilteredExaminationQuestion[]>([])
const showCount = computed(() => questionListRef.value.length > 0)

// 左侧统计数据
const statisticsReactive = reactive<
Record<string, { count: number; score: number }>
>({
  单选题: { count: 0, score: 1 },
  多选题: { count: 0, score: 1 },
  填空题: { count: 0, score: 1 },
  判断题: { count: 0, score: 1 },
})
// 总分
const totalScore = computed(() =>
  Object.keys(statisticsReactive).reduce((pre, current) => {
    if (statisticsReactive[current].count === 0) {
      return pre + 0
    } else {
      return (
        pre
        + statisticsReactive[current].count * statisticsReactive[current].score
      )
    }
  }, 0),
)

// 点击选题按钮
function handleClickChoose() {
  showQuestionPickerRef.value = true
}

// 下移题目
function handleDown(index: number) {
  const temp = questionListRef.value[index]
  questionListRef.value[index] = questionListRef.value[index + 1]
  questionListRef.value[index + 1] = temp
}
// 上移题目
function handleUp(index: number) {
  const temp = questionListRef.value[index]
  questionListRef.value[index] = questionListRef.value[index - 1]
  questionListRef.value[index - 1] = temp
}
// 删除题目
function handleDelete(index: number) {
  questionListRef.value.splice(index, 1)
  questionListRef.value = [...questionListRef.value]
}

// 点击提交
function handleCommit() {
  // 处理数据
  const data: IExaminationPaperPostQuestion[] = []
  questionListRef.value.forEach((item) => {
    const obj: IExaminationPaperPostQuestion = {
      subjectId: item.id,
      score: statisticsReactive[item.type].score,
    }
    if (item.type === '多选题') {
      obj.missScore = statisticsReactive[item.type].score / 2
    }
    data.push(obj)
  })
  emits('commit', data, totalScore.value)
}

// 设置题目
function setQuestionList(data: IFilteredExaminationQuestion[]) {
  questionListRef.value = data
  for (const key in statisticsReactive) {
    const target = (statisticsReactive[key].score = data.find(
      item => item.type === key,
    ) as any)
    if (target) {
      statisticsReactive[key].score = target.score
    }
  }
}

watch(questionListRef, (newV) => {
  for (const k in statisticsReactive) {
    statisticsReactive[k].count = 0
  }
  newV.forEach((item) => {
    statisticsReactive[item.type].count++
  })
})

defineExpose({
  setQuestionList,
})
</script>

<style lang="scss" scoped>
.choose {
  margin-top: 25px;
  display: flex;
  align-items: flex-start;

  > .count {
    width: 124px;
    box-sizing: border-box;
    border-radius: 3px;
    border: 1px solid #dfe0e1;
    margin-right: 8px;
    margin-top: 47px;
    padding: 10px 21px 16px 9px;
    font-size: 12px;
    font-weight: 400;
    color: #333333;
    height: auto;
    position: sticky;
    top: 0;
    left: 0;

    > .cate {
      > div:first-child {
        line-height: 26px;
      }

      &:nth-child(n + 2) {
        margin-top: 19px;
      }

      > .score {
        display: flex;
        align-items: center;
      }
    }

    .n-input-number {
      width: 37px;
      margin: 0 5px;
    }
  }

  > .list {
    border-radius: 3px;
    border: 1px solid #dfe0e1;
    flex-grow: 1;
    margin-bottom: 100px;

    > .top {
      height: 47px;
      padding: 0 26px 0 18px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #dfe0e1;
      font-size: 14px;
      font-weight: 400;
      background: #fff;
      z-index: 99;
      position: sticky;
      top: 0;
      left: 0;

      > .left {
        color: #333;
      }

      > .right {
        color: #999;

        > span {
          color: #333;
          font-weight: 500;
        }
      }
    }

    > .questions {
      padding: 20px 26px 22px 24px;
    }
  }
}

.btns {
  padding: 35px 0;
  position: fixed;
  bottom: 0;
  background: #fff;
  width: 100%;

  > button {
    width: 80px;
    height: 30px;
    border-radius: 3px;

    &:nth-child(n + 2) {
      margin-left: 12px;
    }
  }

  &.margin {
    margin-left: 130px;
  }
}
</style>
