import type {
  PartyMemberActivityListItem,
  PartyMemberAddOrEditData,
} from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 查询党员活动列表 */
export function getPartyMemberActivityTableList(
  params: { title: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<PartyMemberActivityListItem>>({
    url: '/party-affairs/backend/v1/party_activity/page',
    params,
  })
}

/** 查询党员活动详情 */
export function getPartyMemberActivityDetail(id: string) {
  return commonReq.get<PartyMemberActivityListItem>({
    url: `/party-affairs/backend/v1/party_activity/${id}`,
  })
}

/** 新增党员活动 */
export function postPartyMemberActivity(data: PartyMemberAddOrEditData) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/party_activity',
    data,
  })
}

/** 编辑党员活动 */
export function putPartyMemberActivity(data: PartyMemberAddOrEditData) {
  return commonReq.put({
    url: '/org-construction/backend/v1/branch-activity',
    data,
  })
}

/** 删除党员活动 */
export function deletePartyMemberActivity(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/party_activity',
    params: { ids },
  })
}
