/**
 * 操作权限处理
 */

import { useAuthStore } from '@/store/auth/auth'
// table表格内部操作按钮使用，没有权限配合display:none使用，有权限则display:inline-block
export default function(permissionFlag: string) {
  const store = useAuthStore()
  // 鉴权信息
  const permissionCollect = computed(() =>
    store.wholeUserInfo ? store.wholeUserInfo.permissionCollect : [],
  )

  const hasPermission = permissionCollect.value.includes(permissionFlag)
  if (hasPermission) {
    return 'inline-block'
  } else {
    return 'none'
  }
}

// 返回true和false,表示是否有该权限
export const judgePermission = (permissionFlag: string) => {
  const store = useAuthStore()
  // 鉴权信息
  const permissionCollect = computed(() =>
    store.wholeUserInfo ? store.wholeUserInfo.permissionCollect : [],
  )

  const hasPermission = permissionCollect.value.includes(permissionFlag)
  return hasPermission
}
