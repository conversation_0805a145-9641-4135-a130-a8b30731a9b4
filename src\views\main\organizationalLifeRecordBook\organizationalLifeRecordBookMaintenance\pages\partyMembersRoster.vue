<script lang="ts" setup>
import { NButton, NForm, NIcon, type TreeSelectOption } from 'naive-ui'
import { AddTwotone, CachedSharp, DownloadRound } from '@vicons/material'
import { IosArrowDropdown, IosArrowDropup } from '@vicons/ionicons4'
import dayjs from 'dayjs'
import { useRecordBookStore } from '@/store/recordBook'
import { useFetchEnumerationOptions } from '@/hooks/use-select-options'
import { getPartyMemberColumns } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/config'
import type { PartyMemberType } from '@/services/recordBookMaintenance/types'
import { partyMemberFormRules } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/formRules'
import {
  addPartyMemberRoster,
  deletePartyMemberRoster,
  editPartyMemberRoster,
  exchangePartyMemberRoster,
  getPartyMemberDetail,
  getPartyMemberList,
  getPartyMemberRosterShowSyncDialog,
  getSyncTime,
  syncPartyMemberRoster,
} from '@/services/recordBookMaintenance'

const partyMemberFormRef = ref<InstanceType<typeof NForm>>()
const sexType = ref(useFetchEnumerationOptions('sex'))
const eduType = ref(useFetchEnumerationOptions('edu'))
const politicalType = ref(useFetchEnumerationOptions('political')) // 政治面貌
const partyDutyType = ref(useFetchEnumerationOptions('party_duty_on_show')) // 党内职

const recordBookStore = useRecordBookStore()
const currentYearOptions = computed(() => {
  return recordBookStore.getYearOptions
})

const fileLoading = ref<boolean>(false)
const drawerTitle = ref<string>('新增用户')
const actionType = ref<'add' | 'edit' | 'view'>('add')
const showAddPartyMemberModal = ref<boolean>(false)
const pageNum = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const total = ref(0)
const selectedDeptId = ref<string | null>(null)
const selectedYear = ref<string | null>(null)
const showSyncDialog = ref<boolean>(false)
const tableData = ref([])

const currentDownloadFileType = ref<'word' | 'pdf'>('word')
const showPopConfirm = ref<boolean>(false)
const syncRecordTime = ref<string>('')
const isNeedShowSyncDialog = ref<boolean>(false)

const formDataRef = ref<PartyMemberType>({
  trueName: '',
  sex: null,
  birth: null,
  edu: null,
  joinTime: null,
  political: null,
  partyDuty: null,
  remark: '',
  deptId: '',
  year: '',
})

const calcOrganizationListTree = computed(() => {
  return recordBookStore.getOrganizationListTreeList
})

const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})
// 获取当前年份
const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

// 判断是不是当年
const isCurrentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear !== dayjs().year().toString()
})

const calcPartyMemberFormRules = ref([])

watchEffect(() => {
  if (formDataRef.value.political === '3') {
    nextTick(() => {
      calcPartyMemberFormRules.value = partyMemberFormRules as any
      partyMemberFormRef.value?.restoreValidation()
    })
  }
  else {
    nextTick(() => {
      calcPartyMemberFormRules.value = {
        ...partyMemberFormRules,
        joinTime: {
          required: true,
          message: '请选择入党时间',
          trigger: 'change',
        },
        partyDuty: {
          required: true,
          message: '请选择支部内职务',
          trigger: 'change',
        },
      } as any
    })
  }
})

const tableColumns = getPartyMemberColumns(
  (row) => {
    return h(
      'span',
      {},
      {
        default: () => {
          const options = toRaw(sexType.value.enumerationList)
          const findData = options.find(item => item.value === row.sex)
          return findData?.label || ''
        },
      },
    )
  },
  (row) => {
    return h(
      'span',
      {},
      {
        default: () => {
          const options = toRaw(eduType.value.enumerationList)
          const findData = options.find(item => item.value === row.edu)
          return findData?.label || ''
        },
      },
    )
  },
  (row) => {
    return h(
      'span',
      {},
      {
        default: () => {
          const options = toRaw(politicalType.value.enumerationList)
          const findData = options.find(item => item.value === row.political)
          return findData?.label || ''
        },
      },
    )
  },
  (row) => {
    return h(
      'span',
      {},
      {
        default: () => {
          const options = toRaw(partyDutyType.value.enumerationList)
          const findData = options.find(item => item.value === row.partyDuty)
          return findData?.label || ''
        },
      },
    )
  },
  (row, index) => {
    return [
      h(
        'div',
        {
          class: 'flex flex-row gap-x-[10px]',
        },
        {
          default: () => [
            h(
              NButton,
              {
                text: true,
                color: '#AC241D',
                onClick: () => {
                  actionType.value = 'view'
                  drawerTitle.value = '查看用户'
                  getPartyMemberDetail(row.id).then((res) => {
                    formDataRef.value = res as any
                  })
                  showAddPartyMemberModal.value = true
                },
              },
              {
                default: () => '查看',
              },
            ),
            h(
              NButton,
              {
                text: true,
                color: '#AC241D',
                onClick: () => {
                  actionType.value = 'edit'
                  drawerTitle.value = '编辑用户'
                  getPartyMemberDetail(row.id).then((res) => {
                    formDataRef.value = res as any
                  })
                  showAddPartyMemberModal.value = true
                },
              },
              {
                default: () => '编辑',
              },
            ),
            h(
              NButton,
              {
                text: true,
                color: '#AC241D',
                onClick: () => {
                  window.$dialog.create({
                    type: 'default',
                    closable: false,
                    content: '确认删除该条记录？',
                    showIcon: false,
                    positiveText: '确认',
                    negativeText: '取消',
                    onPositiveClick: () => {
                      deletePartyMemberRoster(row.id).then(() => {
                        window.$message.success('删除成功！')
                        loadData()
                      })
                    },
                  })
                },
              },
              {
                default: () => '删除',
              },
            ),
            h(
              NIcon,
              {
                size: 20,
                color: isDisabledRow(row, index, 'down') ? '#ccc' : '#AC241D',
                onClick: () => {
                  if (isDisabledRow(row, index, 'down')) {
                    return
                  }
                  handelMoveRow('down', row)
                },
                class: isDisabledRow(row, index, 'down')
                  ? 'cursor-not-allowed'
                  : 'cursor-pointer',
              },
              { default: () => h(IosArrowDropdown) },
            ),
            h(
              NIcon,
              {
                size: 20,
                color: isDisabledRow(row, index, 'up') ? '#ccc' : '#AC241D',
                class: isDisabledRow(row, index, 'up')
                  ? 'cursor-not-allowed'
                  : 'cursor-pointer',
                onClick: () => {
                  if (isDisabledRow(row, index, 'up')) {
                    return
                  }
                  handelMoveRow('up', row)
                },
              },
              { default: () => h(IosArrowDropup) },
            ),
          ],
        },
      ),
    ]
  },
)

function isDisabledRow(
  row: PartyMemberType,
  index: number,
  type: 'down' | 'up',
) {
  const isFirstItem = index === 0
  const isLastItem = index === tableData.value.length - 1
  const isFirstPage = pageNum.value === 1
  const isLastPage = pageSize.value * pageNum.value >= total.value

  // 如果是唯一一行数据，禁用所有操作
  if (tableData.value.length === 1) {
    return true
  }

  // 处理上移按钮的禁用逻辑
  if (type === 'up') {
    // 第一页的第一个元素禁用上移
    if (isFirstPage && isFirstItem) {
      return true
    }
    // 非第一页的第一个元素不禁用
    return false
  }

  // 处理下移按钮的禁用逻辑
  if (type === 'down') {
    // 最后一页的最后一个元素禁用下移
    if (isLastPage && isLastItem) {
      return true
    }
    // 非最后一页的最后一个元素不禁用
    return false
  }

  return false
}

function handelMoveRow(type: 'down' | 'up', row: PartyMemberType) {
  exchangePartyMemberRoster({
    id: row.id,
    type,
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
  }).then((res) => {
    window.$message.success('交换成功！')
    loadData()
  })
}

function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  pageNum.value = 1
  selectedDeptId.value = v
  loadData()
  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearSelectedValue(v: string) {
  if (!v) {
    return
  }
  pageNum.value = 1
  selectedYear.value = v
  loadData()
  recordBookStore.setCurrentSelectedYear(v || '')
}

function handleDownLoadFile() {
  fileLoading.value = true
  recordBookStore.fetchDownLoadFile(
    {
      deptId: selectedDeptId.value!,
      year: selectedYear.value!,
      type: currentDownloadFileType.value,
    },
    () => {
      fileLoading.value = false
    },
  )

  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleCancelShowPopConfirm() {
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function initFormData() {
  formDataRef.value = {
    trueName: '',
    sex: null,
    birth: null,
    edu: null,
    joinTime: null,
    political: null,
    partyDuty: null,
    remark: '',
    deptId: '',
    year: '',
  }
}

function handleAddPartyMember() {
  initFormData()
  actionType.value = 'add'
  drawerTitle.value = '新增用户'
  showAddPartyMemberModal.value = true
}

watch(
  () => pageNum.value,
  () => {
    loadData()
  },
)

watch(
  () => pageSize.value,
  () => {
    pageNum.value = 1
    loadData()
  },
)

function loadData() {
  if (!selectedDeptId.value || !selectedYear.value) {
    return
  }
  loading.value = true
  fetchShowSyncDialog()
  getPartyMemberList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    deptId: selectedDeptId.value!,
    // deptId: '1813837432794152962',
    year: selectedYear.value!,
  })
    .then((res) => {
      total.value = res.total || 0
      tableData.value = (res.records as any) || []
    })
    .finally(() => {
      loading.value = false
      fetchSyncTime()
    })
}

function handleConfirm() {
  partyMemberFormRef.value?.validate((errors: any) => {
    if (!errors) {
      if (actionType.value === 'add') {
        addPartyMemberRoster({
          ...formDataRef.value,
          deptId: selectedDeptId.value!,
          year: selectedYear.value!,
        }).then(() => {
          window.$message.success('添加成功！')
          loadData()
        })
      }
      else if (actionType.value === 'edit') {
        editPartyMemberRoster({ ...formDataRef.value }).then(() => {
          window.$message.success('修改成功！')
          loadData()
        })
      }

      partyMemberFormRef.value?.restoreValidation() // 重置验证状态
      showAddPartyMemberModal.value = false
    }
  })
}

function handelSyncMemberData(syncType: '0' | '1') {
  syncPartyMemberRoster({ deptId: selectedDeptId.value!, syncType })
    .then(() => {
      window.$message.success('同步成功！')
      fetchSyncTime()
      loadData()
    })
    .finally(() => {
      showSyncDialog.value = false
    })
}

function fetchSyncTime() {
  getSyncTime({
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
    fieldType: '0', // 0 党员名册
  }).then((res) => {
    syncRecordTime.value = (res as any) || ''
  })
}

function handleUpdatePoliticalValue(v: string) {}

function handelShowSyncDialog() {
  if (isNeedShowSyncDialog.value) {
    showSyncDialog.value = true
  }
  else {
    handelSyncMemberData('1') // 覆盖同步
  }
}

function fetchShowSyncDialog() {
  getPartyMemberRosterShowSyncDialog({
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
  }).then((res) => {
    isNeedShowSyncDialog.value = res
  })
}

onMounted(() => {
  selectedDeptId.value = currentSelectedDept.value.deptId
  selectedYear.value = currentYear.value
  loadData()
  fetchShowSyncDialog()
  fetchSyncTime()
})
</script>

<template>
  <div>
    <table-container
      v-model:page="pageNum"
      v-model:page-size="pageSize"
      :loading="loading"
      :show-delete="false"
      :show-pagination="true"
      :show-toolbar="false"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      default-expand-all
      title="党员名册"
    >
      <template #row>
        <div
          class="w-[100%] flex flex-row items-start justify-between gap-[10px] pb-[10px]"
        >
          <div class="flex flex-row gap-x-[10px] justify-start items-start">
            <n-tree-select
              v-model:value="selectedDeptId"
              :options="calcOrganizationListTree"
              :show-path="false"
              check-strategy="all"
              children-field="children"
              filterable
              key-field="id"
              label-field="name"
              placeholder="请选择所属党组织"
              style="width: 400px"
              value-field="id"
              @update:value="handleUpdateTreeSelectedValue"
            />
            <n-select
              v-model:value="selectedYear"
              :options="currentYearOptions"
              placeholder="请选择年份"
              size="medium"
              style="width: 200px"
              @update:value="handleUpdateYearSelectedValue"
            />
          </div>

          <div class="flex flex-row gap-x-[10px] justify-start items-start">
            <n-button type="primary" @click="handleAddPartyMember">
              <template #icon>
                <n-icon size="16">
                  <AddTwotone />
                </n-icon>
              </template>
              新增
            </n-button>
            <n-popconfirm
              :show="showPopConfirm"
              :show-icon="false"
              placement="bottom"
            >
              <template #trigger>
                <n-button
                  :loading="fileLoading"
                  @click="showPopConfirm = !showPopConfirm"
                >
                  <template #icon>
                    <n-icon size="16">
                      <DownloadRound />
                    </n-icon>
                  </template>
                  下载
                </n-button>
              </template>
              <div class="py-[10px]">
                <n-radio-group v-model:value="currentDownloadFileType">
                  <n-radio
                    v-for="(fileType, fileTypeIndex) in [
                      { label: 'word', value: 'word' },
                      { label: 'pdf', value: 'pdf' },
                    ]"
                    :key="fileTypeIndex"
                    :value="fileType.value"
                  >
                    {{ fileType.label }}
                  </n-radio>
                </n-radio-group>
              </div>

              <template #action>
                <div class="w-[100%] flex flex-row justify-center items-center">
                  <n-button
                    size="small"
                    type="primary"
                    @click="handleDownLoadFile(item)"
                  >
                    确定
                  </n-button>
                  <n-button size="small" @click="handleCancelShowPopConfirm">
                    取消
                  </n-button>
                </div>
              </template>
            </n-popconfirm>
            <div class="flex flex-col gap-y-[10px]">
              <n-button :disabled="isCurrentYear" @click="handelShowSyncDialog">
                <template #icon>
                  <n-icon size="16">
                    <CachedSharp />
                  </n-icon>
                </template>
                同步数据
              </n-button>
              <span
                v-if="syncRecordTime"
                class="text-[#333333] text-[12px] leading-[17px] flex flex-row items-center gap-x-[6px]"
              ><n-icon color="#AC241D" size="16"><CachedSharp /></n-icon>{{ syncRecordTime }}</span>
            </div>
          </div>
        </div>
      </template>
    </table-container>

    <n-drawer
      v-model:show="showAddPartyMemberModal"
      :width="502"
      placement="right"
    >
      <n-drawer-content :title="drawerTitle">
        <n-form
          ref="partyMemberFormRef"
          :disabled="actionType === 'view'"
          :model="formDataRef"
          :rules="calcPartyMemberFormRules"
          label-placement="left"
          label-width="100"
        >
          <n-form-item label="姓名：" path="trueName">
            <n-input
              v-model:value="formDataRef.trueName"
              clearable
              maxlength="10"
              placeholder="请输入姓名"
              show-count
            />
          </n-form-item>
          <n-form-item label="性别：" path="sex">
            <n-select
              v-model:value="formDataRef.sex"
              :options="sexType.enumerationList"
              clearable
              placeholder="请选择性别"
            />
          </n-form-item>
          <n-form-item label="出生年月：" path="birth">
            <n-date-picker
              v-model:formatted-value="formDataRef.birth"
              placeholder="请选择出生年月"
              style="width: 100%"
              type="month"
              value-format="yyyy-MM"
            />
          </n-form-item>
          <n-form-item label="学历：" path="edu">
            <n-select
              v-model:value="formDataRef.edu"
              :options="eduType.enumerationList"
              clearable
              placeholder="请选择学历"
            />
          </n-form-item>
          <n-form-item label="政治面貌：" path="political">
            <n-select
              v-model:value="formDataRef.political"
              :options="politicalType.enumerationList"
              clearable
              filterable
              placeholder="请选择政治面貌"
              @update:value="handleUpdatePoliticalValue"
            />
          </n-form-item>
          <n-form-item label="入党时间：" path="joinTime">
            <n-date-picker
              v-model:formatted-value="formDataRef.joinTime"
              :disabled="formDataRef.political === '3' || actionType === 'view'"
              clearable
              placeholder="请选择入党时间"
              style="width: 100%"
              type="date"
              value-format="yyyy-MM-dd"
            />
          </n-form-item>

          <n-form-item label="支部内职务：" path="partyDuty">
            <n-select
              v-model:value="formDataRef.partyDuty"
              :disabled="formDataRef.political === '3' || actionType === 'view'"
              :options="partyDutyType.enumerationList"
              clearable
              filterable
              placeholder="请选择支部内职务"
            />
          </n-form-item>
          <n-form-item label="备注：">
            <n-input
              v-model:value="formDataRef.remark"
              :rows="12"
              maxlength="200"
              placeholder="请输入备注"
              show-count
              type="textarea"
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <div
            class="w-[100%] flex flex-row justify-center items-center gap-x-[10px]"
          >
            <n-button @click="showAddPartyMemberModal = false">
              取消
            </n-button>
            <n-button type="primary" @click="handleConfirm">
              确定
            </n-button>
          </div>
        </template>
      </n-drawer-content>
    </n-drawer>

    <n-modal v-model:show="showSyncDialog" preset="dialog" title="同步数据">
      <div class="py-[10px]">
        <span class="text-[16px] leading-[32px]">是否保留上次修改内容？</span>
      </div>
      <template #action>
        <div
          class="w-[100%] flex flex-row justify-center items-center gap-x-[20px]"
        >
          <n-button @click="handelSyncMemberData('0')">
            确认保留
          </n-button>
          <n-button @click="handelSyncMemberData('1')">
            覆盖所有
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<style lang="scss" scoped></style>
