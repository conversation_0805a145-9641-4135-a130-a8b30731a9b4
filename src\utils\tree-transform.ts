/*
 * @Description: 二级树结构转换
 */
import type { ITreeNode } from '@/components/awesome-tree'
import { CascaderOption, TreeOption } from 'naive-ui'

/**
 * 将后端返回的树结构转换成Naive的树结构
 */
export function transformToTreeOptions(
  treeData: ITreeNode[] | null,
  leafOnly = false
): TreeOption[] {
  if (!treeData || treeData.length === 0) {
    return []
  }
  return treeData.map((item) => {
    const node: TreeOption = {
      label: item.nodeName,
      key: item.nodeId,
      editing: false,
      pid: item.pid,
    }
    if (leafOnly && item.pid === 0) {
      node.disabled = true
    }
    if (item.children && item.children.length) {
      node.children = transformToTreeOptions(item.children)
    }
    return node
  })
}

/**
 * 获取树的第一个可选中节点
 * @param treeData 数据
 * @param leafOnly 是否仅叶子节点
 * @returns
 */

export function getFirstSelectableNode<
  T extends { children: T[] | null; pid: number | null }
>(treeData: T[], leafOnly: boolean): T | null {
  if (leafOnly) {
    for (const node of treeData) {
      if (node.children && node.children.length) {
        return getFirstSelectableNode(node.children, leafOnly)
      } else if (node.pid) {
        return node
      } else {
        return null
      }
    }
    return null
  } else {
    // 如果leafOnly为false，直接返回第一个节点
    return treeData[0]
  }
}

/**
 * 将树转换成数组
 * @param treeData 原始数据
 * @param container 接收结果的数组容器
 */
export function transformToArray<T extends { children: T[] | null }>(
  treeData: T[],
  container: T[]
) {
  treeData.forEach((node) => {
    container.push({ ...node })
    if (node.children?.length) {
      transformToArray(node.children, container)
    }
  })
}

/**
 * 将树转换成级联选择器的选项
 * @param treeData 原始数据
 */
export function transformToCascaderOptions(
  treeData: ITreeNode[]
): CascaderOption[] {
  const result: CascaderOption[] = []

  treeData.forEach((node) => {
    const option: CascaderOption = { label: node.nodeName, value: node.nodeId }
    if (node.children && node.children.length) {
      option.children = transformToCascaderOptions(node.children)
    }
    result.push(option)
  })

  return result
}
