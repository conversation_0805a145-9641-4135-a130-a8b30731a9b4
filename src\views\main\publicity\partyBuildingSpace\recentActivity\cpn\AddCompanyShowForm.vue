<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { reactive } from 'vue'
import { formRules } from './config'
import type { addAndEditParams } from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
// import RichEditor from '@/components/RichEditor.vue'
import {
  getViewPartyBuildingExamIndIcatorsItem,
  postPartyBuildingExamAdd,
  putEditExamIndIcatorsIdsItem,
} from '@/services/affairs/discipline-inspection-list/exam-indicators'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<addAndEditParams>({
  id: '',
  title: '',
  evaluationRequirements: '',
  evaluationMode: '',
  evaluationScore: 0,
  matter: '',
  fileIds: [],
  fileList: [],
})

const formRef = ref<InstanceType<typeof NForm>>()

onBeforeMount(() => {
  if (props.type === 'modify' && props.id) {
    getViewPartyBuildingExamIndIcatorsItem(props.id).then((res) => {
      formDataReactive.evaluationMode = res.evaluationMode
      formDataReactive.evaluationRequirements = res.evaluationRequirements
      formDataReactive.evaluationScore = res.evaluationScore
      formDataReactive.fileIds = res.fileList?.map(item => item.id) || []
      formDataReactive.fileList = res.fileList || []
      formDataReactive.matter = res.matter
      formDataReactive.title = res.title
      formDataReactive.id = props.id
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (formDataReactive.id) {
        putEditExamIndIcatorsIdsItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      } else {
        postPartyBuildingExamAdd(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// 文件相关
// async function handleFileChange(
//   fileInfoList: uploadFileItemNew[],
//   isDelIDs: string,
// ) {
//   try {
//     const fileData = new FormData()
//     const fileIdArr: Array<string> = []
//     // 获取所有FileID
//     formDataReactive.fileList.forEach(item => fileIdArr.push(item.id))
//     // 删除动作
//     if (isDelIDs) {
//       fileIdArr.forEach((item, index) => {
//         if (item === isDelIDs) {
//           fileIdArr.splice(index, 1)
//         }
//       })
//       formDataReactive.fileIds = [...fileIdArr]
//     } else {
//       // 新增动作
//       const lastFileItem = fileInfoList[fileInfoList.length - 1]?.file
//       fileData.append('file', lastFileItem as Blob)
//       const data: uploadFileItem = await uploadImg(fileData)
//       formDataReactive.fileList.push({
//         original: lastFileItem?.name as string,
//         fileName: '',
//         id: '',
//       })
//       if (data) {
//         fileIdArr.push(data.fileId)
//         if (formDataReactive.fileList.length) {
//           formDataReactive.fileIds = formDataReactive.fileIds.concat(fileIdArr)
//         } else {
//           formDataReactive.fileIds = [...fileIdArr]
//         }
//       }
//     }
//   } catch (error) {}
// }

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="140"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item label="公司名称：" path="title">
      <n-input
        v-model:value="formDataReactive.title"
        placeholder="请输入公司名称"
        clearable
      />
    </n-form-item>
    <n-form-item label="是否置顶：" path="matter">
      <n-switch />
    </n-form-item>
    <n-form-item span="24" label="APP封面图片：" path="evaluationRequirements">
      <ImgUploader :need-cropper="false" />
    </n-form-item>

    <n-form-item span="24" label="PC封面图片：" path="evaluationMode">
      <ImgUploader :need-cropper="false" />
    </n-form-item>
    <n-form-item span="24" label="展示模块标题：" path="evaluationScore">
      <n-card>
        <n-form-item label-align="left" label="1、" path="title">
          <n-input clearable />
        </n-form-item>
        <n-form-item label-align="left" label="2、" path="title">
          <n-input clearable />
          <n-button>删除</n-button>
        </n-form-item>
        <n-form-item path="title">
          <n-button>添加展示模块</n-button>
        </n-form-item>
      </n-card>
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
