<script setup lang="ts">
import UnReviewed from './unreviewed/UnReviewed.vue'
import Pass from './pass/Pass.vue'
import Rejected from './rejected/Rejected.vue'
</script>

<template>
  <div class="px-[22px] py-[25px]">
    <div class="text-[14px] font-[600] text-[#333] leading-[20px] mb-[26px]">
      文化展示
    </div>
    <n-tabs type="line" animated>
      <n-tab-pane name="unreviewed" tab="未审核">
        <UnReviewed second-menu-status="0" />
      </n-tab-pane>
      <n-tab-pane name="pass" tab="已通过">
        <Pass second-menu-status="1" />
      </n-tab-pane>
      <n-tab-pane name="rejected" tab="已驳回">
        <Rejected second-menu-status="2" />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<style lang="scss" scoped>
:deep(.n-tabs.n-tabs--top .n-tab-pane) {
  padding: 0;
}
</style>
