<script setup lang="ts">
import { NButton, NPopconfirm } from 'naive-ui'
import NoticeForm from '../cpn/NoticeForm.vue'
import { getTableColumns } from './config'
import { useDrawerEditOrganize, useMyTable } from '@/hooks'
import ifHasPermi from '@/directive/permission/ifHasPermi'

import {
  deleteSecretary,
  getSecretaryList,
} from '@/services/secretary-box/index'

const filterReactive = ref<{
  suggesterName: string
  reply: string
  suggest: string
  deptName: string
}>({
  suggesterName: '',
  reply: '',
  deptName: '',
  suggest: '',
})
// 有接口后添加：loading,tableData
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleBatchDelete,
  handleSingleDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getSecretaryList, filterReactive, {
  batchDeleteTable: false,
  delApi: deleteSecretary,
})

// 查看
const idViewRef = ref()
const addNoticeFormRef = ref()
const { drawerTitle, showEditRef, editTypeRef } = useDrawerEditOrganize({
  name: '',
  confirmFn: handelConfirmEdit,
})

/** 添加根节点 */
const handleClickAddRootNode = () => {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}

// 修改和删除按钮渲染

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          idViewRef.value = row.id
          editTypeRef.value = 'view'
          showEditRef.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
          display: ifHasPermi('secretary_view_btn'),
        },
      },
      {
        default: () => '查看',
      },
    ),
    h(
      NPopconfirm,
      {
        positiveText: '确定',
        placement: 'left',
        onPositiveClick: () => {
          handleSingleDelete(String(row.id))
        },
      },
      {
        trigger: () =>
          h(
            NButton,
            {
              text: true,
              type: 'primary',
              style: {
                marginRight: '10px',
                display: ifHasPermi('secretary_delete_btn'),
              },
            },
            { default: () => '删除' },
          ),
        default: () => '确认需要删除该项内容吗？',
      },
    ),
  ]
})

onMounted(loadData)
</script>

<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="书记信箱"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :loading="loading"
    :show-delete="false"
    :checked-row-keys="checkedRowKeys"
    default-expand-all
    @click-add="handleClickAddRootNode"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <div
        class="w-[500px] flex flex-row justify-start items-center gap-[10px]"
      >
        <n-input
          v-model:value="filterReactive.deptName"
          size="small"
          placeholder="所属组织"
          clearable
        />
        <n-input
          v-model:value="filterReactive.suggesterName"
          size="small"
          placeholder="姓名"
          clearable
        />
        <n-input
          v-model:value="filterReactive.suggest"
          size="small"
          placeholder="提问内容"
          clearable
        />
        <n-input
          v-model:value="filterReactive.reply"
          size="small"
          placeholder="书记回复内容"
          clearable
        />
      </div>
    </template>
  </table-container>

  <!-- 查看抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="564" :mask-closable="false">
    <n-drawer-content :title="drawerTitle">
      <notice-form
        :id="idViewRef"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        @saved="showEditRef = false"
      />

      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="showEditRef = false"
          >
            确定
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<style lang="scss" scoped>
:deep(.n-drawer-body-content-wrapper) {
  padding: 0 !important;
  background: rgb(245, 246, 248);
}

:deep(.n-drawer-body) {
  padding: 0 !important;
}
</style>
