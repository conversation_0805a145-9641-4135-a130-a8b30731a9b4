import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  title: {
    required: true,
    message: '请输入组织工作法标题',
    trigger: 'input',
  },
  coverUrl: {
    required: true,
    message: '请上传封面图片',
    trigger: 'change',
    type: 'string',
  },
  // contentCoverUrl: {
  //   required: true,
  //   message: '请上传内容图片',
  //   trigger: 'change',
  // },
  fileList: {
    required: true,
    validator(rule: any, value: any) {
      if (!value || value.length === 0) {
        return new Error('请选择内容图片')
      }
      return true
    },
    trigger: 'change',
  },
  description: {
    required: true,
    message: '请输入组织工作法说明',
    trigger: 'input',
  },
}
