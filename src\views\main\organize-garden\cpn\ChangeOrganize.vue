<script setup lang="ts">
import type { FormRules } from 'naive-ui'
import { NForm } from 'naive-ui'
import { ExchangeAlt } from '@vicons/fa'
import { useOrganizationListOptionsNew } from '@/hooks/use-select-options'
import { getCurrentOrganize, getOrganizeById } from '@/services/organize-garden'

import { useOrganizeGardenStore } from '@/store/organize-garden'

const { setDeptId, setDeptName, getDeptName, getDeptId }
  = useOrganizeGardenStore()

const showModal = ref(false)
const formDataReactive = reactive({
  name: '',
  deptId: '',
})
/** 打开弹窗 */
async function handleClick() {
  showModal.value = true
}

/** 获取当前组织id查询组织名称并返回 */
async function getCurrentOrganizeById() {
  const res = await getOrganizeById(formDataReactive.deptId)
  formDataReactive.name = res.name
  return res.name
}

const formRef = ref<InstanceType<typeof NForm>>()
const { organizationListTree } = useOrganizationListOptionsNew()

const formRules: FormRules = {
  name: [
    {
      required: true,
      message: '当前组织不能为空',
      trigger: 'input',
    },
  ],
  deptId: {
    required: true,
    message: '所属党组织不能为空',
    trigger: 'change',
  },
}

/** 获取党组织id */
const handleUpdateValue = (v: string) => {
  formDataReactive.deptId = v
}

const emits = defineEmits<{
  (e: 'changeOrg', value: string): void
}>()

function handleConfirm() {
  formRef.value?.validate(async(errors: any) => {
    if (!errors) {
      setDeptId(formDataReactive.deptId)
      const name = await getCurrentOrganizeById()
      setDeptName(name)
      showModal.value = false
      emits('changeOrg', formDataReactive.deptId)
    }
  })
}

onMounted(async() => {
  if (!getDeptName()) {
    const res = await getCurrentOrganize()
    formDataReactive.name = res.name
    formDataReactive.deptId = res.deptId
    setDeptName(res.name)
    setDeptId(res.deptId)
  }
  else {
    formDataReactive.name = getDeptName()
    formDataReactive.deptId = getDeptId()
  }
})
</script>
<template>
  <div>
    <n-button
      text
      icon-placement="right"
      size="large"
      strong
      @click="handleClick"
    >
      <template #icon>
        <n-icon>
          <exchange-alt />
        </n-icon>
      </template>
      {{ getDeptName() }}
    </n-button>
  </div>
  <n-modal
    v-model:show="showModal"
    preset="card"
    title="切换当前所属组织"
    style="width: 600px"
  >
    <n-form
      ref="formRef"
      :model="formDataReactive"
      :rules="formRules"
      label-placement="left"
      require-mark-placement="left"
      label-width="auto"
      class="w-[100%]"
    >
      <n-form-item path="name" label="当前组织：">
        <n-input v-model:value="formDataReactive.name" disabled clearable />
      </n-form-item>
      <n-form-item path="deptId" label="切换至组织：">
        <n-tree-select
          v-model:value="formDataReactive.deptId"
          :options="organizationListTree"
          value-field="deptId"
          label-field="name"
          key-field="deptId"
          children-field="children"
          check-strategy="all"
          placeholder="请选择所属党组织"
          :show-path="false"
          clearable
          filterable
          @update:value="handleUpdateValue"
        />
      </n-form-item>
    </n-form>
    <template #action>
      <div class="flex justify-center items-center gap-[20px]">
        <n-button size="large" @click="showModal = false">
          取消
        </n-button>
        <n-button size="large" type="primary" @click="handleConfirm">
          确定
        </n-button>
      </div>
    </template>
  </n-modal>
</template>
<style lang="scss" scoped></style>
