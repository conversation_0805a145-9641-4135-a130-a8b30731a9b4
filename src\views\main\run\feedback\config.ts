import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { VNodeChild } from 'vue'
import type { ICarousel } from '@/services/run/carousel/types'

export function getTableColumns(
  optionColumnRenderer: (row: ICarousel) => VNodeChild,
  IndexRenderer: (row: ICarousel, index: number) => VNodeChild,
  phoneRenderer: (row: ICarousel) => VNodeChild,
): TableColumns<ICarousel> {
  return [
    {
      title: '序号',
      key: 'pictureUrl',
      render: IndexRenderer,
    },
    {
      title: '姓名',
      key: 'userName',
    },
    {
      title: '手机号',
      key: 'phone',
      render: phoneRenderer,
    },
    {
      title: '组织名称',
      key: 'orgName',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      title: '意见反馈',
      key: 'content',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      title: '提交时间',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      align: 'left',
      width: '120px',
      render: optionColumnRenderer,
    },
  ]
}
