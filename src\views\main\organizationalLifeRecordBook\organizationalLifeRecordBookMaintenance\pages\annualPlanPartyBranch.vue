<script lang="ts" setup>
import { DownloadRound } from '@vicons/material'
import type { TreeSelectOption } from 'naive-ui'
import { NButton, NIcon, useMessage } from 'naive-ui'
import { onMounted, ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { useRecordBookStore } from '@/store/recordBook'
import ContentEmpty from '@/assets/image/content-empty.png'
import {
  getAnnualPlanDetail,
  updateAnnualPlan,
} from '@/services/annual-plan-party'

const TABLE_TYPE = {
  VIEW: 'view',
  EDIT: 'edit',
}

const currentType = ref(TABLE_TYPE.VIEW)

const formData = ref({
  data: {
    id: '',
    content: '',
  },
})
const rawContent = ref('')

const message = useMessage()
const recordBookStore = useRecordBookStore()
const currentYearOptions = computed(() => {
  return recordBookStore.getYearOptions
})

const loadDataLoading = ref<boolean>(false)
const fileLoading = ref<boolean>(false)
const pageNum = ref(1)
const selectedDeptId = ref<string | null>(null)
const selectedYear = ref<string | null>(null)
const currentDownloadFileType = ref<'word' | 'pdf'>('word')
const showPopConfirm = ref<boolean>(false)

const calcOrganizationListTree = computed(() => {
  return recordBookStore.getOrganizationListTreeList
})

const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})
// 获取当前年份
const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  pageNum.value = 1
  selectedDeptId.value = v
  loadData()
  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearSelectedValue(v: string) {
  if (!v) {
    return
  }

  pageNum.value = 1
  selectedYear.value = v
  loadData()
  recordBookStore.setCurrentSelectedYear(v || '')
}

function handleDownLoadFile() {
  fileLoading.value = true
  recordBookStore.fetchDownLoadFile(
    {
      deptId: selectedDeptId.value!,
      year: selectedYear.value!,
      type: currentDownloadFileType.value,
    },
    () => {
      fileLoading.value = false
    },
  )

  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleCancelShowPopConfirm() {
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function editPlanBranch() {
  currentType.value = TABLE_TYPE.EDIT
}

async function saveEditPlanBranch() {
  try {
    await updateAnnualPlan({
      id: formData.value.data.id,
      deptId: selectedDeptId.value || '',
      year: selectedYear.value || '',
      content: formData.value.data.content,
    })
    await loadData()
    message.success('编辑成功')
  }
  catch (error) {
    await loadData()
    console.error(error)
    message.error('编辑失败')
  }
  finally {
    cancelEditPlanBranch()
  }
}

function cancelEditPlanBranch() {
  formData.value.data.content = cloneDeep(rawContent.value)
  currentType.value = TABLE_TYPE.VIEW
}

async function loadData() {
  try {
    loadDataLoading.value = true
    const res = await getAnnualPlanDetail({
      deptId: selectedDeptId.value || '',
      year: selectedYear.value || '',
    })
    formData.value.data.content = res?.content || ''
    formData.value.data.id = (res?.id as string) || ''
    rawContent.value = cloneDeep(res?.content || '')
  }
  catch (error) {
    console.error(error)
    formData.value.data.content = ''
    loadDataLoading.value = false
  }
  finally {
    loadDataLoading.value = false
  }
}

onMounted(() => {
  selectedDeptId.value = currentSelectedDept.value.deptId
  selectedYear.value = currentYear.value
  loadData()
})
</script>

<template>
  <div class="py-[25px] px-[22px]">
    <div
      class="w-[100%] flex flex-row items-start justify-between gap-[10px] pb-[10px]"
    >
      <div class="flex flex-row gap-x-[10px] justify-start items-start">
        <n-tree-select
          v-model:value="selectedDeptId"
          :options="calcOrganizationListTree"
          :show-path="false"
          check-strategy="all"
          children-field="children"
          filterable
          key-field="id"
          label-field="name"
          placeholder="请选择所属党组织"
          style="width: 400px"
          value-field="id"
          @update:value="handleUpdateTreeSelectedValue"
        />
        <n-select
          v-model:value="selectedYear"
          :options="currentYearOptions"
          placeholder="请选择年份"
          size="medium"
          style="width: 200px"
          @update:value="handleUpdateYearSelectedValue"
        />
      </div>

      <div class="flex flex-row gap-x-[10px] justify-start items-start">
        <n-button
          v-if="currentType === TABLE_TYPE.VIEW"
          type="primary"
          @click="editPlanBranch"
        >
          编辑
        </n-button>
        <n-button
          v-if="currentType === TABLE_TYPE.EDIT"
          @click="cancelEditPlanBranch"
        >
          取消编辑
        </n-button>
        <n-button
          v-if="currentType === TABLE_TYPE.EDIT"
          type="primary"
          @click="saveEditPlanBranch"
        >
          保存编辑
        </n-button>
        <n-popconfirm
          :show="showPopConfirm"
          :show-icon="false"
          placement="bottom"
        >
          <template #trigger>
            <n-button
              :loading="fileLoading"
              @click="showPopConfirm = !showPopConfirm"
            >
              <template #icon>
                <n-icon size="16">
                  <DownloadRound />
                </n-icon>
              </template>
              下载
            </n-button>
          </template>
          <div class="py-[10px]">
            <n-radio-group v-model:value="currentDownloadFileType">
              <n-radio
                v-for="(fileType, fileTypeIndex) in [
                  { label: 'word', value: 'word' },
                  { label: 'pdf', value: 'pdf' },
                ]"
                :key="fileTypeIndex"
                :value="fileType.value"
              >
                {{ fileType.label }}
              </n-radio>
            </n-radio-group>
          </div>

          <template #action>
            <div class="w-[100%] flex flex-row justify-center items-center">
              <n-button
                size="small"
                type="primary"
                @click="handleDownLoadFile(item)"
              >
                确定
              </n-button>
              <n-button size="small" @click="handleCancelShowPopConfirm">
                取消
              </n-button>
            </div>
          </template>
        </n-popconfirm>
      </div>
    </div>
    <div class="title flex justify-center w-full mb-[20px]">
      <span class="title-text">党支部年度工作计划</span>
    </div>
    <div
      v-if="currentType === TABLE_TYPE.VIEW"
      v-autoCalcHeight="{
        id: 'planContent',
        observer: false,
        distanceBottom: '25px',
      }"
      class="plan-content w-[90%] mx-auto border-[#ECECEC] border-solid border-[1px] p-[10px] relative"
    >
      <n-scrollbar class="!h-full">
        <div v-if="formData.data.content" v-html="formData.data.content"></div>
        <div
          v-else-if="!loadDataLoading"
          class="w-[100%] flex-col flex mt-[20px] items-center"
        >
          <div class="flex justify-center items-center">
            <img
              :src="ContentEmpty"
              alt=""
              class="w-[300px] h-[265px] object-contain"
            />
          </div>
          <div class="empty-text text-center ml-[-70px] mt-[20px]">
            <span>暂无数据</span>
          </div>
        </div>
        <div
          v-else
          class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
        >
          <n-spin />
        </div>
      </n-scrollbar>
    </div>
    <div v-else>
      <n-form
        :model="formData.data"
        label-placement="left"
        label-width="120px"
        :rules="FormRules"
      >
        <n-form-item label="正文：" path="content">
          <RichEditor v-model:value="formData.data.content" />
        </n-form-item>
      </n-form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.title-text {
  font-family: Microsoft YaHei;
  font-weight: 500;
  font-size: 19px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
}

.empty-text {
  font-family: Microsoft YaHei;
  font-weight: 400;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
  color: #9e9e9e;
}

:deep(.plan-content) {
  white-space: pre-wrap;
  p {
    white-space: pre-wrap;
  }
}
</style>
