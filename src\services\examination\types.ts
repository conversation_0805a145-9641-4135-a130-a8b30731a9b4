/*
 * @Description: 考试中心相关类型
 */

/**
 * 题库列表查询参数
 */
export interface IExaminationLibraryQuery {
  pageNo: number
  pageSize: number
  name?: string
  categoryId?: string
}

/**
 * 题库实体类型
 */
export interface IExaminationLibrary {
  libraryId: number
  libraryName: string
  description: string
  type: string
  sumSubject: number
  multipleChoice: number
  judgeSubject: number
  fillSubject: number
  updatedAt: string
  singleChoice: number
  allowUse: number
  coverUrl: string
}

/**
 * 题库表单类型
 */
export interface IExaminationLibraryForm {
  id?: number
  name: string
  description: string
  type: string
  allowUse: number
  file: null | File
}

/**
 * 题库标签实体类型
 */
export interface IExaminationLibraryLabel {
  id: number
  name: string
  userId: number
  isDeleted: number
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
}

/**
 * 题目列表查询参数
 */
export interface IExaminationQuestionQuery {
  libraryId: number | string | string[]
  pageNo: number
  pageSize: number
  name?: string
}

/**
 * 题目列表项类型
 */
export interface IExaminationQuestion {
  id: number
  name: string
  type: number
  updatedAt: string
  examLabels: string
}

/**
 * 题目选项类型
 */
export interface IExaminationQuestionOption {
  id?: number
  subjectId?: number
  name?: string
  content: string
}

/**
 * 题目详情实体类型
 */
export interface IExaminationQuestionDetail {
  id: number
  name: string
  type: number
  answer: string
  score: number
  analysis?: any
  level: number
  source: string
  published: number
  libraryId: number
  optionList: IExaminationQuestionOption[]
  labelIdList: number[]
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: string
  answerList?: any
  missScore?: any
}

/**
 * 题目表单类型
 */
export interface IExaminationQuestionForm {
  id?: any
  libraryId: number | string // 题库id
  level: number // 难度
  name: string // 题干
  optionList: IExaminationQuestionOption[] // 选项
  answer: string // 答案
  analysis: string // 解析
  source: string // 来源
  labelIdList: number[] // 标签
  type: number

  answerArr?: string[]
}

/**
 * 试卷列表查询参数
 */
export interface IExaminationPaperQuery {
  pageNum: number
  pageSize: number
  paperName?: string
}

/**
 * 试卷列表项类型
 */
export interface IExaminationPaperItem {
  id: number
  paperType: number
  paperMethod: number
  total: number
  name: string
  isOpen: number
  corp: string
  nums: number
  updatedAt: string
}

/**
 * 题目选择页查询参数
 */
export interface IFilteredExaminationQuestionQuery {
  pageNo: number
  pageSize: number
  libraryId: number
  labelList?: number[]
  levelList?: number[]
  typeList?: number[]
  name?: string
}

/**
 * 题目选择页列表项类型
 */
export interface IFilteredExaminationQuestion {
  id: number
  name: string
  type: string
  level: string
  analysis?: string
  source: string
  answer: string
  libName: string
  labelList: string[]
  answerName: string
  createdAt: string
  optionList: { id: number; name: string; subjectId: number; content: string }[]
}

/**
 * 新增、编辑试卷试题类型
 */
export interface IExaminationPaperPostQuestion {
  missScore?: number
  score: number
  subjectId: number
}

/**
 * 新增、编辑试卷组题规则类型
 */
export interface IExaminationPaperPostRule {
  libraryId: number | null
  type: number | null
  level: number | null
  labelId: number | null
  nums: number
  score: number
  missScore?: number

  /** 漏选得分：0-不得分 1-一半得分 */
  missScoreType?: 0 | 1
}

/**
 * 新增、编辑试卷请求体类型
 */
export interface IExaminationPaperPost {
  id?: any
  name: string
  paperMethod: number
  paperType: number
  score: number
  type: number
  answerList?: IExaminationPaperPostQuestion[]
  ruleList?: IExaminationPaperPostRule[]
}

/**
 * 根据条件查询题目数量参数
 */
export interface IExaminationQuestionCountQuery {
  labelId: number
  level: number
  libraryId: number
  type: number
}

/**
 * 考试列表查询参数
 */
export interface IExaminationExamQuery {
  pageNum: number
  pageSize: number
  keyWord?: string
}

/**
 * 考试列表实体类型
 */
export interface IExaminationExam {
  id: number
  isDeleted: number
  name: string
  examinationType: number
  description: string
  startTime: string
  endTime: string
  duration: number
  chances: number
  totalScore: number
  answerAnlysisRule: number
  orderRule: number
  passScore: number
  isPublished: number
  status: number
  enableRange: number
  source: string
  remark: string
  examNumber: string
  passNumber: string
  userIds?: any
  examPaperList?: any
  createdBy: string | null
  createdAt: string | null
  updatedBy: string | null
  updatedAt: string | null
}

/**
 * 考试编辑表单类型
 */
export interface IExaminationExamForm {
  id?: number
  name: string // 考试名称
  description: string // 考试说明
  examPaperList: number[] // 考试试卷
  duration: number // 考试时长 -1不限时长
  examinationType: number | null
  passScore: number | null // 通过分数
  startTime: string // 开始时间
  endTime: string // 结束时间
  answerAnlysisRule: number // 答案和解析 1交卷后显示 2不允许查看 3仅可查看对错
  chances: number // 补考 -1不限次数  0不允许
  orderRule: number // 顺序打乱 1试题顺序打乱  2选项顺序打乱
  enableRange: number // 可见范围 0我的管理范围  1部分范围
  userIds?: number[] // 可见人员ids
  isPublished: number // 是否发布
}

/**
 * 分页查询返回的数据类型
 */
export interface IResPagination<T = any> {
  pageNum: number
  pageSize: number
  totalPage: number
  total: string
  list: T[]
}

/**
 * 关联考试参数
 * relateType: 1-课程 2-课时 3-培训
 */
export interface IRelateExamData {
  examId: number
  relateId: number
  relateType: 1 | 2 | 3 // 1-课程 2-课时 3-培训
}

/**
 * 考试记录实体类
 */
export interface IExamRecord {
  recordId: number
  examName: string
  examUser: string
  phone: string
  totalScore?: any
  examResult: string
  trainName?: any
  courseName?: any
  chances?: any
  description: string
  startTime?: any
  endTime?: any
  score: number
  points: number | null
  createAt: string
}

/**
 * 考试记录查询参数
 */
export interface IExamRecordQuery {
  deptId: number
  examinationType: number
  pageNo: number
  pageSize: number
  username?: string
  phone?: string
  startTime?: string
  endTime?: string
  from: 'admin'
}
