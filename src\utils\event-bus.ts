import mitt from 'mitt'

export const emitter = mitt()

// import type { EventType } from '@easestrategy/event-bus'
// import EaseEventBus from '@easestrategy/event-bus'

// const emitter = new EaseEventBus()

// export default emitter

// /** 事件总线测试 */
// const EMIT_TYPE_TEST: EventType<{ id: string; name: string }> = Symbol('test')
// const EMIT_BACK: EventType = Symbol('back')
// export { EMIT_TYPE_TEST, EMIT_BACK }
