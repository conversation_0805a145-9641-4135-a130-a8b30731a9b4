import type { FormRules } from 'naive-ui'

export function getFormRules(belong: 'reward' | 'handle'): FormRules {
  return {
    userId: [
      {
        required: true,
        message: belong === 'reward' ? '奖励人员不能为空' : '处分人员不能为空',
        trigger: 'input',
      },
    ],
    date: [
      {
        required: true,
        message: '时间不能为空',
        trigger: 'change',
      },
    ],
    type: [
      {
        required: true,
        message: belong === 'reward' ? '奖励名称不能为空' : '处分类别不能为空',
        trigger: 'input',
      },
    ],
    remark: [
      {
        required: true,
        message: belong === 'reward' ? '简要事迹不能为空' : '处分原因不能为空',
        trigger: 'input',
      },
    ],
  }
}
