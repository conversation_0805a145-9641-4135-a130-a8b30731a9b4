<template>
  <div class="flex flex-row">
    <table-container
      v-model:page="pageNum"
      v-model:page-size="pageSize"
      class="w-full"
      title="民主评议党支部班子"
      :show-toolbar="false"
      custom-toolbar
      :loading="loading"
      :total="total"
      :table-columns="tableColumns"
      :table-data="tableData"
      :show-delete="false"
      :show-pagination="true"
      default-expand-all
      :checked-row-keys="checkedRowKeys"
      @update-checked-row-keys="onUpdateCheckedRowKeys"
    >
      <template #btns>
        <div
          class="flex flex-row items-center justify-between gap-[10px] min-w-[300px]"
        >
          <span class="w-[100px]">所在组织：</span>
          <n-cascader
            v-model:value="deptId"
            :options="organizationCurrentListTree"
            value-field="deptId"
            label-field="name"
            children-field="children"
            check-strategy="all"
            :show-path="false"
            clearable
            placeholder="请选择所属党组织"
            @update:value="handleUpdateValue"
          />
        </div>
      </template>
      <!-- <template #filters>
        <n-button size="small" @click="handleExport">
          <template #icon>
            <n-icon>
              <export />
            </n-icon>
          </template>
          导出
        </n-button>
      </template> -->
    </table-container>
  </div>
</template>

<script setup lang="ts">
import { NButton } from 'naive-ui'
import { talkAboutPartyBranchColumns } from './config'
import {
  getTalkAboutPartyBranchList,
  // postExportTalkAboutPartyBranch,
} from '@/services/publicity/vote/talkAbout'
import type { TalkAboutPartyBranchTableItem } from '@/services/publicity/vote/talkAbout/type'
import { useCurrentOrganizationListOptionsNew } from '@/hooks/use-select-options'
// import { downloadArrayBuffer } from '@/utils/downloader'
// import { formatTimeStamp } from '@/utils/format'
const router = useRouter()
const route = useRoute()
const reviewId = ref((route.query.reviewId as string) || '') // 评议id
// const talkAboutYear = ref((route.query.year as string) || '') // 评议年份
const pageNum = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const tableData = ref<TalkAboutPartyBranchTableItem[]>([])
const total = ref(0)
const deptId = ref<any>(null)
// 获取组织树
const { organizationCurrentListTree } = useCurrentOrganizationListOptionsNew()
const handleUpdateValue = (v: string) => {
  deptId.value = v
}

// 修改和删除按钮渲染
const tableColumns = talkAboutPartyBranchColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          router.push({
            name: 'talk-about-branch-detail',
            query: {
              name: row.orgName,
              orgId: row.orgIdRecord || '',
              reviewId: reviewId.value,
              reviewItemId: row.reviewItemId,
            },
          })
        },
        type: 'primary',
        text: true,
      },
      {
        default: () => '详情',
      },
    ),
  ]
})

const loadData = () => {
  loading.value = true
  const data = {
    pageSize: pageSize.value,
    pageNum: pageNum.value,
    reviewId: reviewId.value,
    orgId: deptId.value,
  }
  getTalkAboutPartyBranchList(data)
    .then((res) => {
      total.value = res.total || 0
      tableData.value = res.records || []
    })
    .finally(() => {
      loading.value = false
    })
}

/** 选中的行id */
const checkedRowKeys = ref<Array<number | string>>(
  tableData.value.map(item => item.orgIdRecord) || [],
)

// /** 行选中 */
function onUpdateCheckedRowKeys(ids: Array<number | string>) {
  checkedRowKeys.value = ids
}

// 导出
// async function handleExport() {
//   if (checkedRowKeys.value.length < 1) {
//     return window.$message.warning('请选择需要导出的数据')
//   }
//   try {
//     loading.value = true
//     const data = {
//       reviewId: reviewId.value,
//       deptId: checkedRowKeys.value as string[],
//       year: talkAboutYear.value,
//     }
//     const res = await postExportTalkAboutPartyBranch(data)
//     downloadArrayBuffer(
//       res,
//       `民主评议党支部班子-${formatTimeStamp(Date.now(), 'YYYYMMDDHHmmss')}`,
//       'application/x-zip-compressed',
//     )
//     checkedRowKeys.value = []
//     loading.value = false
//   } catch (error) {}
// }

watch(
  () => [deptId.value, pageSize.value, pageNum.value],
  () => {
    loadData()
  },
)

onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss"></style>
