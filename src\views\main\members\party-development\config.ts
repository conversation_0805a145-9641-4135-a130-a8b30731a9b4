import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import dayjs from 'dayjs'
import type { PartyDevelopmentListItem } from '@/services/structure/party-development/types'
import { SEX, getDictLabelByValue } from '@/store/dict'

export function getTableColumns(
  optionColumnRenderer: (row: PartyDevelopmentListItem) => VNodeChild,
): DataTableColumns<PartyDevelopmentListItem> {
  return [
    // { type: 'selection' },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'developStage',
      title: '发展阶段',
    },
    {
      key: 'trueName',
      title: '姓名',
    },
    {
      key: 'organization',
      title: '所属党组织',
    },
    {
      key: 'sex',
      title: '性别',
      render: (row) => {
        return getDictLabelByValue(SEX, row.sex)
      },
    },
    {
      key: 'edu',
      title: '学历',
    },
    {
      key: 'birthday',
      title: '出生日期',
    },
    {
      key: 'applyTime',
      title: '申请时间',
      render: row => dayjs(row.applyTime).format('YYYY-MM-DD'),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
