/*
 * @Description: 考试中心相关接口
 */
import qs from 'qs'
import { commonReq, downloadReq } from '../request'
import type {
  IExamRecord,
  IExamRecordQuery,
  IExaminationExam,
  IExaminationExamForm,
  IExaminationExamQuery,
  IExaminationLibrary,
  IExaminationLibraryLabel,
  IExaminationLibraryQuery,
  IExaminationPaperItem,
  IExaminationPaperPost,
  IExaminationPaperQuery,
  IExaminationQuestion,
  IExaminationQuestionCountQuery,
  IExaminationQuestionDetail,
  IExaminationQuestionForm,
  IExaminationQuestionQuery,
  IFilteredExaminationQuestion,
  IFilteredExaminationQuestionQuery,
  IRelateExamData,
  IResPagination,
} from '@/services/examination/index'
export * from './types'

/* ======================================= 题库管理 ======================================= */
/**
 * 获取题库列表
 */
export function getExaminationLibraryList(params: IExaminationLibraryQuery) {
  return commonReq.get<IResPagination<IExaminationLibrary>>({
    url: '/train/admin/v1/exam/library',
    params,
  })
}

/**
 * 删除题库
 */
export function deleteExaminationLibrary(id: number) {
  return commonReq.delete<string>({
    url: `/train/admin/v1/exam/library/${id}`,
  })
}

/**
 * 新增、修改题库
 */
export function postExaminationLibrary(data: FormData) {
  return commonReq.post<string>({
    url: '/train/admin/v1/exam/library',
    data,
  })
}

/* ======================================= 题库标签 ======================================= */
/**
 * 获取标签列表
 */
export function getExaminationLibraryLabelList() {
  return commonReq.get<IExaminationLibraryLabel[]>({
    url: '/train/admin/v1/exam/label',
  })
}

/**
 * 新增标签
 */
export function postExaminationLibraryLabel(labelName: string) {
  return commonReq.post<string>({
    url: '/train/admin/v1/exam/label',
    params: {
      labelName,
    },
  })
}

/**
 * 修改标签
 */
export function putExaminationLibraryLabel(id: number, labelName: string) {
  return commonReq.put<string>({
    url: `/train/admin/v1/exam/label/${id}`,
    params: {
      labelName,
    },
  })
}

/**
 * 删除标签
 */
export function deleteExaminationLibraryLabel(id: number) {
  return commonReq.delete<string>({
    url: `/train/admin/v1/exam/label/${id}`,
  })
}

/* ======================================= 题目 ======================================= */
/**
 * 获取题目列表
 */
export function getExaminationQuestionList(params: IExaminationQuestionQuery) {
  return commonReq.get<IResPagination<IExaminationQuestion>>({
    url: '/train/admin/v1/exam/subject',
    params,
  })
}

/**
 * 获取题目详情
 */
export function getExaminationQuestionDetail(id: number) {
  return commonReq.get<IExaminationQuestionDetail>({
    url: `/train/admin/v1/exam/subject/${id}`,
  })
}

/**
 * 新增、编辑题目
 */
export function postExaminationQuestion(data: IExaminationQuestionForm) {
  return commonReq.post<string>({
    url: '/train/admin/v1/exam/subject',
    data,
  })
}

/**
 * 删除题目
 */
export function deleteExaminationQuestion(ids: string) {
  return commonReq.delete<string>({
    url: `/train/admin/v1/exam/subject/${ids}`,
  })
}

/**
 * 导入题目
 */
export function importExaminationQuestion(data: FormData) {
  return commonReq.post<string>({
    url: '/train/admin/v1/exam/import',
    data,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  })
}

/**
 * 导出题目
 */
export function exportExaminationQuestion(data: any) {
  return downloadReq.post({
    url: '/train/admin/v1/exam/export',
    responseType: 'blob',
    data,
  })
}

/* ======================================= 试卷管理 ======================================= */
/**
 * 获取试卷列表
 */
export function getExaminationPaperList(params: IExaminationPaperQuery) {
  return commonReq.get<IResPagination<IExaminationPaperItem>>({
    url: '/train/admin/v1/papers',
    params,
  })
}

/**
 * 是否开放考试
 */
export function putExaminationPaper(paperId: number, isOpen: 1 | 0) {
  return commonReq.put({
    url: '/train/admin/v1/papers',
    params: {
      paperId,
      isOpen,
    },
  })
}

/**
 * 删除试卷
 */
export function deleteExaminationPaper(id: number) {
  return commonReq.delete<string>({
    url: '/train/admin/v1/papers',
    params: { id },
  })
}

/**
 * 根据条件获取题目列表
 */
export function getFilteredExaminationQuestionList(
  params: IFilteredExaminationQuestionQuery,
) {
  return commonReq.get<IResPagination<IFilteredExaminationQuestion>>({
    url: '/train/admin/v1/papers/subjects',
    params,
    interceptors: {
      requestInterceptor: (config) => {
        config.paramsSerializer = params =>
          qs.stringify(params, { arrayFormat: 'repeat' })
        return config
      },
    },
  })
}

/**
 * 新增、编辑试卷
 */
export function postExaminationPaper(data: IExaminationPaperPost) {
  return commonReq.post<string>({
    url: '/train/admin/v1/papers',
    data,
  })
}

/**
 * 获取试卷详情
 */
export function getExaminationPaperDetail(id: any) {
  return commonReq.get({
    url: `/train/admin/v1/papers/item/${id}`,
  })
}

/**
 * 获取有题目的题库
 */
export function getLibraryListHasQuestion() {
  return commonReq.get<any[]>({
    url: '/train/admin/v1/papers/libraries',
  })
}

/**
 * 根据题库查询有题目的题型
 */
export function getTypeListHasQuestion(libraryId: number) {
  return commonReq.get<number[]>({
    url: '/train/admin/v1/papers/libraries/type',
    params: { libraryId },
  })
}

/**
 * 根据题库和题型查询有题目的难度
 */
export function getLevelListHasQuestion(libraryId: number, type: number) {
  return commonReq.get<number[]>({
    url: '/train/admin/v1/papers/libraries/level',
    params: { libraryId, type },
  })
}

/**
 * 根据题库、题型、难度查询有题目的标签
 */
export function getLabelListHasQuestion(
  libraryId: number,
  type: number,
  level: number,
) {
  return commonReq.get<IExaminationLibraryLabel[]>({
    url: '/train/admin/v1/papers/libraries/label',
    params: { libraryId, type, level },
  })
}

/**
 * 根据条件查询题目数量
 */
export function getExaminationQuestionCount(
  params: IExaminationQuestionCountQuery,
) {
  return commonReq.get<number>({
    url: '/train/admin/v1/papers/library',
    params,
  })
}

/* ======================================= 考试管理 ======================================= */
/**
 * 获取考试列表
 */
export function getExaminationExamList(params: IExaminationExamQuery) {
  return commonReq.get<IResPagination<IExaminationExam>>({
    url: '/train/admin/v1/exam',
    params,
  })
}

/**
 * 保存考试
 */
export function postExaminationExam(data: IExaminationExamForm) {
  return commonReq.post<string>({
    url: '/train/admin/v1/exam',
    data,
  })
}

/**
 * 发布考试
 */
export function publishExaminationExam(examId: number, isPublish: 0 | 1) {
  return commonReq.post<string>({
    url: '/train/admin/v1/published',
    params: { status: isPublish, examId },
  })
}

/**
 * 获取考试详情
 */
export function getExaminationExamDetail(id: any) {
  return commonReq.get<IExaminationExam>({
    url: `/train/admin/v1/exam/${id}`,
  })
}

/**
 * 删除考试
 */
export function deleteExaminationExam(id: number) {
  return commonReq.delete<string>({
    url: `/train/admin/v1/exam/${id}`,
  })
}

/**
 * 获取所有开放考试的试卷
 */
export function getAllOpenedPapers() {
  return commonReq.get<IExaminationPaperItem[]>({
    url: '/train/admin/v1/papers/1',
  })
}

/**
 * 获取考试选项
 */
export function getExamOptions(examinationType: number) {
  return commonReq.get<IExaminationExam[]>({
    url: '/train/admin/v1/finalExamList',
    params: { examinationType },
  })
}

/**
 * 关联考试
 */
export function relateExam(data: IRelateExamData) {
  return commonReq.post<string>({
    url: '/study/admin/v1/paper/1',
    data,
  })
}

/* ======================================= 考试记录 ======================================= */
/**
 * 获取考试记录列表
 */
export function getExamRecordList(params: IExamRecordQuery) {
  return commonReq.post<IResPagination<IExamRecord>>({
    url: '/train/admin/v1/record',
    data: params,
  })
}

/**
 * 获取记录的详情
 */
export function getExamRecordDetail(recordId: number) {
  return commonReq.get({
    url: '/train/admin/v1/out',
    params: { recordId },
  })
}

/**
 * 新增题库分类
 */
export function addBankCategory(data: any) {
  return commonReq.post<string>({
    url: '/train/admin/v1/exam/library/category',
    data,
  })
}

/**
 * 获取题库分类列表
 */
export function getBankCategoryList() {
  return commonReq.get({
    url: '/train/admin/v1/exam/library/category',
  })
}

/**
 * 修改题库分类
 */
export function updateBankCategory(data: any) {
  return commonReq.put<string>({
    url: `/train/admin/v1/exam/library/category/${data.id}`,
    params: { title: data.categoryTitle },
  })
}

/**
 * 题库分类移动
 */
export function moveBankCategory(data: any) {
  return commonReq.put<string>({
    url: '/train/admin/v1/exam/library/category/move',
    data,
  })
}

/**
 * 删除题库分类
 */
export function delBankCategory(id: any) {
  return commonReq.delete<string>({
    url: `/train/admin/v1/exam/library/category/${id}`,
  })
}

/** 校验父类下面是否有子类 */
export function checkCategoryHaveChildren(id: string) {
  return commonReq.delete({
    url: '/train/admin/v1/exam/library/category/try-delete',
    params: {
      id,
    },
  })
}
