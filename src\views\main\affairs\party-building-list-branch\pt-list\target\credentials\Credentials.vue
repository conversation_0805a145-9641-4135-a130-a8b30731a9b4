<script setup lang="ts">
import { IosCloseCircleOutline } from '@vicons/ionicons4'
import MeetingChoice from '../meeting-choice/MeetingChoice.vue'
import VoteChoice from '../vote-choice/VoteChoice.vue'
import DetailHeader from '@/components/DetailHeader.vue'
import { uploadImg } from '@/services'
import {
  getCredentialsDetail,
  putCredentials,
} from '@/services/affairs/party-building-list-branch'
import type {
  BranchPartyMeetingList,
  BranchVoteScoreList,
  CredentialsData,
} from '@/services/affairs/party-building-list-branch/types'
import type {
  uploadFileItem,
  uploadFileItemNew,
} from '@/services/affairs/party-building-list/exam-indicators/types'
import { useMeetingVoteChoicesStore } from '@/store/meeting-vote-choice/meeting-vote-choice'
import type { FileList } from '@/services/affairs/party-building-list/types'

const route = useRoute()
const router = useRouter()
const enterType = computed(() => route.query.type ?? null)
const id = computed(() => route.query.id as string)
const loading = ref(false)

const title = ref()
if (enterType.value === 'modify') {
  title.value = '编辑完成情况'
} else if (enterType.value === 'add') {
  title.value = '填写完成情况'
} else {
  title.value = '查看完成情况'
}

const formData = reactive<CredentialsData>({
  id: id.value,
  meetinglink: '',
  votelink: '',
  performanceDescription: '',
  fileIds: [] as any,
  fileList: [] as any[],
})

const meetingVoteStore = useMeetingVoteChoicesStore()
// 会议选择
const meetingModalVisible = ref(false)
const meetingRef = ref()
const meetingTableRef = ref()
// 点击会议选择按钮
function handleMeetingChoice() {
  meetingModalVisible.value = true
}

// 选择的会议key值
const rowKeys = ref<Array<string | number>>([])
function handleRowKeys(v: Array<string | number>) {
  rowKeys.value = v
  formData.meetinglink = rowKeys.value.join(',')
}
const checkedMeetingList = ref<BranchPartyMeetingList[]>([])
// 会议链接选择
function handleMeetingConfirm() {
  meetingVoteStore.allMeetingList.forEach((item) => {
    if (rowKeys.value.includes(item.id)) {
      item.relatedStatus = true
    }
  })
  checkedMeetingList.value = meetingVoteStore.allMeetingList.filter(item =>
    rowKeys.value.includes(item.id),
  )

  meetingVoteStore.setCheckMeeting(checkedMeetingList.value)
  window.$message.success('会议选择成功')
  meetingModalVisible.value = false
}

// 删除已选择的会议
function handleDeleteChoiceMeeting(id: string) {
  if (enterType.value === 'view') {
    return
  }
  meetingVoteStore.allMeetingList.forEach((item) => {
    if (item.id === id) {
      item.relatedStatus = false
    }
  })
  checkedMeetingList.value = meetingVoteStore.checkMeeting.filter(
    item => item.id !== id,
  )
  meetingVoteStore.setCheckMeeting(checkedMeetingList.value)
  formData.meetinglink = checkedMeetingList.value
    .map(item => item.id)
    .join(',')
}

// 投票打分选择
const voteScoreModalVisible = ref(false)
const voteScoreRef = ref()
const voteScoreTableRef = ref()
// 点击会议选择按钮
function handleVoteScoreChoice() {
  voteScoreModalVisible.value = true
}

// 选择的key值
const voteScoreRowKeys = ref<Array<string | number>>([])
function handleVoteRowKeys(v: Array<string | number>) {
  voteScoreRowKeys.value = v
  formData.votelink = voteScoreRowKeys.value.join(',')
}
const checkedVoteList = ref<BranchVoteScoreList[]>([])
function handleVoteScoreConfirm() {
  meetingVoteStore.allVoteList.forEach((item) => {
    if (voteScoreRowKeys.value.includes(item.id)) {
      item.relatedStatus = true
    }
  })
  checkedVoteList.value = meetingVoteStore.allVoteList.filter(item =>
    voteScoreRowKeys.value.includes(item.id),
  )
  meetingVoteStore.setCheckVote(checkedVoteList.value)
  window.$message.success('投票打分链接选择成功')
  voteScoreModalVisible.value = false
}
// 删除已选择的投票打分
function handleDeleteChoiceVote(id: string) {
  if (enterType.value === 'view') {
    return
  }
  meetingVoteStore.allVoteList.forEach((item) => {
    if (item.id === id) {
      item.relatedStatus = false
    }
  })
  checkedVoteList.value = meetingVoteStore.checkVote.filter(
    item => item.id !== id,
  )
  meetingVoteStore.setCheckVote(checkedVoteList.value)
  formData.votelink = checkedVoteList.value.map(item => item.id).join(',')
}

onBeforeMount(() => {
  if (
    (enterType.value === 'modify' || enterType.value === 'view')
    && id.value
  ) {
    loading.value = true
    getCredentialsDetail(id.value).then((res) => {
      formData.meetinglink = res.meetinglink
        .map((item: any) => item.id)
        .join(',')
      formData.votelink = res.votelink.map((item: any) => item.id).join(',')
      formData.performanceDescription = res.performanceDescription
      // 课程内容
      formData.fileList = res.fileList.map((file: FileList) => {
        return {
          id: file.id,
          fileName: file.fileName,
          original: file.original,
        }
      })
      formData.fileIds = res.fileList.map((item: any) => item.id) || []

      checkedMeetingList.value = res.meetinglink
      checkedVoteList.value = res.votelink
    })
    loading.value = false
  }
})

// 文件上传
const fileIdObj = reactive<{ fileIdsArr: Array<string> }>({ fileIdsArr: [] })

// 文件相关
async function handleFileChange(
  fileInfoList: uploadFileItemNew[],
  isDelIDs: string,
) {
  try {
    const fileData = new FormData()
    // 获取所有FileID
    if (!fileIdObj.fileIdsArr.length) {
      formData.fileList?.forEach((item: any) =>
        fileIdObj.fileIdsArr.push(item.id),
      )
    }
    // 删除动作
    if (isDelIDs || isDelIDs === null) {
      if (isDelIDs === null && fileIdObj.fileIdsArr.length) {
        fileIdObj.fileIdsArr.splice(fileIdObj.fileIdsArr.length - 1, 1)
      } else {
        fileIdObj.fileIdsArr.forEach((item, index) => {
          if (item === isDelIDs) {
            fileIdObj.fileIdsArr.splice(index, 1)
          }
        })
      }
      formData.fileIds = [...fileIdObj.fileIdsArr]
    } else {
      // 新增动作
      const lastFileItem = fileInfoList[fileInfoList.length - 1]?.file
      fileData.append('file', lastFileItem as Blob)
      const data: uploadFileItem = await uploadImg(fileData)
      formData.fileList?.push({
        original: lastFileItem?.name as string,
        fileName: data.url || '',
        id: data.fileId,
      })
      if (data) {
        fileIdObj.fileIdsArr.push(data.fileId)
        formData.fileIds = [...fileIdObj.fileIdsArr]
      }
    }
  } catch (error) {}
}

const requiredItemsList = ['performanceDescription'] as const
async function publishConfirm() {
  let flag = false
  requiredItemsList.forEach((item) => {
    if (!formData[item]) {
      flag = true
    }
  })
  if (flag) {
    window.$message.error('请完成必填项!')
    return
  }
  await putCredentials(formData)
  window.$message.success('已提交')
  meetingVoteStore.clearAll()

  formData.fileList = []
  formData.fileIds = []
  formData.meetinglink = ''
  formData.votelink = ''
  formData.performanceDescription = ''
  router.back()
}
function handleClearMeetingVoteStore() {
  meetingVoteStore.clearAll()
}
</script>
<template>
  <div>
    <DetailHeader
      :header-title="title"
      right-btn-text="提交"
      :is-show-confirm-btn="enterType === 'view' ? false : true"
      :release="publishConfirm"
      :need-show-dialog="enterType === 'view' ? false : true"
      @emit-back="handleClearMeetingVoteStore"
    />
    <n-form
      ref="formRef"
      size="small"
      require-mark-placement="left"
      label-width="120"
      label-align="right"
      label-placement="left"
      class="pl-[200px] w-[70%] pt-[20px] pb-[100px] flex flex-col gap-y-[20px]"
    >
      <n-form-item label="完成情况描述：" path="title" required>
        <n-input
          v-model:value="formData.performanceDescription"
          :disabled="enterType === 'view'"
          style="width: 600px"
          maxlength="200"
          type="textarea"
          show-count
          clearable
          :autosize="{ minRows: 5, maxRows: 8 }"
        />
      </n-form-item>
      <n-form-item label="附件：">
        <div class="w-[620px]">
          <file-uploader
            :is-readonly="enterType === 'view' ? true : false"
            :max="100"
            accept=".doc,.docx,.pdf"
            size-limit="50"
            :original-file-list="(formData.fileList as any)"
            @file-list-change="handleFileChange"
          >
            <template #tips>
              <span class="tips">可上传多个文件，支持扩展名：.doc，.docx，.pdf,大小 50M
                以内</span>
            </template>
          </file-uploader>
        </div>
      </n-form-item>
      <n-form-item label="会议链接：">
        <div class="flex flex-col gap-y-[20px]">
          <div>
            <n-button
              :disabled="enterType === 'view'"
              type="info"
              @click="handleMeetingChoice"
            >
              选择会议
            </n-button>
          </div>
          <div
            v-if="checkedMeetingList.length"
            class="grid grid-cols-5 gap-[20px]"
          >
            <div
              v-for="(item, index) in checkedMeetingList"
              :key="index"
              class="border rounded-[4px] flex justify-between items-center px-[10px] py-[6px]"
            >
              <span>{{ item.title }}</span>
              <n-divider vertical />
              <n-icon
                size="18"
                :class="
                  enterType === 'view' ? 'cursor-not-allowed' : 'cursor-pointer'
                "
                @click="handleDeleteChoiceMeeting(item.id)"
              >
                <IosCloseCircleOutline />
              </n-icon>
            </div>
          </div>
        </div>
      </n-form-item>
      <n-form-item label="投票打分链接：">
        <div class="flex flex-col gap-y-[20px]">
          <div>
            <n-button
              :disabled="enterType === 'view'"
              type="info"
              @click="handleVoteScoreChoice"
            >
              选择投票打分
            </n-button>
          </div>
          <div
            v-if="checkedVoteList.length"
            class="grid grid-cols-5 gap-[20px]"
          >
            <div
              v-for="(item, index) in checkedVoteList"
              :key="index"
              class="border rounded-[4px] flex justify-between items-center px-[10px] py-[6px]"
            >
              <span>{{ item.title }}</span>
              <n-divider vertical />
              <n-icon
                size="18"
                :class="
                  enterType === 'view' ? 'cursor-not-allowed' : 'cursor-pointer'
                "
                @click="handleDeleteChoiceVote(item.id)"
              >
                <IosCloseCircleOutline />
              </n-icon>
            </div>
          </div>
        </div>
      </n-form-item>
    </n-form>
  </div>

  <custom-dialog
    ref="meetingRef"
    v-model:show="meetingModalVisible"
    width="1200px"
    title="选择会议"
    @confirm="handleMeetingConfirm"
  >
    <meeting-choice ref="meetingTableRef" @update:value="handleRowKeys" />
  </custom-dialog>

  <custom-dialog
    ref="voteScoreRef"
    v-model:show="voteScoreModalVisible"
    width="1200px"
    title="选择投票打分"
    @confirm="handleVoteScoreConfirm"
  >
    <vote-choice ref="voteScoreTableRef" @update:value="handleVoteRowKeys" />
  </custom-dialog>
</template>

<style scoped lang="scss">
::v-deep(.n-upload-trigger) {
  width: 220px;
  height: 150px;
}
</style>
