import type {
  MeetingDetailBaseInfo,
  PartyMeetingListItem,
  PartyMeetingListParams,
  PartyMeetingMaterialItem,
  PartyMeetingMemberItem,
  PartyMeetingMemberListParams,
  PartyMeetingNoteDetail,
  PartyMeetingNoteItem,
  PartyMeetingSummaryDetail,
} from './types'
import { commonReq } from '@/services/request'
import type { PaginationRes } from '@/services/types'

/** 获取会议列表 */
export function getMeetingList(params: PartyMeetingListParams) {
  return commonReq.get<PaginationRes<PartyMeetingListItem>>({
    url: '/party-affairs/backend/v1/internal-meeting',
    params,
  })
}

/** 删除会议 */
export function deleteMeetings(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/internal-meeting',
    params: { ids },
  })
}

/** 会议详情 */
export function getMeetingDetail(id: string) {
  return commonReq.get<MeetingDetailBaseInfo>({
    url: `/party-affairs/backend/v1/internal-meeting/${id}`,
  })
}

/** 获取参会人员列表 */
export function getAttendMeetingMemberList(
  params: PartyMeetingMemberListParams,
) {
  return commonReq.get<PaginationRes<PartyMeetingMemberItem>>({
    url: '/party-affairs/backend/v1/internal-meeting-user',
    params,
  })
}

/** 签到 */
export function putSign(id: string, userId: string) {
  return commonReq.put({
    url: `/party-affairs/backend/v1/internal-meeting-user/signed/${id}`,
    params: { userId },
  })
}

/** 签到撤销 */
export function putRevokeSign(id: string, userId: string) {
  return commonReq.put({
    url: `/party-affairs/backend/v1/internal-meeting-user/signed/revoke/${id}`,
    params: { userId },
  })
}

/** 查询会议资料文件列表 */
export function getMeetingMaterialList(params: { meetingId: string }) {
  return commonReq.get<PartyMeetingMaterialItem>({
    url: '/party-affairs/backend/v1/internal-meeting-file',
    params,
  })
}

/** 上传资料 */
export function postMeetingMaterial(data: {
  meetingId: string
  fileIds: string
}) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/internal-meeting-file',
    data,
  })
}

/** 删除资料 */
export function deleteMeetingMaterial(params: { ids: string }) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/internal-meeting-file',
    params,
  })
}

/** 查询心得笔记列表 */
export function getMeetingNoteList(params: { meetingId: string }) {
  return commonReq.get<PartyMeetingNoteItem>({
    url: '/party-affairs/backend/v1/internal-meeting-note',
    params,
  })
}

/** 心得笔记详情 */
export function getMeetingNoteDetail(id: string) {
  return commonReq.get<PartyMeetingNoteDetail>({
    url: `/party-affairs/backend/v1/internal-meeting-note/${id}`,
  })
}

/** 会议总结详情 */
export function getMeetingSummaryDetail(id: string) {
  return commonReq.get<PartyMeetingSummaryDetail>({
    url: `/party-affairs/backend/v1/internal-meeting/summary/${id}`,
  })
}

/** 编辑会议总结 */
export function putMeetingSummary(data: {
  meetingId: string
  content: string
  fileIds: string
}) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/internal-meeting/summary',
    data,
  })
}
