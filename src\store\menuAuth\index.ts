import { defineStore } from 'pinia'
import type { RouteRecordRaw } from 'vue-router'
import { RouterView } from 'vue-router'
import router from '@/router'
import { getMenus } from '@/services/auth/auth'
import { sessionCache } from '@/utils/cache'
import { MENU_DICT } from '@/views/main/system/authority/menu/config'

// import type { RouteRecordRaw } from '@/router/type'
declare module 'vue-router' {
  interface _RouteRecordBase {
    meta?: RouteMeta
    orderBy?: number
    hidden?: boolean
    filePath?: string
    component?: RouteComponent
    icon?: string
    sortOrder?: number
    label?: string
    parentId?: string
    category?: string
    routeName?: string
    type?: string
    visible?: string
  }
}
const modules = import.meta.glob('../../views/**/**.vue')

export const useMenuAuthStore = defineStore('menuAuth', () => {
  const isRoutesGenerated = ref(false)
  const categoryList = ref()
  const asyncRouter2: any = {
    path: '/main',
    name: 'main',
    redirect: sessionCache.get('cur_full_path') ?? { name: 'workbench' },
    filePath: 'Main',
    children: [
      {
        path: 'workbench',
        name: 'workbench',
        routeName: 'workbench',
        label: '工作台',
        icon: 'workbench',
        sortOrder: 0,
        filePath: 'workbench/WorkBench',
      },
    ],
  }
  /** 获取接口动态路由 */
  function getDynamicRoute() {
    return new Promise((resolve) => {
      if (!isRoutesGenerated.value) {
        generateRoutes().then(() => {
          isRoutesGenerated.value = true
          resolve('1')
        })
      }
      else {
        resolve('2')
      }
    })
  }
  /** 处理路由 */
  async function generateRoutes() {
    const menuList: any = await getMenus()
    if (menuList) {
      return new Promise((resolve) => {
        menuList.forEach((route: RouteRecordRaw) => {
          // 说明是一级 在工作台中展示 而不是真实路由
          if (route.parentId === '-1') {
            if (categoryList.value == null) {
              categoryList.value = []
            }
            categoryList.value.push({ title: route.name, name: route.icon })
            // 将真实路由分离
            if (route.children && route.children.length > 0) {
              const list = route.children
              list.forEach((el: RouteRecordRaw) => {
                el.category = route.icon
              })
              asyncRouter2.children = asyncRouter2.children?.concat(list)
            }
          }
        })

        getAsyncRoute(asyncRouter2, true)
        resolve('')
      })
    }
    else {
      return new Promise((resolve) => {
        getAsyncRoute(asyncRouter2, true)
        resolve('1')
      })
    }
  }
  // 处理按钮权限
  function removePermitted(arr: any[]): any[] {
    const result = []

    for (let i = 0; i < arr.length; i++) {
      // 每一个元素
      const item = arr[i]

      if (Array.isArray(item)) {
        result.push(removePermitted(item))
      }
      else if (item.children) {
        // 下面有子菜单，左侧菜单
        item.children = removePermitted(item.children)
        result.push(item)
      }
      else {
        // 如果不是按钮，就加入路由
        if (item.type !== MENU_DICT.BTN) {
          result.push(item)
        }
      }
    }

    return result
  }

  function getAsyncRoute(route: RouteRecordRaw, root: Boolean) {
    if (route.filePath?.toLocaleLowerCase() === 'routerview') {
      route.component = () => RouterView
    }
    else {
      route.component = () =>
        modules[`../../views/main/${route.filePath}.vue`]()
    }

    // 因为各层处理不一致 所以没写递归 后续可改
    if (route.children && route.children.length > 0) {
      // 工作台展示的菜单
      route.children.sort(sortMenu)
      route.children = removePermitted(route.children)

      for (let i = 0; i < route.children.length; i++) {
        const el: RouteRecordRaw = route.children[i]
        el.meta = {
          title: el.label,
          icon: el.icon,
          order: el.sortOrder,
          category: el.category,
        }
        el.name = el.routeName
        if (el.filePath?.toLocaleLowerCase() === 'routerview') {
          el.component = () => RouterView
        }
        else {
          el.component = () => modules[`../../views/main/${el.filePath}.vue`]()
        }

        getAsyncRoute(el, false)

        if (root && el.children && el.children.length > 0 && !el.redirect) {
          // 目前是默认第一层都是重定向到第一个子路由
          el.redirect = { name: el.children[0].name }
        }
        else if (el.redirect) {
          el.redirect = { name: `${el.redirect}` }
        }
      }
    }
    if (root) {
      router.addRoute(route)
    }
  }
  function sortMenu(a: RouteRecordRaw, b: RouteRecordRaw) {
    return (a.sortOrder ?? 0) - (b.sortOrder ?? 0)
  }
  function resetMenu() {
    isRoutesGenerated.value = false
    location.reload()
  }
  return {
    getDynamicRoute,
    categoryList,
    resetMenu,
  }
})
