<script lang="ts" setup>
import type { CategoryItem } from '@/services/study/study-manage/types'

interface Props {
  value: CategoryItem[]
  title: string
  top?: string
  activatedID: string
}

const props = withDefaults(defineProps<Props>(), { top: '110px' })
const emit = defineEmits<{
  (e: 'select', item: CategoryItem | '0' | undefined): void
}>()

const hoverId = ref('-1')
const activatedId = ref('')

// 党建书库列表
const partyLibraryList = ref(
  props.value.filter(item => item.categoryFlag === '1'),
)
// 廉洁课堂列表
const honestClassList = ref(
  props.value.filter(item => item.categoryFlag === '2'),
)

/** 获取分类值  */
watchEffect(() => {
  partyLibraryList.value = props.value.filter(
    item => item.categoryFlag === '1',
  )
  honestClassList.value = props.value.filter(
    item => item.categoryFlag === '2',
  )
})

watch(
  () => props.activatedID,
  (newVal) => {
    activatedId.value = newVal
  },
)

function handleHover(id: string) {
  hoverId.value = id
}

function handleClick(id: string, item?: CategoryItem) {
  activatedId.value = id
  if (id === '-1' || id === '-2') {
    // emit('select', { id: '0', name: '', sort: 0 })
  } else {
    emit('select', item)
  }
}

// 菜单箭头
const expandedMenus = [ref(true), ref(true)]

const handleArrow = (index: number) => {
  expandedMenus[index].value = !expandedMenus[index].value
}
</script>
<template>
  <div class="fixed left-0 w-[259px] bg-[#F9FAFB] bottom-0 px-[15px] root">
    <div class="px-[8px] pt-[25px] flex justify-between text-[14px] font-[500]">
      <span>{{ title }}</span>
    </div>

    <div class="mt-[10px] text-[18px] overflow-scroll h-[calc(100vh-300px)]">
      <div
        :class="{ activated: activatedId === '-1' }"
        class="flex items-center hover:bg-[#E4E8F0] rounded-[6px] ml-[12px] pr-[10px]"
      >
        <icon-right-arrow
          class="w-[12px] cursor-pointer transform"
          :class="[expandedMenus[0].value ? 'rotate-[90deg]' : 'rotate-[0]']"
          @click="handleArrow(0)"
        />
        <span
          class="font-[500] text-[12px] cursor-pointer pl-[10px] leading-[40px] w-full"
          @click="handleClick('-1')"
        >
          党建书库
        </span>
      </div>
      <div>
        <div
          v-for="item in partyLibraryList"
          v-show="expandedMenus[0].value"
          :key="item.id"
          :class="{ activated: activatedId === item.id }"
          class="pl-[30px] my-[5px] text-[12px] relative ml-[10px] font-[400] hover:bg-[#E4E8F0] hover:transition-all cursor-pointer rounded-[6px] flex justify-between items-center pr-[20px] leading-[40px]"
          @mouseleave="() => (hoverId = '-1')"
          @mouseover="handleHover(item.id)"
        >
          <div class="w-[140px]" @click="handleClick(item.id, item)">
            {{ item.name }}
          </div>
        </div>
      </div>
      <div
        :class="{ activated: activatedId === '-2' }"
        class="flex items-center hover:bg-[#E4E8F0] rounded-[6px] ml-[12px] pr-[10px]"
      >
        <icon-right-arrow
          class="w-[12px] cursor-pointer"
          :class="[expandedMenus[1].value ? 'rotate-[90deg]' : 'rotate-[0]']"
          @click="handleArrow(1)"
        />
        <span
          class="font-[500] text-[12px] cursor-pointer pl-[10px] leading-[40px] w-full"
          @click="handleClick('-2')"
        >
          廉洁课堂
        </span>
      </div>
      <div class="overflow-scroll h-[calc(100vh-300px)]">
        <div
          v-for="item in honestClassList"
          v-show="expandedMenus[1].value"
          :key="item.id"
          :class="{ activated: activatedId === item.id }"
          class="pl-[30px] my-[5px] text-[12px] relative ml-[10px] font-[400] hover:bg-[#E4E8F0] hover:transition-all cursor-pointer rounded-[6px] flex justify-between items-center pr-[20px] leading-[40px]"
          @mouseleave="() => (hoverId = '-1')"
          @mouseover="handleHover(item.id)"
        >
          <div class="w-[140px]" @click="handleClick(item.id, item)">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.root {
  top: v-bind(top);

  .activated {
    background-color: #e4e8f0;
  }

  ::-webkit-scrollbar {
    display: none;
  }
}
</style>
