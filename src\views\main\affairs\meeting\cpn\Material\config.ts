import { type DataTableColumns, NEllipsis, NImage } from 'naive-ui'
import type { VNodeChild } from 'vue'
import type { PartyMeetingMaterialItem } from '@/services/affairs/party-meeting/types'
import pdf from '@/assets/image/meeting/pdf.png'
import usePdfPreview from '@/hooks/use-pdf-preview'

// const baseApi = ref(import.meta.env.VITE_API_BASE)
const { useLoadPdfPreview } = usePdfPreview()
function handlePdfPreview(fileName: string) {
  // const urlAll = `http://${window.location.host}${baseApi.value}${fileName}`
  const urlAll = `${window.$previewHost}${fileName}`
  useLoadPdfPreview(urlAll)
}

export function getTableColumns(
  optionColumnRenderer: (row: PartyMeetingMaterialItem) => VNodeChild,
): DataTableColumns<PartyMeetingMaterialItem> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '10%',
      render: (_, i) => i + 1,
    },
    {
      width: '50%',
      key: 'original',
      title: '文件名称',
      render: (row) => {
        if (['jpg', 'jpeg', 'png'].includes(row.fileType)) {
          return h('div', { class: 'flex items-center gap-x-[10px]' }, [
            h(NImage, {
              src: import.meta.env.VITE_API_BASE + row.fileName,
              style: { width: '25px', height: '25px', borderRadius: '2px' },
            }),
            h(
              NEllipsis,
              {
                style: {
                  maxWidth: '500px',
                },
              },
              { default: () => row.original },
            ),
          ])
        }
        else {
          return h(
            'div',
            {
              class: 'flex items-center gap-x-[10px] cursor-pointer',
              onClick: () => handlePdfPreview(row.fileName),
            },
            [
              h('img', { class: 'w-[25px] h-[30px]', src: pdf }),
              h(
                NEllipsis,
                {
                  style: {
                    maxWidth: '500px',
                  },
                },
                { default: () => row.original },
              ),
            ],
          )
        }
      },
    },
    {
      key: 'fileSize',
      title: '文件大小',
      width: '10%',
      render: row => `${(Number(row.fileSize) / 1024 / 1024).toFixed(2)}MB`,
    },

    {
      key: 'uploadTime',
      width: '20%',
      title: '上传时间',
      render: row => (row.uploadTime ? row.uploadTime.slice(0, 16) : '--'),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '10%',
      render: row => optionColumnRenderer(row),
    },
  ]
}
