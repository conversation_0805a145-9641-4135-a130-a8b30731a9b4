import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  annexName: [
    {
      required: true,
      message: '附件名称不能为空',
      trigger: 'input',
    },
  ],
  annexDescribe: [
    {
      required: true,
      message: '附件描述不能为空',
      trigger: 'input',
    },
  ],
  deptId: [
    {
      required: true,
      message: '请选择组织',
      trigger: 'change',
    },
  ],
  publicScope: [
    {
      required: true,
      message: '公开范围不能为空',
      trigger: 'change',
      type: 'array',
    },
  ],
  isHidden: {
    required: true,
    message: '请选择是否隐藏',
    trigger: 'change',
    type: 'number',
  },
  summary: {
    required: true,
    message: '请输入正文',
    trigger: 'input',
    type: 'string',
  },
  pictureUrl: {
    required: true,
    message: '请上传图片',
    trigger: 'change',
    type: 'string',
  },
  fileList: {
    required: true,
    validator(rule: any, value: any) {
      if (value === null) {
        return new Error('请选择文件')
      }
      return true
    },
    trigger: 'change',
  },
}
