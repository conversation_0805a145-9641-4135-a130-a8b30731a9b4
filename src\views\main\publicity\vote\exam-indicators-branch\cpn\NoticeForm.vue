<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { formRules } from './config'
import type {
  addAndEditParams,
  uploadFileItem,
  uploadFileItemNew,
} from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
import RichEditor from '@/components/RichEditor.vue'
import {
  getViewPartyBuildingExamIndIcatorsItem,
  postPartyBuildingExamAdd,
  putEditExamIndIcatorsIdsItem,
} from '@/services/affairs/discipline-inspection-list/exam-indicators'
import { uploadImg } from '@/services'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<addAndEditParams>({
  id: '',
  title: '',
  evaluationRequirements: '',
  evaluationMode: '',
  evaluationScore: 0,
  matter: '',
  fileIds: [],
  fileList: [],
})

const formRef = ref<InstanceType<typeof NForm>>()
const fileIdObj = reactive<{ fileIdsArr: Array<string> }>({ fileIdsArr: [] })

onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getViewPartyBuildingExamIndIcatorsItem(props.id).then((res) => {
      formDataReactive.evaluationMode = res.evaluationMode
      formDataReactive.evaluationRequirements = res.evaluationRequirements
      formDataReactive.evaluationScore = res.evaluationScore
      formDataReactive.fileIds = res.fileList?.map(item => item.id) || []
      formDataReactive.fileList = res.fileList || []
      formDataReactive.matter = res.matter
      formDataReactive.title = res.title
      formDataReactive.id = props.id
      formDataReactive.relatedStatus = res.relatedStatus
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (formDataReactive.id) {
        putEditExamIndIcatorsIdsItem(formDataReactive).then((res) => {
          if (res) {
            fileIdObj.fileIdsArr = []
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      } else {
        postPartyBuildingExamAdd(formDataReactive).then((res) => {
          if (res) {
            fileIdObj.fileIdsArr = []
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// 文件相关
async function handleFileChange(
  fileInfoList: uploadFileItemNew[],
  isDelIDs: string,
) {
  try {
    const fileData = new FormData()
    // 获取所有FileID
    if (!fileIdObj.fileIdsArr.length) {
      formDataReactive.fileList.forEach(item =>
        fileIdObj.fileIdsArr.push(item.id),
      )
    }
    // 删除动作
    if (isDelIDs || isDelIDs === null) {
      if (isDelIDs === null && fileIdObj.fileIdsArr.length) {
        fileIdObj.fileIdsArr.splice(fileIdObj.fileIdsArr.length - 1, 1)
      } else {
        fileIdObj.fileIdsArr.forEach((item, index) => {
          if (item === isDelIDs) {
            fileIdObj.fileIdsArr.splice(index, 1)
          }
        })
      }
      formDataReactive.fileIds = [...fileIdObj.fileIdsArr]
    } else {
      // 新增动作
      const lastFileItem = fileInfoList[fileInfoList.length - 1]?.file
      fileData.append('file', lastFileItem as Blob)
      const data: uploadFileItem = await uploadImg(fileData)
      formDataReactive.fileList.push({
        original: lastFileItem?.name as string,
        fileName: data.url || '',
        id: data.fileId,
      })
      if (data) {
        fileIdObj.fileIdsArr.push(data.fileId)
        formDataReactive.fileIds = [...fileIdObj.fileIdsArr]
      }
    }
  } catch (error) {}
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="90"
    label-align="right"
    label-placement="left"
    :disabled="formDataReactive.relatedStatus"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item label="标题：" path="title">
      <n-input
        v-model:value="formDataReactive.title"
        placeholder="请输入标题内容"
        clearable
      />
    </n-form-item>
    <n-form-item label="事项：" path="matter">
      <n-input
        v-model:value="formDataReactive.matter"
        placeholder="请输入事项"
        clearable
      />
    </n-form-item>
    <n-form-item span="24" label="考核要求：" path="evaluationRequirements">
      <RichEditor
        v-model:value="formDataReactive.evaluationRequirements"
        style="width: 100%"
        :rich-height="350"
        :disabled="formDataReactive.relatedStatus"
      />
    </n-form-item>

    <n-form-item span="24" label="考核方式：" path="evaluationMode">
      <RichEditor
        v-model:value="formDataReactive.evaluationMode"
        style="width: 100%"
        :rich-height="350"
        :disabled="formDataReactive.relatedStatus"
      />
    </n-form-item>
    <n-form-item span="24" label="考核得分：" path="evaluationScore">
      <n-input-number
        v-model:value="formDataReactive.evaluationScore"
        :min="-10"
        :show-button="true"
      />
    </n-form-item>
    <n-form-item span="24" label="附件：">
      <file-uploader
        :max="100"
        accept=".doc, .docx, .pdf"
        :disabled="formDataReactive.relatedStatus"
        :size-limit="200"
        :original-file-list="(formDataReactive.fileList as any)"
        @file-list-change="handleFileChange"
      >
        <template #tips>
          <span class="tips">
            可上传多个文件，支持扩展名：.doc，docx，.pdf，大小200M以内
          </span>
        </template>
      </file-uploader>
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
