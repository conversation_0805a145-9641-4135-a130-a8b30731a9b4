<script lang="ts" setup>
import { DownloadRound } from '@vicons/material'
import type { DataTableColumns, TreeSelectOption } from 'naive-ui'
import { NButton, NIcon, NInput, useMessage } from 'naive-ui'
import { onMounted, ref } from 'vue'
import type { Ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { useRecordBookStore } from '@/store/recordBook'
import { getPartyDuesPaymentRecordsColumns } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/config'
import { getPartyMemberMinutesList } from '@/services/recordBookMaintenance'
import {
  editPayFee,
  getPayFeeList,
} from '@/services/party-dues-payment-records'
import type { DuesPayVO } from '@/services/party-dues-payment-records/types/request'

const TABLE_TYPE = {
  VIEW: 'view',
  EDIT: 'edit',
}

const MONTHS = [
  '一月',
  '二月',
  '三月',
  '四月',
  '五月',
  '六月',
  '七月',
  '八月',
  '九月',
  '十月',
  '十一月',
  '十二月',
]
type Month = (typeof MONTHS)[number]

const MONTHS_EN = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
]
const FIELD_MONTHS = [
  'janAmount',
  'febAmount',
  'marAmount',
  'aprAmount',
  'mayAmount',
  'junAmount',
  'julAmount',
  'augAmount',
  'sepAmount',
  'octAmount',
  'novAmount',
  'decAmount',
]

// 将中文与英文月份配对，生成完整配置项
const MONTHS_MAP = MONTHS.map((label, i) => ({
  label,
  value: (i + 1).toString(),
  en: MONTHS_EN[i],
  field: FIELD_MONTHS[i],
}))

const message = useMessage()
// store 引用
const recordBookStore = useRecordBookStore()

const canEdit = ref(false)
// 相关 ref 和 computed 状态
const fileLoading = ref(false)
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)
const currentTableType = ref<'view' | 'edit'>('view')

const selectedDeptId = ref<string | null>(null)
const selectedYear = ref<string | null>(null)
const currentDownloadFileType = ref<'word' | 'pdf'>('word')
const showPopConfirm = ref(false)

interface TableItem extends Record<Month, string> {
  name: string
}
const tableData: Ref<DuesPayVO[]> = ref([])
const rawTableData: Ref<DuesPayVO[]> = ref([])
const tableColumns = ref<DataTableColumns<any>>([])

const currentYearOptions = computed(() => recordBookStore.getYearOptions)
const calcOrganizationListTree = computed(
  () => recordBookStore.getOrganizationListTreeList,
)
const currentSelectedDept = computed(
  () => recordBookStore.getCurrentSelectedDept,
)
const currentYear = computed(() => recordBookStore.getCurrentSelectedYear)
const monthTableListOptions = computed(() => MONTHS_MAP)

// 切换部门
function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  selectedDeptId.value = v
  recordBookStore.setCurrentSelectedDept({
    deptId: v,
    deptName: (option?.name as string) || '',
  })
  resetAndLoadData()
}

// 切换年份
function handleUpdateYearSelectedValue(v: string) {
  if (!v) {
    return
  }
  selectedYear.value = v
  recordBookStore.setCurrentSelectedYear(v)
  resetAndLoadData()
}

// 下载文件
function handleDownLoadFile() {
  fileLoading.value = true
  recordBookStore.fetchDownLoadFile(
    {
      deptId: selectedDeptId.value!,
      year: selectedYear.value!,
      type: currentDownloadFileType.value,
    },
    () => {
      fileLoading.value = false
      showPopConfirm.value = false
    },
  )
}

// 取消 PopConfirm 弹窗
function handleCancelShowPopConfirm() {
  showPopConfirm.value = false
}

// 编辑按钮点击，切换为可编辑列
function editMonthTable() {
  tableColumns.value = initMonthTableColumns(true) as DataTableColumns<any>
  currentTableType.value = 'edit'
}

async function saveEditMonthTable() {
  // 保存编辑逻辑
  const fields = MONTHS_MAP.map(item => item.field)
  const userDuesPayList = tableData.value.map(item => ({
    id: item.id,
    userId: item.userId,
    ...fields.reduce((acc: any, field: any) => {
      acc[field] = item[field] ? Number(item[field]) : item[field]
      return acc
    }, {}),
  }))
  const deptId = selectedDeptId.value
  const year = selectedYear.value
  await editPayFee({
    deptId,
    year,
    userDuesPayList,
  })
  cancelEditMonthTable()
  await loadData()
  message.success('编辑成功')
}

function cancelEditMonthTable() {
  tableData.value = cloneDeep(rawTableData.value)
  tableColumns.value = initMonthTableColumns(false) as DataTableColumns<any>
  currentTableType.value = 'view'
}

function isDecimal(numStr) {
  // 先判断是否是合法数字格式
  if (!/^-?\d+(\.\d+)?$/.test(numStr)) {
    return false
  }
  const [_, decimalPart = ''] = numStr.split('.')

  // 如果没有小数部分或者小数部分全是0，则不算小数
  return decimalPart.length > 0 && !/^0+$/.test(decimalPart)
}

function formatToTwoDecimalPlacesIfDecimal(numStr) {
  if (!isDecimal(numStr)) {
    // 如果不是小数，可以选择返回原值或其他处理方式
    return numStr
  }

  const num = parseFloat(numStr)
  return num.toFixed(2) // 自动四舍五入并保留两位小数
}

function format(numStr) {
  // 先判断是否是合法数字格式
  if (!/^-?\d+(\.\d+)?$/.test(numStr)) {
    return null
  }
  const [num, _] = numStr.split('.')
  const Num = Number(num)
  if (Num >= 9999) {
    return 9999
  }
  else if (Num <= 0) {
    return 0
  }
  else {
    return formatToTwoDecimalPlacesIfDecimal(numStr)
  }
}

// 初始化列
function initMonthTableColumns(isEdit = false): DataTableColumns<any> {
  const year = new Date().getFullYear().toString()
  const nowMonth = new Date().getMonth() + 1
  const getRender = (row: any, item: (typeof MONTHS_MAP)[number]) => {
    // console.log(row, item, 'row')
    return isEdit
      && (year === selectedYear.value ? nowMonth >= parseInt(item.value) : true)
      ? h(NInput, {
        value: row[item.field]?.toString(),
        placeholder: '请输入金额',
        // 精确到后两位
        onUpdateValue: (val: string | number) =>
          (row[item.field] = val.toString()),
        onChange: () =>
          (row[item.field] = format(row[item.field].toString())),
      })
      : row[item.field] === null || row[item.field] === undefined
        ? '-'
        : row[item.field]
  }

  return getPartyDuesPaymentRecordsColumns(
    monthTableListOptions.value,
    getRender,
  )
}

function onUpdatePage(page: number) {
  pageNum.value = page
  loadData()
}

function onUpdatePageSize(size: number) {
  pageNum.value = 1
  pageSize.value = size
  loadData()
}

// 加载数据（模拟）
async function loadData() {
  if (!selectedDeptId.value || !selectedYear.value) {
    return
  }
  loading.value = true
  getPayFeeList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
  })
    .then((res) => {
      canEdit.value = res.isEditable
      total.value = res.duesPayList.total || 0
      tableData.value = (res.duesPayList.records as DuesPayVO[]) || []
      rawTableData.value = cloneDeep(
        (res.duesPayList.records as DuesPayVO[]) || [],
      )
    })
    .finally(() => {
      loading.value = false
    })
}

// 重置页码并加载
function resetAndLoadData() {
  pageNum.value = 1
  loadData()
}

// 初始化
onMounted(() => {
  selectedDeptId.value = currentSelectedDept.value.deptId
  selectedYear.value = currentYear.value
  tableColumns.value = initMonthTableColumns(false)
  loadData()
})
</script>

<template>
  <div>
    <table-container
      v-model:page="pageNum"
      v-model:page-size="pageSize"
      :loading="loading"
      :show-delete="false"
      :show-pagination="true"
      :show-toolbar="false"
      :show-title="false"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      scroll-x
      default-expand-all
      @update:page="onUpdatePage"
      @update:page-size="onUpdatePageSize"
    >
      <template #row>
        <div
          class="w-[100%] flex flex-row items-start justify-between gap-[10px] pb-[10px]"
        >
          <div class="flex flex-row gap-x-[10px] justify-start items-start">
            <n-tree-select
              v-model:value="selectedDeptId"
              :options="calcOrganizationListTree"
              :show-path="false"
              check-strategy="all"
              children-field="children"
              filterable
              key-field="id"
              label-field="name"
              placeholder="请选择所属党组织"
              style="width: 400px"
              value-field="id"
              @update:value="handleUpdateTreeSelectedValue"
            />
            <n-select
              v-model:value="selectedYear"
              :options="currentYearOptions"
              placeholder="请选择年份"
              size="medium"
              style="width: 200px"
              @update:value="handleUpdateYearSelectedValue"
            />
          </div>

          <div class="flex flex-row gap-x-[10px] justify-start items-start">
            <n-button
              v-if="currentTableType === TABLE_TYPE.VIEW && canEdit"
              type="primary"
              @click="editMonthTable"
            >
              开启编辑
            </n-button>
            <n-button
              v-if="currentTableType === TABLE_TYPE.EDIT"
              @click="cancelEditMonthTable"
            >
              取消编辑
            </n-button>
            <n-button
              v-if="currentTableType === TABLE_TYPE.EDIT"
              type="primary"
              @click="saveEditMonthTable"
            >
              保存编辑
            </n-button>
            <n-popconfirm
              :show="showPopConfirm"
              :show-icon="false"
              placement="bottom"
            >
              <template #trigger>
                <n-button
                  :loading="fileLoading"
                  @click="showPopConfirm = !showPopConfirm"
                >
                  <template #icon>
                    <n-icon size="16">
                      <DownloadRound />
                    </n-icon>
                  </template>
                  下载
                </n-button>
              </template>
              <div class="py-[10px]">
                <n-radio-group v-model:value="currentDownloadFileType">
                  <n-radio
                    v-for="(fileType, fileTypeIndex) in [
                      { label: 'word', value: 'word' },
                      { label: 'pdf', value: 'pdf' },
                    ]"
                    :key="fileTypeIndex"
                    :value="fileType.value"
                  >
                    {{ fileType.label }}
                  </n-radio>
                </n-radio-group>
              </div>

              <template #action>
                <div class="w-[100%] flex flex-row justify-center items-center">
                  <n-button
                    size="small"
                    type="primary"
                    @click="handleDownLoadFile"
                  >
                    确定
                  </n-button>
                  <n-button size="small" @click="handleCancelShowPopConfirm">
                    取消
                  </n-button>
                </div>
              </template>
            </n-popconfirm>
          </div>
        </div>
        <div class="title flex justify-center w-full mb-[20px]">
          <span class="title-text">党员交纳党费情况</span>
        </div>
      </template>
    </table-container>
  </div>
</template>

<style lang="scss" scoped>
.title-text {
  font-family: Microsoft YaHei;
  font-weight: 500;
  font-size: 19px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
}
</style>
