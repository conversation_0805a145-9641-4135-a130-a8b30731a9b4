import type { VNodeChild } from 'vue'
import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import { NImage } from 'naive-ui'
import type { NewsListItem } from '@/services/news/types'
export function getTableColumns(
  operationRender: (row: NewsListItem) => VNodeChild,
  topRender: (row: NewsListItem) => VNodeChild,
  // recommendRender: (row: NewsListItem) => VNodeChild,
  hiddenRender: (row: NewsListItem) => VNodeChild,
  // 是否加入首页轮播池
  rotationPoolRender: (row: NewsListItem) => VNodeChild,
): TableColumns<NewsListItem> {
  return [
    {
      type: 'selection',
      align: 'center',
    },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      title: '活动标题',
      key: 'title',
      width: '18%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      title: '封面图',
      key: 'img',
      render: row =>
        h(NImage, {
          src: import.meta.env.VITE_API_BASE + row.coverUrl,
          width: '62',
          style: { height: '40px' },
        }),
    },
    {
      title: '是否外链',
      key: 'isOutside',
      render: row => (row.isOutside === '1' ? '是' : '否'),
    },
    {
      title: '外链地址',
      key: 'linkUrl',
      width: '15%',
      render: (row) => {
        return row.linkUrl
          ? h(
            'a',
            {
              href: row.linkUrl,
              target: '_blank',
              style: {
                color: '#3f7ee8',
              },
            },
            row.linkUrl,
          )
          : '--'
      },
    },
    {
      title: '阅读数',
      key: 'readNum',
      align: 'center',
    },
    // {
    //   title: '评论数',
    //   key: 'comment',
    //   render: commentRender,
    // },
    {
      title: '发布时间',
      key: 'publishTime',
      width: '12%',
      render: row => row.publishTime ?? '-',
    },
    {
      title: '是否置顶',
      key: 'top',
      render: topRender,
    },
    // {
    //   title: '是否推荐',
    //   key: 'recommend',
    //   render: recommendRender,
    // },
    {
      title: '是否隐藏',
      key: 'hidden',
      render: hiddenRender,
    },
    {
      title: '是否加入首页轮播池',
      key: 'sliderFlag',
      render: rotationPoolRender,
    },
    {
      title: '操作',
      key: 'operation',
      width: '10%',
      render: operationRender,
    },
  ]
}
