import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { VNodeChild } from 'vue'
import type { ICarousel } from '@/services/run/carousel/types'

export function getTableColumns(
  optionColumnRenderer: (row: ICarousel) => VNodeChild,
): TableColumns<ICarousel> {
  return [
    {
      type: 'selection',
      align: 'center',
    },
    {
      title: '序号',
      key: 'pictureUrl',
    },
    {
      title: '姓名',
      key: 'resourceName',
    },
    {
      title: '所属组织',
      key: 'layout',
    },
    {
      title: '身份',
      key: 'jumpType',
    },
    {
      title: '意见标题',
      key: 'sortOrder',
    },
    {
      title: '意见内容',
      key: 'resourceStatus',
    },
    {
      title: '提交时间',
      key: 'resourceStatus',
    },
    {
      title: '操作',
      key: 'action',
      align: 'left',
      width: 200,
      render: optionColumnRenderer,
    },
  ]
}
