<script setup lang="ts">
import { IosArrowBack } from '@vicons/ionicons4'
import NoteList from './notice/NoticeList.vue'
import ActiveDemeanor from './active-demeanor/ActiveDemeanor.vue'
const router = useRouter()
const route = useRoute()
const { id, orgName } = route.query
</script>

<template>
  <div class="flex justify-between items-center py-[25px] px-[30px]">
    <span class="text-[16px] font-[600] text-[#333]">{{ orgName }}</span>
    <n-button
      size="small"
      @click="
        router.replace({
          name: 'branchGardenIndex',
        })
      "
    >
      <template #icon>
        <n-icon>
          <IosArrowBack />
        </n-icon>
      </template>
      返回
    </n-button>
  </div>

  <n-tabs type="line" animated class="pl-[30px]">
    <n-tab-pane name="Organization" tab="公示公告">
      <note-list :id="id" />
    </n-tab-pane>
    <n-tab-pane name="PartyMember" tab="风采展示">
      <active-demeanor :id="id" />
    </n-tab-pane>
  </n-tabs>
</template>

<style lang="scss" scoped></style>
