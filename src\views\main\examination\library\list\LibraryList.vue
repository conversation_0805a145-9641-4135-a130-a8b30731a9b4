<!--
 * @Description: 题库管理-列表页
-->
<template>
  <layout-container style="height: calc(100vh - 114px)">
    <template #side>
      <SideMenuNew
        v-model:show-modal="showModal"
        title="题库管理"
        :tree-data="treeData"
        :modal-title="modalTitle"
        :default-selected-keys="defaultSelectedKeys"
        @move="moveNode"
        @del-node="delNode"
        @save-tree-node="handleFormatterParams"
        @add-child-node="handleAddChildNode"
        @select-node-key="handleChangeTab"
      />
    </template>

    <template #main>
      <table-container
        ref="mainTableRef"
        :title="selectName"
        :loading="loadingRef"
        :show-toolbar="false"
        custom-toolbar
        :total="totalRef"
        :table-columns="tableColumns"
        :table-data="examinationLibraryList"
        @page-change="handlePageChange"
      >
        <template #btns>
          <n-button type="primary" size="small" @click="handleClickAdd">
            <template #icon>
              <n-icon>
                <plus-round />
              </n-icon>
            </template>
            新建
          </n-button>
          <n-button size="small" @click="handleClickLabelManagement">
            <template #icon>
              <n-icon style="transform: rotateZ(90deg)" size="14">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  viewBox="0 0 32 32"
                >
                  <path
                    d="M18.52 30a3 3 0 0 1-2.12-.88L2.88 15.61A3 3 0 0 1 2 13.49V5a3 3 0 0 1 3-3h8.49a3 3 0 0 1 2.12.88l13.51 13.51a3 3 0 0 1 0 4.25l-8.48 8.48a3 3 0 0 1-2.12.88zM5 4a1 1 0 0 0-1 1v8.49a1 1 0 0 0 .3.71l13.51 13.51a1 1 0 0 0 1.41 0l8.49-8.49a1 1 0 0 0 0-1.41L14.2 4.3a1 1 0 0 0-.71-.3H5z"
                    fill="currentColor"
                  />
                  <path
                    d="M10 14a4 4 0 1 1 4-4a4 4 0 0 1-4 4zm0-6a2 2 0 1 0 2 2a2 2 0 0 0-2-2z"
                    fill="currentColor"
                  />
                </svg>
              </n-icon>
            </template>
            试题标签管理
          </n-button>
        </template>
        <template #filters>
          <n-input
            v-model:value="filterReactive.name"
            size="small"
            placeholder="请搜索题库名称"
            clearable
            @input="handleFilter"
            @clear="
              () => {
                filterReactive.name = ''
                handleFilter()
              }
            "
          >
            <template #suffix>
              <n-button text @click="handleFilter">
                <n-icon size="18">
                  <search-round />
                </n-icon>
              </n-button>
            </template>
          </n-input>
        </template>
      </table-container>
    </template>
  </layout-container>

  <CustomDialog
    v-model:show="showModal"
    :title="modalTitle"
    width="600px"
    @confirm="handleFormatterParams"
    @cancel="handleCancel"
    @update:show="(v: boolean) => (showModal = v)"
  >
    <div class="p-[20px]">
      <AddCategoryForm
        v-show="showModalType === 'root'"
        ref="addFirstCategoryRef"
        :type="operateCategoryType"
      />
    </div>
  </CustomDialog>

  <!-- 标签管理页 -->
  <n-drawer v-model:show="showLabelManagementRef" :width="600">
    <n-drawer-content title="标签管理" closable>
      <label-management />
    </n-drawer-content>
  </n-drawer>

  <!-- 题库编辑页 -->
  <n-drawer v-model:show="showEditRef" :width="600" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <library-form
        ref="libraryFormRef"
        :original-data="libraryEditingRef"
        :category-id="currentCategoryId"
        :type="editTypeRef"
        @saved="handleSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button type="primary" @click="handleClickConfirm">
            保存
          </n-button>
          <n-button @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { computed, h, reactive, ref } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import {
  NButton,
  NDrawer,
  NDrawerContent,
  NIcon,
  NInput,
  NPopconfirm,
} from 'naive-ui'
import { PlusRound, SearchRound } from '@vicons/material'
import { examinationLibraryTypes } from '../config'
import AddCategoryForm from './AddCategoryForm.vue'
import LabelManagement from './cpn/label-management/LabelManagement.vue'
import LibraryForm from './cpn/library-form/LibraryForm.vue'
import TableContainer from './TableContainer.vue'
// import SideMenuNew from './SideMenuNew.vue'
import router from '@/router'
import useTableContainer from '@/hooks/use-table-container'
import type {
  IExaminationLibrary,
  IExaminationLibraryQuery,
} from '@/services/examination/index'
import {
  addBankCategory,
  checkCategoryHaveChildren,
  delBankCategory,
  deleteExaminationLibrary,
  getBankCategoryList,
  moveBankCategory,
  updateBankCategory,
} from '@/services/examination/index'
import useExaminationStore from '@/store/examination/index'
import { formatCustomTimeString } from '@/utils/date-time-format'
import { useDrawerEdit, useTreeMenu } from '@/hooks'

// 筛选项：类别id和资讯标题
const filterRef = ref({
  categoryId: '',
  title: null,
})

// 选中菜单名称
const selectName = ref()

// 当前选中的分类id
const currentCategoryId = ref<any>('')

const showModal = ref(false)
const modalTitle = ref()
const addFirstCategoryRef = ref()
const defaultSelectedKeys = ref<string[]>([])

// 操作栏目类型
const operateCategoryType = ref('add')

/** 新增子节点 */
// async function handleAddChildNode(data: any) {
//   console.log('data: ', data)
//   if (
//     data.level === 1
//     && data.model !== 'modify'
//     && (!data.children || data.children.length === 0)
//   ) {
//     const response = await checkCategoryHaveChildren(data.originData.id)
//     if (!response) {
//       // 表明该分类下存在资讯，添加子分类时需要给用户提示
//       window.$dialog.warning({
//         title: '提示',
//         content:
//           '该菜单下已有详细数据，若添加子菜单则无法展示已添加的详细数据，是否继续？',
//         positiveText: '确定',
//         negativeText: '取消',
//         onPositiveClick: () => {
//           showModal.value = true
//           test()
//         },
//         onNegativeClick: () => {
//           showModal.value = false
//         },
//       })
//     }
//     else {
//       // 表明该分类下没有详细数据，添加子分类时无需提示
//       showModal.value = true
//       test()
//     }
//   }
//   else {
//     showModal.value = true
//     test()
//   }
//   // showModal.value = true
//   function test() {
//     operateCategoryType.value = 'add'
//     modalTitle.value = '新增类别'
//     // addChildNode(data)
//     if (data.model === 'modify') {
//       modalTitle.value = '修改类别'
//       operateCategoryType.value = 'modify'
//       nextTick(() => {
//         data.type = 'update'
//         if (data.level === 2) {
//           data.categoryChildTitle = data.categoryTitle
//           data.categoryTitle = data.parentName
//         }
//         addFirstCategoryRef.value.handleSetFormData(data)
//       })
//     }
//     else if (data.level === 1) {
//       modalTitle.value = '新增类别'
//       operateCategoryType.value = 'add'
//       nextTick(() => {
//         data.type = 'add'
//         addFirstCategoryRef.value.handleSetFormData(data)
//       })
//     }
//   }
// }
/** 新增子节点 */
async function handleAddChildNode(data: any) {
  // console.log('data: ', data)

  const shouldShowModal = await shouldShowModalPrompt(data)
  if (shouldShowModal) {
    showModal.value = true
    prepareFormData(data)
  }
}

/** 检查是否需要提示用户并返回布尔值 */
async function shouldShowModalPrompt(data: any): Promise<boolean> {
  if (
    data.level === 1
    && data.model !== 'modify'
    && (!data.children || data.children.length === 0)
  ) {
    const response = await checkCategoryHaveChildren(data.originData.id)
    if (!response) {
      // 提示用户
      return new Promise((resolve) => {
        window.$dialog.warning({
          title: '提示',
          content:
            '该菜单下已有详细数据，若添加子菜单则无法展示已添加的详细数据，是否继续？',
          positiveText: '确定',
          negativeText: '取消',
          onPositiveClick: () => resolve(true),
          onNegativeClick: () => resolve(false),
        })
      })
    }
  }
  return true
}

/** 准备表单数据 */
function prepareFormData(data: any) {
  operateCategoryType.value = data.model === 'modify' ? 'modify' : 'add'
  modalTitle.value = data.model === 'modify' ? '修改类别' : '新增类别'

  nextTick(() => {
    if (addFirstCategoryRef.value) {
      if (data.model === 'modify') {
        data.type = 'update'
        if (data.level === 2) {
          data.categoryChildTitle = data.categoryTitle
          data.categoryTitle = data.parentName
        }
      }
      else if (data.level === 1) {
        data.type = 'add'
      }
      addFirstCategoryRef.value.handleSetFormData(data)
    }
    else {
      console.error('addFirstCategoryRef is not initialized')
    }
  })
}

const examinationStore = useExaminationStore()

const mainTableRef = ref<any>(null)

const examinationLibraryList = computed(
  () => examinationStore.examinationLibraryList,
)
const filterReactive = reactive<{ name: string }>({
  name: '',
})
const showLabelManagementRef = ref(false)
const libraryFormRef = ref()
const libraryEditingRef = ref()

const { loadingRef, totalRef, currentPageRef, pageSizeRef, handlePageChange }
  = useTableContainer(loadExaminationLibraryList)

const {
  drawerTitle,
  editTypeRef,
  showEditRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('题库', () => {
  libraryFormRef.value?.validateAndSave()
})

const { treeData, showModalType, moveNode, delNode, init } = useTreeMenu({
  menuListApi: getBankCategoryList,
  moveNodeApi: moveBankCategory,
  moveChildNodeApi: moveBankCategory,
  delNodeApi: delBankCategory,
  delChildNodeApi: delBankCategory,
  addNodeApi: addBankCategory,
  modifyNodeApi: updateBankCategory,
  refreshTableApi: filterInput,
  checkDeleteApiFunc: checkCategoryHaveChildren,
  maxLevel: 2,
  labelField: 'categoryTitle',
  multiLevelKey: 'children',
  childLabelField: 'categoryTitle',
  sessionId: 'libraryCategoryId',
  sessionName: 'libraryCategoryLabel',
})

// 加载题库列表
function loadExaminationLibraryList(id?: any) {
  if (!loadingRef.value && currentPageRef.value) {
    loadingRef.value = true
    // 生成参数
    const params: IExaminationLibraryQuery = {
      pageNo: currentPageRef.value,
      pageSize: pageSizeRef.value,
      categoryId: id || currentCategoryId.value,
    }
    const { name } = filterReactive
    if (name) {
      params.name = name
    }
    if (!params.categoryId) {
      loadingRef.value = false
      return
    }
    examinationStore.getExaminationLibraryListAction(params).then((res) => {
      totalRef.value = Number(res.total)
      loadingRef.value = false
    })
  }
}

function handleCancel() {
  showModal.value = false
}

/** 处理弹框需要保存的数据及校验弹框必填项 */
async function handleFormatterParams() {
  try {
    if (!(await addFirstCategoryRef.value.handleValidate())) {
      return
    }
    const data = addFirstCategoryRef.value.formData

    if (operateCategoryType.value === 'add') {
      addBankCategory({
        level: data.level === 1 ? 2 : 1,
        categoryTitle:
          data.level === 1 ? data.categoryChildTitle : data.categoryTitle,
        parentId: data.level === 1 ? data.id : null,
      }).then((res) => {
        window.$message.success('操作成功')
        init().then((res: any) => {
          defaultSelectedKeys.value = [res.id]
          filterRef.value.categoryId = res.id
          selectName.value = res.name
        })
      })
    }
    else {
      updateBankCategory({
        id: data.id,
        categoryTitle:
          data.level === 1 ? data.categoryTitle : data.categoryChildTitle,
      }).then((res) => {
        window.$message.success('操作成功')
        init().then((res: any) => {
          defaultSelectedKeys.value = [res.id]
          filterRef.value.categoryId = res.id
          selectName.value = res.name
        })
      })
    }

    showModal.value = false
  }
  catch (error) {
    // console.error(error)
  }
}

function filterInput(res: { id: string; name: string }) {
  // loadData()
  defaultSelectedKeys.value = [res.id]
  filterRef.value.categoryId = res.id
  selectName.value = res.name
}

// 过滤
function handleFilter() {
  currentPageRef.value = 1
  mainTableRef.value!.currentPage = 1
  loadExaminationLibraryList()
}
// 点击新增
function handleClickAdd() {
  // 检查是否已选择分类
  if (!currentCategoryId.value) {
    window.$message.error('请先选择题库分类')
    return
  }
  editTypeRef.value = 'add'
  showEditRef.value = true
}
// 保存成功
function handleSaved() {
  showEditRef.value = false
  loadExaminationLibraryList()
}
// 点击标签管理
function handleClickLabelManagement() {
  showLabelManagementRef.value = true
}

// 选中菜单触发的事件
function handleChangeTab(data: any) {
  const { clickExpand, label } = data
  if (!clickExpand) {
    filterRef.value.categoryId = data.originData.id
    window.sessionStorage.setItem('libraryCategoryId', data.originData.id)
    window.sessionStorage.setItem('libraryCategoryLabel', label)
    filterRef.value.title = null
    selectName.value = label
    // currentPage.value = 1
  }
  if (!data.children) {
    currentCategoryId.value = data.originData.id
    currentPageRef.value = 1
    loadExaminationLibraryList()
  }
}

const tableColumns: DataTableColumns<IExaminationLibrary> = [
  {
    key: 'blank',
    width: 10,
  },
  {
    key: 'libraryName',
    title: '题库名称',
  },
  {
    key: 'description',
    title: '题库说明',
  },
  {
    key: 'type',
    title: '适用答题任务',
    render(row) {
      return examinationLibraryTypes
        .filter(item => row.type.split(',').includes(item.value))
        .map(item => item.label)
        .join('、')
    },
  },
  {
    key: 'sumSubject',
    title: '题量',
  },
  {
    key: 'singleChoice',
    title: '单选',
  },
  {
    key: 'multipleChoice',
    title: '多选',
  },
  {
    key: 'judgeSubject',
    title: '判断',
  },
  {
    key: 'fillSubject',
    title: '填空',
  },
  {
    key: 'updatedAt',
    title: '更新时间',
    render(row) {
      return formatCustomTimeString(row.updatedAt, 'YYYY-MM-DD HH:mm:ss')
    },
  },
  {
    key: 'operation',
    title: '操作',
    render(row) {
      return [
        h(
          NButton,
          {
            text: true,
            style: 'margin-right: 16px',
            type: 'primary',
            onClick: () => {
              router.push({
                name: 'librarySpecific',
                params: { libraryId: row.libraryId },
                query: { lib: encodeURIComponent(row.libraryName) },
              })
            },
          },
          { default: () => '题目' },
        ),
        h(
          NButton,
          {
            text: true,
            style: 'margin-right: 16px',
            type: 'primary',
            onClick: () => {
              editTypeRef.value = 'modify'
              libraryEditingRef.value = row
              showEditRef.value = true
            },
          },
          { default: () => '编辑' },
        ),
        h(
          NPopconfirm,
          {
            positiveText: '确定',
            placement: 'left',
            onPositiveClick: () => {
              deleteExaminationLibrary(row.libraryId).then((res) => {
                window.$message.success(res)
                loadExaminationLibraryList()
              })
            },
          },
          {
            trigger: () =>
              h(
                NButton,
                {
                  text: true,
                  type: 'primary',
                },
                { default: () => '删除' },
              ),
            default: () => '确定删除吗？',
          },
        ),
      ]
    },
  },
]

// onBeforeMount(() => {
//   init().then((res: any) => {
//     if (treeData.value.length > 0) {
//       const firstNode = treeData.value[0]
//       if (firstNode.children && firstNode.children.length > 0) {
//         currentCategoryId.value = firstNode.children[0].id
//         defaultSelectedKeys.value = firstNode.children[0].id
//         filterRef.value.categoryId = firstNode.children[0].id
//         selectName.value = firstNode.children[0].name
//         setTimeout(() => {
//           loadExaminationLibraryList(firstNode.children[0].id)
//         }, 0)
//       }
//       else {
//         defaultSelectedKeys.value = firstNode.id
//         filterRef.value.categoryId = firstNode.id
//         selectName.value = firstNode.name
//         currentCategoryId.value = firstNode.id
//         setTimeout(() => {
//           loadExaminationLibraryList(firstNode.id)
//         }, 0)
//       }
//     }
//   })
// })
onMounted(() => {
  init()
    .then((res: any) => {
      // 添加安全检查
      if (res && res.id) {
        defaultSelectedKeys.value = [res.id]
        filterRef.value.categoryId = res.id
        selectName.value = res.name
        currentCategoryId.value = res.id
      }
      else {
        console.warn('初始化分类数据失败：', res)
        // window.$message.warning('分类数据加载失败，请刷新页面重试')
      }
    })
    .catch((error) => {
      console.error('初始化分类数据出错：', error)
      window.$message.error('分类数据加载出错，请刷新页面重试')
    })
})
</script>

<style lang="scss" scoped>
:deep(.n-icon-slot) {
  display: flex;
  align-items: center;
}
</style>
