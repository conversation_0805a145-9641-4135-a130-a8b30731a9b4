/**
 * v-hasPermi 操作权限处理
 */

import type { DirectiveBinding } from 'vue'
import { useAuthStore } from '@/store/auth/auth'
export default {
  mounted(el: HTMLElement, binding: DirectiveBinding<string[]>) {
    const { value } = binding
    const store = useAuthStore()
    // 鉴权信息
    const permissionCollect = computed(() =>
      store.wholeUserInfo ? store.wholeUserInfo.permissionCollect : [],
    )

    if (value && value instanceof Array && value.length > 0) {
      const permissionFlag = value

      let hasPermissions = true
      permissionFlag.forEach((item) => {
        if (!permissionCollect.value.includes(item)) {
          hasPermissions = false
        }
      })
      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error('请设置操作权限标签值')
    }
  },
}
