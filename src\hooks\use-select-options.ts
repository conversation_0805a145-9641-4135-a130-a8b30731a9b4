import { fetchEnumeration, fetchOriginEnumeration } from '@/services'
import {
  getCurrentPartyOrganization,
  getCurrentPartyOrganizationByUser,
  getPartyOrganization,
} from '@/services/affairs/party-building-list'
import type { partyOrganizationItem } from '@/services/affairs/party-building-list/types'
import { getOrganizationTableList } from '@/services/organization'
import { getUserList } from '@/services/structure/party-development'
import type { DevMemberChoseList } from '@/services/structure/party-development/types'

/** 获取组织列表树 */
export function useOrganizationListOptions() {
  const organizationListTree = ref<partyOrganizationItem[]>([])

  // const organizationListTreeOption = ref<CascaderOption[]>([])
  function loadOrganization() {
    getPartyOrganization().then((res: any) => {
      organizationListTree.value = res
      // organizationListTreeOption.value = res.map((item: any) => ({
      //   label: item.name,
      //   value: item.id,
      //   children: item.children,
      // }))
    })
  }

  onActivated(() => loadOrganization())
  loadOrganization()
  return { organizationListTree }
}

/** 获取当前用户管辖组织列表树 */
export function useCurrentOrganizationListOptions() {
  const organizationCurrentListTree = ref<partyOrganizationItem[]>([])

  // const organizationListTreeOption = ref<CascaderOption[]>([])
  function loadOrganization() {
    getCurrentPartyOrganization().then((res: any) => {
      organizationCurrentListTree.value = res
      // organizationListTreeOption.value = res.map((item: any) => ({
      //   label: item.name,
      //   value: item.id,
      //   children: item.children,
      // }))
    })
  }

  onActivated(() => loadOrganization())
  loadOrganization()
  return { organizationCurrentListTree }
}

function removeEmptyChildren(data: any) {
  if (!Array.isArray(data)) {
    return
  }

  for (let i = 0; i < data.length; i++) {
    const item = data[i]
    if (item.children && item.children.length === 0) {
      delete item.children
    }
    else if (item.children && item.children.length > 0) {
      removeEmptyChildren(item.children)
    }
  }
}

/** 获取当前用户管辖组织列表树（新） */
export function useCurrentOrganizationListOptionsNew() {
  const organizationCurrentListTree = ref<partyOrganizationItem[]>([])

  // const organizationListTreeOption = ref<CascaderOption[]>([])
  function loadOrganization() {
    getCurrentPartyOrganizationByUser().then((res: any) => {
      removeEmptyChildren(res)
      organizationCurrentListTree.value = res
    })
  }

  onActivated(() => loadOrganization())
  loadOrganization()
  return { organizationCurrentListTree }
}

/** 获取组织列表树新 */
export function useOrganizationListOptionsNew() {
  const organizationListTree = ref<partyOrganizationItem[]>([])

  // const organizationListTreeOption = ref<CascaderOption[]>([])
  function loadOrganization() {
    getCurrentPartyOrganizationByUser().then((res: any) => {
      organizationListTree.value = res
    })
  }

  onActivated(() => loadOrganization())
  loadOrganization()
  return { organizationListTree }
}

/** 获取党员选择列表 */
export function useDevMemberChoseList() {
  const userList = ref<DevMemberChoseList[]>([])

  function loadChoseList() {
    getUserList().then((res: any) => {
      userList.value = res
    })
  }

  onActivated(() => loadChoseList())
  loadChoseList()
  return { userList }
}

/** 获取组织列表树 */
export function useBranchGardenListOptions() {
  const branchGardenList = ref([])

  function loadBranchGardenList() {
    getOrganizationTableList().then((res: any) => {
      branchGardenList.value = res
    })
  }

  onActivated(() => loadBranchGardenList())
  loadBranchGardenList()
  return { branchGardenList }
}

/** 获取特定枚举值 */
export interface EnumerationItemType {
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
  id: string
  dictId: string
  dictKey: string
  value: string
  label: string
  type?: any
  description: string
  sortOrder: number
  remark: string
  delFlag: string
  disabled?: boolean
}

export function useFetchEnumerationOptions(key: string) {
  const enumerationList = ref<EnumerationItemType[]>([])

  function loadEnumerationList() {
    fetchEnumeration(key).then((res: any) => {
      enumerationList.value = res
    })
  }

  onActivated(() => loadEnumerationList())
  loadEnumerationList()
  return { enumerationList }
}

/** 获取籍贯枚举值 */
export function useFetchOriginOptions() {
  const OriginOptions = ref([])

  function loadOriginOptionsList() {
    fetchOriginEnumeration().then((res: any) => {
      OriginOptions.value = res
    })
  }

  onActivated(() => loadOriginOptionsList())
  loadOriginOptionsList()
  return { OriginOptions }
}
