<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    :show-add="judgePermission('talkAbout_add_btn')"
    :show-delete="judgePermission('talkAbout_del_btn')"
    :show-toolbar="false"
    :table-columns="tableColumnsOfPartyMember"
    :table-data="tableData"
    :total="total"
    custom-toolbar
    title="民主评议"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button
        v-if="judgePermission('talkAbout_add_btn')"
        size="small"
        type="primary"
        @click="handleClickAdd"
      >
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <n-button
        v-if="judgePermission('talkAbout_del_btn')"
        size="small"
        @click="handleBatchDelete"
      >
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button>
    </template>
    <template #filters>
      <n-input
        v-model:value="filterReactive.reviewTitle"
        clearable
        placeholder="请输入评议名称"
        size="small"
      />
    </template>
  </table-container>
  <!-- 新增明主评议抽屉 -->
  <n-drawer v-model:show="showEditRef" :mask-closable="false" :width="700">
    <n-drawer-content :title="drawerTitle" closable>
      <addTalkAboutForm
        :id="idEditRef"
        ref="addTalkAboutFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            style="width: 80px"
            type="primary"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
import { NButton } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import { columnsOfTalkAbout } from './config'
import addTalkAboutForm from './cpns/addTalkAboutForm.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import {
  delTalkAboutTableListItem,
  distributeReview,
  getTalkAboutTableList,
} from '@/services/publicity/vote/talkAbout'
import { useVoteStore } from '@/store/vote/vote'
import { REVIEWSTATUS } from '@/constant'
import { judgePermission } from '@/directive/permission/ifHasPermi'

const voteStore = useVoteStore()
const filterReactive = ref<{ reviewTitle?: string }>({
  reviewTitle: '',
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getTalkAboutTableList, filterReactive, {
  batchDeleteTable: true,
  delApi: delTalkAboutTableListItem,
})

watch(filterReactive.value, () => {
  loadData()
})

// 新增/编辑明主评议抽屉
const idEditRef = ref()
const addTalkAboutFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('民主评议', handelConfirmEdit)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}

/** 确定保存 */
function handelConfirmEdit() {
  addTalkAboutFormRef.value?.validateAndSave()
}

watch(showEditRef, (newV) => {
  if (!newV) {
    addTalkAboutFormRef.value?.resetForm()
  }
})

/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

const router = useRouter()
// 列表按钮列
const tableColumnsOfPartyMember = columnsOfTalkAbout((row) => {
  // 查看、编辑按钮
  const talkAboutButton = (type: 'modify' | 'view') =>
    h(
      NButton,
      {
        onClick: () => {
          // 打开民主评议编辑框
          idEditRef.value = row.id
          editTypeRef.value = type
          showEditRef.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: type === 'view' ? '查看' : '编辑',
      },
    )

  // 评议详情按钮
  const detailButton = h(
    NButton,
    {
      onClick: () => {
        // 跳转到民主评议-党员
        voteStore.setVoteMenuDataAction({
          showMenu: true,
          currentYear: row.reviewYear as string,
          query: {
            reviewId: row.id,
            year: row.reviewYear,
            partReviewItemId: row.partReviewId,
            branchReviewItemId: row.branchReviewId,
          },
        })
        router.push({
          name: 'talk-about-party-member',
          query: {
            reviewId: row.id,
            year: row.reviewYear,
            partReviewItemId: row.partReviewId,
            branchReviewItemId: row.branchReviewId,
          },
        })
      },
      type: 'primary',
      text: true,
      style: {
        marginRight: '10px',
      },
    },
    {
      default: '评议详情',
    },
  )

  // 下发按钮
  const distributeButton = h(
    NButton,
    {
      onClick: () => {
        // 下发请求
        distributeReview(row.id as string).then((res) => {
          window.$message.success('下发成功！')
          loadData()
        })
      },
      type: 'primary',
      text: true,
      style: {
        marginRight: '10px',
      },
    },
    {
      default: '下发',
    },
  )

  // 删除按钮
  const deleteButton = judgePermission('talkAbout_del_btn')
    ? h(DeleteButton, {
      handleConfirm: () => handleSingleDelete(String(row.id)),
    })
    : null

  const resBtns = []
  resBtns.push(talkAboutButton('view'))
  if (
    row.reviewStatus === REVIEWSTATUS.NOTSTARTED
    || row.reviewStatus === REVIEWSTATUS.TOBEUSED
  ) {
    // 未开始 、待下发 编辑
    resBtns.push(talkAboutButton('modify'))
  }

  if (row.reviewStatus === REVIEWSTATUS.TOBEUSED) {
    // 待下发
    resBtns.push(distributeButton)
  }
  else {
    // 评议详情
    resBtns.push(detailButton)
  }

  resBtns.push(deleteButton)
  return resBtns
})
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped></style>
