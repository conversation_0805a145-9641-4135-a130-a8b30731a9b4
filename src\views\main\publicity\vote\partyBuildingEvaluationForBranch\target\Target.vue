<script setup lang="ts">
import { ArrowBackIosNewRound, KeyboardArrowDownSharp } from '@vicons/material'

import { Export } from '@vicons/carbon'
import type { DataTableColumns } from 'naive-ui'
import { NButton, NCollapse, NCollapseItem, NEllipsis, NIcon } from 'naive-ui'
import { h } from 'vue'
import UploadVoucher from './uploadVoucher/uploadVoucher.vue'

import { downloadArrayBuffer } from '@/utils/downloader'
import { formatTimeStamp } from '@/utils/format'
import {
  exportPartyBuildingTargetFileForBranch,
  getPartyBuildingEvaluationTargetInfoForBranch,
  partyBuildingRevocationVoucherForBranch,
  uploadPartyBuildingVoucherForBranch,
} from '@/services/publicity/vote/evaluationForBranch'
import type {
  TargetDataType,
  TargetItemListItemType,
} from '@/services/publicity/vote/evaluationForBranch/type'
import usePdfPreview from '@/hooks/use-pdf-preview'

const route = useRoute()
const router = useRouter()
const title = route.query.title
const status = computed(() => route.query.status)

const partyListId = computed(() => route.query.partyListId)
const targetListRef = ref<TargetDataType>()
const allTargetItemList = ref<TargetItemListItemType[]>([])

const loading = ref(false)
/** 加载指标数据 */
function loadTargetListData() {
  loading.value = true
  getPartyBuildingEvaluationTargetInfoForBranch(String(partyListId.value))
    .then((res: any) => {
      if (res) {
        targetListRef.value = res
        allTargetItemList.value = res.targetItemForm?.flatMap(
          (item: any) => item.targetItemList,
        )
      }
    })
    .finally(() => {
      loading.value = false
    })
}

/** 导出党建考核 */
async function handleExport() {
  try {
    loading.value = true
    const res = await exportPartyBuildingTargetFileForBranch(
      String(partyListId.value),
    )
    downloadArrayBuffer(
      res,
      `党建考核-${formatTimeStamp(Date.now(), 'YYYYMMDDHHmmss')}`,
    )
    loading.value = false
  }
  catch (error) {}
}

// 打开上传凭证的弹框
const currentIndex = ref(0)
const upLoadVoucherVisible = ref(false)
const fileIDList = reactive<{ list: string[] }>({ list: [] }) // 文件ID
const fileListRef = reactive<{ list: any[] }>({ list: [] }) // 含有文件名的文件对象
// 获取FileIDs
const getUploadFileIDs = (ids: string[]) => {
  fileIDList.list = ids
}
// 获取文件对象
const getFileItem = (fileList: any[]) => {
  fileListRef.list = fileList
}
const openUploadVoucherDialog = (index: number) => {
  currentIndex.value = index
  upLoadVoucherVisible.value = true
}

// 点击 确认 按钮的操作
const handleUploadVoucherConfirm = () => {
  allTargetItemList.value[currentIndex.value].fileList = fileListRef.list
  upLoadVoucherVisible.value = false
}

// 提交凭证
const submitVoucher = (id: string) => {
  loading.value = true
  uploadPartyBuildingVoucherForBranch({ id, fileIds: fileIDList.list })
    .then((res) => {
      window.$message.success('提交成功')
      loadTargetListData()
    })
    .finally(() => {
      loading.value = false
    })
}

// 撤销凭证
const revocationVoucher = (id: string) => {
  partyBuildingRevocationVoucherForBranch(id).then(() => {
    window.$message.success('撤销成功')
    loadTargetListData()
  })
}

// const baseApi = ref(import.meta.env.VITE_API_BASE)
const { useLoadPdfPreview } = usePdfPreview()

function handlePdfPreview(fileName: string) {
  // const urlAll = `http://${window.location.host}${baseApi.value}${fileName}`
  const urlAll = `${window.$previewHost}${fileName}`
  useLoadPdfPreview(urlAll)
}

const tableColumns: DataTableColumns<TargetItemListItemType> = [
  {
    key: 'index',
    title: '序号',
    width: '4%',
    align: 'center',
    render: (_, i) => i + 1,
  },
  {
    key: 'matter',
    width: '12%',
    title: '考核指标',
    render: (row) => {
      return h(
        NEllipsis,
        {
          style: {
            maxWidth: '280px',
          },
          expandTrigger: 'click',
          lineClamp: '2',
          tooltip: true,
        },
        {
          default: () => {
            return h('span', {
              innerHTML: row.matter,
            })
          },
        },
      )
    },
  },
  {
    key: 'evaluationRequirements',
    width: '12%',
    title: '计分标准',
    render: (row) => {
      return h(
        NEllipsis,
        {
          style: {
            maxWidth: '280px',
          },
          expandTrigger: 'click',
          lineClamp: '2',
          tooltip: true,
        },
        {
          default: () => {
            return h('span', {
              innerHTML: row.evaluationRequirements,
            })
          },
        },
      )
    },
  },
  {
    key: 'evaluationMode',
    width: '12%',
    title: '检查材料',
    render: (row) => {
      return h(
        NEllipsis,
        {
          style: {
            maxWidth: '280px',
          },
          expandTrigger: 'click',
          lineClamp: '2',
          tooltip: true,
        },
        {
          default: () => {
            return h('span', {
              innerHTML: row.evaluationMode,
            })
          },
        },
      )
    },
  },
  {
    key: 'dept',
    width: '8%',
    title: '检查部门',
  },
  {
    key: 'evaluationScore',
    width: '8%',
    align: 'center',
    title: '分值',
  },
  {
    key: 'endTime',
    width: '12%',
    title: '截止日期',
  },
  {
    key: 'fileList',
    title: '佐证材料',
    width: '14%',
    render: (row) => {
      return row.fileList?.length
        ? h(
          NCollapse,
          {
            arrowPlacement: 'right',
          },
          [
            h(
              NCollapseItem,
              {},
              {
                header: () =>
                  h(
                    'div',
                    {
                      style: {
                        marginBottom: '2px',
                        cursor: 'pointer',
                        color: '#3f7ee8',
                      },
                    },
                    h(
                      'span',
                      {
                        onClick: (e: Event) => {
                          // downloadFile(
                          //   row.fileList?.[0]?.fileName,
                          //   row.fileList?.[0]?.original,
                          // )
                          handlePdfPreview(row.fileList?.[0]?.fileName)
                          e.stopPropagation()
                        },
                      },
                      row.fileList?.[0]?.original,
                    ),
                  ),
                arrow: () =>
                  h(
                    NIcon,
                    row.fileList?.length === 1
                      ? ''
                      : () => h(KeyboardArrowDownSharp),
                  ),
                default: () =>
                  row.fileList?.slice(1)
                    && row.fileList?.slice(1).map((item) => {
                      return h(
                        'div',
                        {
                          style: {
                            marginBottom: '2px',
                            cursor: 'pointer',
                            color: '#3f7ee8',
                          },
                        },
                        h(
                          'span',
                          {
                            onClick: (e: Event) => {
                              // downloadFile(item.fileName, item.original)
                              handlePdfPreview(item.fileName)
                              e.stopPropagation()
                            },
                          },
                          item.original,
                        ),
                      )
                    }),
              },
            ),
          ],
        )
        : h('span', {}, { default: () => '--' })
    },
  },
  {
    key: 'submitStatus',
    width: '6%',
    align: 'center',
    title: '状态',
  },
  {
    key: 'options',
    title: '操作',
    width: '10%',
    align: 'center',
    render: (row, index) => {
      return h('div', [
        row.submitStatus === '已提交'
          ? h(
            NButton,
            {
              text: true,
              type: 'primary',
              style: 'margin-right:10px',
              onClick: () => {
                revocationVoucher(row.id)
              },
            },
            {
              default: () => '撤销',
            },
          )
          : '',
        row.submitStatus === '已撤销'
          ? h(
            NButton,
            {
              text: true,
              type: 'primary',
              style: 'margin-right:10px',
              onClick: () => {
                openUploadVoucherDialog(index)
              },
            },
            '填写完成情况',
          )
          : '',
        row.submitStatus === '已撤销'
          ? h(
            NButton,
            {
              text: true,
              type: 'primary',
              style: 'margin-right:10px',
              onClick: () => {
                submitVoucher(row.id)
              },
            },
            '重新提交',
          )
          : '',
        row.submitStatus === '未提交'
          ? h(
            NButton,
            {
              text: true,
              type: 'primary',
              style: 'margin-right:10px',
              onClick: () => {
                openUploadVoucherDialog(index)
              },
            },
            '填写完成情况',
          )
          : '',
        row.submitStatus === '未提交'
          ? h(
            NButton,
            {
              text: true,
              type: 'primary',
              style: 'margin-right:10px',
              onClick: () => {
                submitVoucher(row.id)
              },
            },
            '提交',
          )
          : '',
      ])
    },
  },
]

const tableColumns2: DataTableColumns<TargetItemListItemType> = [
  {
    key: 'index',
    title: '序号',
    width: '4%',
    align: 'center',
    render: (_, i) => i + 1,
  },
  {
    key: 'matter',
    width: '12%',
    title: '考核指标',
    render: (row) => {
      return h(
        NEllipsis,
        {
          style: {
            maxWidth: '280px',
          },
          expandTrigger: 'click',
          lineClamp: '2',
          tooltip: true,
        },
        {
          default: () => {
            return h('span', {
              innerHTML: row.matter,
            })
          },
        },
      )
    },
  },
  {
    key: 'evaluationRequirements',
    width: '12%',
    title: '计分标准',
    render: (row) => {
      return h(
        NEllipsis,
        {
          style: {
            maxWidth: '280px',
          },
          expandTrigger: 'click',
          lineClamp: '2',
          tooltip: true,
        },
        {
          default: () => {
            return h('span', {
              innerHTML: row.evaluationRequirements,
            })
          },
        },
      )
    },
  },
  {
    key: 'evaluationMode',
    width: '12%',
    title: '检查材料',
    render: (row) => {
      return h(
        NEllipsis,
        {
          style: {
            maxWidth: '280px',
          },
          expandTrigger: 'click',
          lineClamp: '2',
          tooltip: true,
        },
        {
          default: () => {
            return h('span', {
              innerHTML: row.evaluationMode,
            })
          },
        },
      )
    },
  },
  {
    key: 'dept',
    width: '8%',
    title: '检查部门',
  },
  {
    key: 'evaluationScore',
    width: '8%',
    align: 'center',
    title: '分值',
  },
  {
    key: 'endTime',
    width: '12%',
    title: '截止日期',
  },
  {
    key: 'fileList',
    title: '佐证材料',
    width: '14%',
    render: (row) => {
      return row.fileList?.length
        ? h(
          NCollapse,
          {
            arrowPlacement: 'right',
          },
          [
            h(
              NCollapseItem,
              {},
              {
                header: () =>
                  h(
                    'div',
                    {
                      style: {
                        marginBottom: '2px',
                        cursor: 'pointer',
                        color: '#3f7ee8',
                      },
                    },
                    h(
                      'span',
                      {
                        onClick: (e: Event) => {
                          // downloadFile(
                          //   row.fileList?.[0]?.fileName,
                          //   row.fileList?.[0]?.original,
                          // )
                          handlePdfPreview(row.fileList?.[0]?.fileName)
                          e.stopPropagation()
                        },
                      },
                      row.fileList?.[0]?.original,
                    ),
                  ),
                arrow: () =>
                  h(
                    NIcon,
                    row.fileList?.length === 1
                      ? ''
                      : () => h(KeyboardArrowDownSharp),
                  ),
                default: () =>
                  row.fileList?.slice(1)
                    && row.fileList?.slice(1).map((item) => {
                      return h(
                        'div',
                        {
                          style: {
                            marginBottom: '2px',
                            cursor: 'pointer',
                            color: '#3f7ee8',
                          },
                        },
                        h(
                          'span',
                          {
                            onClick: (e: Event) => {
                              // downloadFile(item.fileName, item.original)
                              handlePdfPreview(item.fileName)
                              e.stopPropagation()
                            },
                          },
                          item.original,
                        ),
                      )
                    }),
              },
            ),
          ],
        )
        : h('span', {}, { default: () => '--' })
    },
  },
  {
    key: 'score',
    title: '状态',
    align: 'center',
  },
]

onMounted(loadTargetListData)
</script>
<template>
  <edit-top>
    <template #left>
      <n-button size="small" @click="() => router.back()">
        <n-icon size="16">
          <arrow-back-ios-new-round />
        </n-icon>
        返回
      </n-button>
    </template>
    <template #mid>
      {{ title }}
    </template>
    <template #right>
      <n-button v-if="status === '已完成'" @click="handleExport">
        <n-icon style="margin-right: 6px" size="14">
          <export />
        </n-icon>
        导出
      </n-button>
    </template>
  </edit-top>

  <div class="px-[20px] py-[23px]">
    <div
      class="bg-[#fafafa] pl-[12px] pr-[20px] text-center leading-[42px] text-[12px] font-[500] mb-[11px] text-[#333] grid grid-cols-[1fr,2fr,2fr,2fr,1fr,1.5fr,1.5fr,3fr,1.5fr,1.5fr]"
    >
      <span class="text-center">序号</span>
      <span>考核指标</span>
      <span>计分标准</span>
      <span class="text-center">检查材料</span>
      <span class="text-center">检查部门</span>
      <span class="text-center">分值</span>
      <span class="text-center">截止日期</span>
      <span class="text-center">佐证材料</span>
      <template v-if="status !== '已完成'">
        <span class="text-center">状态</span>
        <span class="text-center mr-[20px]">操作</span>
      </template>
      <template v-if="status === '已完成'">
        <span class="inline-block ml-[100px]">得分</span>
      </template>
    </div>
    <n-scrollbar style="width: 100%; height: calc(100vh - 330px)">
      <div
        v-for="(item, index) in targetListRef?.targetItemForm"
        :key="index"
        class="bg-[#fafafa] px-[20px] py-[22px] mb-[20px]"
      >
        <div class="flex justify-between items-center mb-[22px]">
          <div class="flex items-center gap-[20px]">
            <span
              class="text-[12px] text-[#CB0000] font-[500] leading-[17px] ml-[10px]"
            >项目：{{ item.categoryName }}</span>
          </div>
        </div>
        <div>
          <n-data-table
            id="targetTable"
            :bordered="true"
            :single-line="false"
            :columns="['已完成'].includes(status as string)?tableColumns2: tableColumns"
            :data="item.targetItemList"
          />
        </div>
      </div>
    </n-scrollbar>

    <div class="sticky bottom-0 z-1 bg-[#fff]">
      <div
        class="bg-[#fafafa] px-[20px] leading-[42px] text-[12px] font-[500] text-[#cb0000]"
      >
        总分：{{ targetListRef?.totalScore }}分
      </div>
    </div>
  </div>
  <!-- 上传凭证 -->
  <custom-dialog
    ref="uploadVoucher"
    v-model:show="upLoadVoucherVisible"
    width="900px"
    title="填写完成情况"
    @confirm="handleUploadVoucherConfirm"
  >
    <UploadVoucher
      @emits-upload-file="getUploadFileIDs"
      @emits-upload-file-item="getFileItem"
    />
  </custom-dialog>
</template>
<style lang="scss" scoped>
:deep(.n-data-table .n-data-table-thead) {
  display: none;
}

:deep(.n-data-table .n-data-table-thead) {
  display: none;
}

.emptyImg {
  width: 100%;
  height: 500px;
  background-image: url('@/assets/image/emptyImg.png');
  background-position: center center;
  background-repeat: no-repeat;
}
</style>
