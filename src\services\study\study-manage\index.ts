import type { StudyContentDetail } from './types'
import type { PaginationReq } from '@/services/types'
import { commonReq } from '@/services/request'

/** 获取学习分类 */
export function getStudyCategoryList() {
  return commonReq.get({
    url: '/study/backend/v1/study/category/listStudyCategory',
  })
}

/** 新增学习分类 */
export function postAddStudyCategory(data: {
  name: string
  parentId?: string
}) {
  return commonReq.post({
    url: '/study/backend/v1/study/category',
    data,
  })
}

/** 编辑学习分类 */
export function putEditStudyCategory(data: {
  id: string
  name: string
  parentId?: string
}) {
  return commonReq.put({
    url: '/study/backend/v1/study/category',
    data,
  })
}

/** 删除学习分类 */
export function deleteStudyCategory(id: string) {
  return commonReq.delete({
    url: '/study/backend/v1/study/category',
    params: {
      id,
    },
  })
}

/** 移动学习分类 */
export function putMoveStudyCategory(data: {
  id: string
  parentId: string
  move: string
}) {
  return commonReq.put({
    url: '/study/backend/v1/study/category/move',
    data,
  })
}

/** 添加学习内容 */
export function postStudyContent(data: StudyContentDetail) {
  return commonReq.post({
    url: '/study/backend/v1/study',
    data,
  })
}

/** 获取学习内容列表 */
export function getStudyTableList(
  params: {
    categoryId?: string
    title?: string
  } & PaginationReq,
) {
  return commonReq.get({
    url: '/study/backend/v1/study',
    params,
  })
}

/** 获取学习内容详情 */
export function getStudyContentDetail(id: string | null) {
  return commonReq.get({
    url: `/study/backend/v1/study/${id}`,
  })
}

/** 编辑学习内容 */
export function putStudyContent(data: StudyContentDetail) {
  return commonReq.put({
    url: '/study/backend/v1/study',
    data,
  })
}

/** 置顶 / 取消置顶 */
export function putStudyIsTop(id: string) {
  return commonReq.put({
    url: `/study/backend/v1/study/top/${id}`,
  })
}

/** 发布 / 取消发布 */
export function publishStudyContent(id: string) {
  return commonReq.put({
    url: `/study/backend/v1/study/publish/${id}`,
  })
}

/** 删除学习内容 */
export function deleteStudyContent(ids: string) {
  return commonReq.delete({
    url: '/study/backend/v1/study',
    params: {
      ids,
    },
  })
}
