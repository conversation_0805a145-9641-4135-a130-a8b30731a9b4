import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  annexName: [
    {
      required: true,
      message: '附件名称不能为空',
      trigger: 'input',
    },
  ],
  annexDescribe: [
    {
      required: true,
      message: '附件描述不能为空',
      trigger: 'input',
    },
  ],
  pictureUrl: {
    required: true,
    message: '请上传图片',
    trigger: 'change',
    type: 'string',
  },
  fileList: {
    required: true,
    validator(rule: any, value: any) {
      if (value === null) {
        return new Error('请选择文件')
      }
      return true
    },
    trigger: 'change',
  },
}
