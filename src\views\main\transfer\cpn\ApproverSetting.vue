<script setup lang="ts">
import { NButton, type NForm, NInput, NSelect } from 'naive-ui'
import { approvalSettingRules, getTableColumnsOfApprover } from './config'
import { getOrganizationTree } from '@/services/system/Admin'
import {
  getRoleList,
  savePartyMemberTransferApprover,
  viewPartyMemberTransferApprover,
} from '@/services/transfer'
import type {
  RoleItemType,
  RoleList,
  SaveApproverType,
} from '@/services/transfer/types'

import { useFetchEnumerationOptions } from '@/hooks/use-select-options'
const { enumerationList } = useFetchEnumerationOptions('org_type')
const calcEnumerationList = computed(() => {
  let arr: any = []
  arr = enumerationList.value.map((item) => {
    return {
      label: item.label,
      value: item.value,
    }
  })
  arr.push({ label: '无限制', value: '99' })
  return arr
})

const loading = ref<boolean>(false)

const emits = defineEmits<{
  (e: 'saved'): void
}>()
// 存放所有部门id的list
const isSaveAllDept = ref<boolean>(false)
const deptIds = ref<string[]>([])
const formRef = ref<InstanceType<typeof NForm>>()
const formDataReactive = reactive<{ value: SaveApproverType }>({
  value: {
    orgidList: [],
    nodeList: [
      {
        approvalNode: '',
        orgTypeId: '',
        orgTypeName: '',
        roleList: [],
        selectRoleList: [],
      },
    ],
  },
})
// 角色列表
const roleList = reactive<{ value: RoleItemType[] }>({ value: [] })
const calcRoleList = computed(() => {
  return roleList.value.map((item) => {
    return {
      label: item.roleName,
      value: item.roleId,
    }
  })
})

onBeforeMount(() => {
  getRoleListFn()
  viewPartyMemberTransferApproverFn()
})

// 获取角色列表
function getRoleListFn() {
  return getRoleList().then((res) => {
    nextTick(() => {
      roleList.value = res || []
    })
  })
}

// 查询审批人设置
function viewPartyMemberTransferApproverFn() {
  return viewPartyMemberTransferApprover().then((res) => {
    formDataReactive.value = res
    formDataReactive.value.nodeList.forEach((item) => {
      item.selectRoleList = item.roleList.map(role => role.approvalRoleId)
    })
    // 默认选择全部部门
    if (!formDataReactive.value.orgidList.length) {
      isSaveAllDept.value = true
    }
    else {
      isSaveAllDept.value = false
    }
  })
}

const approverColumns = getTableColumnsOfApprover(
  // 审批节点名称
  (row) => {
    return [
      h(NInput, {
        placeholder: '请输入审批节点',
        value: row.approvalNode,
        onInput(v: string) {
          row.approvalNode = v
        },
      }),
    ]
  },
  // 审批角色
  (row) => {
    return [
      h(NSelect, {
        placeholder: '请选择审批角色',
        multiple: true,
        value: row.selectRoleList as any,
        options: calcRoleList.value,
        onUpdateValue(v: string[]) {
          row.selectRoleList = v
        },
      }),
    ]
  },
  // 审批角色所属组织
  (row) => {
    return [
      h(NSelect, {
        placeholder: '请选择审批角色所属组织',
        value: row.orgTypeId,
        options: calcEnumerationList.value,
        onUpdateValue(v: string) {
          const findData = calcEnumerationList.value.find(
            (item: { value: string }) => item.value === v,
          )
          if (findData) {
            row.orgTypeId = findData.value
            row.orgTypeName = findData.label
          }
        },
      }),
    ]
  },
  (row, index) => {
    return [
      h(
        NButton,
        {
          onClick: () => {
            formDataReactive.value.nodeList.splice(index, 1)
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '删除',
        },
      ),
    ]
  },
)

// 树结构枚举
const treeData = ref<any[]>([])
// 获取组织结构
const getOrganizationTreeData = () => {
  return getOrganizationTree().then((res: any) => {
    treeData.value = res
    getAllDeptId(treeData.value)
  })
}
getOrganizationTreeData()
const calcTreeDataFn: any = (arr: any[]) => {
  return arr.map((item: any) => {
    return {
      ...item,
      children:
        item.children && item.children.length
          ? calcTreeDataFn(item.children)
          : undefined,
    }
  })
}
// 计算属性重置树结构的children
const calcTreeData = computed(() => {
  return calcTreeDataFn(treeData.value)
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      savePartyMemberTransferApprover(formDataReactive.value).then(() => {})
    }
  })
}

// 转化data为树状结构
function transformTreeData(data: any) {
  if (Array.isArray(data)) {
    data.forEach((item) => {
      item.key = item.id
      item.label = item.name
      item.value = item.id

      if (item.children) {
        item.children.forEach((child: any) => {
          transformTreeData(child)
        })
      }
    })
  }
  else {
    data.key = data.id
    data.label = data.name
    data.value = data.id

    if (data.children) {
      data.children.forEach((child: any) => {
        transformTreeData(child)
      })
    }
  }
  return data
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

// 添加节点
function addNode() {
  formDataReactive.value.nodeList.push({
    approvalNode: '',
    orgTypeId: '',
    orgTypeName: '',
    roleList: [],
  })
}

function formatterRole(arr: string[], originDataIndex: number): RoleList[] {
  if (arr && arr.length) {
    return arr!.map((roleId: string) => {
      const findData = calcRoleList.value.find(
        findRoleItem => findRoleItem.value === roleId,
      )
      return {
        approvalRoleId: findData?.value,
        approvalRoleName: findData?.label,
      }
    })
  }
  else {
    return []
  }
}

// 保存审批人设置
function saveApprover() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      loading.value = true
      // 保存审批人设置时需要组装审批角色的数据
      formDataReactive.value.nodeList.forEach((item, index) => {
        item.roleList = formatterRole(item.selectRoleList as string[], index)
      })
      savePartyMemberTransferApprover(formDataReactive.value)
        .then(() => {
          window.$message.success('保存成功')
        })
        .finally(() => {
          loading.value = false
        })
    }
  })
}

// 递归取出所有组织的id
function getAllDeptId(arr: any[]) {
  const idList: Array<string> = []
  arr.forEach((dept) => {
    idList.push(dept.id)
    if (dept.children && dept.children.length) {
      getAllDeptId(dept.children)
    }
  })

  deptIds.value = deptIds.value.concat(idList)
}

watch(
  () => deptIds.value,
  (newVal) => {
    if (isSaveAllDept.value) {
      formDataReactive.value.orgidList = newVal
    }
  },
)
defineExpose({
  saveApprover,
  validateAndSave,
  resetForm,
})
</script>
<template>
  <div class="w-[1000px] px-[20px] py-[20px]">
    <n-form
      ref="formRef"
      size="small"
      require-mark-placement="left"
      label-width="100"
      label-align="left"
      label-placement="left"
      :model="formDataReactive.value"
      :rules="approvalSettingRules"
    >
      <n-form-item label="审批组织：" label-placement="top" path="orgidList">
        <template #label>
          <div>
            <span class="inline-block h-[26px] font-bold">审批组织：</span>
          </div>
        </template>
        <n-tree-select
          v-model:value="formDataReactive.value.orgidList"
          multiple
          checkable
          clearable
          placeholder="请选择当前组织"
          size="small"
          max-tag-count="responsive"
          :options="transformTreeData(calcTreeData)"
        />
      </n-form-item>
      <n-form-item label="审批人设置：" label-placement="top" path="nodeList">
        <template #label>
          <div>
            <span class="inline-block h-[26px] font-bold">审批人设置：</span>
          </div>
        </template>
        <n-data-table
          :columns="approverColumns"
          :data="formDataReactive.value.nodeList"
          :loading="loading"
        />
      </n-form-item>
      <n-form-item>
        <div
          class="box-border flex flex-row justify-start items-start gap-x-[10px]"
        >
          <n-button @click="addNode">
            添加节点
          </n-button>

          <n-button :loading="loading" type="primary" @click="saveApprover">
            保存
          </n-button>
        </div>
      </n-form-item>
    </n-form>
  </div>
</template>
<style lang="scss" scoped></style>
