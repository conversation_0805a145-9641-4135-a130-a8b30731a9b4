export interface categoryListType {
  commentNum: number
  coverId: string
  coverUrl: string
  id: string
  isRecommend: string
  isTop: string
  publishTime: string
  readNum: number
  title: string
}

export interface categoryDetailType {
  /**
   * 作者
   */
  author: string
  /**
   * 分类id
   */
  categoryId: string
  /**
   * 内容
   */
  content: string
  /**
   * 封面id
   */
  coverId: string
  /**
   * 主键id
   */
  id?: null | string
  /**
   * 是否推荐
   */
  isRecommend: string
  /**
   * 是否置顶
   */
  isTop: string
  /**
   * 发布时间
   */
  publishTime: null | string
  /**
   * 阅读时间
   */
  readTimeConfig: number
  /**
   * 标题
   */
  title: string
}
