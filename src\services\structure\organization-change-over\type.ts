// 组织关系转接列表item
export interface TableItem {
  id: string
  userName: string
  transferType: string
  oldDeptName: string
  newDeptName: string
  reason?: any
  phaseStatus: string
  remark?: any
  joinTime: string
  paymentTime: string
  letterId?: any
  proveId?: any
  transferTime: string
}

export interface fileItem {
  fileName: number | string | null
  id: string
  original: number | string | null
}

// 组织关系转接-新增-body参数
export interface AddChangeOverItem {
  id: number | string | null
  userId: number | string | null
  transferType: number | null
  oldDeptId: number | string | null
  oldDeptName?: string | null
  newDeptId: number | string | null
  newDeptName: string | null
  reason: string | null
  phaseStatus: number | null
  remark: string | null
  paymentTime: string | null
  letterId: number | string | null
  letterName?: number | string | null
  proveId: number | string | null
  transferTime: string | null
  joinTime: string | null
  userName: string | null
  fileIds?: Array<string> | null
  fileList?: Array<fileItem> | null
}

// 更新组织关系转移
export interface UpdateChangeOver {
  id: number | string | null
  phaseStatus: number | null
  remark: string | null
}
