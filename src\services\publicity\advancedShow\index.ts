import type {
  AdvanCedPartyOrganizationItem,
  AdvancedPersonItem,
  PartyOrganizationTreeItem,
  PartyPersonItem,
  SearchAdvanCedPartyOrganizationList,
} from './type'
import type { PaginationReq, PaginationRes } from '@/services/types'
import { commonReq } from '@/services/request'

/** 获取先进党组织列表 */
export function getAdvancedPartyOrganizationList(
  params: SearchAdvanCedPartyOrganizationList & PaginationReq,
) {
  return commonReq.get<PaginationRes<AdvanCedPartyOrganizationItem>>({
    url: '/propaganda/backend/v1/party/organization/list',
    params,
  })
}

/** 获取单个先进党组织详情 */
export function getAdvancedPartyOrganizationItem(id: string) {
  return commonReq.get<AdvanCedPartyOrganizationItem>({
    url: `/propaganda/backend/v1/party/organization/${id}`,
  })
}

/** 新建先进党组织单项 */
export function postInsertAdvancedPartyOrganizationItem(
  data: AdvanCedPartyOrganizationItem,
) {
  return commonReq.post({
    url: '/propaganda/backend/v1/party/organization',
    data,
  })
}

/** 编辑先进党组织单项 */
export function putEditorAdvancedPartyOrganizationItem(
  data: AdvanCedPartyOrganizationItem,
) {
  return commonReq.put({
    url: '/propaganda/backend/v1/party/organization',
    data,
  })
}

/** 推荐先进党组织 */
export function putUpdateAdvancedPartyOrganizationItemTop(id: string) {
  return commonReq.put({
    url: `/propaganda/backend/v1/party/organization/recommend/${id}`,
  })
}

/**
 * 删除先进党组织单项
 *  */
export function delAdvancedPartyOrganizationItem(ids: string) {
  return commonReq.delete({
    url: '/propaganda/backend/v1/party/organization',
    params: { ids },
  })
}

/** 获取树形党组织结构 */
export function getPartyOrganizationTree() {
  return commonReq.get<Array<PartyOrganizationTreeItem>>({
    url: '/org-construction/organization/tree',
  })
}
/** --------------优秀党员 */
/** 获取先进党员列表 */
export function getAdvancedPersonList(params: any & PaginationReq) {
  return commonReq.get<PaginationRes<AdvancedPersonItem>>({
    url: '/propaganda/backend/v1/party/person/list',
    params,
  })
}

/** 获取单个优秀党员详情 */
export function getAdvancedPersonItem(id: string) {
  return commonReq.get<AdvancedPersonItem>({
    url: `/propaganda/backend/v1/party/person/${id}`,
  })
}

/** 新建先进党员单项 */
export function postInsertAdvancedPersonItem(data: AdvancedPersonItem) {
  return commonReq.post({
    url: '/propaganda/backend/v1/party/person',
    data,
  })
}

/** 编辑先进党员单项 */
export function putEditorAdvancedPersonItem(data: AdvancedPersonItem) {
  return commonReq.put({
    url: '/propaganda/backend/v1/party/person',
    data,
  })
}

/** 推荐先进党员推荐 */
export function putUpdateAdvancedPersonItemRecommend(id: string) {
  return commonReq.put({
    url: `/propaganda/backend/v1/party/person/recommend/${id}`,
  })
}
/** 推荐先进党员置顶 */
export function putUpdateAdvancedPersonItemTop(id: string) {
  return commonReq.put({
    url: `/propaganda/backend/v1/party/person/top/${id}`,
  })
}

/**
 * 删除先进党员单项
 *  */
export function delAdvancedPersonItem(ids: string) {
  return commonReq.delete({
    url: '/propaganda/backend/v1/party/person',
    params: { ids },
  })
}

/** 获取先进党员列表 */
export function getPartyPersonList(params: { id: string } & PaginationReq) {
  return commonReq.get<PaginationRes<PartyPersonItem>>({
    url: '/propaganda/backend/v1/party/person/user/list',
    params,
  })
}
