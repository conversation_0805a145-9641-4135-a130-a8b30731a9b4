<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { reactive } from 'vue'
import { formRules } from './config'
import type {
  addAndEditParams,
  // uploadFileItem,
  // uploadFileItemNew,
} from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
// import RichEditor from '@/components/RichEditor.vue'
import {
  getViewPartyBuildingExamIndIcatorsItem,
  postPartyBuildingExamAdd,
  putEditExamIndIcatorsIdsItem,
} from '@/services/affairs/discipline-inspection-list/exam-indicators'
// import { uploadImg } from '@/services'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<addAndEditParams>({
  id: '',
  title: '',
  evaluationRequirements: '',
  evaluationMode: '',
  evaluationScore: 0,
  matter: '',
  fileIds: [],
  fileList: [],
})

const formRef = ref<InstanceType<typeof NForm>>()

onBeforeMount(() => {
  if (props.type === 'modify' && props.id) {
    getViewPartyBuildingExamIndIcatorsItem(props.id).then((res) => {
      formDataReactive.evaluationMode = res.evaluationMode
      formDataReactive.evaluationRequirements = res.evaluationRequirements
      formDataReactive.evaluationScore = res.evaluationScore
      formDataReactive.fileIds = res.fileList?.map(item => item.id) || []
      formDataReactive.fileList = res.fileList || []
      formDataReactive.matter = res.matter
      formDataReactive.title = res.title
      formDataReactive.id = props.id
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (formDataReactive.id) {
        putEditExamIndIcatorsIdsItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      } else {
        postPartyBuildingExamAdd(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// 文件相关
// async function handleFileChange(
//   fileInfoList: uploadFileItemNew[],
//   isDelIDs: string,
// ) {
//   try {
//     const fileData = new FormData()
//     const fileIdArr: Array<string> = []
//     // 获取所有FileID
//     formDataReactive.fileList.forEach(item => fileIdArr.push(item.id))
//     // 删除动作
//     if (isDelIDs) {
//       fileIdArr.forEach((item, index) => {
//         if (item === isDelIDs) {
//           fileIdArr.splice(index, 1)
//         }
//       })
//       formDataReactive.fileIds = [...fileIdArr]
//     } else {
//       // 新增动作
//       const lastFileItem = fileInfoList[fileInfoList.length - 1]?.file
//       fileData.append('file', lastFileItem as Blob)
//       const data: uploadFileItem = await uploadImg(fileData)
//       formDataReactive.fileList.push({
//         original: lastFileItem?.name as string,
//         fileName: '',
//         id: '',
//       })
//       if (data) {
//         fileIdArr.push(data.fileId)
//         if (formDataReactive.fileList.length) {
//           formDataReactive.fileIds = formDataReactive.fileIds.concat(fileIdArr)
//         } else {
//           formDataReactive.fileIds = [...fileIdArr]
//         }
//       }
//     }
//   } catch (error) {}
// }

interface dropdownOptionsItem {
  label: string
  key: string
  disabled?: boolean
}

/** 下拉菜单 */
const dropdownOptions: Array<dropdownOptionsItem> = reactive([
  {
    label: '重命名',
    key: '1',
  },
  {
    label: '添加子类',
    key: '2',
  },
  {
    label: '上移',
    key: '3',
  },
  {
    label: '下移',
    key: '4',
  },
  {
    label: '删除',
    key: '5',
  },
])
/** 下拉菜单选择 */
const handleSelectDropdownOptions = (key: string | number) => {}
// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="140"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item label="" path="title">
      <n-input v-model:value="formDataReactive.title" clearable />
    </n-form-item>
    <n-collapse>
      <n-collapse-item title="大类一" name="1">
        <template #header-extra>
          <n-dropdown
            trigger="hover"
            :options="dropdownOptions"
            @select="handleSelectDropdownOptions"
          >
            <n-button text>
              +
            </n-button>
          </n-dropdown>
        </template>
        <div>小类1</div>
        <div>小类2</div>
        <div>小类3</div>
      </n-collapse-item>
      <n-collapse-item title="大类二" name="2">
        <div>很好</div>
      </n-collapse-item>
      <n-collapse-item title="大类三" name="3">
        <div>真棒</div>
      </n-collapse-item>
    </n-collapse>
  </n-form>
</template>
<style lang="scss" scoped></style>
