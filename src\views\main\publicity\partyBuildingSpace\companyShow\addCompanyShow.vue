<script setup lang="ts">
import { NForm } from 'naive-ui'
import { reactive } from 'vue'
import { formRules } from './cpn/config'
import type { uploadFileItem } from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
import RichEditor from '@/components/RichEditor.vue'
import { uploadImg } from '@/services'
import type { InsertCommonParamsOfCompanyAndRegulation } from '@/services/publicity/companyShow/types'
import {
  getCompanyShowListItem,
  postInsertCompanyShowListItem,
  putEditorCompanyShowListItem,
} from '@/services/publicity/companyShow'
import { formatTimeStamp } from '@/utils/format'

const route = useRoute()
const router = useRouter()

const formDataReactive = reactive<InsertCommonParamsOfCompanyAndRegulation>({
  id: '',
  title: '',
  module: [
    {
      id: '',
      moduleName: '',
      content: '',
    },
  ],
  publishTime: '',
  topStatus: '0',
  // cover: { id: '', url: '' },
  appCover: { id: '', url: '' },
  // customCover: { id: '', url: '' },
  customAppCover: { id: '', url: '' },
  categoryId: '',
  location: '',
  fileList: [],
  activityMemberId: [],
  type: '0',
})

const formRef = ref<InstanceType<typeof NForm>>()
const queryDetail = (id: string) => {
  getCompanyShowListItem({ id, type: 0 }).then((res) => {
    formDataReactive.location = res.location
    formDataReactive.categoryId = res.categoryId
    formDataReactive.appCover = JSON.parse(JSON.stringify(res.appCover))
    // formDataReactive.cover = JSON.parse(JSON.stringify(res.cover || ''))
    formDataReactive.customAppCover = JSON.parse(JSON.stringify(res.appCover))
    // formDataReactive.customCover = JSON.parse(JSON.stringify(res.cover || ''))
    formDataReactive.topStatus = res.topStatus
    formDataReactive.publishTime = res.publishTime
    formDataReactive.activityMemberId = res.activityMemberId || []
    formDataReactive.fileList = res.fileList || []
    formDataReactive.module = res.module || []
    formDataReactive.title = res.title
    formDataReactive.id = res.id
  })
}

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // 新增或编辑最后确定发布时间
      formDataReactive.publishTime = formatTimeStamp(new Date().getTime())
      if (formDataReactive.id) {
        putEditorCompanyShowListItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            router.back()
          }
        })
      }
      else {
        postInsertCompanyShowListItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            router.back()
          }
        })
      }
    }
  })
}

// app封面图片上传
const handleAppCoverDone = async(file: File) => {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formDataReactive.appCover.id) {
      return
    }
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      formDataReactive.customAppCover!.id = data.fileId || ''
      formDataReactive.appCover.id = data.fileId || ''
      formDataReactive.appCover.url = ''
      // formRef.value?.validate()
    }
  }
  catch (error) {}
}
// app封面图片删除操作
const handleAppCoverDelete = () => {
  formDataReactive.customAppCover!.id = ''
  formDataReactive.appCover.id = ''
  formDataReactive.appCover.url = ''
}

// PC封面图片上传
// const handleCoverDone = async(file: File) => {
//   const imgFileData = new FormData()
//   imgFileData.append('file', file)
//   try {
//     const data: uploadFileItem = await uploadImg(imgFileData)
//     if (data) {
//       formDataReactive.customCover!.id = data.fileId || ''
//       formDataReactive.cover.id = data.fileId || ''
//       formDataReactive.cover.url = ''
//       // formRef.value?.validate()
//     }
//   } catch (error) {}
// }
// PC封面图片删除操作
// const handleCoverDelete = () => {
//   formDataReactive.customCover!.id = ''
//   formDataReactive.cover.id = ''
//   formDataReactive.cover.url = ''
// }

const addModule = () => {
  formDataReactive.module.push({ id: '', moduleName: '', content: '' })
}
const delModuleItem = (index: number) => {
  formDataReactive.module.splice(index, 1)
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

const goBack = () => {
  window.$dialog.warning({
    title: '提示',
    content: '当前存在未保存数据，确认关闭弹窗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      router.push({ name: 'companyIndex' })
    },
  })
}
onMounted(() => {
  const { id } = route.query
  if (id && id !== '-1') {
    queryDetail(id as string)
  }
})
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-card>
      <n-form-item label="">
        <div class="w-[100%] flex flex-row justify-between items-center">
          <NButton @click="goBack">
            返回
          </NButton>
          <NButton type="primary" @click="validateAndSave">
            发布
          </NButton>
        </div>
      </n-form-item>
      <div class="flex flex-row">
        <div class="flex-1 flex flex-col w-[100%] pl-[20px] pr-[20px]">
          <n-grid :cols="12" :x-gap="12">
            <n-form-item-gi label="公司名称" :span="12" path="title">
              <n-input
                v-model:value="formDataReactive.title"
                placeholder="请输入公司名称"
                maxlength="20"
                show-count
                clearable
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="是否置顶：" path="topStatus">
              <n-switch
                v-model:value="formDataReactive.topStatus"
                checked-value="1"
                unchecked-value="0"
              />
            </n-form-item-gi>
            <n-form-item-gi
              :span="12"
              label="APP封面图片："
              path="customAppCover.id"
            >
              <ImgUploader
                v-model:oldImgUrl="formDataReactive.customAppCover!.url"
                :width="375"
                :height="190"
                :need-cropper="false"
                @done="handleAppCoverDone"
                @delete="handleAppCoverDelete"
              />
            </n-form-item-gi>

            <!-- <n-form-item-gi
              :span="12"
              label="PC封面图片："
              path="customCover.id"
            >
              <ImgUploader
                :width="940"
                :height="220"
                :need-cropper="false"
                :old-img-url="formDataReactive.customCover!.url"
                @done="handleCoverDone"
                @delete="handleCoverDelete"
              />
            </n-form-item-gi> -->
            <n-form-item-gi
              v-for="(item, index) in formDataReactive.module"
              :key="index"
              :span="12"
              :label="'展示模块的标题'"
              path="module[0].moduleName"
            >
              <n-input
                v-model:value="item.moduleName"
                placeholder="请输入标题"
                clearable
              />
              <n-button v-show="index" @click="delModuleItem(index)">
                删除
              </n-button>
            </n-form-item-gi>
            <n-form-item-gi>
              <n-button @click="addModule">
                添加展示模块
              </n-button>
            </n-form-item-gi>
          </n-grid>
        </div>
        <div class="flex-1 flex flex-col w-[100%] pl-[20px] pr-[20px]">
          <n-scrollbar style="max-height: 700px">
            <n-grid :cols="12" :x-gap="12">
              <n-form-item-gi
                v-for="(item, index) in formDataReactive.module"
                :key="index"
                :label="`${item.moduleName}` + '介绍：'"
                :span="12"
                path="module[0].content"
              >
                <RichEditor
                  v-model:value="item.content"
                  style="width: 100%"
                  :rich-height="350"
                />
              </n-form-item-gi>
            </n-grid>
          </n-scrollbar>
        </div>
      </div>
    </n-card>
  </n-form>
</template>
<style lang="scss" scoped></style>
