import type {
  ActiveDemeanorDetail,
  ActiveDemeanorItem,
  BranchDetail,
  BranchGardenDetailItem,
  BranchGardenTableItem,
  NoticeDetail,
  NoticeItem,
} from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 获取支部园地表格列表 */
export function getBranchGardenTableList(
  params: { content: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<BranchGardenTableItem>>({
    url: '/org-construction/backend/v1/branch',
    params,
  })
}

/** 获取支部园地详情 */
export function getBranchGardenDetail(id: string) {
  return commonReq.get<BranchGardenDetailItem>({
    url: `/org-construction/backend/v1/branch/${id}`,
  })
}

/** 获取支部详情 -- 变更后 */
export function getBranchDetail(id: string) {
  return commonReq.get<BranchDetail>({
    url: `/org-construction/backend/v1/branch/${id}`,
  })
}

/** 添加支部园地 -- 变更后 */
export function postBranch(data: BranchDetail) {
  return commonReq.post({
    url: '/org-construction/backend/v1/branch',
    data,
  })
}

/** 编辑支部园地 -- 变更后 */
export function putBranch(data: BranchDetail) {
  return commonReq.put({
    url: '/org-construction/backend/v1/branch',
    data,
  })
}

/** 添加支部园地 */
export function postBranchGardenItem(data: BranchGardenDetailItem) {
  return commonReq.post({
    url: '/org-construction/backend/v1/branch',
    data,
  })
}

/** 编辑支部园地 */
export function putBranchGardenItem(data: BranchGardenDetailItem) {
  return commonReq.put({
    url: '/org-construction/backend/v1/branch',
    data,
  })
}

/** 删除支部园地 */
export function deleteBranchGardenItem(ids: string) {
  return commonReq.delete({
    url: '/org-construction/backend/v1/branch',
    params: { ids },
  })
}

/** 查询公示公告列表 */
export function getNoticeTableList(params: { title: string } & PaginationReq) {
  return commonReq.get<PaginationRes<NoticeItem>>({
    url: '/org-construction/backend/v1/branch-publicity',
    params,
  })
}

/** 查询公示公告详情 */
export function getNoticeDetail(id: string) {
  return commonReq.get<NoticeDetail>({
    url: `/org-construction/backend/v1/branch-publicity/${id}`,
  })
}

/** 新增公示公告 */
export function postNotice(data: NoticeDetail) {
  return commonReq.post({
    url: '/org-construction/backend/v1/branch-publicity',
    data,
  })
}

/** 编辑公示公告 */
export function putNotice(data: NoticeDetail) {
  return commonReq.put({
    url: '/org-construction/backend/v1/branch-publicity',
    data,
  })
}

/** 删除公示公告 */
export function deleteNotice(ids: string) {
  return commonReq.delete({
    url: '/org-construction/backend/v1/branch-publicity',
    params: { ids },
  })
}

/** 置顶 / 取消置顶 */
export function putNoticeIsTop(id: string) {
  return commonReq.put({
    url: `/org-construction/backend/v1/branch-publicity/top/${id}`,
  })
}

/** 查询活动风采列表 */
export function getActivityTableList(
  params: { title: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<ActiveDemeanorItem>>({
    url: '/org-construction/backend/v1/branch-activity',
    params,
  })
}

/** 查询活动风采详情 */
export function getActivityDetail(id: string) {
  return commonReq.get<ActiveDemeanorDetail>({
    url: `/org-construction/backend/v1/branch-activity/${id}`,
  })
}

/** 新增活动风采 */
export function postActivity(data: ActiveDemeanorDetail) {
  return commonReq.post({
    url: '/org-construction/backend/v1/branch-activity',
    data,
  })
}

/** 编辑活动风采 */
export function putActivity(data: ActiveDemeanorDetail) {
  return commonReq.put({
    url: '/org-construction/backend/v1/branch-activity',
    data,
  })
}

/** 删除活动风采 */
export function deleteActivity(ids: string) {
  return commonReq.delete({
    url: '/org-construction/backend/v1/branch-activity',
    params: { ids },
  })
}
