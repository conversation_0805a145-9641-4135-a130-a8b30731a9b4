<script setup lang="ts">
import { defineProps } from 'vue'
const props = defineProps<{
  title: string
}>()
</script>
<template>
  <div class="pl-[100px] pr-[100px] box-border">
    <div class="flex flex-row mt-[50px] justify-start items-center">
      <span class="text-[14px] font-bold leading-[14px]">{{
        props.title
      }}</span>
      <span class="line" />
      &emsp;<slot name="btn" />
    </div>
    <slot />
  </div>
</template>
<style lang="scss" scoped>
.line {
  margin-left: 20px;
  width: 80%;
  display: inline-block;
  border-top: 0.5px solid #ccc;
  font-weight: 100;
}
</style>
