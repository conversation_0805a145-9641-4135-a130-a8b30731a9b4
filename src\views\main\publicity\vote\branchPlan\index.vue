<template>
  <div class="outer-container">
    <div v-if="!templateVisible">
      <div
        v-for="item in templateList"
        :key="item.name"
        class="card"
        @click="linkToDetail(item.id)"
      >
        <n-icon>
          <AddCircleOutlineFilled class="add-icon" />
        </n-icon>

        <div class="title ml-[130px]">
          {{ item.name }}
        </div>
      </div>
    </div>

    <div v-else>
      <n-grid :cols="24">
        <n-gi :span="14">
          <div class="title">
            基本信息
          </div>
          <div class="mt-[20px]">
            <n-form
              ref="formRef"
              :model="baseInfo"
              :rules="baseInfoRules"
              label-placement="left"
              label-width="auto"
              require-mark-placement="left"
            >
              <n-form-item label="评议项名称" path="name">
                <n-input
                  v-model:value="baseInfo.name"
                  placeholder="请输入评议项名称"
                />
              </n-form-item>
              <n-form-item label="评议项简介" path="content">
                <n-input
                  v-model:value="baseInfo.content"
                  placeholder="请输入评议项说明"
                />
              </n-form-item>
            </n-form>
          </div>

          <div class="title">
            考核项
          </div>
          <div class="setting-btn">
            <div v-if="commonEnable">
              已启用
            </div>
            <div v-else>
              未启用
            </div>
            <n-button size="small" type="tertiary" @click="handleSetting">
              前往模板配置
            </n-button>
          </div>

          <TransitionGroup tag="ul" name="fade">
            <div
              v-for="(item, index) in assessmentItems"
              :key="item.id"
              :class="['assessment-item', { active: editItemIndex === index }]"
              @click="handleItem(item, index)"
            >
              <div class="operate-btns">
                <n-button
                  text
                  :disabled="index === 0"
                  @click="swapItem(index, index - 1)"
                >
                  <n-icon size="14">
                    <ArrowUp />
                  </n-icon>
                </n-button>
                <n-button
                  text
                  :disabled="index === assessmentItems.length - 1"
                  @click="swapItem(index, index + 1)"
                >
                  <n-icon size="14">
                    <ArrowDown />
                  </n-icon>
                </n-button>
                <n-button text @click="(e) => onDelete(e, index)">
                  <n-icon size="14">
                    <Delete />
                  </n-icon>
                </n-button>
                <n-button text @click="onCopy(index)">
                  <n-icon size="14">
                    <Copy />
                  </n-icon>
                </n-button>
              </div>
              <div>
                {{ item.id }}、{{ item.name ? item.name : '请输入考核项名称' }}
              </div>
              <div
                class="text-12px text-[#999] mt-[20px] rich-container"
                v-html="item.content"
              />
            </div>
          </TransitionGroup>

          <div class="w-full mt-30px add-btn" @click="addAssessment">
            <n-icon size="18">
              <Add />
            </n-icon>
            新增
          </div>

          <div class="mt-[20px] font-bold">
            上限总分：{{ totalTopScore }}
          </div>

          <div class="mt-[20px]">
            <button class="save-btn" @click="handleSave">
              保存
            </button>
            <button class="cancel-btn">
              取消
            </button>
          </div>
        </n-gi>

        <n-gi
          v-if="editItemIndex !== '' && assessmentItems.length !== 0"
          :span="6"
          :offset="2"
        >
          <n-form
            ref="editRef"
            :model="editItem"
            :rules="editItemRules"
            label-placement="left"
            label-width="auto"
            require-mark-placement="left"
            class="mt-[40px]"
          >
            <n-form-item label="考核项名称" path="name">
              <n-input
                v-model:value="editItem.name"
                :oninput="handleNameChange"
                placeholder="请输入考核项名称"
              />
            </n-form-item>
            <n-form-item label="分值计算方式" path="method">
              <n-select
                v-model:value="editItem.method"
                :options="[
                  {
                    label: '加分项(考核项分值上限为正整数)',
                    value: '0',
                  },
                  {
                    label: '减分项(考核项分值上限为负整数)',
                    value: '1',
                  },
                ]"
                :on-update:value="handleMethodChange"
              />
            </n-form-item>
            <n-form-item label="是否支持排名" path="isRank">
              <n-select
                v-model:value="editItem.isRank"
                :options="[
                  {
                    label: '是',
                    value: '0',
                  },
                  {
                    label: '否',
                    value: '1',
                  },
                ]"
                :on-update:value="handleRankChange"
              />
            </n-form-item>
            <n-form-item label="考核项分值上限" path="top">
              <n-input
                v-model:value="editItem.top"
                placeholder="请输入考核项分值上限"
              />
            </n-form-item>
            <n-form-item label="考核项内容" path="content">
              <RichEditor
                v-model:value="editItem.content"
                style="width: 100%"
                :rich-height="350"
              />
            </n-form-item>
            <n-form-item label="其他项" path="other" />
          </n-form>
          <div class="mt-[-64px]">
            <div
              v-for="(item, index) in editItem.other"
              :key="index"
              class="other-container"
            >
              <n-input
                v-model:value="item.name"
                class="other-option"
                placeholder="请输入选项内容"
                :disabled="commonEnable"
              />
              <n-icon
                v-if="!commonEnable"
                size="14"
                class="item-del"
                @click="handleDelOther(index)"
              >
                <Delete />
              </n-icon>
            </div>
          </div>
          <div v-if="!commonEnable" class="mt-30px add-other" @click="addOther">
            添加其他项
          </div>
        </n-gi>
      </n-grid>
    </div>

    <custom-dialog
      v-model:show="settingVisible"
      :show-action="false"
      title="模板设置"
    >
      <div>
        <div class="ml-[30px] mb-[10px] mt-[10px]">
          <span class="red-span">*</span>
          是否启用通用模板:
          <n-switch v-model:value="commonEnable" class="ml-[36px]">
            <template #checked>
              是
            </template>
            <template #unchecked>
              否
            </template>
          </n-switch>
          <div class="mt-[20px]">
            启用通用模板，所有单选题的选项将固定使用通用模板选项。
            <br>
            且详情页在开启通用模板时候才能够按照选项统计。
          </div>
        </div>
        <div class="setting-title">
          其他考核项:
        </div>
        <n-data-table
          :columns="columns"
          :data="settingData"
          :pagination="false"
        />
        <n-icon>
          <AddFilled
            class="add-icon ml-[36px] text-[15px] mt-[10px]"
            @click="addSetting"
          />
        </n-icon>
      </div>
      <div class="setting-submit">
        <n-button type="primary" @click="handleSettingSubmit">
          提交
        </n-button>
      </div>
    </custom-dialog>
  </div>
</template>

<script setup>
import { AddCircleOutlineFilled, AddFilled } from '@vicons/material'
import { Add, ArrowDown, ArrowUp, Copy, Delete } from '@vicons/carbon'
import { TransitionGroup } from 'vue'
import { cloneDeep } from 'lodash-es'
import { NButton, NInput, NSelect, useMessage } from 'naive-ui'

import RichEditor from '@/components/RichEditor.vue'
const router = useRouter()
const templateVisible = ref(true)
// 基本信息
const formRef = ref(null)
// 考核项编辑
const editRef = ref(null)
// 基本信息内容
const baseInfo = ref({
  name: '',
  content: '',
})
// 正在编辑的考核项信息
const editItem = ref({
  name: null,
  content: null,
  method: null,
  isRank: null,
  top: null,
  other: [
    { name: '总体评价内容', path: 'other1' },
    { name: '存在不足与问题', path: 'other2' },
  ],
})
const commonEnable = ref(true)

const message = useMessage()
const settingData = ref([
  {
    key: 0,
    name: '总体评价内容',
    age: '32',
    address: '',
  },
  {
    key: 1,
    name: '存在不足与问题',
    age: '42',
    address: '',
  },
])

const createColumns = () => [
  {
    title: '考核项名称',
    key: 'name',
    render(row, index) {
      return h(NInput, {
        value: row.name,
        onUpdateValue(v) {
          settingData.value[index].name = v
        },
      })
    },
  },
  {
    title: '字数上限',
    key: 'age',
    render(row, index) {
      return h(NInput, {
        value: row.age,
        style: {
          width: '60px',
        },
        onUpdateValue(v) {
          const intPattern = /^-?\d+$/
          if (!intPattern.test(v)) {
            message.warning('请输入整数')
          }
          settingData.value[index].age = v
        },
      })
    },
  },
  {
    title: '是否必填',
    key: 'address',
    render(row, index) {
      return h(NSelect, {
        value: row.address,
        style: {
          width: '100px',
        },
        options: [
          { key: 0, value: 0, label: '是' },
          { key: 1, value: 1, label: '否' },
        ],
        onUpdateValue(v) {
          settingData.value[index].address = v
        },
      })
    },
  },
  {
    title: '操作',
    key: 'address',
    render(row, index) {
      return h(
        NButton,
        {
          size: 'small',
          onClick: () => handleDelSetting(index),
        },
        { default: () => '删除' },
      )
    },
  },
]
const columns = createColumns()
const templateList = ref([{ id: 1, name: '党支部星级考核模板一' }])

// 正在编辑的考核项索引
const editItemIndex = ref('')

// 模板详情
const settingVisible = ref(false)

// 基本信息规则
const baseInfoRules = ref({
  name: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入评议项名称',
  },
  content: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入评议项说明',
    tool: '请输入内容',
  },
})

// 编辑项rules
const editItemRules = ref({
  name: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入考核项名称',
  },
  method: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择分值计算方式',
  },
  isRank: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择是否支持排名',
  },
  top: {
    required: true,
    trigger: ['blur', 'input'],
    validator: checkInterger,
    message: '请输入考核项分值上限(整数)',
  },
  content: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入考核项内容',
  },
})

// 考核项数组
const assessmentItems = ref([
  {
    id: 1,
    name: '',
    content: '请输入考核项内容',
    method: '',
    isRank: null,
    top: null,
    enable: false,
    other: [
      { name: '总体评价内容', path: 'other1' },
      { name: '存在不足与问题', path: 'other2' },
    ],
  },
])

// 上限总分计算
const totalTopScore = computed(() => {
  let target = ''
  assessmentItems.value.forEach((element) => {
    if (isNaN(element.top)) {
      return
    }
    target += Number(element.top)
  })
  return target
})

// 展示模板详情
function linkToDetail() {
  templateVisible.value = true
}

// 操作公共项
function handleCommonEnable(value) {
  // 隐藏
}

// 点击单个考核项
function handleItem(value, index) {
  if (value) {
    editItemIndex.value = index
    editItem.value = assessmentItems.value[index]
  } else {
    editItemIndex.value = ''
    editItem.value = {
      name: null,
      content: null,
      method: null,
      isRank: null,
      top: null,
      other: [
        { name: '总体评价内容', path: 'other1' },
        { name: '存在不足与问题', path: 'other2' },
      ],
    }
  }
}

// 增加考核项
function addAssessment() {
  const length = assessmentItems.value.length
  const data = {
    id: 1,
    name: '',
    content: '请输入评议项说明',
    method: '',
    isRank: null,
    top: null,
    other: [
      { name: '总体评价内容', path: 'other1' },
      { name: '存在不足与问题', path: 'other2' },
    ],
  }
  const lastItem = assessmentItems.value[length - 1]
  if (length !== 0) {
    data.id = lastItem.id + 1
  }
  if (commonEnable.value) {
    data.other = []
    settingData.value.forEach((item) => {
      data.other.push({
        name: item.name,
      })
    })
  }
  assessmentItems.value.push(data)
}

// 考核项名称赋值
function handleNameChange(e) {
  if (editItemIndex.value === '') {
    return
  }
  assessmentItems.value[editItemIndex.value].name = e.target.value
}

// 分值计算方式赋值
function handleMethodChange(value) {
  if (editItemIndex.value === '') {
    return
  }
  assessmentItems.value[editItemIndex.value].method = value
}

function handleDelOther(i) {
  editItem.value.other.splice(i, 1)
}

// 是否支持排名赋值
function handleRankChange(value) {
  if (editItemIndex.value === '') {
    return
  }
  assessmentItems.value[editItemIndex.value].isRank = value
}

/** 交换 */
function swapItem(a, b) {
  const temp = assessmentItems.value[a]
  assessmentItems.value[a] = assessmentItems.value[b]
  assessmentItems.value[b] = temp
}

// 删除考核项
function onDelete(e, index) {
  e.stopPropagation()
  assessmentItems.value.splice(index, 1)
  if (editItemIndex.value === index) {
    editItemIndex.value = ''
  }
}

// 赋值考核项
function onCopy(index) {
  let target = assessmentItems.value[index]
  target = cloneDeep(target)
  const length = assessmentItems.value.length
  target.id = length + 1
  assessmentItems.value.push(target)
}

// 判断是否为整数
function checkInterger(rule, value) {
  const intPattern = /^-?\d+$/
  return intPattern.test(value)
}

// 增加其他项
function addOther() {
  if (editItemIndex.value === '') {
    return
  }

  editItem.value.other.push({
    name: '',
    path: '',
  })
}

// 保存数据
function handleSave() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      // message.success('验证成功')
    } else {
      // message.error('验证失败')
    }
  })
}

function handleSetting() {
  settingVisible.value = true
}

function handleDelSetting(index) {
  settingData.value.splice(index, 1)
}

function handleSettingSubmit() {
  settingVisible.value = false
  if (commonEnable.value) {
    editItem.value.other = []
    settingData.value.forEach((data) => {
      editItem.value.other.push({
        name: data.name,
      })
    })
  }
}

function addSetting() {
  settingData.value.push({
    key: 0,
    name: '',
    age: '',
    address: '',
  })
}
</script>

<style lang="scss" scoped>
.outer-container {
  padding: 22px;

  .add-icon {
    font-size: 30px;
    margin-left: 100px;
    margin-top: -8px;
    cursor: pointer;
  }

  .card {
    height: 80px;
    background: rgb(242, 246, 252);
    display: flex;
    align-items: center;
    color: rgb(0, 144, 214);
    margin-bottom: 20px;
  }

  .title {
    font-size: 14px;
    font-weight: bold;
  }

  .assessment-item {
    padding: 17px 24px 24px;
    margin-bottom: 20px;
    transition: all 250ms;
    position: relative;
    border-radius: 3px;
    cursor: pointer;

    &:hover {
      box-shadow: 0 0 5px 0px rgba(0, 0, 0, 0.2) inset;
    }

    &.active {
      box-shadow: 0px 0px 10px 0px rgba(66, 133, 247, 0.5);

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 2px;
        background: rgb(66, 133, 247);
        border-radius: 3px;
      }
    }
  }

  .add-btn {
    width: 100%;
    height: 34px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #4285f7;
    border: 1px dashed #4285f7;
    cursor: pointer;
  }

  .save-btn {
    width: 52px;
    height: 34px;
    background: rgb(77, 122, 240) !important;
    color: #fff !important;
    border-radius: 3px;
    box-sizing: border-box !important;
    margin-right: 15px;
    font-size: 12px;
    color: #fff;

    &:hover {
      background: #338cff !important;
    }
  }

  .cancel-btn {
    width: 52px;
    height: 34px;
    border: 1px solid rgb(224, 224, 230);
    border-radius: 3px;
    font-size: 12px;
    color: #000;
    /*指定过渡的属性*/
    transition-property: 'border,color';
    /*设置过渡时间为0.5秒*/
    transition-duration: 0.5s;

    &:hover {
      color: #338cff;
      border: 1px solid #338cff !important;
    }
  }

  .operate-btns {
    position: absolute;
    top: 16px;
    right: 19px;
    width: 110px;
    height: 24px;
    background: #f6f7f8;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }

  .add-other {
    width: 305px;
    height: 34px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #4285f7;
    border: 1px dashed #4285f7;
    cursor: pointer;
    margin-left: auto;
  }

  .other-option {
    width: 305px;
    margin-left: auto;
    margin-bottom: 20px;
    display: block;
  }

  .setting-btn {
    width: 160px;
    height: 24px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    margin-top: 20px;
    margin-bottom: 20px;
  }
}

.setting-submit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.setting-title {
  margin-left: 36px;
  margin-bottom: 20px;
}

.item-del {
  position: absolute;
  right: -20px;
  top: 10px;
  margin-left: -110px;
}

.other-container {
  // display: flex;
  padding-left: 0;
  position: relative;
}

:deep(.n-form-item-label__text) {
  font-size: 13px;
}

:deep(.n-data-table) {
  width: 530px !important;
  margin: auto;
}

.red-span {
  color: red;
}

/* 1. 声明过渡效果 */
.fade-move,
.fade-enter-active,
.fade-leave-active {
  transition: all 300ms cubic-bezier(0.55, 0, 0.1, 1);
}

/* 2. 声明进入和离开的状态 */
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scaleY(0.01);
}

.fade-leave-to {
  display: none;
}

/* 3. 确保离开的项目被移除出了布局流
      以便正确地计算移动时的动画效果。 */
.fade-leave-active {
  position: absolute;
  left: 0;
  right: 0;
}
</style>

<style lang="scss">
.rich-container {
  table {
    border-top: 1px solid #ccc !important;
    border-left: 1px solid #ccc !important;
  }

  table td,
  table th {
    border-bottom: 1px solid #ccc !important;
    border-right: 1px solid #ccc !important;
    padding: 3px 5px;
  }

  table th {
    border-bottom: 2px solid #ccc !important;
    text-align: center !important;
  }
}
</style>
