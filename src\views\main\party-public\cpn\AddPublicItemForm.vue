<script setup lang="ts">
import { NForm } from 'naive-ui'
import { formRules } from './config'
import type {
  AddOrEditPublicItem,
  uploadFileItem,
  // uploadFileItemNew,
} from '@/services/party-public/types'
import RichEditor from '@/components/RichEditor.vue'
import { getPublicDetail, postPublic, putPublic } from '@/services/party-public'
import { uploadImg } from '@/services'

interface Props {
  type?: string
  id?: string
  categoryId: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<AddOrEditPublicItem>({
  id: '',
  categoryId: props.categoryId,
  annexName: '',
  annexDescribe: '',
  annexUrl: '',
  fileList: [],
  pictureUrl: '',
})

const formRef = ref<InstanceType<typeof NForm>>()
const fileIdObj = reactive<{ fileIdsArr: Array<string> }>({ fileIdsArr: [] })
const oldImgUrlRef = ref('')
const oldImgUrlFirstFlag = ref(true)
onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getPublicDetail(props.id).then((res) => {
      formDataReactive.categoryId = res.categoryId
      formDataReactive.annexName = res.annexName
      formDataReactive.annexDescribe = res.annexDescribe
      formDataReactive.annexUrl = res.annexUrl
      formDataReactive.fileList = res.fileList || []
      formDataReactive.id = props.id
      oldImgUrlRef.value = res.pictureUrl
      oldImgUrlFirstFlag.value = false

      // 从详情接口取出的附件需要设置它的百分比为100
      if (res.fileList.length) {
        formDataReactive.fileList.forEach((item) => {
          item.percentage = 100
          item.url = item.fileName
          item.name = item.original
        })
      }
    })
  }
})

// 图片裁剪完毕
const handleCoverDone = async(file: File) => {
  if (
    (!oldImgUrlFirstFlag.value && props.type === 'modify')
    || props.type === 'add'
  ) {
    const imgFileData = new FormData()
    imgFileData.append('file', file)
    try {
      if (formDataReactive.pictureUrl) {
        return
      }
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formDataReactive.pictureUrl = data.url || ''
      }
    }
    catch (error) {}
  }
  oldImgUrlFirstFlag.value = false
}

// PC封面图片删除操作
const handleCoverDelete = () => {
  formDataReactive.pictureUrl = ''
}
// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // 保存和编辑时 需要将文件的id拼接起来
      formDataReactive.annexUrl = formDataReactive.fileList.length
        ? formDataReactive.fileList.map(file => file.id).join(',')
        : ''
      if (formDataReactive.id) {
        putPublic(formDataReactive).then((res) => {
          if (res) {
            fileIdObj.fileIdsArr = []
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
      else {
        postPublic(formDataReactive).then((res) => {
          if (res) {
            fileIdObj.fileIdsArr = []
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

async function handleUpload(
  file: File,
  options: any,
  callBack: (data: any) => void,
) {
  const fileData = new FormData()
  const fileItem = options.file.file as Blob
  fileData.append('file', fileItem)
  const data: uploadFileItem = await uploadImg(fileData)
  callBack(data)
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="90"
    label-align="right"
    label-placement="left"
    :disabled="props.type === 'view'"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item label="附件名称：" path="annexName">
      <n-input
        v-model:value="formDataReactive.annexName"
        placeholder="请输入附件名称"
        clearable
      />
    </n-form-item>

    <n-form-item span="24" label="附件描述：" path="annexDescribe">
      <RichEditor
        v-model:value="formDataReactive.annexDescribe"
        :disabled="props.type === 'view'"
        style="width: 100%"
        :rich-height="350"
      />
    </n-form-item>
    <n-form-item span="24" label="图片：" path="pictureUrl">
      <ImgUploader
        v-model:oldImgUrl="oldImgUrlRef"
        :width="380"
        :height="200"
        :need-cropper="false"
        :is-readonly="props.type === 'view'"
        @done="handleCoverDone"
        @delete="handleCoverDelete"
      />
    </n-form-item>
    <n-form-item span="24" label="附件：">
      <file-uploader-new
        v-model:original-file-list="formDataReactive.fileList"
        need-progress
        :max="1"
        accept=".pdf"
        :is-readonly="props.type === 'view'"
        :size-limit="200"
        :upload-method="handleUpload"
      >
        <template #tips>
          <span class="tips">
            可上传1个文件，仅支持文件扩展名：.pdf，大小200M以内
          </span>
        </template>
      </file-uploader-new>
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
