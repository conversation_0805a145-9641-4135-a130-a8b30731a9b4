/**
 * @description: 查询年度工作总结详情参数类型
 * */
export interface QueryAnnualSummaryDetailType {
  /**
   * 组织id
   */
  deptId: string
  /**
   * 年份
   */
  year: string
}

/**
 * @description: 查询年度工作总结详情返回类型
 * */
export interface QueryAnnualSummaryDetailResponseType {
  /**
   * 正文内容
   */
  bookText?: string
  /**
   * 0-正常，1-删除
   */
  delFlag?: string
  /**
   * 支部id
   */
  deptId?: number
  /**
   * 主键id
   */
  id?: number
  /**
   * 年份
   */
  year?: string
}

/**
 * @description: 修改年度工作总结参数类型
 * */
export interface UpdateAnnualSummaryType {
  /**
   * 正文内容
   */
  bookText?: string
  /**
   * 0-正常，1-删除
   */
  delFlag?: string
  /**
   * 支部id
   */
  deptId?: number | string
  /**
   * 主键id
   */
  id: number | string
  /**
   * 年份
   */
  year?: string
}

export type AddAnnualSummaryType = Omit<UpdateAnnualSummaryType, 'id'>
