<script setup lang="ts">
import { useDialog, useLoadingBar, useMessage } from 'naive-ui'
import { useSystemStore } from './store/system'

// 将全局api挂载到window
window.$message = useMessage()
window.$dialog = useDialog()
window.$loadingBar = useLoadingBar()

const { getPreviewHostData } = useSystemStore()
if (getPreviewHostData()) {
  window.$previewHost = getPreviewHostData()
}

useTitle(import.meta.env.VITE_APP_NAME)
useFavicon(import.meta.env.VITE_APP_LOGO)
</script>

<template>
  <router-view />
</template>

<style lang="scss" scoped></style>
