import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { FunctionSettingFormParams } from '@/services/organize-garden/types'

export function getTableColumns(
  handleDrawStar: (row: FunctionSettingFormParams) => VNodeChild,
): DataTableColumns<FunctionSettingFormParams> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'functionName',
      title: '功能名称',
    },
    {
      key: 'isShow',
      title: '是否展示',
      render: row => handleDrawStar(row),
    },
  ]
}
