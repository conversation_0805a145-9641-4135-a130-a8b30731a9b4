<script setup lang="ts">
import { NButton } from 'naive-ui'
import { getTableColumns } from './config'
import { useMyTable } from '@/hooks'
import { deletePartyBuildList } from '@/services/affairs/discipline-inspection-list'
import { getPartyBuildingEvaluationTableListForBranch } from '@/services/publicity/vote/evaluationForBranch'
const router = useRouter()
// 年份过滤
const filterRef = ref({
  year: null,
})

// 有接口后添加：loading,tableData
const {
  loading,
  tableData,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(getPartyBuildingEvaluationTableListForBranch, filterRef, {
  batchDeleteTable: true,
  delApi: deletePartyBuildList,
})

watch(
  () => filterRef.value.year,
  () => {
    loadData()
  },
)

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    row.status !== '已完成'
      ? h(
        NButton,
        {
          onClick: () => {
            router.push({
              name: 'partyBuildingEvaluationTargetBranch',
              query: {
                partyListId: row.id,
                title: row.title,
                status: row.status,
              },
            })
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '指标',
        },
      )
      : h(
        NButton,
        {
          onClick: () => {
            router.push({
              name: 'partyBuildingEvaluationTargetBranch',
              query: {
                partyListId: row.id,
                title: row.title,
                status: row.status,
              },
            })
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '查看得分',
        },
      ),
  ]
})
onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :loading="loading"
    title="党建考核（支部）"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
  >
    <template #btns>
      <div />
    </template>
    <template #filters>
      <n-date-picker
        v-model:formatted-value="filterRef.year"
        style="width: 100%"
        placeholder="请选择考核年度"
        clearable
        type="year"
        @update:formatted-value="
          (v:any) => (filterRef.year = v)
        "
      />
    </template>
  </table-container>
</template>
<style lang="scss" scoped>
:deep(.n-progress.n-progress--line .n-progress-icon.n-progress-icon--as-text) {
  width: 46px;
}
</style>
