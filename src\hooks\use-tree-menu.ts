/**
 * @description 树形菜单
 */

type ShowModalType = 'root' | 'sub' | 'check'
export function useTreeMenu(options: {
  menuListApi?: (params: any) => Promise<any>
  moveNodeApi?: (params: any) => Promise<any>
  moveChildNodeApi?: (params: any) => Promise<any>
  delNodeApi?: (params: any) => Promise<any>
  delChildNodeApi?: (params: any) => Promise<any>
  addNodeApi?: (params: any) => Promise<any>
  modifyNodeApi?: (params: any) => Promise<any>
  addChildNodeApi?: (params: any) => Promise<any>
  modifyChildNodeApi?: (params: any) => Promise<any>
  refreshTableApi?: (params: any) => void
  customPackDataFunc?: (params: any) => void
  checkDeleteApiFunc?: (params: any) => Promise<any>
  multiLevelKey?: string
  labelField?: string
  childLabelField?: string
  maxLevel?: number
  sessionId?: string
  sessionName?: string
  sessionData?: string
}) {
  const menuList = ref<any>([]) // 菜单列表
  const showModalType = ref<ShowModalType>('root') // root | sub 添加主类和副类区分开
  const defaultCategoryID = ref('')
  const defaultCategoryName = ref('')
  const defaultCategoryData = ref()
  const commonSessionId = window.sessionStorage.getItem(
    String(options.sessionId),
  )
  const commonSessionName = window.sessionStorage.getItem(
    String(options.sessionName),
  )
  const commonSessionData = JSON.parse(
    window.sessionStorage.getItem(String(options.sessionData)) as string,
  )

  // 初始化时调用菜单列表
  async function init() {
    const res = await options.menuListApi?.({})
    menuList.value = res
    if (options.maxLevel) {
      const data = getDefaultCategoryId(menuList.value)
      defaultCategoryID.value = commonSessionId || data.id
      defaultCategoryName.value = commonSessionName || data.name
      defaultCategoryData.value = commonSessionData || data.data
    }
    else {
      defaultCategoryID.value = commonSessionId || menuList.value[0]?.id
      defaultCategoryName.value
        = commonSessionName || menuList.value[0]?.[options.labelField as string]
      defaultCategoryData.value = commonSessionData || menuList.value[0]
    }

    return {
      id: defaultCategoryID.value,
      name: defaultCategoryName.value,
      data: defaultCategoryData.value,
    }
  }

  // 取出第一个有资讯的
  function getDefaultCategoryId(arr: any[]) {
    let data = { id: '', name: '', data: undefined }
    for (let i = 0; i < arr.length; i++) {
      if (
        Array.isArray(arr[i][options.multiLevelKey as string])
        && arr[i][options.multiLevelKey as string].length
      ) {
        data = {
          id: arr[i][options.multiLevelKey as string][0].id,
          name: arr[i][options.multiLevelKey as string][0][
            options.childLabelField as string
          ],
          data: arr[i][options.multiLevelKey as string][0],
        }
        break
      }
    }

    return data
  }

  // 生成uuid
  function uuid() {
    const s: string[] = []
    const hexDigits = '0123456789abcdef'
    for (let i = 0; i < 36; i++) {
      s[i] = hexDigits.substring(Math.floor(Math.random() * 0x10), 1)
    }
    s[14] = '4'
    s[19] = hexDigits.substring((Number(s[19]) & 0x3) | 0x8, 1)
    s[8] = s[13] = s[18] = s[23] = '-'

    return s.join('')
  }

  // const treeData = computed(() => {
  //   if (options.maxLevel) {
  //     if (options.multiLevelKey) {
  //       return menuList.value && menuList.value.length
  //         ? calcLevelData(menuList.value, true)
  //         : []
  //     } else {
  //       return calcLevelData(menuList.value, true)
  //     }
  //   } else {
  //     return calcLevelData(menuList.value, true)
  //   }
  // })
  const treeData = computed(() => {
    const res = menuList.value?.length
      ? calcLevelData(menuList.value, true)
      : []
    if (options.customPackDataFunc) {
      options.customPackDataFunc(res)
    }
    return res
  })

  // function calcNeedChildBtn(flag: boolean) {
  //   if (options.maxLevel && options.maxLevel >= 2) {
  //     // 树形结构有多层的情况需要有添加子集的按钮
  //     return flag
  //   }
  //   else {
  //     // 树形结构只有一层的情况不需要有添加子集的按钮 true 代表按钮disabled的值
  //     return true
  //   }
  // }

  function calcIsShowChildBtn(level: number) {
    // 返回的是`添加子级` 的disabled属性的值
    return !(options.maxLevel && level < options.maxLevel)
  }

  // 递归实现多层级自动组装数据
  function calcLevelData(
    arr: any[],
    currentParentLevel: boolean,
    currentParentID?: string,
    currentParentName?: string,
    currentLevel = 1,
  ): any[] {
    return arr.map((item: any, index: number) => {
      return {
        ...item,
        id: uuid(),
        originData: { ...item },
        isChild: !currentParentLevel,
        clickExpand: !!(
          item[options.multiLevelKey as string]
          && item[options.multiLevelKey as string].length
        ),
        // needAddChildBtn: calcNeedChildBtn(!currentParentLevel),
        needAddChildBtn: calcIsShowChildBtn(currentLevel),
        currentLevel,
        parentID: currentParentID || undefined,
        parentName: currentParentName || '',
        key: uuid(),
        label: currentParentLevel
          ? item[options.labelField as string]
          : item[options.childLabelField as string],
        editing: false,
        canUp: index !== 0,
        canDown: index !== arr.length - 1,
        children:
          item[options.multiLevelKey as string]
          && item[options.multiLevelKey as string].length
            ? calcLevelData(
              item[options.multiLevelKey as string],
              false,
              item.id,
              item[options.labelField as string],
              currentLevel + 1,
            )
            : undefined,
      }
    })
  }

  // 移动节点操作
  async function moveNode(data: any) {
    const moveNum = data.type === 'up' ? '-1' : '+1'
    try {
      if (data.isChild) {
        await options.moveChildNodeApi?.({
          id: data.id,
          move: moveNum,
          parentId: data.parentID,
        })
      }
      else {
        await options.moveNodeApi?.({
          id: data.id,
          move: moveNum,
          parentId: '0',
        })
      }
      window.$message.success('操作成功')
      await init()
    }
    catch (error) {
      // 在这里处理可能发生的错误
      console.error('发生错误:', error)
    }
  }

  async function handleDelResponse() {
    try {
      if (options.sessionId && options.sessionName) {
        window.sessionStorage.removeItem(String(options.sessionId))
        window.sessionStorage.removeItem(String(options.sessionName))
      }
      if (options.sessionData) {
        window.sessionStorage.removeItem(String(options.sessionData))
      }
      const result = await init()
      options.refreshTableApi?.(result)
      window.$message.success('操作成功')
    }
    catch (err) {
      console.error(err) // Handle error here
    }
  }
  /** 删除节点 */
  async function delNode(data: any) {
    try {
      // 是否需要校验删除的权限（智云课堂的一二分类删除时需要校验）
      if (options.checkDeleteApiFunc) {
        const response = await options.checkDeleteApiFunc?.(data.id)
        if (!response) {
          window.$message.error('该分类下存在有效数据，无法删除')
          return
        }
      }
      if (data.isChild) {
        await options.delChildNodeApi?.(data.id) // 删除子节点
      }
      else {
        await options.delNodeApi?.(data.id) // 删除主节点
      }
      await handleDelResponse()
    }
    catch (err) {
      console.error(err) // Handle error here, or throw it if you prefer it to be handled elsewhere
    }
  }

  // 添加主节点操作
  async function saveNode(data: any) {
    let apiCall
    if (data.type === 'root') {
      apiCall = data?.id
        ? options.modifyNodeApi?.(data)
        : options.addNodeApi?.(data)
    }
    else if (data.type === 'sub') {
      apiCall = data?.id
        ? options.modifyChildNodeApi?.(data)
        : options.addChildNodeApi?.(data)
    }

    if (apiCall) {
      try {
        await apiCall
        window.$message.success('操作成功')
        await init()
      }
      catch (error) {
        // 在这里处理可能发生的错误
        console.error('发生错误:', error)
      }
    }
  }

  return {
    menuList,
    treeData,
    showModalType,
    defaultCategoryID,

    init,
    moveNode,
    delNode,
    saveNode,
  }
}
