<script setup lang="ts">
import { NForm } from 'naive-ui'
import { formRules } from './config'
import {
  addHeartTalkRecord,
  editHeartTalkRecord,
  getHeartTalkRecordDetail,
  getUserByDepartmentId,
} from '@/services/recordBookMaintenance'
import type {
  AddHeartTalkRecordItem,
  BranchBasicOrganizationConditionUserType,
  TalkRecordIntervieweeVO,
} from '@/services/recordBookMaintenance/types'

interface Props {
  type?: string
  id?: string
  deptId: string
  year: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const userList = ref<BranchBasicOrganizationConditionUserType[]>([])
function fetchUserList() {
  getUserByDepartmentId({ deptId: props.deptId }).then((res) => {
    userList.value = res || []
  })
}
const userOptions = computed(() => {
  return userList.value.map((item) => {
    return {
      label: item.trueName,
      value: item.userId,
    }
  })
})

const formDataReactive = reactive<AddHeartTalkRecordItem>({
  id: null,
  deptId: props.deptId,
  year: props.year,
  startTime: '',
  timeRange: null,
  endTime: '',
  talkerId: null,
  intervieweeIdList: [],
  location: '',
  mainSuggestion: '',
})

const handleUpdateTimeRange = (formattedValue: [string, string] | null) => {
  if (formattedValue) {
    formDataReactive.startTime = formattedValue[0]
    formDataReactive.endTime = formattedValue[1]
  }
  else {
    formDataReactive.startTime = ''
    formDataReactive.endTime = ''
  }
}

function isDateDisabled(ts: number, type: 'start' | 'end', range: [number, number] | null): boolean {
  const date = new Date(ts)
  const selectedYear = Number(props.year)

  // 限制只能选指定年份
  if (date.getFullYear() !== selectedYear) {
    return true
  }

  // 如果已有一个日期选中了
  if (range !== null) {
    const [start, end] = range

    // 已选择开始时间，禁用不在同一天的结束时间
    if (type === 'end' && start) {
      const startDate = new Date(start)
      if (date.toDateString() !== startDate.toDateString()) {
        return true
      }
    }

    // 已选择结束时间，禁用不在同一天的开始时间
    if (type === 'start' && end) {
      const endDate = new Date(end)
      if (date.toDateString() !== endDate.toDateString()) {
        return true
      }
    }
  }

  return false
}
const formRef = ref<InstanceType<typeof NForm>>()

onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getHeartTalkRecordDetail(props.id).then((res) => {
      formDataReactive.id = res.id
      formDataReactive.talkerId = res.talkerId
      formDataReactive.intervieweeIdList = res.intervieweeList?.map((item: TalkRecordIntervieweeVO) => item.userId)
      formDataReactive.location = res.location
      formDataReactive.mainSuggestion = res.mainSuggestion
      formDataReactive.startTime = res.startTime
      formDataReactive.endTime = res.endTime
      formDataReactive.timeRange = [res.startTime!, res.endTime!]
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // 深拷贝，避免修改原始响应式数据
      const payload = JSON.parse(JSON.stringify(formDataReactive))

      // 所有情况下都去除 timeRange
      delete payload.timeRange

      if (!payload.id) {
        // 新增时去掉 id
        delete payload.id
        addHeartTalkRecord(payload).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
      else {
        // 编辑时保留 id
        editHeartTalkRecord(payload).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})

onMounted(() => {
  fetchUserList()
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="110"
    label-align="right"
    label-placement="left"
    :disabled="props.type === 'view'"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item label="谈话时间：" path="timeRange">
      <n-date-picker
        v-model:value="formDataReactive.timeRange"
        type="datetimerange"
        start-placeholder="谈话开始时间"
        end-placeholder="谈话结束时间"
        :is-date-disabled="isDateDisabled"
        style="width: 100%"
        @update:formatted-value="handleUpdateTimeRange"
      />
    </n-form-item>
    <n-form-item label="谈话人：" path="talkerId">
      <n-select
        v-model:value="formDataReactive.talkerId"
        :options="userOptions"
        clearable
        placeholder="请选择谈话人"
      />
    </n-form-item>
    <n-form-item label="谈话地点：" path="location">
      <n-input
        v-model:value="formDataReactive.location"
        placeholder="请输入谈话地点"
        maxlength="50"
        show-count
        clearable
      />
    </n-form-item>
    <n-form-item label="谈话对象：" path="intervieweeIdList">
      <n-select
        v-model:value="formDataReactive.intervieweeIdList"
        :options="userOptions"
        clearable
        multiple
        placeholder="请选择谈话对象"
      />
    </n-form-item>

    <n-form-item
      label="主要意见建议："
      path="mainSuggestion"
    >
      <n-input
        v-model:value="formDataReactive.mainSuggestion"
        :rows="12"
        maxlength="500"
        placeholder="请输入主要意见建议"
        show-count
        type="textarea"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
