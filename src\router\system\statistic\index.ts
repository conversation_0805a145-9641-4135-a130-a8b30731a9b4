import type { RouteRecordRaw } from 'vue-router'

const statisticRoute: RouteRecordRaw = {
  path: 'statistic',
  name: 'statistic',
  meta: { title: '登录统计', icon: 'statistic', category: 'system', order: 2 },
  redirect: { name: 'statisticList' },
  component: () => import('@/views/main/system/statistic/Statistic.vue'),
  children: [
    {
      path: 'list',
      name: 'statisticList',
      component: () => import('@/views/main/system/statistic/cpns/Notice.vue'),
    },
    // {
    //   path: 'add',
    //   name: 'noticeAdd',
    //   component: () => import('@/views/main/system/statistic/cpns/Detail.vue'),
    // },
  ],
}
export default statisticRoute
