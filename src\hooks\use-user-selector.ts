/*
 * @Description: 选择用户相关逻辑
 */
import { computed, ref } from 'vue'
import useBaseStore from '@/store/base/index'

function useUserSelector() {
  const baseStore = useBaseStore()
  const userIdsRef = ref<string[]>([])
  const showUserSelectorRef = ref(false)
  const userSelectorRef = ref()
  const companyDeptUserList = computed(() => baseStore.companyDeptUserList)

  // 点击选择用户
  function handleClickChooseUser() {
    showUserSelectorRef.value = true
    userSelectorRef.value?.setCheckedKeys([...(userIdsRef.value ?? [])])
  }
  // 确定选择用户
  function handleConfirmSelectUser(v: string[]) {
    userIdsRef.value = v
    return userIdsRef.value.map(item => Number(item.split('&')[1]))
  }
  // 删除用户
  function handleRemoveUser(index: number) {
    userIdsRef.value.splice(index, 1)
    return handleConfirmSelectUser(userIdsRef.value)
  }

  return {
    baseStore,
    companyDeptUserList,
    userIdsRef,
    showUserSelectorRef,
    userSelectorRef,

    handleClickChooseUser,
    handleConfirmSelectUser,
    handleRemoveUser,
  }
}

export default useUserSelector
