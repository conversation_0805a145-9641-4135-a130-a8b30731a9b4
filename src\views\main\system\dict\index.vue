<template>
  <div class="h-full">
    <div class="h-full">
      <div class="ml10 h-full">
        <div class="flex h-full">
          <div class="w-[353px] bg-[#EEEFF3] h-full px-[13px] pt-[25px]">
            <div class="layout-padding-auto layout-padding-view">
              <n-row>
                <div class="mb8 flex" style="width: 100%">
                  <div class="font-[600] leading-[20px]">
                    字典项&emsp;
                  </div>
                  <n-icon class="cursor-pointer ml-auto" size="24">
                    <IosAdd @click="dicDialogRef.openDialog()" />
                  </n-icon>
                  <n-icon class="cursor-pointer" size="22">
                    <MdRefresh @click="handleRefreshCache()" />
                  </n-icon>
                </div>
              </n-row>
              <n-scrollbar>
                <query-tree
                  ref="dictTreeRef"
                  :query="state.queryList"
                  :edit="(row:any)=>dicDialogRef.openDialog(row.id)"
                  :delete="(row:any)=>handleDelete([row.id])"
                  :disable-del="(row:any)=>row.systemFlag !== SYSTEM_FLAG.service"
                  suffix="dictType"
                  disable-del-content="系统内置数据不能删除"
                  placeholder="请输入字典项或名称"
                  @node-click="handleNodeClick"
                />
              </n-scrollbar>
            </div>
          </div>
          <div class="ml8 w-full">
            <DicDialog ref="dicDialogRef" @refresh="handleRefreshTree" />
            <dict-item-dialog ref="dictItemDialogRef" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="systemDic" setup>
import { IosAdd, MdRefresh } from '@vicons/ionicons4'
import {
  delDicts,
  getDictList,
  refreshDictCache,
} from '@/services/system/Admin/index'
import { SYSTEM_FLAG } from '@/store/dict'

// 引入组件
const DicDialog = defineAsyncComponent(() => import('./form.vue'))
const DictItemDialog = defineAsyncComponent(
  () => import('./dictItem/index.vue'),
)
const QueryTree = defineAsyncComponent(
  () => import('@/components/QueryTree/index.vue'),
)

// 定义变量内容
const dicDialogRef = ref()
const dictTreeRef = ref()
const dictItemDialogRef = ref()
const state = reactive({
  queryForm: {},
  queryList: (name?: string) => {
    return getDictList({
      name,
    })
  },
})

// 刷新缓存
const handleRefreshCache = () => {
  refreshDictCache().then(() => {
    dictTreeRef.value.getdeptTree()
    window.$message.success('同步成功')
  })
}

// 点击树
const handleNodeClick = (data: any) => {
  dictItemDialogRef.value.open(data)
}

// 刷新树
const handleRefreshTree = async(data: any) => {
  await dictTreeRef.value.getdeptTree()
  // 选择当前编辑、新增的节点
  handleNodeClick(data)
}

// 删除操作
const handleDelete = (ids: string[]) => {
  try {
    window.$dialog.warning({
      title: '提示',
      content: '确定删除吗？',
      positiveText: '确认',
      negativeText: '取消',
      onPositiveClick: async() => {
        await delDicts(ids)
        window.$message.success('删除成功')
        dictTreeRef.value.getdeptTree()
      },
    })
  } catch (err: any) {
    window.$message.error(err.msg)
  }
}
</script>

<style scoped>
.menu:deep(.n-tree-node__label) {
  display: flex;
  flex: 1;
  height: 100%;
}
.custom-tree-node {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 24px;
  height: 100%;
}
.custom-tree-node .code {
  font-size: 12px;
  color: #999;
}
.custom-tree-node .do {
  display: none;
}
.custom-tree-node:hover .code {
  display: none;
}
.custom-tree-node:hover .do {
  display: inline-block;
}
</style>
