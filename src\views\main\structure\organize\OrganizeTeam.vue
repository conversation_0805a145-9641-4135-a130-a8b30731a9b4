<!-- 班子管理 -->
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="班子管理"
    :loading="loading"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    :show-add="judgePermission('publicity_add_btn')"
    :show-delete="judgePermission('publicity_delete_btn')"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #filters>
      <n-button
        size="small"
        type="primary"
        @click="handleClickOpenDrawer('新增换届信息', 'add')"
      >
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        新增换届信息
      </n-button>
      <n-button
        size="small"
        type="primary"
        @click="handleClickOpenDrawer('换届', 'add')"
      >
        <template #icon>
          <n-icon>
            <ChangeCircleOutlined />
          </n-icon>
        </template>
        换届
      </n-button>
      <n-button size="small" @click="handleClickBatchDel">
        <template #icon>
          <n-icon>
            <DeleteForeverRound />
          </n-icon>
        </template>
        删除
      </n-button>
    </template>
    <template #btns>
      <div class="flex flex-row gap-[10px]">
        <!-- 届期名称 -->
        <n-input
          v-model:value="filterRef.appointedName"
          placeholder="请输入届期名称"
          size="small"
          style="width: 200px"
          clearable
        />
        <!-- 所属党组织 -->
        <n-tree-select
          v-model:value="filterRef.orgId"
          clearable
          placeholder="请选择所属党组织"
          size="small"
          style="width: 300px"
          :options="transformTreeData(calcTreeData)"
        />
        <!-- 届期状态 -->
        <n-select
          v-model:value="filterRef.teamTransitionStatus"
          style="width: 200px"
          placeholder="请选择届期状态"
          size="small"
          clearable
          :options="teamTransitionStatusOptions"
        />
      </div>
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="editTitle" closable>
      <n-form
        ref="formRef"
        :model="formData.data"
        :rules="PartyTeamFormRules"
        label-placement="top"
        label-width="100px"
        require-mark-placement="left"
        :disabled="formDisabled"
        :style="{
          maxWidth: '640px',
        }"
      >
        <n-form-item label="组织名称" path="organizationId">
          <n-tree-select
            v-model:value="formData.data.organizationId"
            clearable
            placeholder="请选择所属党组织"
            size="small"
            :options="transformTreeData(calcTreeData)"
            :disabled="['换届'].includes(editTitle) ? false : formDisabled"
            @update:value="getCheckedData"
          />
        </n-form-item>
        <n-form-item label="届期" path="appointedName">
          <n-select
            v-if="['换届'].includes(editTitle)"
            v-model:value="formData.data.appointedName"
            :options="calcAppointedNameOptions"
            placeholder="请选择届期"
            :disabled="['换届'].includes(editTitle) ? false : formDisabled"
            @update:value="updateAppointed"
          />
          <n-input
            v-else
            v-model:value="formData.data.appointedName"
            placeholder="请输入届期"
          />
        </n-form-item>
        <n-form-item label="届期开始时间" path="appointedStartTime">
          <n-date-picker
            v-model:formatted-value="formData.data.appointedStartTime"
            style="width: 100%"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
            @clear="() => (formData.data.appointedStartTime = null)"
          />
        </n-form-item>
        <n-form-item label="届期结束时间" path="appointedEndTime">
          <n-date-picker
            v-model:formatted-value="formData.data.appointedEndTime"
            style="width: 100%"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
            @clear="() => (formData.data.appointedEndTime = null)"
          />
        </n-form-item>
        <n-form-item label="领导班子职务配额" path="quotaList">
          <n-data-table
            :columns="quotaColumns"
            :data="formData.data.quotaList"
            :max-height="250"
            virtual-scroll
          />
        </n-form-item>
        <div
          v-if="['换届'].includes(editTitle) ? false : !formDisabled"
          class="text-right mt-[-20px] cursor-pointer"
          @click="handleAddTable"
        >
          <n-button size="small" type="primary">
            <template #icon>
              <n-icon>
                <plus-round />
              </n-icon>
            </template>
            新增
          </n-button>
        </div>
        <n-form-item class="mt-[20px]" label="班子简介" path="teamIntroduction">
          <n-input
            v-model:value="formData.data.teamIntroduction"
            type="textarea"
            placeholder="请输入班子简介"
            :rows="5"
          />
        </n-form-item>
      </n-form>
      <template v-if="editTypeRef === 'view'" #footer>
        <span />
      </template>
      <template v-else #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<script setup lang="ts">
import type { TreeSelectOption } from 'naive-ui'
import { NButton, NSelect } from 'naive-ui'
import {
  ChangeCircleOutlined,
  DeleteForeverRound,
  PlusRound,
} from '@vicons/material'
import type { SelectBaseOption } from 'naive-ui/es/select/src/interface'
import { getQuotaColumns, getTableColumns } from './OrganizeTeamConfig'
import { PartyTeamFormRules } from './ruleConfig'
import { useDrawerEditOrganize, useMyTable } from '@/hooks'
import {
  deletePartyTeam,
  getDeptPerson,
  getOrganizationTree,
  getPartyTeam,
  getPartyTeamList,
  postPartyTeamChange,
  postPartyTeamSaveInfo,
  putPartyTeamSaveInfo,
} from '@/services/structure/organize'
import type {
  PartyTeamInfoType,
  PartyTeamListType,
  organizationPersonType,
} from '@/services/structure/organize/types'
import { judgePermission } from '@/directive/permission/ifHasPermi'
const filterRef = ref<{
  appointedName: string
  orgId: string | null
  teamTransitionStatus: string | null
}>({
  appointedName: '',
  orgId: null,
  teamTransitionStatus: null,
})
const formRef = ref()
const currentSelectId = ref() // 当前选中的换届id
const appointedNameOptions = ref<PartyTeamListType[]>([]) // 届期信息枚举值
const calcAppointedNameOptions = computed(() => {
  return appointedNameOptions.value.map((item) => {
    return {
      label: item.appointedName,
      value: item.appointedName,
      id: item.id,
    }
  })
})

const teamTransitionId = ref() // 届期id
const formData = reactive<{ data: PartyTeamInfoType }>({
  data: {
    organizationId: '',
    organizationName: '',
    appointedName: null,
    appointedStartTime: null,
    appointedEndTime: null,
    teamTransitionStatus: '',
    teamIntroduction: '',
    quotaList: [],
  },
})

const teamTransitionStatusOptions = ref([
  {
    label: '往届',
    value: '1',
  },
  {
    label: '当届',
    value: '2',
  },
])

// 树结构枚举
const treeData = ref<any[]>([])
const calcTreeDataFn: any = (arr: any[]) => {
  return arr.map((item: any) => {
    return {
      ...item,
      children:
        item.children && item.children.length
          ? calcTreeDataFn(item.children)
          : undefined,
    }
  })
}
// 计算属性重置树结构的children
const calcTreeData = computed(() => {
  return calcTreeDataFn(treeData.value)
})

const personList = ref<Array<organizationPersonType>>([])
const calcPersonList = computed(() => {
  return personList.value.map((item) => {
    return {
      label: `${item.username} ${item.trueName}`,
      value: item.userId,
    }
  })
})

const {
  loading,
  tableData,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getPartyTeamList, filterRef, {
  batchDeleteTable: true,
  delApi: deletePartyTeam,
})

const {
  showEditRef,
  editTypeRef,
  editTitle,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEditOrganize({ name: '换届信息', confirmFn: handleConfirmEdit })

const formDisabled = computed(() => {
  if (editTypeRef.value === 'view') {
    return true
  } else if (editTitle.value === '换届') {
    return true
  } else {
    return false
  }
})

const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          currentSelectId.value = row.id
          handleClickOpenDrawer('查看换届信息', 'view')
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '查看',
      },
    ),
    h(
      NButton,
      {
        onClick: () => {
          currentSelectId.value = row.id
          handleClickOpenDrawer('修改换届信息', 'modify')
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '修改',
      },
    ),
    h(
      NButton,
      {
        onClick: () => {
          // 删除
          window.$dialog.warning({
            title: '提示',
            content: '确定删除吗？',
            positiveText: '确定',
            negativeText: '取消',
            onPositiveClick: () => {
              handleSingleDelete(row.id)
            },
          })
        },
        type: 'primary',
        text: true,
        disabled: row.teamTransitionStatus === '2',
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '删除',
      },
    ),
  ]
})

const quotaColumns = getQuotaColumns(
  (row, index) => {
    return [
      h(NSelect, {
        value: row.userId,
        style: {
          width: '100%',
        },
        options: calcPersonList.value,
        onUpdateValue(v: string) {
          const selectPersonData = personList.value.find(
            item => item.userId === v,
          )
          if (selectPersonData) {
            formData.data.quotaList[index].userId = selectPersonData.userId
            formData.data.quotaList[index].userName = selectPersonData.username
            formData.data.quotaList[index].trueName = selectPersonData.trueName
            formData.data.quotaList[index].jobId
              = selectPersonData.partyIdentity
            formData.data.quotaList[index].jobName
              = selectPersonData.partyIdentityName
          }
        },
      }),
    ]
  },
  (row, index) => {
    return [
      h(
        NButton,
        {
          onClick: () => {
            // 删除
            formData.data.quotaList.splice(index, 1)
          },
          type: 'primary',
          text: true,
          disabled: ['换届'].includes(editTitle.value)
            ? true
            : formDisabled.value,
        },
        {
          default: () => '删除',
        },
      ),
    ]
  },
)

// 批次删除
function handleClickBatchDel() {
  if (!checkedRowKeys.value.length) {
    window.$message.error('请先选择要删除的数据')
  } else {
    // 如果批量删除中选中的包括 当届 需要提示
    for (let i = 0; i < tableData.value.length; i++) {
      const item: any = tableData.value[i]
      if (
        checkedRowKeys.value.includes(item.id)
        && item.teamTransitionStatus === '2'
      ) {
        // teamTransitionStatus === '2' 当届
        window.$message.error('选中的数据中包含当届数据，不允删除')
        return
      }
    }

    handleSingleDelete(checkedRowKeys.value.join(',')).then(() => {
      loadData()
    })
  }
}

// 打开换届弹框
function handleClickOpenDrawer(title: string, type: 'add' | 'modify' | 'view') {
  resetForm()
  editTitle.value = title
  editTypeRef.value = type
  showEditRef.value = true
  if (['modify', 'view'].includes(type)) {
    getDetailInfo(currentSelectId.value) // 查详情
  }
}

// 获取组织结构
const getOrganizationTreeData = () => {
  return getOrganizationTree().then((res: any) => {
    treeData.value = res
  })
}

// 获取届期信息--主要是在换届时弹框时用到
function getAppointedNameOptions(id: string) {
  return getPartyTeamList({ pageNum: 1, pageSize: 9999999, orgId: id }).then(
    (res) => {
      appointedNameOptions.value = (res.records || []) as any
    },
  )
}

// 跟新换届
function updateAppointed(value: string, option: SelectBaseOption) {
  // 换届需要重新赋值届期id
  teamTransitionId.value = option.id
  getDetailInfo(teamTransitionId.value) // 需要查接口带出这个届期的其它信息
}

// 监听组织选择，根据组织查该组织下的人
watch(
  () => formData.data.organizationId,
  (newVal) => {
    if (newVal && newVal !== '' && editTitle.value === '换届') {
      getAppointedNameOptions(newVal)
    }
    if (newVal && newVal !== '') {
      getPersonList()
    }
  },
  { deep: true },
)

function resetForm() {
  teamTransitionId.value = ''
  formData.data = {
    organizationId: '',
    organizationName: '',
    appointedName: null,
    appointedStartTime: null,
    appointedEndTime: null,
    teamTransitionStatus: '',
    teamIntroduction: '',
    quotaList: [],
  }
}

// 转化data为树状结构
function transformTreeData(data: any) {
  if (Array.isArray(data)) {
    data.forEach((item) => {
      item.key = item.id
      item.label = item.name
      item.value = item.id

      if (item.children) {
        item.children.forEach((child: any) => {
          transformTreeData(child)
        })
      }
    })
  } else {
    data.key = data.id
    data.label = data.name
    data.value = data.id

    if (data.children) {
      data.children.forEach((child: any) => {
        transformTreeData(child)
      })
    }
  }
  return data
}

/** 确定保存 */
function handleConfirmEdit() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // 表单验证通过执行保存或更新接口
      if (editTypeRef.value === 'add' && editTitle.value !== '换届') {
        handleSave()
      } else if (editTypeRef.value === 'modify') {
        handleEditor()
      } else if (editTitle.value === '换届') {
        handleChangeTeam()
      }
    }
  })
}

// 保存
function handleSave() {
  return postPartyTeamSaveInfo(formData.data).then(() => {
    showEditRef.value = false
    window.$message.success('保存成功')
    loadData()
  })
}

// 编辑
function handleEditor() {
  return putPartyTeamSaveInfo(formData.data).then(() => {
    showEditRef.value = false
    window.$message.success('更新成功')
    loadData()
  })
}

// 新增表单表格数据
function handleAddTable() {
  formData.data.quotaList.push({
    teamTransitionId: '',
    jobId: '',
    jobName: '',
    userId: null,
    userName: '',
    trueName: '',
  })
}

// 换届
function handleChangeTeam() {
  postPartyTeamChange(teamTransitionId.value).then(() => {
    showEditRef.value = false
    window.$message.success('换届成功')
    loadData()
  })
}

// 获取组织名称
function getCheckedData(
  value: string | number,
  option: TreeSelectOption | null,
) {
  formData.data.organizationName = option?.label as string
  if (editTitle.value === '换届' && (!value || value === '')) {
    resetForm()
  }
  // 修改换届信息时 需要清空领导班子职务配额
  const cacheInfo = JSON.parse(
    sessionStorage.getItem('cacheDetailInfo') as string,
  )
  if (
    editTitle.value === '修改换届信息'
    && cacheInfo.organizationId !== value
  ) {
    formData.data.quotaList = []
  }
}

// 查看或修改需要调用详情接口
function getDetailInfo(id: string) {
  return getPartyTeam(id).then((data) => {
    formData.data = data
    sessionStorage.setItem('cacheDetailInfo', JSON.stringify(data))
  })
}

// 获取组织下的人员
function getPersonList() {
  getDeptPerson({ deptId: formData.data.organizationId }).then((res) => {
    personList.value = res || []
  })
}

onMounted(() => {
  loadData()
  getOrganizationTreeData()
})
</script>

<style lang="scss" scoped>
.relegation-select {
  width: 100px;
  margin-left: 10px;
  margin-right: 10px;
}

.current-level-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}

.relegation-container {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  margin-top: 30px;
}

.relegation-cancel-btn {
  margin-left: 10px;
}

.merge-select {
  width: 400px;
}

.team-search {
  width: 220px;
}

.import-btn {
  margin-left: 8px;
}
</style>
