/**
 * 自定义指令：autoCalcHeight
 *
 * 功能：
 * 根据绑定元素距离视口顶部的实际像素距离，自动计算并设置其高度为：
 * `calc(100vh - 元素到视口顶部的距离)`，从而使该元素在不同屏幕尺寸下始终填满剩余垂直空间。
 *
 * 适用场景：
 * 适用于页面中需要自适应剩余高度的容器组件，如内容区域、滚动容器、表格视图等。
 * 常见于“头部固定 + 内容区域自适应”的布局场景。
 *
 * 监听机制：
 * - 使用 MutationObserver 监听 #app 根元素的 DOM 结构或属性变化；
 * - 当检测到变化时，重新计算并更新绑定元素的高度；
 * - 挂载时立即计算一次；
 * - 卸载时自动断开监听，释放资源。
 *
 * 参数说明（通过指令绑定的 value）：
 * - id: string（必填）用于标识当前 observer 实例，避免重复监听并确保正确释放。
 *
 * 使用示例：
 * ```html
 * <div v-auto-calc-height="{ id: 'mainContainer' }"></div>
 * ```
 *
 * 注意事项：
 * - `id` 为必传参数，需唯一；
 * - 依赖于页面中存在 `#app` 容器；
 * - 若使用在 `v-if` 控制的元素上，请确保元素真实渲染后再挂载指令。
 */

interface Option {
  id: string | number
  observer?: boolean
  distanceBottom?: string
}

// 全局存储每个绑定元素的 observer
const observerRegistry = new Map()

const autoCalcHeight = {
  mounted(
    el: HTMLElement,
    binding: {
      value: Option
    },
  ) {
    if (!el) {
      return
    }

    const { value } = binding
    const id = value?.id

    if (!id) {
      console.error('[autoCalcHeight] 缺少唯一标识 id')
      return
    }

    // 初次设置高度
    updateElementHeight(el, value)

    // 检查 app 元素是否存在
    const appRoot = document.querySelector('#app')
    if (!appRoot) {
      console.error('[autoCalcHeight] 未找到 #app 元素')
      return
    }

    // 避免重复绑定
    if (observerRegistry.has(id)) {
      console.warn(`[autoCalcHeight] id 为 "${id}" 的 observer 已存在`)
      return
    }

    const observer = value?.observer
      ? createMutationObserver(appRoot as HTMLElement, () => {
        updateElementHeight(el, value)
      })
      : null

    if (observer) {
      observerRegistry.set(id, observer)
    }
  },

  beforeUnmount(
    el: HTMLElement,
    binding: {
      value: Option
    },
  ) {
    const { value } = binding
    const id = value?.id

    if (!id) {
      console.error('[autoCalcHeight] 缺少唯一标识 id')
      return
    }

    const observer = observerRegistry.get(id)
    if (observer) {
      observer.disconnect()
      observerRegistry.delete(id)
    }
  },
}

function createMutationObserver(
  targetEl: HTMLElement,
  callback: () => void,
  options = {},
) {
  if (!targetEl || typeof callback !== 'function') {
    return null
  }

  const defaultOptions = {
    childList: true,
    attributes: true,
    characterData: true,
    subtree: true,
  }

  const observer = new MutationObserver(callback)
  observer.observe(targetEl, { ...defaultOptions, ...options })
  return observer
}

function updateElementHeight(el: HTMLElement, option: Option) {
  if (!el) {
    return
  }
  const topOffset = el.getBoundingClientRect().top
  el.style.height = `calc(100vh - ${topOffset}px - ${
    option.distanceBottom || 0
  })`
}

export default autoCalcHeight
