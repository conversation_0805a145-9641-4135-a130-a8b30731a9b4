import type {
  CultureShowAuditParams,
  CultureShowListItem,
  CultureShowListItemDetail,
  CultureShowSearchParams,
} from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 获取文化展示列表 */
export function getCultureShowList(
  params: CultureShowSearchParams & PaginationReq,
) {
  return commonReq.get<PaginationRes<CultureShowListItem>>({
    url: '/sys-operation/backend/v1/interaction',
    params,
  })
}

/** 互动管理删除列表项 */
export function deleteInteractionSingleData(ids: string) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/interaction',
    params: { ids },
  })
}

/** 更新置顶公司展示 */
export function putInteractionSingleDataTop(id: string) {
  return commonReq.put({
    url: `/sys-operation/backend/v1/interaction/top/${id}`,
  })
}

/** 文化展示审核（通过 / 驳回） */
export function putInteractionSingleDataAudit(params: CultureShowAuditParams) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/interaction/audit',
    params,
  })
}

/** 获取文化展示单个数据详情 */
export function getCulSingleDataDetail(id: string) {
  return commonReq.get<CultureShowListItemDetail>({
    url: `/sys-operation/backend/v1/interaction/${id}`,
  })
}

/** 学习心得审核（通过 / 驳回） */
export function postInteractionSingleDataAudit(params: CultureShowAuditParams) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/interaction/audit',
    params,
  })
}
