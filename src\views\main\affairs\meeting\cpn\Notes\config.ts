import { type DataTableColumns } from 'naive-ui'
import type { VNodeChild } from 'vue'
import type { PartyMeetingNoteItem } from '@/services/affairs/party-meeting/types'

export function getTableColumns(
  optionColumnRenderer: (row: PartyMeetingNoteItem) => VNodeChild,
): DataTableColumns<PartyMeetingNoteItem> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '10%',
      render: (_, i) => i + 1,
    },
    {
      key: 'user',
      title: '姓名',
    },
    {
      key: 'uploadTime',
      title: '上传时间',
      render: row => (row.uploadTime ? row.uploadTime.slice(0, 16) : '--'),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      render: row => optionColumnRenderer(row),
    },
  ]
}
