<template>
  <div>
    <n-form
      ref="newsCategoryRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="130px"
      require-mark-placement="left"
    >
      <n-form-item label="类别名称" path="name">
        <n-input
          v-model:value="formData.name"
          clearable
          maxlength="50"
          placeholder="请输入类别名称"
          show-count
        />
      </n-form-item>
      <n-form-item label="资讯是否展示该分类" path="onShow">
        <n-switch v-model:value="formData.onShow" />
      </n-form-item>
      <!-- <n-form-item label="学习可得的学分" path="configStudyScore">
        <n-input-number
          v-model:value="formData.configStudyScore"
          style="width: 100%"
        />
      </n-form-item> -->
    </n-form>
  </div>
</template>

<script lang="ts" setup>
import type { FormRules } from 'naive-ui'

const formRules: FormRules = {
  name: {
    required: true,
    message: '资讯类别不能为空',
    trigger: 'input',
    type: 'string',
  },
  onShow: {
    required: true,
    validator: validateOnShow,
    message: '请选择资讯是否展示该分类',
    trigger: ['change'],
  },
}
const newsCategoryRef = ref()
const formData = ref({
  name: '',
  onShow: '',
  // configStudyScore: '',
})

function validateOnShow(rule: any, value: string): boolean {
  return true
}

function handleValidate() {
  return new Promise((resolve, reject) => {
    newsCategoryRef.value?.validate((errors: any) => {
      if (!errors) {
        resolve(true)
      }
      else {
        resolve(false)
      }
    })
  })
}

function handleSetFormData(data: any) {
  formData.value = data
}

defineExpose({
  formData,
  handleValidate,
  handleSetFormData,
})
</script>

<style lang="scss" scoped></style>
