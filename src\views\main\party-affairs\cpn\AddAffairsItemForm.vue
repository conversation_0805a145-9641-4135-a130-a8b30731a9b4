<script setup lang="ts">
import { NForm } from 'naive-ui'
import { formRules } from './config'
import type {
  uploadFileItem,
  // uploadFileItemNew,
} from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
import RichEditor from '@/components/RichEditor.vue'
import { uploadImg } from '@/services'
import {
  getAffairsDetail,
  postAffairs,
  putAffairs,
} from '@/services/party-affairs'
import type { AddOrEditAffairsItem } from '@/services/party-affairs/types'

interface Props {
  type?: string
  id?: string
  categoryId?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
  categoryId: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<AddOrEditAffairsItem>({
  id: '',
  themeName: '',
  content: '',
  isTop: '0',
  isOutside: '0',
  linkUrl: '',
  categoryId: props.categoryId,
  pictureUrl: '',
  annexUrl: '',
  fileList: [],
})

const formRef = ref<InstanceType<typeof NForm>>()
const oldImgUrlRef = ref('')
const oldImgUrlFirstFlag = ref(true)
onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getAffairsDetail(props.id).then((res) => {
      formDataReactive.categoryId = res.categoryId
      formDataReactive.themeName = res.themeName
      formDataReactive.content = res.content
      formDataReactive.pictureUrl = res.pictureUrl
      formDataReactive.isTop = res.isTop
      formDataReactive.isOutside = res.isOutside
      formDataReactive.linkUrl = res.linkUrl
      formDataReactive.annexUrl = res.annexUrl
      formDataReactive.fileList = res.fileList || []
      formDataReactive.id = props.id
      oldImgUrlRef.value = res.pictureUrl
      oldImgUrlFirstFlag.value = false

      // 从详情接口取出的附件需要设置它的百分比为100
      if (res.fileList.length) {
        formDataReactive.fileList.forEach((item) => {
          item.percentage = 100
          item.url = item.fileName
          item.name = item.original
        })
      }
    })
  }
})

async function handleUpload(
  file: File,
  options: any,
  callBack: (data: any) => void,
) {
  const fileData = new FormData()
  const fileItem = options.file.file as Blob
  fileData.append('file', fileItem)
  const data: uploadFileItem = await uploadImg(fileData)
  callBack(data)
}

// 图片裁剪完毕
const handleCoverDone = async(file: File) => {
  if (
    (!oldImgUrlFirstFlag.value && props.type === 'modify')
    || props.type === 'add'
  ) {
    const imgFileData = new FormData()
    imgFileData.append('file', file)
    try {
      if (formDataReactive.pictureUrl) {
        return
      }
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formDataReactive.pictureUrl = data.url || ''
      }
    }
    catch (error) {}
  }
  oldImgUrlFirstFlag.value = false
}

// PC封面图片删除操作
const handleCoverDelete = () => {
  formDataReactive.pictureUrl = ''
}
// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // 保存和编辑时 需要将文件的id拼接起来
      formDataReactive.annexUrl = formDataReactive.fileList.length
        ? formDataReactive.fileList.map(file => file.id).join(',')
        : ''
      if (formDataReactive.id) {
        putAffairs(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
      else {
        postAffairs(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="100"
    label-align="right"
    label-placement="left"
    :disabled="props.type === 'view'"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item label="主题：" path="themeName">
      <n-input
        v-model:value="formDataReactive.themeName"
        placeholder="请输入主题名称"
        maxlength="60"
        show-count
        clearable
      />
    </n-form-item>
    <n-form-item span="24" label="图片：" path="pictureUrl">
      <ImgUploader
        v-model:oldImgUrl="oldImgUrlRef"
        :width="380"
        :height="200"
        :need-cropper="false"
        :is-readonly="props.type === 'view'"
        @done="handleCoverDone"
        @delete="handleCoverDelete"
      />
    </n-form-item>
    <n-form-item label="是否置顶：" path="isTop">
      <n-switch
        v-model:value="formDataReactive.isTop"
        checked-value="1"
        unchecked-value="0"
      />
    </n-form-item>
    <n-form-item label="是否外链：" path="isTop">
      <n-switch
        v-model:value="formDataReactive.isOutside"
        checked-value="1"
        unchecked-value="0"
      />
    </n-form-item>
    <n-form-item
      v-if="formDataReactive.isOutside === '0'"
      label="内容："
      path="content"
    >
      <RichEditor
        v-model:value="formDataReactive.content"
        :disabled="props.type === 'view'"
        style="width: 100%"
        :rich-height="350"
      />
    </n-form-item>

    <n-form-item
      v-if="formDataReactive.isOutside === '0'"
      span="24"
      label="附件："
    >
      <file-uploader-new
        v-model:original-file-list="formDataReactive.fileList"
        need-progress
        :max="1"
        accept=".pdf"
        :size-limit="200"
        :is-readonly="props.type === 'view'"
        :upload-method="handleUpload"
      >
        <template #tips>
          <span class="tips">
            可上传1个文件，仅支持文件扩展名：.pdf，大小200M以内
          </span>
        </template>
      </file-uploader-new>
    </n-form-item>
    <n-form-item
      v-if="formDataReactive.isOutside === '1'"
      label="链接地址："
      path="linkUrl"
    >
      <n-input
        v-model:value="formDataReactive.linkUrl"
        placeholder="请输入链接地址"
        clearable
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
