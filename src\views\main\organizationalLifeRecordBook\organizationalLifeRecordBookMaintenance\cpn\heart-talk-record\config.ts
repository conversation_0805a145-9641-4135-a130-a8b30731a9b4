import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  timeRange: {
    required: true,
    message: '请选择谈话时间',
    trigger: 'change',
    validator: (rule: any, value: any) => {
      if (value == null || value.length === 0) {
        return new Error('请选择时间')
      }
      else {
        return true
      }
    },
  },
  talkerId: {
    required: true,
    message: '请选择谈话人',
    trigger: 'change',
  },
  location: {
    required: true,
    message: '谈话地点不能为空',
    trigger: 'input',
  },

  intervieweeIdList: {
    required: true,
    message: '请选择谈话对象',
    trigger: 'blur',
    type: 'array',
  },
  mainSuggestion: {
    required: true,
    message: '请填写主要意见建议',
    trigger: 'input',
  },
}
