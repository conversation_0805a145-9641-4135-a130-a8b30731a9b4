import type { UploadFileInfo } from 'naive-ui'
/** 党务公开分类列表 */
export interface PublicCategoryList {
  /** 学习得分 */
  studyScore?: number | null
  /** 主键id */
  id?: number | null
  /** 分类名称 */
  name?: null | string
  /** 父级id */
  parentId?: number | null
  /** 排序 */
  sort?: number | null
  /** 简介 */
  description: string
}

/** 新增 / 编辑 党务公开分类 */
export interface AddOrEditPublicCategory {
  /** 学习得分 */
  studyScore?: number | null
  /** 分类名称 */
  name?: null | string
  /** 父级id */
  parentId?: number | null
  /** 排序 */
  sort?: number | null
  /** 主键Id */
  id?: string | null
  description?: string | null
}

/** 移动资讯分类 */
export interface MovePublicCategory {
  /** 主键id */
  id: string
  /** 上移 -1、下移 +1, 默认值为0 */
  move: string
  /** 分类名称 */
  name?: null | string
  /** 父级id */
  parentId: number
  /** 排序 */
  sort?: number | null
  studyScore?: number | null
  description?: string | null
}

/** 类别列表 */
export interface PublicListItem {
  id: string
  categoryId: string
  annexName: string
  annexDescribe: string
  annexUrl: string
  fileList?: fileItem[]
  fileIds: string[]
  updateTime: string
  pictureUrl: string
  /** 是否隐藏 (0-否，1-是) */
  isHidden?: number | null
  /** 是否置顶 (0-否，1-是) */
  isTop?: number | null
}
export interface fileItem {
  fileName: string
  id: string
  original: string
  percentage?: number
  url?: string
  name?: string
}

/** 新增 / 编辑 党务公开  */
export interface AddOrEditPublicItem {
  id?: number | string
  annexName: string
  annexDescribe: string
  categoryId: number | string
  annexUrl: string
  fileList: Array<fileItem>
  pictureUrl: string
  deptId?: string | null
  publicScope?: string | null
  /** 是否隐藏 (0-否，1-是) */
  isHidden?: number | null
  /** 是否置顶 (0-否，1-是) */
  isTop?: number | null
}
export interface uploadFileItem {
  fileId: string
  bucketName: string
  url: string
  fileName: string
}
export interface uploadFileItemNew extends UploadFileInfo {
  fileName?: string | undefined
  original?: string | undefined
}

/** 资讯详情 */
export interface NewsDetail {
  /** 作者 */
  author?: null | string
  /** 分类id */
  categoryId?: number | null
  /** 收藏次数 */
  collectNum?: number | null
  /** 评论次数 */
  commentNum?: number | null
  /** 内容 */
  content?: null | string
  /** 封面Url */
  coverUrl?: number | null
  /** 主键id */
  id?: number | null
  /** 是否隐藏 (0-否，1-是) */
  isHidden?: number | null
  /** 是否推荐 */
  isRecommand?: null | string
  /** 是否置顶 */
  isTop?: null | string
  /** 点赞次数 */
  likeNum?: number | null
  /** 发布时间 */
  publishTime?: null | string
  /** 阅读人次 */
  readNum?: number | null
  /** 阅读时间配置，单位（秒） */
  readTimeConfig?: number | null
  /** 审核是否通过 (0-否，1-是) */
  reviewed?: number | null
  /** 审核时间 */
  reviewedTime?: null | string
  /** 审核人 */
  reviewedUser?: null | string
  /** 短标题 */
  shortTitle?: null | string
  /** 排序 */
  sort?: number | null
  /** 标题 */
  title?: null | string
}
