<script lang="ts" setup>
import type { TreeOption } from 'naive-ui'
import { useOrganizationListOptionsNew } from '@/hooks/use-select-options'
import folderClose from '@/assets/image/vote/folderClose.png'
import folderOpen from '@/assets/image/vote/folderOpen.png'
import partyEmblem from '@/assets/image/vote/partyEmblem.png'
interface MenuProps {
  title: string
}
withDefaults(defineProps<MenuProps>(), { title: 'title' })

const emits = defineEmits<{
  (e: 'getCurrentDeptId', value: string): void
}>()

const { organizationListTree } = useOrganizationListOptionsNew() // 获取支部列表

const pattern = ref('')

// 递归取党组织的第一个有效的 党支部
const idStr = ref('')
const getFirstPartyBranch = (list: any[]) => {
  if (Array.isArray(list) && list.length) {
    for (let i = 0; i < list.length; i++) {
      if (list[i].org_type === '党支部') {
        idStr.value = list[i].deptId
        break
      }
      else {
        getFirstPartyBranch(list[i].children)
      }
    }
  }
}

// 添加组织结构图标
const changeTreeIcon = (list: any[]) => {
  if (Array.isArray(list) && list.length) {
    for (let i = 0; i < list.length; i++) {
      if (['党委', '党总支'].includes(list[i].org_type)) {
        list[i].prefix = () =>
          h(
            'img',
            {
              src: folderOpen,
              style: { width: '15px', height: '13px' },
            },
            {},
          )
      }
      else {
        list[i].prefix = () =>
          h(
            'img',
            {
              src: partyEmblem,
              style: { width: '15px', height: '13px' },
            },
            {},
          )
      }
      changeTreeIcon(list[i].children)
    }
  }
}

watch(organizationListTree, (newVal) => {
  if (newVal[0] && newVal[0].deptId) {
    getFirstPartyBranch(newVal)
    changeTreeIcon(newVal)
  }
})
watch(
  () => idStr.value,
  (newVal) => {
    if (newVal) {
      emits('getCurrentDeptId', newVal)
    }
  },
)

/**
 * 点击树节点触发的方法
 * @param {any} {option}:{option:TreeOption}
 */
const nodeProps = ({ option }: { option: TreeOption }) => {
  return {
    onClick() {
      // 党委 | 党总支 点击无效
      if (
        !option.disabled
        && !['党委', '党总支'].includes(option.org_type as string)
      ) {
        emits('getCurrentDeptId', option.deptId as string)
      }
    },
  }
}

const updatePrefixWithExpand = (
  _keys: Array<string | number>,
  _option: Array<TreeOption | null>,
  meta: {
    node: TreeOption | null
    action: 'expand' | 'collapse' | 'filter'
  },
) => {
  if (!meta.node) {
    return
  }
  switch (meta.action) {
  case 'expand':
    meta.node.prefix = () => {
      if (['党委', '党总支'].includes(meta.node!.org_type as string)) {
        return h(
          'img',
          {
            src: folderOpen,
            style: { width: '15px', height: '13px' },
          },
          {},
        )
      }
      else {
        return h(
          'img',
          {
            src: partyEmblem,
            style: { width: '15px', height: '13px' },
          },
          {},
        )
      }
    }
    break
  case 'collapse':
    meta.node.prefix = () => {
      if (['党委', '党总支'].includes(meta.node!.org_type as string)) {
        return h(
          'img',
          {
            src: folderClose,
            style: { width: '15px', height: '13px' },
          },
          {},
        )
      }
      else {
        return h(
          'img',
          {
            src: partyEmblem,
            style: { width: '15px', height: '13px' },
          },
          {},
        )
      }
    }
    break
  }
}
</script>
<template>
  <div class="w-[400px] h-[calc(100vh-114px)] bg-[#F9FAFB] root">
    <div class="px-[8px] pt-[25px] flex justify-between text-[14px] font-[500]">
      <span class="text-[14px] font-[600] text-[#333] leading-[20px]">{{
        title
      }}</span>
    </div>
    <div class="py-[30px] px-[10px]">
      <n-input v-model:value="pattern" placeholder="搜索" />
    </div>
    <div class="leading-[60px] text-[18px] h-[calc(100%-70px)] overflow-hidden">
      <div class="h-[calc(100%-100px)]">
        <n-scrollbar style="height: 100%">
          <n-tree
            block-node
            :data="organizationListTree"
            :pattern="pattern"
            :show-irrelevant-nodes="false"
            default-expand-all
            label-field="name"
            key-field="deptId"
            value-field="deptId"
            children-field="children"
            check-strategy="child"
            :node-props="nodeProps"
            :on-update:expanded-keys="updatePrefixWithExpand"
          />
        </n-scrollbar>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.root {
  .activated {
    background-color: #e4e8f0;
  }
  ::-webkit-scrollbar {
    display: none;
  }
}
</style>
