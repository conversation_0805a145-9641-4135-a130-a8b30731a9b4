import type { RouteComponent, RouteMeta } from 'vue-router'

// export type AppRouteRecordRaw = children<children<children<[]>>>
export interface AppRouteRecordRaw {
  meta?: RouteMeta
  name?: string
  orderBy?: number
  children?: AppRouteRecordRaw[]
  hidden?: boolean
  filePath?: string
  component?: RouteComponent
  icon?: string
  sortOrder?: number
  redirect?: object
  label?: string
  path?: string
  parentId?: string
  category?: string
  routeName?: string
  type?: string
}
export interface categoryListType {
  title: string
  name: string
}

export interface menuItem {
  sortOrder: number
}
