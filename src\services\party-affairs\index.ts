import { commonReq } from '../request'
import type { PaginationReq } from '../types'
import type {
  AddOrEditAffairsCategory,
  AddOrEditAffairsItem,
  AffairsCategoryList,
  MoveAffairsCategory,
} from './types'

/** 获取党务通分类列表 */
export function getAffairsCategoryList() {
  return commonReq.get<AffairsCategoryList[]>({
    url: '/party-affairs/backend/v1/communication_category/list_all',
  })
}

/** 新增党务通分类 */
export function postAffairsCategory(data: AddOrEditAffairsCategory) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/communication_category',
    data,
  })
}

/** 编辑党务通分类 */
export function putAffairsCategory(data: AddOrEditAffairsCategory) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/communication_category',
    data,
  })
}

/** 移动党务通分类 */
export function moveAffairsCategory(data: MoveAffairsCategory) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/communication_category/move',
    data,
  })
}

/** 删除党务通分类 */
export function deleteAffairsCategory(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/communication_category',
    params: {
      ids,
    },
  })
}

/** 获取党务通列表 */
export function getCategoryList(
  params: {
    categoryId: string
    themeName?: string
    content?: string
  } & PaginationReq,
) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/communication/page',
    params,
  })
}

/** 新增党务通 */
export function postAffairs(data: AddOrEditAffairsItem) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/communication',
    data,
  })
}

/** 编辑党务通 */
export function putAffairs(data: AddOrEditAffairsItem) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/communication',
    data,
  })
}

/** 查看党务通详情 */
export function getAffairsDetail(id: string) {
  return commonReq.get<AddOrEditAffairsItem>({
    url: `/party-affairs/backend/v1/communication/${id}`,
  })
}

/** 删除党务通  */
export function deleteAffairs(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/communication',
    params: {
      ids,
    },
  })
}

/** 置顶资讯 */
export function putAffairsTop(id: string, isTop: string) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/communication/top',
    params: {
      id,
      isTop,
    },
  })
}
