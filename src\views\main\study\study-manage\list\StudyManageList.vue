<script setup lang="ts">
import { NButton, NSwitch } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
// import AddNewsCategoryForm from './AddNewsCategoryForm.vue'
import AddStudyCategoryRootForm from '../cpns/AddStudyCategoryRootForm.vue'
import AddStudyCategorySubForm from '../cpns/AddStudyCategorySubForm.vue'
import { createColumns } from './config'
import { useMyTable, useTreeMenuSy } from '@/hooks'

import {
  deleteStudyCategory,
  deleteStudyContent,
  getStudyCategoryList,
  getStudyTableList,
  postAddStudyCategory,
  publishStudyContent,
  putEditStudyCategory,
  putMoveStudyCategory,
  putStudyIsTop,
} from '@/services/study/study-manage'
const router = useRouter()
const { treeData, showModalType, moveNode, delNode, saveNode, init }
  = useTreeMenuSy({
    menuListApi: getStudyCategoryList,
    addNodeApi: postAddStudyCategory,
    delNodeApi: deleteStudyCategory,
    modifyNodeApi: putEditStudyCategory,
    moveNodeApi: putMoveStudyCategory,
    refreshTableApi: filterInput,
    labelField: 'name',
    childLabelField: 'name',
    multiLevelKey: 'children',
    maxLevel: 2,
    sessionId: 'studyCategoryId',
    sessionName: 'studyCategoryLabel',
  })

const showModal = ref(false)
const modalTitle = ref()
const studyRootFormRef = ref()
const studySubFormRef = ref()
const currentPionnerId = ref('0')
/** 新增子节点 */
const parentName = ref()
async function handleAddChildNode(data: any) {
  // console.log('data: ', data)
  if (data.type === 'root') {
    modalTitle.value = '新增类别'
    showModalType.value = 'root'
  } else {
    modalTitle.value = '新增栏目'
    showModalType.value = 'sub'
    currentPionnerId.value = data.id
    parentName.value = data.name
  }
  showModal.value = true
  if (data.model === 'modify') {
    // 编辑状态 需要将数据回显
    if (data.isChild) {
      modalTitle.value = '修改栏目'
      currentPionnerId.value = data.parentId
      nextTick(() => {
        studySubFormRef.value.handleSetFormData(data)
      })
    } else {
      modalTitle.value = '修改类别'
      nextTick(() => {
        studyRootFormRef.value.handleSetFormData(data)
      })
    }
  }
}
/** 处理弹框需要保存的数据及校验弹框必填项 */
async function handleFormatterParams() {
  let flag = false
  let parentId
  if (showModalType.value === 'root') {
    parentId = currentPionnerId.value
    flag = await studyRootFormRef.value.handleValidate()
  }
  if (showModalType.value === 'sub') {
    parentId = currentPionnerId.value
    flag = await studySubFormRef.value.handleValidate()
  }
  if (!flag) {
    return
  }
  const data
    = showModalType.value === 'root'
      ? studyRootFormRef.value.formData
      : studySubFormRef.value.formData
  saveNode({ ...data, type: showModalType.value, parentId })
  showModal.value = false
}

function handleCancel() {
  showModal.value = false
}

// 选中菜单名称
const selectName = ref()

// 筛选项：类别id和资讯标题
const filterRef = ref({
  categoryId: '',
  title: null,
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getStudyTableList, filterRef, {
  batchDeleteTable: true,
  delApi: deleteStudyContent,
})

// 选中菜单触发的事件
function handleChangeTab(data: any) {
  const { id, isChild, label } = data
  if (isChild) {
    filterRef.value.categoryId = id
    window.sessionStorage.setItem('studyCategoryId', id)
    window.sessionStorage.setItem('studyCategoryLabel', label)
    filterRef.value.title = null
    selectName.value = label
    currentPage.value = 1
  }
}

/** 发布 / 取消发布 */
function handlePublishConfirm(id: string, publishStatus: string) {
  window.$dialog.create({
    type: 'default',
    closable: false,
    content:
      publishStatus === '1'
        ? '确认发布该学习内容吗？'
        : '确认撤销该学习内容吗？',
    showIcon: false,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      publishStudyContent(id).then((res) => {
        window.$message.success('操作成功！')
        loadData()
      })
    },
  })
}
/**  设置置顶 */
async function switchTop(value: boolean, id: string) {
  await putStudyIsTop(id)
  window.$message.success('修改置顶成功')
  loadData()
}

/** 点击添加按钮 */
function handleClickAdd() {
  router.push({
    name: 'studyManageAdd',
    query: {
      enterType: 'add',
    },
  })
}

/** 删除 */
const delStudyItemFn = (ids: string) => {
  if (!ids) {
    window.$message.error('请选择删除项')
    return
  }
  window.$dialog.create({
    type: 'default',
    closable: false,
    content: '确认删除该学习内容吗？',
    showIcon: false,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      deleteStudyContent(ids).then((res) => {
        window.$message.success('删除成功！')
        loadData()
      })
    },
  })
}
/** 列表操作 */
const tableColumns = createColumns(
  (row) => {
    return h(
      'div',
      {
        style: {
          display: 'flex',
          gap: '10px',
          color: '#006FFF',
          cursor: 'pointer',
        },
      },
      [
        row.publishStatus === '1'
          && h(
            NButton,
            {
              type: 'primary',
              text: true,
              onClick: () =>
                router.push({
                  name: 'studyManageAdd',
                  query: {
                    id: row.id,
                    enterType: 'edit',
                  },
                }),
            },
            { default: () => '编辑' },
          ),
        row.publishStatus === '1'
          && h(
            NButton,
            {
              type: 'primary',
              text: true,
              onClick: () =>
                handlePublishConfirm(String(row.id), row.publishStatus),
            },
            { default: () => '发布' },
          ),
        row.publishStatus === '0'
          && h(
            NButton,
            {
              type: 'primary',
              text: true,
              onClick: () =>
                router.push({
                  name: 'studyManageAdd',
                  query: {
                    id: row.id,
                    enterType: 'view',
                  },
                }),
            },
            { default: () => '详情' },
          ),
        row.publishStatus === '0'
          && h(
            NButton,
            {
              type: 'primary',
              text: true,
              onClick: () =>
                handlePublishConfirm(String(row.id), row.publishStatus),
            },
            { default: () => '取消发布' },
          ),
        row.publishStatus === '1'
          && h(
            NButton,
            {
              type: 'primary',
              text: true,
              onClick: () => {
                delStudyItemFn(String(row.id))
              },
            },
            { default: () => '删除' },
          ),
      ],
    )
  },
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => switchTop(value, String(row.id)),
      value: Boolean(Number(row.isTop)),
    }),
)

const defaultSelectedKeys = ref<string[]>([])
function filterInput(res: { id: string; name: string }) {
  defaultSelectedKeys.value = [res.id]
  filterRef.value.categoryId = res.id
  selectName.value = res.name
}
onMounted(() => {
  init().then((res: any) => {
    defaultSelectedKeys.value = [res.id]
    filterRef.value.categoryId = res.id
    selectName.value = res.name
  })
})
</script>
<template>
  <layout-container style="height: calc(100vh - 114px)">
    <template #side>
      <SideMenuNew
        v-model:show-modal="showModal"
        title="学习管理"
        :tree-data="treeData"
        :modal-title="modalTitle"
        :default-selected-keys="defaultSelectedKeys"
        @move="moveNode"
        @del-node="delNode"
        @save-tree-node="handleFormatterParams"
        @add-child-node="handleAddChildNode"
        @select-node-key="handleChangeTab"
      />
    </template>
    <template #main>
      <table-container
        v-model:page="currentPage"
        v-model:page-size="pageSize"
        :title="selectName"
        :loading="loading"
        :show-toolbar="false"
        custom-toolbar
        :table-columns="tableColumns"
        :table-data="tableData"
        :total="total"
        :checked-row-keys="checkedRowKeys"
        @click-add="handleClickAdd"
        @click-delete="handleBatchDelete"
        @update-page="onUpdatePage"
        @update-page-size="onUpdatePageSize"
        @update-checked-row-keys="onUpdateCheckedRowKeys"
      >
        <template #btns>
          <n-button size="small" type="primary" @click="handleClickAdd">
            <template #icon>
              <n-icon>
                <plus-round />
              </n-icon>
            </template>
            添加
          </n-button>

          <n-button size="small" @click="handleBatchDelete">
            <template #icon>
              <n-icon>
                <delete-forever-round />
              </n-icon>
            </template>
            删除
          </n-button>
        </template>
        <template #filters>
          <n-input
            v-model:value="filterRef.title"
            style="width: 200px"
            size="small"
            placeholder="请输入课程标题"
            clearable
          />
        </template>
      </table-container>
    </template>
  </layout-container>
  <CustomDialog
    :show="showModal"
    :title="modalTitle"
    width="600px"
    @confirm="handleFormatterParams"
    @cancel="handleCancel"
    @update:show="(v:boolean) => (showModal = v)"
  >
    <div class="p-[20px]">
      <AddStudyCategoryRootForm
        v-show="showModalType === 'root'"
        ref="studyRootFormRef"
      />
      <AddStudyCategorySubForm
        v-show="showModalType === 'sub'"
        ref="studySubFormRef"
      />
    </div>
  </CustomDialog>
</template>
<style lang="scss" scoped></style>
