<template>
  <div class="flex flex-row h-[calc(100%-60px)] overflow-hidden">
    <left-menu title="党组织" @get-current-dept-id="getCurrentDeptId" />
    <table-container
      v-model:page="pageNum"
      v-model:page-size="pageSize"
      class="w-full"
      title="民主评议党员"
      :show-toolbar="false"
      custom-toolbar
      :loading="loading"
      :total="total"
      :table-columns="tableColumns"
      :table-data="tableData"
      :show-delete="false"
      :show-pagination="true"
      default-expand-all
      :checked-row-keys="checkedRowKeys"
      @update-checked-row-keys="onUpdateCheckedRowKeys"
    >
      <template #btns>
        <n-button size="small" @click="handleExport">
          <template #icon>
            <n-icon>
              <export />
            </n-icon>
          </template>
          导出
        </n-button>
      </template>
      <template #filters>
        <div
          class="flex flex-row items-center justify-between gap-[10px] w-[200px]"
        >
          <n-input
            v-model:value="partyMemberName"
            size="medium"
            placeholder="请输入党员姓名搜索"
            clearable
            @input="loadDataForInput"
          />
        </div>
      </template>
    </table-container>
  </div>

  <div ref="exportImg" class="absolute inline-block left-full">
    <n-data-table
      :columns="exportColumns"
      :data="exportData"
      :single-line="false"
    />
  </div>
</template>

<script setup lang="ts">
import type { DataTableColumns } from 'naive-ui'
import { NButton } from 'naive-ui'
import { Export } from '@vicons/carbon'
import LeftMenu from '../cpns/LeftMenu.vue'
import { talkAboutPartyMemberColumns } from './config'
import { downloadArrayBuffer } from '@/utils/downloader'
import {
  getTalkAboutPartyMemberList,
  postExportTalkAboutPartyMember,
} from '@/services/publicity/vote/talkAbout'
import type { TalkAboutPartyMemberTableItem } from '@/services/publicity/vote/talkAbout/type'
import { formatTimeStamp } from '@/utils/format'

const router = useRouter()
const route = useRoute()
const reviewId = ref((route.query.reviewId as string) || '') // 评议id
const reviewItemId = ref((route.query.partReviewItemId as string) || '') // 评议id
const exportData = ref<any[]>([])
const exportColumns = ref<DataTableColumns<any>>([])
const pageNum = ref(1)
const pageSize = ref(10)
const partyMemberName = ref('')
const loading = ref(false)
const tableData = ref<TalkAboutPartyMemberTableItem[]>([])
const deptId = ref('')
const total = ref(0)
const exportImg = ref<HTMLElement | null>()

const getCurrentDeptId = (id: string) => {
  deptId.value = id
}

// 修改和删除按钮渲染
const tableColumns = ref<DataTableColumns<TalkAboutPartyMemberTableItem>>([])
// 生成列
const generateColumns = (additionalData: {
  eachOpen: Boolean
  selfOpen: Boolean
}) => {
  return talkAboutPartyMemberColumns((row) => {
    return [
      h(
        NButton,
        {
          onClick: () => {
            router.push({
              name: 'talk-about-party-member-Detail',
              query: {
                reviewItemId: row.reviewItemId as string,
                commentatorId: row.commentatorId as string,

                orgId: deptId.value,
                reviewId: reviewId.value,
              },
            })
          },
          type: 'primary',
          text: true,
        },
        {
          default: () => '详情',
        },
      ),
    ]
  }, additionalData)
}
// 加载数据
const loadData = () => {
  loading.value = true
  const data = {
    pageSize: pageSize.value,
    pageNum: pageNum.value,
    reviewId: reviewId.value,
    orgId: deptId.value,
    commentatorName: partyMemberName.value,
  }
  getTalkAboutPartyMemberList(data)
    .then((res) => {
      total.value = res.total
      tableData.value = res.records || []
      tableColumns.value = generateColumns(res.additionalData)
    })
    .then(() => {
      loading.value = false
    })
}

const loadDataForInput = () => {
  if (pageNum.value === 1) {
    loadData()
  }
  else {
    // 值改变监听会触发loadData的
    pageNum.value = 1
  }
}

/** 选中的行id */
const checkedRowKeys = ref<Array<number | string>>(
  tableData.value.map(item => item.reviewId) || [],
)

// /** 行选中 */
function onUpdateCheckedRowKeys(ids: Array<number | string>) {
  checkedRowKeys.value = ids
}

// function getTextWidth(text: string) {
//   // const span = document.createElement('span')
//   // span.style.visibility = 'hidden'
//   // span.style.whiteSpace = 'nowrap'
//   // span.style.position = 'absolute'
//   // span.innerText = text
//   // document.body.appendChild(span)
//   // const width = span.offsetWidth
//   // document.body.removeChild(span)
//   const width = text.length * 14 // 每个汉字14px
//   // 24是表格内部的padding值
//   return width + 24
// }

// 导出
async function handleExport() {
  try {
    exportColumns.value = []
    exportData.value = []
    const data = {
      reviewId: reviewId.value,
      orgId: deptId.value,
      reviewItemId: reviewItemId.value,
    }
    const res = await postExportTalkAboutPartyMember(data)

    downloadArrayBuffer(
      res,
      `民主评议党员-${formatTimeStamp(Date.now(), 'YYYYMMDDHHmmss')}`,
    )

    // if (res.partInfo.length) {
    //   exportColumns.value.push({
    //     title: '姓名',
    //     key: 'name',
    //     width: 80,
    //   })
    //   for (const name in res.partInfo[0]) {
    //     let colIndex = 0
    //     const columns = res.partInfo[0][name].map((item: any) => {
    //       let tmpParCol = ''
    //       const subCol = []
    //       let countSubWidth = 0
    //       for (const parCol in item) {
    //         const parObj = item[parCol] as any

    //         for (const choice in parObj) {
    //           const subWidth = getTextWidth(choice)

    //           countSubWidth += subWidth
    //           subCol.push({
    //             title: choice,
    //             key: `${parCol}-${choice}${colIndex++}`,
    //             width: subWidth,
    //           })
    //         }
    //         tmpParCol = parCol
    //       }
    //       const tmpParWith = getTextWidth(tmpParCol)
    //       // 若子列宽度和 小于 父列文本宽度  需为每个子列增长对应宽度
    //       if (countSubWidth < tmpParWith) {
    //         const addtion = Math.ceil(
    //           (tmpParWith - countSubWidth) / subCol.length,
    //         )
    //         subCol.forEach(item => (item.width += addtion))
    //       }
    //       return {
    //         title: tmpParCol,
    //         children: subCol,
    //       }
    //     })

    //     exportColumns.value = exportColumns.value.concat(columns)
    //   }

    //   for (let i = 0; i < res.partInfo.length; i++) {
    //     const item = res.partInfo[i]
    //     const dataItem = {} as any
    //     for (const name in item) {
    //       dataItem.name = name
    //       let colIndex = 0
    //       item[name].forEach((parItem: any) => {
    //         for (const prop in parItem) {
    //           for (const choice in parItem[prop]) {
    //             const key = `${prop}-${choice}${colIndex++}`
    //             dataItem[key] = parItem[prop][choice]
    //           }
    //         }
    //       })
    //     }
    //     exportData.value.push(dataItem)
    //   }
    // }
  }
  catch (error) {}
}

watch(
  () => [deptId.value, pageSize.value, pageNum.value],
  () => {
    if (deptId.value) {
      loadData()
    }
  },
)
</script>

<style scoped lang="scss"></style>
