<template>
  <div>
    <div
      class="w-full box-border px-[22px] py-[25px] bg-[#FFFFFF] flex flex-col justify-start items-start sticky top-0 box-shadow"
    >
      <p>
        <span
          class="text-[14px] font-[600] text-[#333] leading-[20px] mb-[26px]"
        >支部星级评定</span>
      </p>
      <div
        class="w-full flex flex-row justify-between items-center border-b-[1px] py-[25px]"
      >
        <n-button size="small" @click="toBack">
          <template #icon>
            <n-icon>
              <IosArrowBack />
            </n-icon>
          </template>
          返回
        </n-button>
        <n-button
          size="small"
          type="primary"
          @click="submit(editorType as string)"
        >
          <template #icon>
            <n-icon v-if="editorType === 'modify'">
              <plus-round />
            </n-icon>
            <n-icon v-else>
              <export />
            </n-icon>
          </template>
          {{ ((editorType as string) === 'modify')?'保存':'导出' }}
        </n-button>
      </div>
      <div class="w-[300px] flex flex-row justify-start items-center pt-[15px]">
        <span class="block w-[80px]">党支部：</span>
        <n-cascader
          :options="organizationListTree"
          value-field="id"
          label-field="name"
          children-field="children"
          check-strategy="child"
          :show-path="false"
          clearable
          placeholder="请选择所属党组织"
          @update:value="handleUpdateValue"
        />
      </div>
    </div>
    <div
      class="w-full box-border px-[22px] py-[25px] flex flex-col justify-start items-start gap-y-[15px]"
    >
      <!-- 党建考核 -->
      <div
        v-for="(item, index) in dataInfo.data.evaluation"
        :key="index"
        class="flex flex-col justify-start items-start gap-y-[10px] w-[800px]"
      >
        <div>
          <span
            class="text-[14px] font-[600] text-[#333] leading-[20px] mb-[26px]"
          >{{ filterTitle(item.type) }}</span>
        </div>
        <template v-if="item.type === '3'">
          <div
            class="px-[30px] flex flex-col justify-start items-start gap-y-[15px] w-full"
          >
            <p>
              <span>{{ item.star }}</span>
            </p>
          </div>
        </template>
        <template v-else>
          <div
            class="px-[30px] flex flex-col justify-start items-start gap-y-[15px] w-full"
          >
            <p class="w-full flex flex-row justify-between items-center">
              <span>分值结果：{{ item.value }}</span>
              <span>排名情况：{{ item.rank }}</span>
            </p>
            <p>
              <span>总体评价内容：{{ item.evaluation }}</span>
            </p>
            <p>
              <span>存在不足与问题：{{ item.shortage }}</span>
            </p>
          </div>
        </template>
      </div>
      <!-- 复评定级 -->
      <div
        v-show="['modify','view'].includes(editorType as string) && dataInfo.data.evaluation.length"
        class="flex flex-col justify-start items-start gap-y-[10px]"
      >
        <div>
          <span
            class="text-[14px] font-[600] text-[#333] leading-[20px] mb-[26px]"
          >复评定级</span>
        </div>
        <div
          class="pl-[30px] flex flex-col justify-start items-start gap-y-[15px]"
        >
          <n-radio-group
            v-model:value="reEvaluationVal"
            name="radiogroup"
            :disabled="editorType === 'view'"
          >
            <n-space>
              <n-radio
                v-for="(item, index) in radioOption"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { CascaderOption } from 'naive-ui'
import { NButton } from 'naive-ui'
import { PlusRound } from '@vicons/material'
import { IosArrowBack } from '@vicons/ionicons4'
import { Export } from '@vicons/carbon'
import { useOrganizationListOptions } from '@/hooks/use-select-options'
import {
  addBranchRatingReEvaluation,
  exportRatingResult,
  getBranchRatingReEvaluation,
  updatedBranchRatingReEvaluation,
} from '@/services/publicity/vote/branchRating'
import type { ReEvaluationDataType } from '@/services/publicity/vote/branchRating/type'
import { downloadArrayBuffer } from '@/utils/downloader'
import { formatTimeStamp } from '@/utils/format'
const router = useRouter()
const route = useRoute()
const deptId = ref()
const reEvaluationVal = ref()
const editorType = ref(route.query.type)
const radioOption = computed(() => {
  return [
    {
      label: '一星党支部',
      value: '一星党支部',
    },
    {
      label: '二星党支部',
      value: '二星党支部',
    },
    {
      label: '三星党支部',
      value: '三星党支部',
    },
    {
      label: '四星党支部',
      value: '四星党支部',
    },
    {
      label: '五星党支部',
      value: '五星党支部',
    },
  ]
})
const { organizationListTree } = useOrganizationListOptions()

const handleUpdateValue = (v: string, option: CascaderOption) => {
  if (['党委', '党总支'].includes(option.org_type as string)) {
    window.$message.info('党委或党总支暂不支持筛选！')
    return
  }
  deptId.value = v
}
const starId = ref(route.query.starId || '')
const dataInfo = reactive<{ data: ReEvaluationDataType }>({
  data: {
    evaluation: [],
    star: '',
    starReId: '',
  },
})

const loadData = (deptId: string) => {
  getBranchRatingReEvaluation({ starId: starId.value as string, deptId }).then(
    (res) => {
      reEvaluationVal.value = res.star
      dataInfo.data = res
      dataInfo.data.evaluation.sort((a, b) => Number(a.type) - Number(b.type))
    },
  )
}

const filterTitle = (type: string) => {
  const nameList = [
    '项目维度一：党建考核',
    '项目维度二：绩效考核',
    '项目维度三：民主测评',
    '自评定级',
  ]
  return nameList[Number(type)]
}

const toBack = () => {
  if (editorType.value === 'view') {
    router.go(-1)
  } else {
    window.$dialog.warning({
      title: '提示',
      content: '当前数据未保存，确认返回吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        router.go(-1)
      },
    })
  }
}

const submit = (type: string) => {
  // type === view 导出
  if (type === 'view') {
    // 导出
    exportRatingResult(starId.value as string).then((res) => {
      downloadArrayBuffer(
        res,
        `支部星级评定-${formatTimeStamp(Date.now(), 'YYYYMMDDHHmmss')}`,
      )
      window.$message.success('导出成功')
    })
  } else {
    // 保存
    window.$dialog.warning({
      title: '提示',
      content: '确认保存复评定级吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        if (dataInfo.data.starReId) {
          // 更新复评
          updatedBranchRatingReEvaluation({
            id: dataInfo.data.starReId,
            star: reEvaluationVal.value,
          }).then((res) => {
            window.$message.success('更新成功')
            router.go(-1)
          })
        } else {
          // 添加复评
          addBranchRatingReEvaluation({
            starId: starId.value as string,
            deptId: deptId.value,
            star: reEvaluationVal.value,
          }).then((res) => {
            window.$message.success('保存成功')
            router.go(-1)
          })
        }
      },
    })
  }
}

const calcOrganizationListTree = reactive<{ list: any[] }>({ list: [] })

const formatterTree = (data: any) => {
  const obj = {
    ...data,
    disabled: !!['党委', '党总支'].includes(data.org_type),
  }
  if (obj.children) {
    obj.children = obj.children.map((item: any) => formatterTree(item))
  }
  return obj
}
watch(
  () => deptId.value,
  (newVal) => {
    loadData(newVal)
  },
)
watch(
  () => organizationListTree.value,
  (newVal) => {
    loadData(newVal[0].id as string)
    calcOrganizationListTree.list = organizationListTree.value.map((item) => {
      return formatterTree(item)
    })
  },
)
</script>

<style scoped lang="scss">
.box-shadow {
  box-shadow: 0px 2px 10px 2px rgba(232, 232, 232, 0.5);
}
</style>
