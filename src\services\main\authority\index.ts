// 权限-菜单管理相关接口
import qs from 'qs'
import { commonReq } from '../../request'
import type {
  IAuthorityResPagination,
  IDeptForm,
  IDeptTreeNode,
  IMenu,
  IResPagination,
  IRole,
  IRoleForm,
  IRoleQuery,
  IUser,
  IUserForm,
  IUserQuery,
  IUserQueryByDept,
  IVerifyUser,
  IVerifyUserQuery,
} from '../../../services'
export * from './types'

/* ======================================= 组织管理 ======================================= */
/**
 * 获取组织树
 */
export function getDeptTree() {
  return commonReq.get<IDeptTreeNode[]>({
    url: '/org-construction/organization/list',
  })
}

/**
 * 获取组织详情
 */
export function getDeptDetail(id: number) {
  return commonReq.get<IDeptTreeNode>({
    url: `/admin/admin/dept/${id}`,
  })
}

/**
 * 删除组织
 */
export function deleteDept(id: number) {
  return commonReq.delete<string>({
    url: `/admin/admin/dept/${id}`,
  })
}

/**
 * 新增组织
 */
export function postDept(data: IDeptForm) {
  return commonReq.post<string>({
    url: '/admin/admin/dept',
    data,
  })
}

/**
 * 编辑组织
 */
export function putDept(data: IDeptForm) {
  return commonReq.put<string>({
    url: '/admin/admin/dept',
    data,
  })
}

/* ======================================= 用户管理 ======================================= */
/**
 * 查询用户
 */
export function getUserList(params: IUserQuery) {
  return commonReq.get<IAuthorityResPagination<IUser>>({
    url: '/admin/admin/user/page',
    params,
  })
}

/**
 * 根据部门id和其它条件查询用户
 */
export function getUserListByDept(deptId: number, params: IUserQueryByDept) {
  return commonReq.get<IResPagination<IUser>>({
    url: `/admin/admin/dept/users/${deptId}`,
    params,
    interceptors: {
      requestInterceptor: (config) => {
        config.paramsSerializer = params =>
          qs.stringify(params, { arrayFormat: 'repeat' })
        return config
      },
    },
  })
}

/**
 * 新增用户
 */
export function postUser(data: IUserForm) {
  return commonReq.post<boolean>({
    url: '/admin/admin/user',
    data,
  })
}

/**
 * 修改用户
 */
export function putUser(data: IUserForm & { userId: number }) {
  return commonReq.put<boolean>({
    url: '/admin/admin/user',
    data,
  })
}

/**
 * 获取用户详情
 */
export function getUserDetail(id: number) {
  return commonReq.get<any>({
    url: `/admin/admin/user/${id}`,
  })
}

/**
 * 重置密码
 */
export function resetPassword(userId: number) {
  return commonReq.patch<string>({
    url: `/admin/admin/user/resetpassword/${userId}`,
  })
}

/**
 * 是否允许登录
 */
export function patchLoginLock(userId: number, lockFlag: '0' | '1') {
  return commonReq.patch<string>({
    url: `/admin/admin/user/sysuser/${userId}/${lockFlag}`,
  })
}

/**
 * 下载导入模板
 * 1用户、2部门
 */
export function getTemplate(type: 1 | 2) {
  return commonReq.get({
    url: `/admin/admin/user/file/${type}`,
    responseType: 'blob',
  })
}

/**
 * 导入用户
 */
export function importUsers(data: FormData) {
  return commonReq.post<string>({
    url: '/admin/admin/user/import',
    data,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  })
}

/* ======================================= 用户审核 ======================================= */
/**
 * 获取审核用户列表
 */
export function getVerifyUserList(params: IVerifyUserQuery) {
  return commonReq.get<IResPagination<IVerifyUser>>({
    url: '/admin/admin/user/verifyuser',
    params,
  })
}

/**
 * 通过待审核
 */
export function patchPassVerify(id: number) {
  return commonReq.patch<string>({
    url: `/admin/admin/user/verifyuser/pass/${id}`,
  })
}

/**
 * 忽略审核
 */
export function patchIgnoreVerify(id: number) {
  return commonReq.patch<string>({
    url: `/admin/admin/user/verifyuser/ignore/${id}`,
  })
}

/* ======================================= 菜单管理 ======================================= */
/**
 * 获取所有菜单
 */
export function getAllMenus() {
  return commonReq.get<IMenu[]>({
    url: '/upms/menu/tree_all',
  })
}

/**
 * 删除菜单
 */
export function deleteMenu(id: number) {
  return commonReq.delete<boolean>({
    url: `/upms/menu/${id}`,
  })
}

/**
 * 新增菜单
 */
export function postMenu(data: FormData) {
  return commonReq.post<string>({
    url: '/upms/menu',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 更新菜单
 */
export function updateMenu(data: FormData) {
  return commonReq.put<string>({
    url: '/upms/menu',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 获取菜单详情
 */
export function getMenuDetail(id: number) {
  return commonReq.get<IMenu>({
    url: `/upms/menu/${id}`,
  })
}

/* ======================================= 角色管理 ======================================= */
/**
 * 获取角色列表
 */
export function getRoleList(params: IRoleQuery) {
  return commonReq.get<IResPagination<IRole>>({
    url: '/admin/admin/role/page',
    params,
  })
}

/**
 * 删除角色
 */
export function deleteRole(id: number) {
  return commonReq.delete<boolean>({
    url: `/admin/admin/role/${id}`,
  })
}

/**
 * 新增角色
 */
export function postRole(data: IRoleForm) {
  return commonReq.post<string>({
    url: '/admin/admin/role',
    data,
  })
}

/**
 * 修改角色
 */
export function putRole(data: IRoleForm & { roleId: number }) {
  return commonReq.put<string>({
    url: '/admin/admin/role',
    data,
  })
}

/* ======================================= 其它 ======================================= */
/**
 * 获取所有机构（公司）
 */
export function getAllCompanys() {
  return commonReq.get<any[]>({
    url: '/admin/admin/dept/company',
  })
}

/**
 * 根据机构获取部门
 */
export function getDeptsByCompany(corpId: number) {
  return commonReq.get<any[]>({
    url: `/admin/admin/dept/dept/${corpId}`,
  })
}

/**
 * 获取所有职务、岗位、角色
 */
export function getOtherOptions() {
  return commonReq.get({
    url: '/admin/admin/user/user/info',
  })
}
