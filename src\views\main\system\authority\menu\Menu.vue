<!--
 * @Description: 权限管理-菜单
-->
<template>
  <table-container
    title="菜单管理"
    :show-delete="false"
    add-text="新增"
    :loading="loadingRef"
    :table-columns="tableColumns"
    :table-data="allMenus"
    :total="allMenus.length"
    :show-pagination="false"
    :show-add="judgePermission('sys_menu_add')"
    @page-change="handlePageChange"
    @click-add="handleClickAdd"
  />

  <custom-dialog
    v-model:show="showDialogRef"
    :title="dialogTitle"
    @confirm="handleConfirm"
  >
    <menu-form
      :id="idRef"
      ref="menuFormRef"
      style="padding: 20px"
      :parent-list="allMenus"
      :edit-type="editTypeRef"
      :parent-id="parentIdRef"
      @saved="handleSaved"
    />
  </custom-dialog>
</template>

<script setup lang="ts">
import { computed, h, onMounted, ref } from 'vue'

import type { DataTableColumns } from 'naive-ui'
import { NButton, NImage, NPopconfirm } from 'naive-ui'
import type { IMenu } from '../../../../../services'
import { deleteMenu } from '../../../../../services'
import MenuForm from './cpn/menu-form/MenuForm.vue'
import { MENU_DICT, menuTypes } from './config'
import useTableContainer from '@/hooks/use-table-container'
import useAuthorityStore from '@/store/main/authority/index'
import ifHasPermi, { judgePermission } from '@/directive/permission/ifHasPermi'

const showDialogRef = ref(false)
const editTypeRef = ref<'add' | 'modify'>('add')
const dialogTitle = computed(
  () => `${editTypeRef.value === 'add' ? '新增' : '编辑'}菜单`,
)
const idRef = ref<number>()
const parentIdRef = ref<number>(-1)
const menuFormRef = ref()
const authorityStore = useAuthorityStore()
const { loadingRef, handlePageChange } = useTableContainer(loadAllMenus)
const allMenus = computed(() => authorityStore.allMenus)

// 加载数据
function loadAllMenus() {
  if (!loadingRef.value) {
    loadingRef.value = true
    authorityStore.getAllMenusAction().then(() => {
      loadingRef.value = false
    })
  }
}
const tableColumns: DataTableColumns<IMenu> = [
  { key: 'name', title: '菜单名称' },
  {
    key: 'type',
    title: '菜单类型',
    render(row) {
      return menuTypes.find(item => Number(item.value) === Number(row.type))
        ?.label
    },
  },
  {
    key: 'icon',
    title: '图标',
    ellipsis: {
      tooltip: {
        contentStyle: { width: '400px', 'word-break': 'break-all' },
      },
    },
    render(row) {
      if (row.type === MENU_DICT.BTN) {
        return h(NImage, {
          src: '/src/assets/image/common_btn.svg',
          style: {
            height: '25px',
            width: '16px',
          },
        })
      } else {
        return h(NImage, {
          src: `/src/assets/icons/${row.icon}.svg`,
          style: {
            height: '25px',
            width: '16px',
          },
        })
      }
    },
  },
  { key: 'sortOrder', title: '排序' },
  { key: 'path', title: '路径' },
  {
    key: 'operation',
    title: '操作',
    render(row) {
      return [
        h(
          NButton,
          {
            text: true,
            type: 'primary',
            style: {
              marginRight: '16px',
              display: ifHasPermi('sys_menu_add'),
            },
            onClick: () => {
              parentIdRef.value = row.id
              editTypeRef.value = 'add'
              showDialogRef.value = true
            },
          },
          { default: () => '新增' },
        ),
        h(
          NButton,
          {
            text: true,
            type: 'primary',
            style: {
              marginRight: '16px',
              display: ifHasPermi('sys_menu_edit'),
            },
            onClick: () => {
              idRef.value = row.id
              editTypeRef.value = 'modify'
              showDialogRef.value = true
            },
          },
          { default: () => '修改' },
        ),
        h(
          NPopconfirm,
          {
            positiveText: '确定',
            onPositiveClick: () => {
              deleteMenu(row.id).then((res) => {
                if (res) {
                  window.$message.success('删除成功')
                  loadAllMenus()
                }
              })
            },
          },
          {
            trigger: () =>
              h(
                NButton,
                {
                  text: true,
                  type: 'primary',
                  style: {
                    display: ifHasPermi('sys_menu_del'),
                  },
                },
                { default: () => '删除' },
              ),
            default: () => '确定删除吗？',
          },
        ),
      ]
    },
  },
]

function handleClickAdd() {
  editTypeRef.value = 'add'
  parentIdRef.value = -1
  showDialogRef.value = true
}
function handleConfirm() {
  menuFormRef.value?.validateAndSave()
}
function handleSaved() {
  showDialogRef.value = false
  loadAllMenus()
}

onMounted(() => {
  loadAllMenus()
})
</script>
