<template>
  <div>
    <n-form
      v-if="formData.type !== 'update'"
      ref="libraryCategoryRef"
      require-mark-placement="left"
      label-placement="left"
      label-width="120px"
      :model="formData"
      :rules="formRules"
    >
      <n-form-item label="题库一级分类名称" path="categoryTitle">
        <n-input
          v-model:value="formData.categoryTitle"
          placeholder="请输入题库一级类别，不超过50个字"
          clearable
          maxlength="50"
          show-word-limit
          :disabled="formData.level == 1"
        />
      </n-form-item>
      <n-form-item
        v-if="formData.level === 1"
        label="题库二级分类名称"
        path="categoryChildTitle"
      >
        <n-input
          v-model:value="formData.categoryChildTitle"
          placeholder="请输入题库二级类别，不超过50个字"
          clearable
          maxlength="50"
          show-word-limit
        />
      </n-form-item>
    </n-form>

    <n-form
      v-else
      ref="libraryCategoryRef"
      require-mark-placement="left"
      label-placement="left"
      label-width="120px"
      :model="formData"
      :rules="formRules"
    >
      <n-form-item label="题库一级分类名称" path="categoryTitle">
        <n-input
          v-model:value="formData.categoryTitle"
          placeholder="请输入题库一级类别，不超过50个字"
          clearable
          maxlength="50"
          show-word-limit
          :disabled="formData.level === 2"
        />
      </n-form-item>
      <n-form-item
        v-if="formData.level === 2"
        label="题库二级分类名称"
        path="categoryChildTitle"
      >
        <n-input
          v-model:value="formData.categoryChildTitle"
          placeholder="请输入题库二级类别，不超过50个字"
          clearable
          maxlength="50"
          show-word-limit
        />
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormRules } from 'naive-ui'
interface IData {
  categoryTitle: string | null
  id: string | null
  level: string | number
  categoryChildTitle?: string | null
  type: string | null
}

const formData = ref<IData>({
  categoryTitle: '',
  id: '',
  level: '',
  categoryChildTitle: '',
  type: '',
})

const formRules: FormRules = {
  categoryTitle: {
    required: true,
    message: '请输入类别名称',
    trigger: 'input',
    type: 'string',
  },
  categoryChildTitle: {
    required: true,
    message: '请输入类别名称',
    trigger: 'input',
    type: 'string',
  },
}
const libraryCategoryRef = ref()

function handleValidate() {
  return new Promise((resolve, reject) => {
    libraryCategoryRef.value?.validate((errors: any) => {
      if (!errors) {
        resolve(true)
      }
      else {
        resolve(false)
      }
    })
  })
}

function handleSetFormData(data: any) {
  formData.value = {
    categoryTitle: data?.categoryTitle,
    id: '',
    level: data?.level,
    type: data?.type,
    categoryChildTitle: data?.categoryChildTitle,
  }
  if (data.originData.id) {
    formData.value.id = data.originData.id
  }
}

defineExpose({
  formData,
  handleValidate,
  handleSetFormData,
})
</script>

<style scoped lang="scss"></style>
