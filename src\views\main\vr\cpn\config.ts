import type { FormRules } from 'naive-ui'
import { isHttpOrHttpsLink } from '@/utils/utils'

export const formRules: FormRules = {
  title: [
    {
      required: true,
      message: '标题不能为空',
      trigger: 'input',
    },
  ],
  linkUrl: [
    {
      required: true,
      // message: '链接不能为空',
      validator(rule: any, value: any) {
        if (value === null || !isHttpOrHttpsLink(value)) {
          return new Error('请输入合法的链接地址')
        }
        return true
      },
      trigger: 'input',
    },
  ],
  coverUrl: {
    required: true,
    message: '请上传图片',
    trigger: 'change',
    type: 'string',
  },
  isOutside: {
    required: true,
    message: '请选择是否跳转到外部链接',
    trigger: 'change',
    type: 'number',
  },
  isHidden: {
    required: true,
    message: '请选择是否隐藏',
    trigger: 'change',
    type: 'number',
  },
}
