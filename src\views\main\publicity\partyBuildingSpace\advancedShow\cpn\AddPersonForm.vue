<script setup lang="ts">
import { NForm } from 'naive-ui'
import type { SelectOption } from 'naive-ui'
import { reactive } from 'vue'
import { formRulesForPerson } from './config'
import type { uploadFileItem } from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
import { uploadImg } from '@/services'
import {
  getAdvancedPersonItem,
  getPartyPersonList,
  postInsertAdvancedPersonItem,
  putEditorAdvancedPersonItem,
} from '@/services/publicity/advancedShow'
import type {
  AdvancedPersonItem,
  PartyOrganizationTreeItem,
} from '@/services/publicity/advancedShow/type'
import { useCurrentOrganizationListOptions } from '@/hooks/use-select-options'
import { formatTimeStamp } from '@/utils/format'

interface Props {
  belongTabType: number
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<AdvancedPersonItem>({
  id: '',
  userId: '',
  userName: '',
  year: null,
  epithet: '',
  orgName: '',
  avatarId: '',
  avatarUrl: '',
  recommandStatus: '0',
  topStatus: '0',
  publishTime: '',
  certificateId: '',
  orgId: '',
  deptId: '',
  type: props.belongTabType,
  content: '',
})

const formRef = ref<InstanceType<typeof NForm>>()
const { organizationCurrentListTree } = useCurrentOrganizationListOptions()
const treeObj = reactive<{ treeOptions: PartyOrganizationTreeItem[] }>({
  treeOptions: organizationCurrentListTree as any,
})

/** 获取组织ID */
const partyPersonObj = reactive<{
  partyPersonList: Array<{ label: string; value: string }>
}>({ partyPersonList: [] })
const handleUpdateValue = (v: string) => {
  formDataReactive.deptId = v
  getPartyPersonList({ id: v, pageNum: 1, pageSize: 99999 }).then((res) => {
    partyPersonObj.partyPersonList = res.records.map((item) => {
      return {
        label: item.trueName,
        value: item.userId,
      }
    })
  })
}
/** 返回党员列表 */
const PartyPersonListOptions = computed(() => partyPersonObj.partyPersonList)

/** 选择党员id */
const handleUpdatePartyPersonValue = (v: string, option: SelectOption) => {
  formDataReactive.userId = v
  formDataReactive.userName = option.label as string
}

onBeforeMount(() => {
  if (props.type === 'modify' && props.id) {
    getAdvancedPersonItem(props.id).then((res) => {
      formDataReactive.id = props.id
      formDataReactive.userId = res.userId
      formDataReactive.userName = res.userName
      formDataReactive.year = res.year
      formDataReactive.epithet = res.epithet
      formDataReactive.orgName = res.orgName
      formDataReactive.avatarId = res.avatarId
      formDataReactive.avatarUrl = res.avatarUrl
      formDataReactive.recommandStatus = res.recommandStatus
      formDataReactive.topStatus = res.topStatus
      formDataReactive.publishTime = res.publishTime
      formDataReactive.deptId = res.deptId
      formDataReactive.content = res.content
      handleUpdateValue(formDataReactive.deptId)
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // 新增或编辑最后确定发布时间
      formDataReactive.publishTime = formatTimeStamp(new Date().getTime())
      if (formDataReactive.id) {
        putEditorAdvancedPersonItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
      else {
        postInsertAdvancedPersonItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// 文件相关
const handleAvatarUrlDone = async(file: File) => {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      formDataReactive.avatarId = data.fileId
    }
  }
  catch (error) {}
}
const handleAvatarUrlDelete = () => {
  formDataReactive.avatarId = ''
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="140"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRulesForPerson"
  >
    <n-form-item label="组织名称：" path="deptId">
      <!-- <n-cascader
        v-model:value="formDataReactive.deptId"
        :options="treeObj.treeOptions"
        value-field="id"
        label-field="name"
        children-field="children"
        check-strategy="child"
        :show-path="false"
        clearable
        @update:value="handleUpdateValue"
      /> -->
      <n-tree-select
        v-model:value="formDataReactive.deptId"
        :options="treeObj.treeOptions"
        value-field="id"
        label-field="name"
        key-field="id"
        children-field="children"
        check-strategy="all"
        placeholder="请选择所属党组织"
        :show-path="false"
        clearable
        filterable
        @update:value="handleUpdateValue"
      />
    </n-form-item>
    <n-form-item label="党员选择：" path="userId">
      <n-select
        v-model:value="formDataReactive.userId"
        :options="PartyPersonListOptions"
        @update:value="handleUpdatePartyPersonValue"
      />
    </n-form-item>
    <n-form-item label="荣誉名称：" path="epithet">
      <n-input
        v-model:value="formDataReactive.epithet"
        clearable
        maxlength="20"
        show-count
      />
    </n-form-item>
    <n-form-item label="年度：" path="year">
      <n-date-picker
        v-model:formatted-value="formDataReactive.year"
        size="small"
        value-format="yyyy"
        placeholder="请选择年份"
        type="year"
        clearable
      />
    </n-form-item>
    <n-form-item label="是否置顶：" path="topStatus">
      <n-switch
        v-model:value="formDataReactive.topStatus"
        checked-value="1"
        unchecked-value="0"
      />
    </n-form-item>
    <n-form-item label="是否推荐：" path="recommandStatus">
      <n-switch
        v-model:value="formDataReactive.recommandStatus"
        checked-value="1"
        unchecked-value="0"
      />
    </n-form-item>
    <n-form-item span="24" label="照片：" path="avatarId">
      <ImgUploader
        v-model:oldImgUrl="formDataReactive.avatarUrl"
        :width="295"
        :height="413"
        :need-cropper="false"
        @done="handleAvatarUrlDone"
        @delete="handleAvatarUrlDelete"
      />
    </n-form-item>
    <n-form-item label="先进事迹：" path="content" required>
      <RichEditor
        v-model:value="formDataReactive.content"
        style="width: 100%"
        :rich-height="350"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped>
:deep(.n-upload-file-list .n-upload-file.n-upload-file--image-card-type) {
  width: 136px;
  height: 191px;
}
</style>
