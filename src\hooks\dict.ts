import { ref, toRefs } from 'vue'
import { dict } from '@/store/dict'
import { getDicts } from '@/services/system/Admin/index'

/**
 * 获取字典数据
 */
export function useDict(...args: any): any {
  const res = ref({})
  return (() => {
    args.forEach((dictType: String) => {
      // @ts-expect-error 根据string获取对应数组
      res.value[dictType] = []
      const dicts = dict().getDict(dictType)
      if (dicts) {
        // @ts-expect-error 根据string获取对应数组
        res.value[dictType] = dicts
      } else {
        getDicts(dictType).then((resp) => {
          // @ts-expect-error 根据string获取对应数组
          res.value[dictType] = resp.map((p: any) => ({
            label: p.label,
            value: p.value,
            elTagType: p.listClass,
            elTagClass: p.cssClass,
          }))
          // @ts-expect-error 根据string获取对应数组
          dict().setDict(dictType, res.value[dictType])
        })
      }
    })
    return toRefs(res.value)
  })()
}
