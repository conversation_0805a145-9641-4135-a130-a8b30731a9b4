import type { VNodeChild } from 'vue'
import { type DataTableColumns } from 'naive-ui'
// import { NImage } from 'naive-ui'
import type { NoticeItem } from '@/services/structure/branch-garden/types'

export function getTableColumns(
  optionColumnRenderer: (row: NoticeItem) => VNodeChild,
  handleUpdateValueRender: (row: NoticeItem) => VNodeChild,
): DataTableColumns<NoticeItem> {
  return [
    { type: 'selection' },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      key: 'title',
      title: '公告标题',
      width: '45%',
    },
    {
      key: 'updateTime',
      title: '更新时间',
      width: '20%',
      render: (row) => {
        return h('span', {
          innerHTML: row.updateTime,
        })
      },
    },
    {
      key: 'isTop',
      title: '是否置顶',
      width: '20%',
      render: row => handleUpdateValueRender(row),
    },

    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '10%',
      render: row => optionColumnRenderer(row),
    },
  ]
}
