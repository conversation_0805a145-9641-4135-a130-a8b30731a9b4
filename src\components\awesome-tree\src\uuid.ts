/*
 * @Description: 生成随机uuid
 * @Author: 朱备 <zhu<PERSON>@easestrategy.com>
 * @Date: 2021-10-30 14:57:30
 * @LastEditTime: 2021-10-30 15:03:17
 * @LastEditors: 朱备 <zhu<PERSON>@easestrategy.com>
 */
function uuid() {
  const s: string[] = []
  const hexDigits = '0123456789abcdef'
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4'
  s[19] = hexDigits.substr((Number(s[19]) & 0x3) | 0x8, 1)
  s[8] = s[13] = s[18] = s[23] = '-'

  return s.join('')
}

export default uuid
