import type { IUploadRes } from './types'
import { commonReq } from '@/services/request'

/**
 * 上传文件
 */
export function uploadFile(
  file: File,
  onUploadProgress?: (e: ProgressEvent) => void,
) {
  const formData = new FormData()
  formData.append('file', file)
  return commonReq.post<IUploadRes>({
    url: '/admin/sys-file/upload',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
    onUploadProgress,
  })
}

/**
 * 上传课时视频
 */
export function uploadHugeFile(
  file: File,
  lessonId: number,
  type: number,
  onUploadProgress?: (e: ProgressEvent) => void,
) {
  const formData = new FormData()
  formData.append('file', file)
  return commonReq.post({
    url: `/admin/oss/bigfile/upload/${type}/${lessonId}`,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
    onUploadProgress,
  })
}

/**
 * 下载文件
 */
export function downloadFile(url: string) {
  return commonReq.get({
    url,
    responseType: 'blob',
  })
}
