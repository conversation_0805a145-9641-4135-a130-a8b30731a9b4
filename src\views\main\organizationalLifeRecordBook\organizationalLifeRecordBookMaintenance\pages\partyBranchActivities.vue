<script lang="ts" setup>
import { AddTwotone, DownloadRound } from '@vicons/material'
import type { TreeSelectOption } from 'naive-ui'
import { NButton, NForm, NIcon, NInput } from 'naive-ui'
import { onMounted, ref } from 'vue'
import { useRecordBookStore } from '@/store/recordBook'
import {
  addPartyBranchActivity,
  deletePartyBranchActivity,
  editPartyBranchActivity,
  getPartyBranchActivityDetail,
  getPartyBranchActivityList,
  getUserByDepartmentId,
} from '@/services/recordBookMaintenance'
import type {
  BranchBasicOrganizationConditionUserType,
  PartyBranchActivityType,
  PartyMemberActivityType,
} from '@/services/recordBookMaintenance/types'
import { getPartyBranchActivitiesRecordsColumns } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/config'
import { partyBranchActivitiesRecordsFormRules } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/formRules'

const FormRef = ref<InstanceType<typeof NForm>>()
const recordBookStore = useRecordBookStore()
const currentYearOptions = computed(() => {
  return recordBookStore.getYearOptions
})

const fileLoading = ref<boolean>(false)
const drawerTitle = ref<string>('新增')
const actionType = ref<'add' | 'edit' | 'view'>('add')
const showAddModal = ref<boolean>(false)
const pageNum = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const total = ref(0)
const selectedDeptId = ref<string | null>(null)
const selectedYear = ref<string | null>(null)
const currentDownloadFileType = ref<'word' | 'pdf'>('word')
const showPopConfirm = ref<boolean>(false)
const formDataRef = ref<
PartyBranchActivityType & {
  minutesTime?: Array<string> | null
  absenteeIdList: string[]
}
>({
  minutesTime: null,
  startTime: null,
  endTime: null,
  location: null,
  content: '',
  absenteeList: [],
  absenteeIdList: [],
})

const userList = ref<BranchBasicOrganizationConditionUserType[]>([])
const userOptions = computed(() => {
  return userList.value.map((item) => {
    return {
      label: item.trueName,
      value: item.userId,
    }
  })
})

const tableData = ref<PartyMemberActivityType[]>([])
const tableColumns: any = getPartyBranchActivitiesRecordsColumns((row) => {
  return [
    h(
      'div',
      {
        class: 'flex flex-row gap-x-[10px]',
      },
      {
        default: () => [
          h(
            NButton,
            {
              text: true,
              color: '#AC241D',
              onClick: () => {
                actionType.value = 'view'
                drawerTitle.value = '查看'
                getPartyBranchActivityDetail(row.id!).then((res) => {
                  formDataRef.value = res as any
                  formDataRef.value.minutesTime = [
                    res.startTime as string,
                    res.endTime as string,
                  ]
                  formDataRef.value.absenteeIdList = res.absenteeList.map(
                    (user: any) => user.userId,
                  )
                })
                showAddModal.value = true
              },
            },
            {
              default: () => '查看',
            },
          ),
          h(
            NButton,
            {
              text: true,
              color: '#AC241D',
              onClick: () => {
                actionType.value = 'edit'
                drawerTitle.value = '编辑'
                getPartyBranchActivityDetail(row.id!).then((res) => {
                  formDataRef.value = res as any
                  formDataRef.value.minutesTime = [
                    res.startTime as string,
                    res.endTime as string,
                  ]
                  formDataRef.value.absenteeIdList = res.absenteeList.map(
                    (user: any) => user.userId,
                  )
                })
                showAddModal.value = true
              },
            },
            {
              default: () => '编辑',
            },
          ),
          h(
            NButton,
            {
              text: true,
              color: '#AC241D',
              onClick: () => {
                window.$dialog.create({
                  type: 'default',
                  closable: false,
                  content: '确认删除该条记录？',
                  showIcon: false,
                  positiveText: '确认',
                  negativeText: '取消',
                  onPositiveClick: () => {
                    deletePartyBranchActivity(row.id!).then(() => {
                      window.$message.success('删除成功！')
                      loadData()
                    })
                  },
                })
              },
            },
            {
              default: () => '删除',
            },
          ),
        ],
      },
    ),
  ]
})

const absenteeListColumns = [
  {
    key: 'id',
    title: '序号',
    width: '80',
    render: (_: any, i: number) => i + 1,
  },
  {
    key: 'absentee',
    width: '120',
    title: '姓名',
  },
  {
    key: 'reason',
    title: '缺席原因',
    render: (row: any) => {
      return h(
        NInput,
        {
          value: row.reason,
          showCount: true,
          maxlength: 10,
          disabled: actionType.value === 'view',
          onUpdateValue: (v) => {
            row.reason = v
          },
        },
        {},
      )
    },
  },
]

const calcOrganizationListTree = computed(() => {
  return recordBookStore.getOrganizationListTreeList
})

const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})
// 获取当前年份
const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  pageNum.value = 1
  selectedDeptId.value = v
  loadData()
  fetchUserList()
  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearSelectedValue(v: string) {
  if (!v) {
    return
  }
  pageNum.value = 1
  selectedYear.value = v
  loadData()
  recordBookStore.setCurrentSelectedYear(v || '')
}

function handleDownLoadFile() {
  fileLoading.value = true
  recordBookStore.fetchDownLoadFile(
    {
      deptId: selectedDeptId.value!,
      year: selectedYear.value!,
      type: currentDownloadFileType.value,
    },
    () => {
      fileLoading.value = false
    },
  )

  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleCancelShowPopConfirm() {
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function initFormData() {
  formDataRef.value = {
    startTime: null,
    endTime: null,
    location: null,
    content: '',
    absenteeList: [],
    absenteeIdList: [],
  }
}

function handleAddPartyMember() {
  initFormData()
  actionType.value = 'add'
  drawerTitle.value = '新增'
  showAddModal.value = true
}

watch(
  () => pageNum.value,
  () => {
    loadData()
  },
)

watch(
  () => pageSize.value,
  () => {
    pageNum.value = 1
    loadData()
  },
)

function loadData() {
  if (!selectedDeptId.value || !selectedYear.value) {
    return
  }
  loading.value = true
  getPartyBranchActivityList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
  })
    .then((res) => {
      total.value = res.total || 0
      tableData.value = (res.records as any) || []
    })
    .finally(() => {
      loading.value = false
    })
}

const isDateDisabled = (ts: number) => {
  const date = new Date(ts)
  const year = date.getFullYear()

  // 如果年份不是目标年份，禁用该日期
  return year !== Number(selectedYear.value)
}

function handleConfirm() {
  FormRef.value?.validate((errors: any) => {
    if (!errors) {
      if (actionType.value === 'add') {
        addPartyBranchActivity({
          ...formDataRef.value,
          deptId: selectedDeptId.value!,
          year: selectedYear.value!,
        }).then(() => {
          window.$message.success('添加成功！')
          loadData()
        })
      }
      else if (actionType.value === 'edit') {
        editPartyBranchActivity({ ...formDataRef.value }).then(() => {
          window.$message.success('修改成功！')
          loadData()
        })
      }

      FormRef.value?.restoreValidation() // 重置验证状态
      showAddModal.value = false
    }
  })
}

function fetchUserList() {
  getUserByDepartmentId({ deptId: selectedDeptId.value! }).then((res) => {
    userList.value = res || []
  })
}

watch(
  () => formDataRef.value.minutesTime,
  () => {
    if (
      formDataRef.value.minutesTime
      && formDataRef.value.minutesTime.length > 1
    ) {
      const startTime = formDataRef.value.minutesTime[0]
      const endTime = formDataRef.value.minutesTime[1]
      formDataRef.value.startTime = startTime
      formDataRef.value.endTime = endTime
    }
  },
  {
    deep: true,
  },
)

watch(
  () => formDataRef.value.absenteeIdList,
  () => {
    if (userList.value && userList.value.length) {
      // 确保list顺序, 判断absenteeIdList的长度
      if (formDataRef.value.absenteeIdList.length) {
        formDataRef.value.absenteeList = formDataRef.value.absenteeIdList.map(
          (userItem) => {
            const findData = userList.value.find(
              (item: any) => item.userId === userItem,
            ) // 找到姓名的原始数据
            const findReasonData = formDataRef.value.absenteeList.find(
              (item: any) => item.userId === userItem,
            ) // 找到原因的原始数据
            return {
              userId: findData?.userId,
              absentee: findData?.trueName,
              reason: findReasonData?.reason || '',
            }
          },
        )
      }
    }
  },
)

onMounted(() => {
  selectedDeptId.value = currentSelectedDept.value.deptId
  selectedYear.value = currentYear.value
  loadData()
  fetchUserList()
})
</script>

<template>
  <div>
    <table-container
      v-model:page="pageNum"
      v-model:page-size="pageSize"
      :loading="loading"
      :show-delete="false"
      :show-pagination="true"
      :show-title="true"
      :show-toolbar="false"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      default-expand-all
      title="党支部参加党委（总支）组织的活动情况登记"
    >
      <template #row>
        <div
          class="w-[100%] flex flex-row items-start justify-between gap-[10px] pb-[10px]"
        >
          <div class="flex flex-row gap-x-[10px] justify-start items-start">
            <n-tree-select
              v-model:value="selectedDeptId"
              :options="calcOrganizationListTree"
              :show-path="false"
              check-strategy="all"
              children-field="children"
              filterable
              key-field="id"
              label-field="name"
              placeholder="请选择所属党组织"
              style="width: 400px"
              value-field="id"
              @update:value="handleUpdateTreeSelectedValue"
            />
            <n-select
              v-model:value="selectedYear"
              :options="currentYearOptions"
              placeholder="请选择年份"
              size="medium"
              style="width: 200px"
              @update:value="handleUpdateYearSelectedValue"
            />
          </div>

          <div class="flex flex-row gap-x-[10px] justify-start items-start">
            <n-button type="primary" @click="handleAddPartyMember">
              <template #icon>
                <n-icon size="16">
                  <AddTwotone />
                </n-icon>
              </template>
              新增
            </n-button>
            <n-popconfirm
              :show="showPopConfirm"
              :show-icon="false"
              placement="bottom"
            >
              <template #trigger>
                <n-button
                  :loading="fileLoading"
                  @click="showPopConfirm = !showPopConfirm"
                >
                  <template #icon>
                    <n-icon size="16">
                      <DownloadRound />
                    </n-icon>
                  </template>
                  下载
                </n-button>
              </template>
              <div class="py-[10px]">
                <n-radio-group v-model:value="currentDownloadFileType">
                  <n-radio
                    v-for="(fileType, fileTypeIndex) in [
                      { label: 'word', value: 'word' },
                      { label: 'pdf', value: 'pdf' },
                    ]"
                    :key="fileTypeIndex"
                    :value="fileType.value"
                  >
                    {{ fileType.label }}
                  </n-radio>
                </n-radio-group>
              </div>

              <template #action>
                <div class="w-[100%] flex flex-row justify-center items-center">
                  <n-button
                    size="small"
                    type="primary"
                    @click="handleDownLoadFile(item)"
                  >
                    确定
                  </n-button>
                  <n-button size="small" @click="handleCancelShowPopConfirm">
                    取消
                  </n-button>
                </div>
              </template>
            </n-popconfirm>
          </div>
        </div>
      </template>
    </table-container>

    <n-drawer v-model:show="showAddModal" :width="502" placement="right">
      <n-drawer-content :title="drawerTitle">
        <n-form
          ref="FormRef"
          :disabled="actionType === 'view'"
          :model="formDataRef"
          :rules="partyBranchActivitiesRecordsFormRules"
          label-placement="left"
          label-width="100"
        >
          <n-form-item label="时间：" path="minutesTime">
            <n-date-picker
              v-model:formatted-value="formDataRef.minutesTime"
              :is-date-disabled="isDateDisabled"
              clearable
              placeholder="请选择时间"
              style="width: 100%"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </n-form-item>
          <n-form-item label="地点：" path="location">
            <n-input
              v-model:value="formDataRef.location"
              clearable
              maxlength="50"
              placeholder="请输入地点"
              show-count
            />
          </n-form-item>
          <n-form-item label="活动内容：" path="content">
            <n-input
              v-model:value="formDataRef.content"
              :rows="20"
              maxlength="500"
              placeholder="请输入活动内容"
              show-count
              type="textarea"
            />
          </n-form-item>

          <n-form-item label="缺席人员：" path="absenteeIdList">
            <n-select
              v-model:value="formDataRef.absenteeIdList"
              :options="userOptions"
              clearable
              max-tag-count="responsive"
              multiple
              placeholder="请选择缺席人员"
            />
          </n-form-item>
          <div>
            <n-data-table
              :columns="absenteeListColumns"
              :data="formDataRef.absenteeList"
            />
          </div>
        </n-form>
        <template #footer>
          <div
            class="w-[100%] flex flex-row justify-center items-center gap-x-[10px]"
          >
            <n-button @click="showAddModal = false">
              取消
            </n-button>
            <n-button type="primary" @click="handleConfirm">
              确定
            </n-button>
          </div>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<style lang="scss" scoped></style>
