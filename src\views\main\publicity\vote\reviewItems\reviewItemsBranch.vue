<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="民主评议项-支部班子"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumnsOfPartyMember"
    :table-data="tableData"
    :total="total"
    :show-delete="false"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button>
    </template>
  </table-container>
</template>

<script setup lang="ts">
import { NButton } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import { columnsOfReviewItems } from './config'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import {
  delReviewItemListItem,
  getReviewitemsBranchList,
  reviewItemtoBeIssued,
} from '@/services/publicity/vote/talkAbout'

const filterReactive = ref<{ title?: string; name?: string }>({
  title: '',
  name: '',
})
const router = useRouter()
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getReviewitemsBranchList, filterReactive, {
  batchDeleteTable: true,
  delApi: delReviewItemListItem,
})

watch(filterReactive.value, () => {
  loadData()
})

// 新增/编辑明主评议抽屉
const addTalkAboutFormRef = ref()
const { showEditRef, editTypeRef } = useDrawerEdit(
  '民主评议',
  handelConfirmEdit,
)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  router.push({
    name: 'review-items-branch-detail',
    query: {
      action: 'add',
    },
  })
}
/** 确定保存 */
function handelConfirmEdit() {
  addTalkAboutFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addTalkAboutFormRef.value?.resetForm()
  }
})

// 修改和删除按钮渲染
const tableColumnsOfPartyMember = columnsOfReviewItems((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          router.push({
            name: 'review-items-branch-detail',
            query: {
              id: row.id,
              action: 'view',
            },
          })
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      '查看',
    ),
    h(
      NButton,
      {
        onClick: () => {
          reviewItemtoBeIssued({ reviewItemId: row.id as string }).then(
            (res) => {
              if (res) {
                window.$message.error('该评议项已关联已下发问卷，不可修改！')
              } else {
                router.push({
                  name: 'review-items-branch-detail',
                  query: {
                    id: row.id,
                    action: 'modify',
                  },
                })
              }
            },
          )
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      '修改',
    ),
    h(DeleteButton, {
      handleConfirm: async() => {
        await reviewItemtoBeIssued({ reviewItemId: row.id as string }).then(
          async(res) => {
            if (res) {
              window.$message.error('该评议项已关联已下发问卷，不可删除！')
            } else {
              await handleSingleDelete(String(row.id))
            }
          },
        )
      },
    }),
    h(
      NButton,
      {
        onClick: () => {
          router.push({
            name: 'review-items-branch-detail',
            query: {
              id: row.id,
              action: 'copy',
            },
          })
        },
        type: 'primary',
        text: true,
        style: {
          marginLeft: '10px',
        },
      },
      '复制新建',
    ),
  ]
})
onMounted(loadData)
</script>

<style lang="scss" scoped></style>
