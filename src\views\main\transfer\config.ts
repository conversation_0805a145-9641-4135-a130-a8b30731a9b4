import type { VNodeChild } from 'vue'
import type { DataTableColumns, FormRules } from 'naive-ui'
import type {
  ApprovalNodesType,
  TransferTableItemType,
} from '@/services/transfer/types'

// 调动类型
export const transferType = ref([
  {
    label: '系统内调入',
    value: 0,
  },
  {
    label: '系统内调出',
    value: 1,
  },
  {
    label: '调出系统外',
    value: 2,
  },
  {
    label: '系统外调入',
    value: 3,
  },
])

// 状态枚举
export const statusOptions = ref([
  {
    label: '待审批',
    value: 0,
  },
  {
    label: '已驳回',
    value: 1,
  },
  {
    label: '已撤销',
    value: 2,
  },
  {
    label: '已完成',
    value: 3,
  },
])

// 适用于 由我发起 列表的列字段
export function getTableColumns(
  optionColumnRenderer: (row: TransferTableItemType) => VNodeChild,
): DataTableColumns<TransferTableItemType> {
  return [
    {
      key: 'transferType',
      title: '调动类型',
      width: '100',
    },
    {
      key: 'transferTime',
      title: '调动时间',
      width: '160',
    },
    {
      key: 'userName',
      title: '姓名',
      width: '120',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'oldDeptName',
      title: '原组织',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'newDeptName',
      title: '接收组织',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'joinTime',
      title: '入党时间',
      width: '160',
    },
    {
      key: 'paymentTime',
      title: '党费缴纳时间',
      width: '100',
    },
    {
      key: 'phaseStatus',
      title: '状态',
      width: '100',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '120',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 适用于 待审核 已审核 列表的列
export function getTableColumnsOfCheck(
  optionColumnRenderer: (row: TransferTableItemType) => VNodeChild,
): DataTableColumns<TransferTableItemType> {
  return [
    {
      key: 'transferType',
      title: '调动类型',
      width: '100',
    },
    {
      key: 'transferTime',
      title: '调动时间',
      width: '160',
    },
    {
      key: 'userName',
      title: '姓名',
      width: '120',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'oldDeptName',
      title: '原组织',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'newDeptName',
      title: '接收组织',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'joinTime',
      title: '入党时间',
      width: '160',
    },
    {
      key: 'paymentTime',
      title: '党费缴纳时间',
      width: '100',
    },
    {
      key: 'phaseStatus',
      title: '状态',
      width: '100',
    },
    {
      key: 'approvalStatus',
      title: '审批状态',
      width: '100',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '120',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 适用于审核页审批流程表格列
export function getTableColumnsOfCheckDetail(
  approvalResultRenderer: (row: ApprovalNodesType) => VNodeChild,
  approverRenderer: (row: ApprovalNodesType) => VNodeChild,
  approvalPhoneRenderer: (row: ApprovalNodesType) => VNodeChild,
): DataTableColumns<ApprovalNodesType> {
  return [
    {
      key: 'approvalResult',
      title: '',
      width: '60px',
      render: approvalResultRenderer,
    },
    {
      key: 'approveNode',
      title: '审批节点名称',
    },
    {
      key: 'username',
      title: '审批人',
      render: approverRenderer,
    },
    {
      key: 'phone',
      title: '联系电话',
      render: approvalPhoneRenderer,
    },
    {
      key: 'approvalStatus',
      title: '审核状态',
    },
    {
      key: 'approvalComment',
      title: '审核意见',
    },
  ]
}

export const formRules: FormRules = {
  approvalResult: {
    required: true,
    message: '审核结果不能为空',
    trigger: 'change',
  },
  approvalComment: {
    required: true,
    message: '审核意见不能为空',
    trigger: 'change',
  },
}

export function approvalColumn(
  approverRenderer: (row: ApprovalNodesType) => VNodeChild,
): DataTableColumns<ApprovalNodesType> {
  return [
    {
      key: 'approveNode',
      title: '审批节点名称',
    },
    {
      key: 'username',
      title: '审批人',
      render: approverRenderer,
    },
    {
      key: 'approvalStatus',
      title: '审核状态',
    },
    {
      key: 'approvalResult',
      title: '审核结果',
    },
    {
      key: 'approvalComment',
      title: '审核意见',
    },
  ]
}
