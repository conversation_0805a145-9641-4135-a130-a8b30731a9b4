<script setup lang="ts">
import { NForm } from 'naive-ui'
import { reactive } from 'vue'
import type { uploadFileItem } from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
import RichEditor from '@/components/RichEditor.vue'
import { uploadImg } from '@/services'
import type { InsertCommonParamsOfCompanyAndRegulation } from '@/services/publicity/companyShow/types'
import {
  getCompanyShowListItem,
  postInsertCompanyShowListItem,
  putEditorCompanyShowListItem,
} from '@/services/publicity/companyShow'
import { formatTimeStamp } from '@/utils/format'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<InsertCommonParamsOfCompanyAndRegulation>({
  id: '',
  title: '',
  module: [
    {
      id: '',
      moduleName: '',
      content: '',
    },
  ],
  publishTime: '',
  topStatus: '0',
  cover: { id: '', url: '' },
  appCover: { id: '', url: '' },
  customCover: { id: '', url: '' },
  customAppCover: { id: '', url: '' },
  categoryId: '',
  location: '',
  fileList: [],
  activityMemberId: [],
  type: '0',
})

const formRef = ref<InstanceType<typeof NForm>>()
const queryDetail = (id: string) => {
  getCompanyShowListItem({ id: props.id, type: 0 }).then((res) => {
    formDataReactive.location = res.location
    formDataReactive.categoryId = res.categoryId
    formDataReactive.appCover = JSON.parse(JSON.stringify(res.appCover))
    formDataReactive.cover = JSON.parse(JSON.stringify(res.cover))
    formDataReactive.customAppCover = JSON.parse(JSON.stringify(res.appCover))
    formDataReactive.customCover = JSON.parse(JSON.stringify(res.cover))
    formDataReactive.topStatus = res.topStatus
    formDataReactive.publishTime = res.publishTime
    formDataReactive.activityMemberId = res.activityMemberId || []
    formDataReactive.fileList = res.fileList || []
    formDataReactive.module = res.module || []
    formDataReactive.title = res.title
    formDataReactive.id = props.id
  })
}

onBeforeMount(() => {
  if (props.type === 'modify' && props.id) {
    queryDetail(props.id)
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      // 新增或编辑最后确定发布时间
      formDataReactive.publishTime = formatTimeStamp(new Date().getTime())
      if (formDataReactive.id) {
        putEditorCompanyShowListItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
      else {
        postInsertCompanyShowListItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// app封面图片上传
const handleAppCoverDone = async(file: File) => {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formDataReactive.appCover.id) {
      return
    }
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      formDataReactive.customAppCover!.id = data.fileId || ''
      formDataReactive.appCover.id = data.fileId || ''
      formDataReactive.appCover.url = ''
    }
  }
  catch (error) {}
}
// app封面图片删除操作
const handleAppCoverDelete = () => {
  formDataReactive.customAppCover!.id = ''
  formDataReactive.appCover.id = ''
  formDataReactive.appCover.url = ''
}

// PC封面图片上传
const handleCoverDone = async(file: File) => {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formDataReactive?.cover?.id) {
      return
    }
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      formDataReactive.customCover!.id = data.fileId || ''
      formDataReactive.cover!.id = data.fileId || ''
      formDataReactive.cover!.url = ''
    }
  }
  catch (error) {}
}
// PC封面图片删除操作
const handleCoverDelete = () => {
  formDataReactive.customCover!.id = ''
  formDataReactive.cover!.id = ''
  formDataReactive.cover!.url = ''
}

const addModule = () => {
  formDataReactive.module.push({ id: '', moduleName: '', content: '' })
}
const delModuleItem = (index: number) => {
  formDataReactive.module.splice(index, 1)
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
  >
    <n-card>
      <n-form-item label="公司名称：" path="title">
        <n-input
          v-model:value="formDataReactive.title"
          placeholder="请输入公司名称"
          clearable
        />
      </n-form-item>
      <n-form-item label="是否置顶：" path="topStatus">
        <n-switch
          v-model:value="formDataReactive.topStatus"
          checked-value="1"
          unchecked-value="0"
        />
      </n-form-item>
      <n-form-item span="24" label="APP封面图片：" path="customAppCover">
        <ImgUploader
          v-model:oldImgUrl="formDataReactive.customAppCover!.url"
          :width="194"
          :height="256"
          :need-cropper="false"
          @done="handleAppCoverDone"
          @delete="handleAppCoverDelete"
        />
      </n-form-item>

      <n-form-item span="24" label="PC封面图片：" path="customCover">
        <ImgUploader
          :width="194"
          :height="256"
          :need-cropper="false"
          :old-img-url="formDataReactive.customCover!.url"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </n-form-item>
      <n-card
        v-for="(item, index) in formDataReactive.module"
        :key="index"
        :title="'请输入模块' + (index + 1) + '的标题和内容介绍'"
      >
        <n-form-item>
          <n-input
            v-model:value="item.moduleName"
            placeholder="请输入标题"
            clearable
          />
        </n-form-item>
        <n-form-item span="24" label-width="80" path="module[0].content">
          <RichEditor
            v-model:value="item.content"
            style="width: 100%"
            :rich-height="350"
          />
        </n-form-item>
        <n-button v-show="index" @click="delModuleItem(index)">
          删除
        </n-button>
      </n-card>
    </n-card>
    <n-form-item label-width="40" path="title">
      <n-button @click="addModule">
        添加展示模块
      </n-button>
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
