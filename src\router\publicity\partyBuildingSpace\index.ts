import type { RouteRecordRaw } from 'vue-router'

const partyBuildingSpaceRoute: RouteRecordRaw = {
  path: 'partyBuildingSpace',
  name: 'party-building-space',
  meta: { title: '党建园地', icon: 'dues', category: 'publicity', order: 3 },
  redirect: { name: 'company-Index' },
  component: () =>
    import('@/views/main/publicity/partyBuildingSpace/index.vue'),
  children: [
    // 公司展示
    {
      path: 'companyIndex',
      name: 'company-Index',
      meta: { title: '公司展示', icon: 'role' },
      redirect: { name: 'company-show' },
      component: () =>
        import(
          '@/views/main/publicity/partyBuildingSpace/companyShow/index.vue'
        ),
      children: [
        {
          path: 'companyShow',
          name: 'company-show',
          meta: { title: '公司展示', icon: 'role' },
          component: () =>
            import(
              '@/views/main/publicity/partyBuildingSpace/companyShow/companyShow.vue'
            ),
        },
        // 添加公司展示
        {
          path: 'addCompanyShow/:id',
          name: 'add-company-show',
          meta: { title: '添加公司展示', icon: 'role' },
          component: () =>
            import(
              '@/views/main/publicity/partyBuildingSpace/companyShow/addCompanyShow.vue'
            ),
        },
      ],
    },
    // 制度法规
    // {
    //   path: 'regulation',
    //   name: 'regulation-page',
    //   meta: { title: '制度法规', icon: 'AppleOutlined' },
    //   component: () =>
    //     import(
    //       '@/views/main/publicity/partyBuildingSpace/regulation/regulation.vue'
    //     ),
    // },
    // 近期活动
    // {
    //   path: 'recentActivity',
    //   name: 'recent-activity',
    //   meta: { title: '近期活动', icon: 'CarTwotone' },
    //   component: () =>
    //     import(
    //       '@/views/main/publicity/partyBuildingSpace/recentActivity/recentActivity.vue'
    //     ),
    // },
    // 先进展示
    {
      path: 'advancedShow',
      name: 'advanced-show',
      meta: { title: '先进展示', icon: 'role' },
      component: () =>
        import(
          '@/views/main/publicity/partyBuildingSpace/advancedShow/advancedShow.vue'
        ),
    },
  ],
}
export default partyBuildingSpaceRoute
