<!-- 文件上传组件 -->
<template>
  <div class="file-uploader">
    <n-upload
      v-model:file-list="fileListRef"
      :accept="accept"
      :disabled="isReadonly"
      :max="props.max"
      abstract
      @change="handleChange"
    >
      <n-upload-trigger #="{ handleClick }" abstract>
        <div style="display: flex; align-items: center; margin-bottom: 15px">
          <n-button
            :disabled="fileListRef.length >= props.max || disabled"
            class="trigger-btn"
            size="small"
            @click="handleClick"
          >
            <template #icon>
              <n-icon size="16">
                <file-upload-outlined />
              </n-icon>
            </template>
            点击上传
          </n-button>

          <span
            v-if="showTip"
            style="
              font-size: 12px;
              font-weight: 400;
              color: #999999;
              margin-left: 20px;
            "
          >
            <span v-if="props.customTipsText">{{ props.customTipsText }}</span>
            <span v-else>支持{{ accept }}文件</span>
          </span>
        </div>
      </n-upload-trigger>
    </n-upload>

    <div v-for="(file, index) in fileListRef" :key="file.id" class="file-body">
      <div class="file-item">
        <span class="file-bg">
          <span>{{
            getCoverByExtension(getExtension(file.name || file.original))
          }}</span>
          <img v-if="file.url && needProgress" src="@/assets/image/done.png" />
        </span>

        <span v-if="(file as any).editing">
          <n-input
            v-model:value="fileNameRef"
            size="small"
            @blur="() => handleSaveFileName(index)"
          />
          <span>{{ fileExtRef }}</span>
        </span>

        <n-ellipsis v-else class="file-name">
          {{ file.name || file.original }}
        </n-ellipsis>

        <span class="btns">
          <n-button
            v-show="!isReadonly"
            size="large"
            text
            @click="handleClickDelete(index)"
          >
            <template #icon>
              <n-icon>
                <delete-outlined />
              </n-icon>
            </template>
          </n-button>

          <n-button
            v-show="true"
            size="large"
            text
            @click="handleClickDown(index)"
          >
            <template #icon>
              <n-icon>
                <ArrowDownwardRound />
              </n-icon>
            </template>
          </n-button>
        </span>

        <n-progress
          v-if="needProgress && progress"
          :height="3"
          :percentage="progress"
          :show-indicator="false"
          class="progress-bar"
          status="success"
        />

        <n-progress
          v-else-if="needProgress && !file.url"
          :height="3"
          :percentage="(file.percentage as number)"
          :show-indicator="false"
          class="progress-bar"
          status="success"
        />
      </div>

      <n-checkbox
        v-if="showDownloadController"
        :value="1"
        disabled
        label="允许下载"
      />
    </div>

    <div v-if="accept !== '*/*'" class="tips-ctn">
      <slot name="tips"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import type { UploadFileInfo } from 'naive-ui'
import {
  NButton,
  NCheckbox,
  NEllipsis,
  NIcon,
  NInput,
  NProgress,
  NUpload,
  NUploadTrigger,
} from 'naive-ui'
import {
  ArrowDownwardRound,
  DeleteOutlined,
  FileUploadOutlined,
} from '@vicons/material'
import type { uploadFileItemNew } from '@/services/affairs/party-building-list/exam-indicators/types'
import { downloadFile } from '@/utils/downloader'

interface FileInfoType {
  fileName: string
  id: string
  original: string
  fileSize?: any
  uploadTime?: any
  fileType?: any
}

interface Props {
  max: number
  showDownloadController?: boolean
  accept: string
  sizeLimit: number
  // originalFileList: PropType<UploadFileInfo[]>
  originalFileList: FileInfoType[]
  showTip?: boolean
  needProgress: boolean
  uploadMethod: Function
  disabled?: boolean
  isReadonly?: boolean
  progress?: number
  customTipsText?: string
}

const props = withDefaults(defineProps<Props>(), {
  max: 1,
  showDownloadController: false,
  accept: '*/*',
  sizeLimit: 0,
  showTip: false,
  needProgress: true,
  disabled: false,
  isReadonly: false,
})

const emits = defineEmits([
  'file-list-change',
  'delete-original-file',
  'video-done',
  'progress-done',
  'update:originalFileList',
])

const playTimeRef = ref() // 视频时长
const fileListRef = ref<uploadFileItemNew[]>([]) // 文件列表
const fileNameRef = ref('') // 文件名
const fileExtRef = ref('') // 文件拓展名
// const percentageRef = ref(0) // 进度条百分比

function handleChange(options: {
  file: UploadFileInfo
  fileList: Array<UploadFileInfo>
  event?: Event
}) {
  ;(options.file as any).editing = false

  // 判断文件拓展名
  const fileExt = options.file.file?.name.split('.').pop()?.toLowerCase()
  if (!fileExt || !props.accept.includes(fileExt)) {
    window.$message.warning(`请选择${props.accept}文件`, {
      duration: 3000,
    })
    options.fileList.pop()
    return false
  }

  if (
    props.sizeLimit > 0
    && (options.file.file?.size ?? 0) > props.sizeLimit * 1000 * 1000
  ) {
    options.fileList.pop()
    window.$message.warning('文件大小超出限制，请重新选择')
    return false
  }

  const isVideo = options.file.file?.type.includes('video')
  if (isVideo) {
    const fileUrl = URL.createObjectURL(options.file.file!)
    const audio = new Audio(fileUrl)
    audio.addEventListener('loadedmetadata', function() {
      playTimeRef.value = Math.round(this.duration * 1000)
      emits('video-done', playTimeRef.value)
    })
  }

  // 判断是否展示进度条，如果展示，直接调用上传接口
  if (props.needProgress) {
    props.uploadMethod?.(
      options.file.file,
      options,
      (fileInfo: any) => {
        emits('progress-done')
        fileListRef.value.forEach((item, index) => {
          if (item.id === options.file.id) {
            fileListRef.value[index].percentage = 100
            fileListRef.value[index].url = fileInfo.url
            fileListRef.value[index].id = fileInfo.fileId
            fileListRef.value[index].fileName
              = fileListRef.value[index].fileName || fileInfo.url
            fileListRef.value[index].original
              = fileListRef.value[index].original || item.name
          }
        })
        window.$message.success('上传成功')
      },
      (progress: number) => {
        fileListRef.value.forEach((item, index) => {
          if (item.id === options.file.id) {
            fileListRef.value[index].percentage = progress
          }
        })
      },
    )
  }
  else {
    emits('file-list-change', options.fileList)
  }
}

/** 保存文件名称 */
function handleSaveFileName(index: number) {
  const newName = `${fileNameRef.value}.${fileExtRef.value}`
  const oldFile = fileListRef.value[index].file
  fileListRef.value[index].name = newName
  if (oldFile) {
    fileListRef.value[index].file = new File([oldFile], newName, {
      type: oldFile?.type,
    })
  }
  ;(fileListRef.value[index] as any).editing = false
}

/** 点击删除按钮 */
function handleClickDelete(index: number) {
  fileListRef.value.splice(index, 1)
}

function getCoverByExtension(extension: string) {
  const low = extension.toLowerCase()
  switch (low) {
  case 'doc':
  case 'docx':
    return 'WORD'
  case 'ppt':
  case 'pptx':
    return 'PPT'
  default:
    return low.toUpperCase()
  }
}

function getExtension(fileName: string | undefined) {
  return fileName?.split('.').pop() ?? ''
}

function getFileList() {
  return fileListRef.value
}

watch(
  props,
  (newV) => {
    if (newV.originalFileList) {
      fileListRef.value = newV.originalFileList as any
    }
  },
  {
    deep: true,
    immediate: true,
  },
)

// 实时更新文件list
watch(
  () => fileListRef.value,
  () => {
    emits('update:originalFileList', fileListRef.value)
  },
)

const handleClickDown = (index: number) => {
  const fileItem = fileListRef.value[index] || null
  if (fileItem) {
    downloadFile(fileItem.url as string, fileItem.name)
    window.$message.success('下载成功')
  }
  else {
    window.$message.error('未找到下载的文件')
  }
}

defineExpose({
  getFileList,
})
</script>

<style lang="scss" scoped>
.file-uploader {
  width: 100%;

  .trigger-btn {
    width: 94px;
    height: 30px;
    background: linear-gradient(180deg, #ffffff 0%, #f5f5f5 100%);
  }

  .tips-ctn {
    margin-top: 14px;

    :deep(.tips) {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      line-height: 22px;
    }
  }

  .file-body {
    display: flex;

    &:nth-child(n + 3) > .file-item {
      border-top: none;
    }

    > .file-item {
      width: calc(100% - 99px);
      flex-grow: 1;
      height: 61px;
      background: #ffffff;
      border: 1px solid #dcddde;
      display: flex;
      align-items: center;
      padding-left: 11px;
      margin-right: 21px;
      position: relative;

      > .file-bg {
        width: 78px;
        height: 44px;
        background: #4c98fc;
        opacity: 0.7;
        font-size: 12px;
        font-family: ArialMT;
        color: #ffffff;
        line-height: 44px;
        text-align: center;
        position: relative;

        img {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 12px;
          height: 12px;
        }
      }

      :deep(.file-name) {
        max-width: 60%;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        margin-left: 10px;
      }

      > .btns {
        position: absolute;
        right: 20px;
        display: flex;
        align-items: center;

        .n-button:nth-child(n + 2) {
          margin-left: 10px;
        }
      }

      .progress-bar {
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
  }
}
</style>
