<template>
  <div class="relative h-100vh overflow-hidden">
    <div class="h-100vh overflow-y-auto">
      <DetailHeader
        :is-show-confirm-btn="false"
        :need-show-dialog="false"
        back-name="check-pending"
        header-title="审核"
      />
      <div
        class="flex flex-col justify-center items-center gap-y-[40px] box-border py-[70px]"
      >
        <!-- 转接信息 -->
        <div>
          <div class="flex flex-row justify-start items-center gap-x-[20px]">
            <span class="text-[#333333] font-bold text-[16px] leading-[22px]">转接信息</span>
            <div
              class="w-[1000px] h-[1px] border-b-[1px] border-[#E9EAEB]"
            ></div>
          </div>
          <div class="flex flex-row justify-between mt-[20px]">
            <div class="flex flex-row justify-between gap-x-[30px]">
              <div class="flex flex-col gap-y-[20px] text-[#666666] mt-[20px]">
                <span>调动类型：</span>
                <span>姓名：</span>
                <span>原组织：</span>
                <span>入党时间：</span>
                <span>党费缴纳时间：</span>
              </div>
              <div
                class="flex flex-col gap-y-[20px] text-[#333333] mt-[20px] font-bold"
              >
                <span>{{ checkDetailData.value.transferType }}&nbsp;</span>
                <span>{{ checkDetailData.value.userName }}&nbsp;</span>
                <span>{{ checkDetailData.value.oldDeptName }}&nbsp;</span>
                <span>{{ checkDetailData.value.joinTime }}&nbsp;</span>
                <span>{{ checkDetailData.value.paymentTime }}&nbsp;</span>
              </div>
            </div>
            <div class="flex flex-row justify-between gap-x-[30px]">
              <div class="flex flex-col gap-y-[20px] text-[#666666] mt-[20px]">
                <span>调动时间：</span>
                <span>状态：</span>
                <span>接收组织：</span>
                <span>党费缴纳证明：</span>
                <span>介绍信：</span>
              </div>
              <div
                class="flex flex-col gap-y-[20px] text-[#333333] mt-[20px] font-bold"
              >
                <span>{{ checkDetailData.value.transferTime }}</span>
                <span>{{ checkDetailData.value.phaseStatus }}</span>
                <span>{{ checkDetailData.value.newDeptName }}</span>
                <span
                  class="cursor-pointer text-[#4584f8]"
                  @click="previewImg"
                >{{
                  checkDetailData.value.proveId ? '证明图片.jpg' : ''
                }}</span>
                <span
                  class="cursor-pointer text-[#4584f8]"
                  @click="previewFile"
                >{{ fileName }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 审批流程 -->
        <div>
          <div class="flex flex-row justify-start items-center gap-x-[20px]">
            <span class="text-[#333333] font-bold text-[16px] leading-[22px]">审批流程</span>
            <div
              class="w-[1000px] h-[1px] border-b-[1px] border-[#E9EAEB]"
            ></div>
          </div>
          <div class="mt-[40px]">
            <n-data-table
              :columns="approverProcessColumns"
              :data="checkDetailData.value.approvalNodes"
            />
          </div>
        </div>
      </div>
      <div
        class="absolute box-shadow bottom-0 left-0 h-[60px] w-[100%] z-[100] bg-[#ffffff] flex flex-row items-center justify-center gap-x-[10px]"
      >
        <n-button
          :disabled="checkDetailData.value.continueApprovalStatus !== '1'"
          type="primary"
          @click="handleCheck"
        >
          审核
        </n-button>
        <n-button @click="handleCancel">
          取消
        </n-button>
      </div>

      <!-- 查看图片 -->
      <n-modal
        v-model:show="showImageModal"
        :mask-closable="false"
        :show-icon="false"
        preset="dialog"
        title="Dialog"
      >
        <template #header>
          <div>证明图片</div>
        </template>
        <div class="flex flex-row justify-center items-center min-h-[200px]">
          <n-image :src="baseApi + checkDetailData.value.proveId" width="100" />
        </div>
      </n-modal>
      <!-- 审核弹框 -->
      <n-modal
        v-model:show="showCheckModal"
        :mask-closable="false"
        :show-icon="false"
        preset="dialog"
        title="Dialog"
      >
        <template #header>
          <div>审核</div>
        </template>
        <div>
          <n-form
            ref="formRef"
            :model="formDataReactive.value"
            :rules="formRules"
            label-align="right"
            label-placement="left"
            label-width="100"
            require-mark-placement="left"
            size="small"
          >
            <n-form-item label="审核结果：" path="approvalResult">
              <n-select
                v-model:value="formDataReactive.value.approvalResult"
                :options="approvalResultOptions"
                clearable
                placeholder="请选择审核结果"
                size="medium"
              />
            </n-form-item>
            <n-form-item label="审核意见：" path="approvalComment">
              <n-input
                v-model:value="formDataReactive.value.approvalComment"
                :rows="5"
                placeholder="请选择审核意见"
                type="textarea"
              />
            </n-form-item>
          </n-form>
        </div>
        <template #action>
          <div
            class="w-[100%] flex flex-row justify-center items-center gap-x-[20px]"
          >
            <n-button
              type="primary"
              :disabled="isSubmitting"
              @click="handleSubmit"
            >
              {{ isSubmitting ? '提交中...' : '提交审核' }}
            </n-button>
            <n-button @click="handleCancelSubmit">
              返回
            </n-button>
          </div>
        </template>
      </n-modal>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { NForm } from 'naive-ui'
import { formRules, getTableColumnsOfCheckDetail } from './config'
import type {
  AddTransferItemType,
  SubmitCheckDataType,
} from '@/services/transfer/types'
import {
  checkPartyMemberTransfer,
  getPartyMemberTransferCheckStatus,
  viewPartyMemberTransfer,
} from '@/services/transfer'
import checkedIcon from '@/assets/image/checked_icon.png'
import waitingCheckedIcon from '@/assets/image/waiting_check_icon.png'
import { downloadFile } from '@/utils/downloader'

const baseApi = ref(import.meta.env.VITE_API_BASE)
const formRef = ref<InstanceType<typeof NForm>>()
const route = useRoute()
const isSubmitting = ref(false) // 添加提交状态标记

const approvalResultOptions = computed(() => {
  return [
    {
      label: '通过',
      value: '1',
    },
    {
      label: '驳回',
      value: '0',
    },
  ]
})
const approverProcessColumns = getTableColumnsOfCheckDetail(
  (row) => {
    return [
      ['待审核', '已审核'].includes(row.approvalStatus)
        ? h('img', {
          class: 'w-[22px] h-[22px]',
          src: calcApprovalStatus(row.approvalStatus),
        })
        : h('span', {}, { default: () => '' }),
    ]
  },
  (row) => {
    return row.approvalUserNameNodes.map((node) => {
      return h(
        'div',
        {
          class: 'h-[20px]',
        },
        {
          default: () => node.username,
        },
      )
    })
  },
  (row) => {
    return row.approvalUserNameNodes.map((node) => {
      return h(
        'div',
        {
          class: 'h-[20px]',
        },
        {
          default: () => node.phone,
        },
      )
    })
  },
)
const checkDetailData = reactive<{ value: AddTransferItemType }>({
  value: {
    id: null,
    userId: null,
    transferType: null,
    oldDeptId: null,
    newDeptId: null,
    newDeptName: null,
    reason: null,
    phaseStatus: null,
    remark: null,
    paymentTime: null,
    letterId: undefined,
    proveId: null,
    transferTime: null,
    joinTime: null,
    userName: null,
    continueApprovalStatus: null,
    fileList: [],
    approvalNodes: [],
  },
})
const router = useRouter()
const formDataReactive = reactive<{ value: SubmitCheckDataType }>({
  value: {
    approvalId: route.query.approvalId as string,
    id: route.query.id as string,
    approvalResult: null,
    approvalComment: null,
    sort: route.query.sort as string,
  },
})
const showImageModal = ref(false)
const showCheckModal = ref(false)
watch(
  () => showCheckModal.value,
  (newVal) => {
    if (!newVal) {
      formDataReactive.value = {
        approvalId: route.query.approvalId as string,
        id: route.query.id as string,
        approvalResult: null,
        approvalComment: null,
        sort: route.query.sort as string,
      }
      resetForm()
    }
  },
)

// 文件名称
const fileName = computed(() => {
  let fileNameStr = ''
  if (checkDetailData.value.fileList && checkDetailData.value.fileList.length) {
    fileNameStr = checkDetailData.value.fileList[0].original as string
  }
  return fileNameStr
})

// 文件地址
const fileUrl = computed(() => {
  let fileUrlStr = ''
  if (checkDetailData.value.fileList && checkDetailData.value.fileList.length) {
    fileUrlStr = checkDetailData.value.fileList[0].fileName as string
  }
  return fileUrlStr
})

// 获取详细信息
function getApproverDetail() {
  return viewPartyMemberTransfer(route.query.id as string).then((res) => {
    checkDetailData.value = res
  })
}

// 查询下次审核是否有审核权限
function findGetCheckStatus() {
  return getPartyMemberTransferCheckStatus({
    id: route.query.id as string,
    approvalId: formDataReactive.value.approvalId as string,
  }).then((res) => {
    formDataReactive.value.approvalId = res.approvalId
    checkDetailData.value = res
  })
}

// 计算节点状态显示的图片
function calcApprovalStatus(status: string) {
  if (status === '待审核') {
    return waitingCheckedIcon
  }
  else if (status === '已审核') {
    return checkedIcon
  }
  else {
    return ''
  }
}

// 预览文件
function previewFile() {
  downloadFile(fileUrl.value, fileName.value)
  window.$message.success('下载成功')
}

// 点击审核打开审核按钮
function handleCheck() {
  showCheckModal.value = true
}

// 取消提交
function handleCancelSubmit() {
  showCheckModal.value = false
}

// 提交审核
function handleSubmit() {
  // 如果正在提交中，则不重复执行
  if (isSubmitting.value) {
    return
  }

  formRef.value?.validate((errors: any) => {
    if (!errors) {
      isSubmitting.value = true // 设置提交状态为true

      checkPartyMemberTransfer(formDataReactive.value)
        .then(() => {
          window.$message.success('提交成功')
          findGetCheckStatus()
          showCheckModal.value = false
        })
        .catch((error) => {
          // 处理错误
          window.$message.error('提交失败')
          console.error('提交审核失败:', error)
        })
        .finally(() => {
          // 无论成功失败，都将提交状态重置
          isSubmitting.value = false
        })
    }
  })
}

// 取消审核
function handleCancel() {
  router.push({
    name: 'check-pending',
  })
}

// 预览图片
function previewImg() {
  showImageModal.value = true
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

onMounted(() => {
  getApproverDetail()
})
</script>

<style lang="scss" scoped>
.h-100vh {
  height: calc(100vh - 114px);
}

.box-shadow {
  box-shadow: 10px 10px 30px 0 rgba(0, 0, 0, 0.1);
}
</style>
