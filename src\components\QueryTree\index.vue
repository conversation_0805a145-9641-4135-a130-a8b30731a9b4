<template>
  <div class="head-container">
    <div class="head-container-header">
      <div class="mt-[21px] w-full">
        <n-input
          v-model:value="searchName"
          suffix-icon="search"
          :placeholder="placeholder"
          clearable
          @change="getdeptTree"
        />
      </div>
      <!-- <div class="head-container-header-dropdown" v-if="showExpand">
        <n-dropdown :hide-on-click="false">
          <n-icon style="transform: rotate(90deg)">
            <MoreFilled />
          </n-icon>
          <template #dropdown>
            <n-dropdown-menu>
              <n-dropdown-item>
                <n-button
                  :class="buttonClass"
                  link
                  type="primary"
                  :icon="isExpand ? 'expand' : 'fold'"
                  @click="toggleRowExpansionAll(isExpand ? false : true)"
                >
                  {{ isExpand ? '折叠' : '展开' }}
                </n-button>
              </n-dropdown-item>
            </n-dropdown-menu>
          </template>
        </n-dropdown>
      </div> -->
    </div>
    <n-tree
      ref="deptTreeRef"
      class="mt-[20px]"
      :data="state.List"
      :props="props.props"
      :expand-on-click-node="false"
      key-field="id"
      label-field="description"
      highlight-current
      default-expand-all
      :node-props="nodeProps"
    >
      <template v-if="$slots.default" #default="{ node, data }">
        <slot :node="node" :data="data" />
      </template>
    </n-tree>
  </div>
</template>

<script setup lang="ts" name="query-tree">
import { NButton, NTooltip } from 'naive-ui'
import type { TreeOption } from 'naive-ui'
import { Delete, Edit } from '@vicons/carbon'
const emit = defineEmits(['search', 'nodeClick'])

const props = defineProps({
  /**
   * 树结构属性配置。
   *
   * @default { label: 'name', children: 'children', value: 'id' }
   */
  props: {
    type: Object,
    default: () => {
      return {
        label: 'name',
        children: 'children',
        value: 'id',
      }
    },
  },

  /**
   * 输入框占位符。
   *
   * @default ''
   */
  placeholder: {
    type: String,
    default: '',
  },
  /**
   * 后缀字段。
   *
   * @default ''
   */
  suffix: {
    type: String,
    default: '',
  },
  /**
   * 禁止删除提示文字
   *
   * @default ''
   */
  disableDelContent: {
    type: String,
    default: '',
  },
  /**
   * 是否显示加载中状态。
   *
   * @default false
   */
  loading: {
    type: Boolean,
    default: false,
  },

  /**
   * 查询函数，必须返回 Promise 类型数据。
   */
  query: {
    type: Function,
    required: true,
  },
  /**
   * 编辑函数，必须返回 Promise 类型数据。
   */
  edit: {
    type: Function,
    required: true,
  },
  /**
   * 删除函数，必须返回 Promise 类型数据。
   */
  delete: {
    type: Function,
    required: true,
  },
  /**
   * 是否显示折叠控制
   */
  showExpand: {
    type: Boolean,
    default: false,
  },
  /**
   * 是否禁用该行删除
   */
  disableDel: {
    type: Function,
    required: true,
  },
})

const state = reactive({
  List: [], // 树形结构列表数据
  localLoading: props.loading, // 是否加载中
})

const deptTreeRef = ref() // 部门树形结构组件实例引用
const searchName = ref() // 查询关键字
// const isExpand = ref(true) // 是否展开所有节点

// const buttonClass = computed(() => {
//   return [
//     '!h-[20px]',
//     'reset-margin',
//     '!text-gray-500',
//     'dark:!text-white',
//     'dark:hover:!text-primary',
//   ]
// })

/**
 * 点击树形结构节点触发的事件。
 *
 * @param item 被点击的节点数据。
 */
const handleNodeClick = (item: any) => {
  emit('nodeClick', item)
}

/**
 * 点击树节点触发的方法
 * @param {any} {option}:{option:TreeOption}
 */
const nodeProps = ({ option }: { option: TreeOption }) => {
  return {
    onClick() {
      if (!option.disabled) {
        handleNodeClick(option)
      }
    },
  }
}

/**
 * 获取部门树形结构数据。
 */
const getdeptTree = async() => {
  if (props.query instanceof Function) {
    state.localLoading = true

    // 调用传入的查询函数，并将查询关键字作为参数传入
    const result = await props.query(unref(searchName))
    // 如果查询结果为 Promise 类型，则进行后续处理
    if (result && result.length > 0) {
      state.List = result.map((item: any) => {
        item.suffix = () =>
          h('div', { class: 'text-[#999999]' }, [
            h(
              'div',
              { class: 'showSuffix' },
              { default: () => item[props.suffix] },
            ),
            h('div', { class: 'showBtns flex' }, [
              h(Edit, {
                class: 'edit h-[18px]',
                onClick: (e: any) => {
                  props.edit(item)
                  e.stopPropagation()
                },
              }),
              h(
                NTooltip,
                { disabled: !props.disableDel(item) },
                {
                  // 使用具名插槽
                  trigger: () =>
                    h(Delete, {
                      class: 'mx-[10px] h-[18px] focus:outline-none',
                      disabled: props.disableDel(item),
                      onClick: (e: any) => {
                        if (!props.disableDel(item)) {
                          props.delete(item)
                        }
                        e.stopPropagation()
                      },
                    }),
                  default: () => props.disableDelContent,
                },
              ),
            ]),
          ])
        return item
      })
    }
  }
}

/**
 * 切换所有节点的展开/收起状态。
 *
 * @param status 目标状态，true 为展开，false 为收起。
 */
// const toggleRowExpansionAll = (status: any) => {
//   isExpand.value = status
//   const nodes = deptTreeRef.value.store._getAllNodes()
//   for (let i = 0; i < nodes.length; i++) {
//     nodes[i].expanded = status
//   }
// }

onMounted(async() => {
  await getdeptTree()
  if (state.List.length > 0) {
    handleNodeClick(state.List[0])
  }
})

// 方便父组件调用刷新树方法
defineExpose({
  getdeptTree,
})
</script>
<style lang="scss" scoped>
.head-container {
  &-header {
    display: flex;
    align-items: center;
    &-input {
      width: 90%;
    }
    &-dropdown {
      flex: 1;
      margin-left: 5%;
    }
  }
}
:deep(.n-tree-node-content) {
  flex: 1;
  font-size: 12px;
  .n-tree-node-content__text {
    color: #333333;
  }
}

:deep(.n-tree-node) {
  &:hover .showSuffix {
    display: none;
  }

  .showBtns {
    display: none;
  }

  &:hover .showBtns {
    display: flex;
  }
}
</style>
