import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { UserItem } from '@/services/system/User/types'

export function getTableColumns(
  optionColumnRenderer: (row: UserItem, index: number) => VNodeChild,
  handleUpdateValueRender: (row: UserItem) => VNodeChild,
): DataTableColumns<UserItem> {
  return [
    {
      key: 'index',
      title: '序号',
      width: '5%',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'trueName',
      title: '姓名',
      width: '8%',
    },
    {
      key: 'phone',
      title: '手机号',
      width: '10%',
    },
    {
      key: 'political',
      title: '政治面貌',
      width: '8%',
      render: row => row.political ?? '--',
    },
    // {
    //   key: 'partyIdentity',
    //   title: '支部内身份',
    //   width: '16%',
    //   ellipsis: {
    //     tooltip: {
    //       contentStyle: { width: '400px', 'word-break': 'break-all' },
    //     },
    //   },
    // },
    // {
    //   key: 'partyIdentity',
    //   title: '部门',
    // },
    {
      key: 'deptName',
      title: '所属党组织',
      render: row => row.deptName ?? '--',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'updateTime',
      title: '更新时间',
      width: '12%',
    },
    {
      key: 'lockFlag',
      title: '状态',
      width: '10%',
      render: row => handleUpdateValueRender(row),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '16%',
      render: (row, index) => optionColumnRenderer(row, index),
    },
  ]
}
