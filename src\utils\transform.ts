// 将Uint8Array转换为Base64编码
export function base64FromArrayBuffer(buffer: Uint8Array): Promise<string> {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onload = () => {
      if (reader.result) {
        resolve(reader.result.toString().split(',')[1])
      }
    }
    reader.readAsDataURL(new Blob([buffer]))
  })
}

// 转化data为树状结构
export function transformTreeData(data: any) {
  if (Array.isArray(data)) {
    data.forEach((item) => {
      item.key = item.id
      item.label = item.name
      item.value = item.id

      if (item.children) {
        item.children.forEach((child: any) => {
          transformTreeData(child)
        })
      }
    })
  } else {
    data.key = data.id
    data.label = data.name
    data.value = data.id

    if (data.children) {
      data.children.forEach((child: any) => {
        transformTreeData(child)
      })
    }
  }
  return data
}
