import type { RouteRecordRaw } from 'vue-router'

const promotionalRoute: RouteRecordRaw = {
  path: 'notice',
  name: 'news',
  meta: { title: '宣传管理', icon: 'notice', category: 'run', order: 1 },
  redirect: { name: 'noticeList' },
  component: () => import('@/views/main/cloudPush/promotionalManage/Index.vue'),
  children: [
    {
      path: 'list',
      name: 'noticeList',
      component: () =>
        import('@/views/main/cloudPush/promotionalManage/cpns/Notice.vue'),
    },
    {
      path: 'add',
      name: 'noticeAdd',
      component: () =>
        import('@/views/main/cloudPush/promotionalManage/cpns/Detail.vue'),
    },
  ],
}
export default promotionalRoute
