import type {
  AllTargetList,
  EvaluationType,
  PartyBuildingAdd,
  PartyBuildingDetail,
  PartyBuildingListItem,
  RelateAdd,
  RelateListItem,
  RelateMark,
  RelateMarkListRow,
  RelateMarkListSearchParams,
  RelateWriteDeadLine,
  SearchRelateList,
  StatisticScoreList,
} from './types'
import { commonReq, downloadReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 获取纪检清单列表 */
export function getPartyBuildList(
  params: { year: string; month: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<PartyBuildingListItem>>({
    url: '/party-affairs/backend/v1/discipline',
    params,
  })
}

/** 新建党建清单 */
export function postPartyBuildList(data: PartyBuildingAdd) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/discipline',
    data,
  })
}

/** 编辑党建清单 */
export function putPartyBuildList(data: PartyBuildingAdd) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/discipline',
    data,
  })
}
/** 下发单个清单 */
export function putIssueSingleParty(id: string) {
  return commonReq.put({
    url: `/party-affairs/backend/v1/discipline/status/${id}`,
  })
}

/** 删除党建清单 */
export function deletePartyBuildList(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/discipline',
    params: { ids },
  })
}

/** 查看单个党建清单 */
export function getSinglePartyListDetail(id: string) {
  return commonReq.get<PartyBuildingDetail>({
    url: `/party-affairs/backend/v1/discipline/${id}`,
  })
}

/** 下发单个党建清单 */
export function putSinglePartyList(id: string) {
  return commonReq.put<PartyBuildingDetail>({
    url: `/party-affairs/backend/v1/discipline/status/${id}`,
  })
}

/** 新增考核类别 */
export function postEvaluationTypes(data: EvaluationType) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/discipline/category',
    data,
  })
}
/** 修改考核类别 */
export function putEvaluationTypes(data: EvaluationType) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/discipline/category',
    data,
  })
}

/** 删除考核类别 */
export function deleteEvaluationTypes(id: string) {
  return commonReq.delete({
    url: `/party-affairs/backend/v1/discipline/category/${id}`,
  })
}

/** 查看清单下的所有指标 */
export function getAllTargetList(id: string) {
  return commonReq.get<AllTargetList>({
    url: `/party-affairs/backend/v1/discipline/target/${id}`,
  })
}

/** 关联指标项 */
export function postRelate(data: RelateAdd) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/discipline/target/relation',
    data,
  })
}

/** 关联指标项列表 */
export function getRelateList(params: SearchRelateList & PaginationReq) {
  return commonReq.get<PaginationRes<RelateListItem>>({
    url: '/party-affairs/backend/v1/discipline/target/relation',
    params,
  })
}

/** 关联指标项打分列表 */
export function getRelateMarkListDiscipline(
  params: RelateMarkListSearchParams,
) {
  return commonReq.get<RelateMarkListRow[]>({
    url: '/party-affairs/backend/v1/discipline/target/mark',
    params,
  })
}

/** 关联指标项打分 */
export function putRelateMarkDiscipline(data: RelateMark) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/discipline/target/mark',
    data,
  })
}

/** 删除关联指标项 */
export function deleteRelate(id: string) {
  return commonReq.delete({
    url: `/party-affairs/backend/v1/discipline/target/relation/${id}`,
  })
}

/** 关联指标项填写截止日期 */
export function putRelateDeadLine(data: RelateWriteDeadLine) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/discipline/target',
    data,
  })
}

/** 导出指标 */
export function postExportTargetFile(id: string) {
  return downloadReq.post({
    url: `/party-affairs/backend/v1/discipline/target/export/${id}`,
    responseType: 'blob',
  })
}

/** 统计得分列表 */
export function getStatisticScoreListDiscipline(id: string) {
  return commonReq.get<PaginationRes<StatisticScoreList>>({
    url: `/party-affairs/backend/v1/discipline/target/score/${id}`,
  })
}

/** 导出得分 */
export function postExportScoreFile(id: string) {
  return downloadReq.post({
    url: `/party-affairs/backend/v1/discipline/target/score/export/${id}`,
    responseType: 'blob',
  })
}
