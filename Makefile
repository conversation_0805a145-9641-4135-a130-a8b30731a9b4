registry=c.easestrategy.com
namespace=xfh
project=xfh-admin
tag=$(shell git rev-parse --short HEAD)

compile:
	pnpm build

dev: compile
	@docker buildx build --load -t ${registry}/${namespace}/${project}:dev .

build: compile
	@docker buildx build --platform linux/amd64 --push -t ${registry}/${namespace}/${project}:${tag} -t ${registry}/${namespace}/${project}:latest .

push:
	@docker buildx build --platform linux/amd64 --push -t ${registry}/${namespace}/${project}:${tag} -t ${registry}/${namespace}/${project}:latest .

local:
	npm run build & docker buildx build --platform linux/amd64 --load -t ${registry}/${namespace}/${project}:${tag} -t ${registry}/${namespace}/${project}:latest .
