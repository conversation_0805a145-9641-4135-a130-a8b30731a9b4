import type { RouteRecordRaw } from 'vue-router'

const interactionManageRoute: RouteRecordRaw = {
  path: 'interaction-manage',
  name: 'interactionManage',
  meta: { title: '互动管理', icon: 'notice', category: 'run', order: 8 },
  redirect: { name: 'cultureShow' },
  component: () => import('@/views/main/run/interaction/index.vue'),
  children: [
    // {
    //   path: 'culture-show',
    //   name: 'cultureShow',
    //   meta: { title: '文化展示', icon: 'role' },
    //   component: () =>
    //     import('@/views/main/run/interaction/culture-show/CultureShow.vue'),
    // },
    // {
    //   path: 'friend-circle',
    //   name: 'friendCircle',
    //   meta: { title: '朋友圈管理', icon: 'admin' },
    //   component: () =>
    //     import('@/views/main/run/interaction/friend-circle/FriendCircle.vue'),
    // },
    {
      path: 'learning',
      name: 'learning',
      meta: { title: '学习心得', icon: 'statistics' },
      component: () =>
        import('@/views/main/run/interaction/learning/Learning.vue'),
    },
  ],
}
export default interactionManageRoute
