<script setup lang="ts">
import { NDataTable } from 'naive-ui'
import type { DataTableColumns } from 'naive-ui'
import { reactive } from 'vue'
import ContentBox from '../cpns/ContentBox.vue'
import type { ChoiceItem, Questionnaire } from '../reviewItems/type'
import type { BranchDetail, BranchInfo, BranchType } from './type'
import {
  getPartDetailEachDetail,
  getTalkAboutPartyBranchDetail,
  postExportTalkAboutPartyBranch,
} from '@/services/publicity/vote/talkAbout'
import { emitter } from '@/utils/event-bus'
import { formatTimeStamp } from '@/utils/format'
import { downloadArrayBuffer } from '@/utils/downloader'
import { BOOLEANTYPE, QUESTIONTYPE } from '@/constant'
import { useVoteStore } from '@/store/vote/vote'

const voteStore = useVoteStore()

const backQuery = {
  reviewId: voteStore.getQueryData.reviewId,
  year: voteStore.getQueryData.year,
  partReviewItemId: voteStore.getQueryData.partReviewItemId,
  branchReviewItemId: voteStore.getQueryData.branchReviewItemId,
}

const showModal = ref(false)
const modalLoading = ref(false)
const modalTitle = ref<string>()
const selectedQuesDetail = reactive<Questionnaire>({
  reviewSubjectArr: [],
})

const displayMap = reactive<{
  branchEvaluationDisplay: string
  branchEvaluationIsScoreItem: string
  branchEvaluationIsTemplate: string
}>({
  branchEvaluationDisplay: BOOLEANTYPE.NO, // 支部班子评价展示
  branchEvaluationIsScoreItem: BOOLEANTYPE.NO, // 支部班子总分项展示
  branchEvaluationIsTemplate: BOOLEANTYPE.NO, // 支部班子模板展示
})

const evaluatedTableColumns: DataTableColumns<Object> = reactive([])
const evaluationProjectColumns: DataTableColumns<Object> = reactive([])
const branchDetailColumns: DataTableColumns<BranchDetail> = [
  { key: 'index', title: '序号', render: (_, i) => i + 1 },
  { key: 'commentatorName', title: '评价人' },
  { key: 'commentTime', title: '评价时间' },
  {
    key: 'distributeId',
    title: '评价详情',
    render: (row) => {
      return h(
        'div',
        {
          class: 'text-blue-500 cursor-pointer',
          onClick: () => {
            openModal(row.distributeId, '评价详情')
          },
        },
        '评价详情',
      )
    },
  },
]
// 支部班子基本信息
const orgInfo = reactive<{ data: BranchInfo }>({
  data: {
    orgName: '',
    orgTypeName: '',
    partNum: '',
    reviewedNum: '',
  },
})

// 总体评价统计
const totalEvaluatedTable = reactive<{ data: Array<Object> }>({ data: [] })
// 评定项目统计
const evaluationProject = reactive<{ data: Array<Object> }>({ data: [] })

const route = useRoute()
const userName = computed(() => `${orgInfo.data.orgName}的评价详情` || '')

// 支部班子评价
const branchInfo = reactive<{ data: BranchType }>({
  data: {
    itemColumn: [],
    itemData: [],
    reviewDetailList: [],
    totalStatistic: {},
    isOpenTemplate: false,
    hasScoreRule: false,
    ruleScoreName: '',
  },
})
const loadData = () => {
  const data = {
    reviewItemId: route.query.reviewItemId as string,
    orgId: route.query.orgId as string,
    reviewId: route.query.reviewId as string,
  }
  getTalkAboutPartyBranchDetail(data).then((res) => {
    orgInfo.data = res.org || orgInfo.data // 党员基本信息
    branchInfo.data = res.branch || branchInfo.data // 党员互评
    displayMap.branchEvaluationDisplay = res.branchEvaluationDisplay // 支部班子评价展示
    displayMap.branchEvaluationIsScoreItem = res.branchEvaluationIsScoreItem // 支部班子总分项展示
    displayMap.branchEvaluationIsTemplate = res.branchEvaluationIsTemplate // 支部班子模板开启
    // 总体评价统计
    if (
      branchInfo.data.totalStatistic
      && Object.keys(branchInfo.data.totalStatistic).length > 0
    ) {
      const tmp = {} as Record<string, any>
      for (const key in branchInfo.data.totalStatistic) {
        evaluatedTableColumns.push({ key, title: key })

        tmp[key]
          = branchInfo.data.totalStatistic[
            key as keyof typeof branchInfo.data.totalStatistic
          ]
      }
      totalEvaluatedTable.data.push(tmp)
    }
    // 评定项目统计表头
    if (branchInfo.data.itemColumn) {
      evaluationProjectColumns.push(
        ...branchInfo.data.itemColumn.map((item) => {
          return {
            key: item,
            title: item,
          }
        }),
      )
    }

    // 评定项目统计数据
    if (branchInfo.data.itemData) {
      evaluationProject.data = branchInfo.data.itemData
    }
  })
}
// onMounted(() => {
//   loadData()
// })

// 锚点相关
const anchorList = reactive([
  {
    title: '基本信息',
    anchor: 'basic',
  },
  {
    title: '党员自评',
    anchor: 'materials',
  },
  {
    title: '党员互评',
    anchor: 'notes',
  },
  {
    title: '党支部评价',
    anchor: 'summary',
  },
])

const basicRef = ref<HTMLElement | null>(null)
const notesRef = ref<HTMLElement | null>(null)

const activeAnchor = ref<string | null>('basic')

const emitScrollTop = ref()

emitter.on('my-scroller', (v: any) => (emitScrollTop.value = v))
watch(
  () => emitScrollTop.value,
  (newV) => {
    anchorList.forEach((item) => {
      const element = document.getElementById(item.anchor)
      if (element) {
        const offsetTop = element.offsetTop - 60
        const offsetBottom = offsetTop + element.offsetHeight
        if (newV >= offsetTop && newV < offsetBottom) {
          activeAnchor.value = item.anchor // 更新临时变量
        }
      }
    })
  },
)

watchEffect(() => {
  // 遍历锚点位置，找到当前视口位置应激活的锚点
})

onMounted(() => {
  loadData()
})
onBeforeUnmount(() => {
  emitter.off('my-scroller')
})

async function handleExport() {
  const data = {
    reviewId: route.query.reviewId as string,
    orgId: route.query.orgId as string,
    reviewItemId: route.query.reviewItemId as string,
  }
  const res = await postExportTalkAboutPartyBranch(data)
  downloadArrayBuffer(
    res,
    `民主评议党支部班子-${formatTimeStamp(Date.now(), 'YYYYMMDDHHmmss')}`,
  )
}

function formatChoiceTitle(i: number, str: string) {
  let trimmed = str.trim()
  trimmed = trimmed ? `${trimmed}` : '请输入选项内容'
  return `${String.fromCharCode(65 + i)}. ${trimmed}`
}

function openModal(distributeId: string | undefined, title: string) {
  if (distributeId) {
    modalLoading.value = true

    modalTitle.value = title
    getPartDetailEachDetail({ distributeId }).then((res) => {
      modalLoading.value = false
      Object.assign(selectedQuesDetail, res)
      showModal.value = true
      if (!selectedQuesDetail?.reviewSubjectArr?.length) {
        showModal.value = false
        window.$message.error('暂无数据!')
      }
    })
  } else {
    window.$message.error('暂无数据!')
  }
}

// 为单选按钮获取数组中选中的第一个值
function getRadioCheckedValue(arr: ChoiceItem[]) {
  return arr.findIndex(item => item.checked === BOOLEANTYPE.YES)
}
</script>
<template>
  <div>
    <DetailHeader
      :header-title="userName"
      :is-show-confirm-btn="true"
      :right-btn-text="'导出'"
      :release="handleExport"
      :back-name="'talk-about-branch'"
      :back-query="backQuery"
    />

    <div class="flex pb-[500px] pl-[50px] relative">
      <div class="flex-1 pl-[100px]">
        <div id="basic" ref="basicRef">
          <!-- 基本信息 -->
          <div class="pl-[100px] pr-[100px] box-border">
            <div class="flex flex-row mt-[50px] justify-start items-center">
              <span class="text-[14px] font-bold leading-[14px]">基本信息</span>
              <span class="line" />
            </div>
            <div class="mt-[50px] flex flex-row justify-start items-start">
              <div class="meetingInfo">
                <div>
                  <div>
                    <span class="text-[#b1b1b1]">
                      组织名称：
                      <span class="ml-[20px] text-[#000000]">{{ orgInfo.data.orgName || '--' }}
                      </span>
                    </span>
                  </div>
                  <div>
                    <span class="text-[#b1b1b1]">
                      组织性质：
                      <span class="ml-[20px] text-[#000000]">{{
                        orgInfo.data.orgTypeName || '--'
                      }}</span>
                    </span>
                  </div>
                </div>
                <div>
                  <div>
                    <span class="text-[#b1b1b1]">党员人数：
                      <span class="ml-[20px] text-[#000000]">
                        {{ orgInfo.data.partNum || '--' }}
                      </span>
                    </span>
                  </div>
                  <div>
                    <span class="text-[#b1b1b1]">已评人数：
                      <span class="ml-[20px] text-[#000000]">
                        {{ orgInfo.data.reviewedNum || '--' }}
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-show="displayMap.branchEvaluationDisplay === BOOLEANTYPE.YES"
          id="notes"
          ref="notesRef"
        >
          <!-- 支部班子评价 -->
          <ContentBox title="支部班子评价">
            <template #default>
              <div
                v-show="
                  displayMap.branchEvaluationIsScoreItem === BOOLEANTYPE.YES
                "
                class="w-[70%] pt-[30px]"
              >
                <div class="pb-[10px]">
                  <span>总体评价统计：</span>
                </div>
                <NDataTable
                  :columns="evaluatedTableColumns"
                  :data="totalEvaluatedTable.data"
                />
              </div>
              <div
                v-show="
                  displayMap.branchEvaluationIsTemplate === BOOLEANTYPE.YES
                "
                class="w-[70%] pt-[30px]"
              >
                <div class="pb-[10px]">
                  <span>评定项目统计：</span>
                </div>
                <NDataTable
                  :columns="evaluationProjectColumns"
                  :data="evaluationProject.data"
                />
              </div>
              <div class="w-[70%] pt-[30px]">
                <div class="pb-[10px]">
                  <span>评价详情：</span>
                </div>
                <NDataTable
                  :columns="branchDetailColumns"
                  :data="branchInfo.data.reviewDetailList"
                />
              </div>
            </template>
          </ContentBox>
        </div>
        <n-modal
          v-model:show="showModal"
          preset="dialog"
          title="Dialog"
          :loading="modalLoading"
        >
          <template #header>
            <div>{{ modalTitle }}</div>
          </template>
          <div class="w-full">
            <div
              v-for="(data, index1) in selectedQuesDetail.reviewSubjectArr"
              :key="index1"
              class="card"
            >
              <div class="text-13px text-[#333] font-500 leading-26px mb-8px">
                <span class="questionTitle">
                  {{ index1 + 1 }}、{{
                    data.subjectName.trim() || '请输入题目信息'
                  }}
                </span>
              </div>
              <div v-if="data.subjectType === QUESTIONTYPE.FILLED">
                <NInput
                  v-model:value="data.subjectItemContent"
                  placeholder="请输入意见内容"
                  :resizable="false"
                  type="textarea"
                />
              </div>
              <div
                v-else-if="
                  data.subjectType === QUESTIONTYPE.MULTI &&
                    data.reviewSubjectItemArr?.length
                "
              >
                <NCheckboxGroup :value="[]" size="small">
                  <div
                    v-for="(item, index) in data.reviewSubjectItemArr"
                    :key="item.subjectId ?? item.renderId"
                  >
                    <NCheckbox
                      v-model:checked="item.checked"
                      class="leading-32px"
                      :label="formatChoiceTitle(index, item.subjectItemName)"
                      checked-value="1"
                      unchecked-value="0"
                    />
                  </div>
                </NCheckboxGroup>
              </div>
              <div
                v-else-if="
                  data.subjectType === QUESTIONTYPE.RADIO &&
                    data.reviewSubjectItemArr?.length
                "
              >
                <NRadioGroup
                  size="small"
                  :value="getRadioCheckedValue(data.reviewSubjectItemArr)"
                >
                  <div
                    v-for="(item, index) in data.reviewSubjectItemArr"
                    :key="item.subjectId ?? item.renderId"
                  >
                    <NRadio
                      :label="formatChoiceTitle(index, item.subjectItemName)"
                      :checked="item.checked === BOOLEANTYPE.YES"
                      :value="index"
                    />
                  </div>
                </NRadioGroup>
              </div>
            </div>
          </div>
        </n-modal>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.line {
  margin-left: 20px;
  width: 80%;
  display: inline-block;
  border-top: 0.5px solid #ccc;
  font-weight: 100;
}
.meetingInfo {
  min-width: 1000px;
  display: flex;
  flex-direction: column;
  gap: 30px;
  div {
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
}

.summarize {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  .title {
    width: 100px;
    color: #b1b1b1;
  }
  .meeting-content {
    flex: 1;
    color: #000000;
  }
}
.attachment {
  padding-top: 30px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .title {
    width: 100px;
    color: #666666;
  }
}

.time-line-item-active {
  :deep(.n-timeline-item-content__title) {
    font-size: 12px;
  }
}
</style>
