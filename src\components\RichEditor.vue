<script setup lang="ts">
import Editor from '@tinymce/tinymce-vue'
// 引入node_modules里的tinymce相关文件文件
import tinymce from 'tinymce/tinymce' // tinymce默认hidden，不引入则不显示编辑器
// import { useRichStore } from '@/store/components/richText'
import 'tinymce/themes/silver' // 编辑器主题，不引入则报错
import 'tinymce/icons/default' // 引入编辑器图标icon，不引入则不显示对应图标
// 引入编辑器插件
import 'tinymce/plugins/indent2em'
import 'tinymce/plugins/advlist' // 高级列表
import 'tinymce/plugins/anchor' // 锚点
import 'tinymce/plugins/autolink' // 自动链接
import 'tinymce/plugins/autoresize' // 编辑器高度自适应,注：plugins里引入此插件时，Init里设置的height将失效
import 'tinymce/plugins/autosave' // 自动存稿
import 'tinymce/plugins/charmap' // 特殊字符
import 'tinymce/plugins/code' // 编辑源码
import 'tinymce/plugins/codesample' // 代码示例
import 'tinymce/plugins/directionality' // 文字方向
import 'tinymce/plugins/emoticons' // 表情
import 'tinymce/plugins/fullpage' // 文档属性
import 'tinymce/plugins/fullscreen' // 全屏
import 'tinymce/plugins/help' // 帮助
import 'tinymce/plugins/hr' // 水平分割线
import 'tinymce/plugins/image' // 插入编辑图片
import 'tinymce/plugins/importcss' // 引入css
import 'tinymce/plugins/insertdatetime' // 插入日期时间
import 'tinymce/plugins/link' // 超链接
import 'tinymce/plugins/lists' // 列表插件
import 'tinymce/plugins/media' // 插入编辑媒体
import 'tinymce/plugins/nonbreaking' // 插入不间断空格
import 'tinymce/plugins/pagebreak' // 插入分页符
import 'tinymce/plugins/paste' // 粘贴插件
import 'tinymce/plugins/preview' // 预览
import 'tinymce/plugins/print' // 打印
import 'tinymce/plugins/quickbars' // 快速工具栏
import 'tinymce/plugins/save' // 保存
import 'tinymce/plugins/searchreplace' // 查找替换
// import 'tinymce/plugins/spellchecker'  //拼写检查，暂未加入汉化，不建议使用
import 'tinymce/plugins/tabfocus' // 切入切出，按tab键切出编辑器，切入页面其他输入框中
import 'tinymce/plugins/table' // 表格
import 'tinymce/plugins/template' // 内容模板
import 'tinymce/plugins/textcolor' // 文字颜色
import 'tinymce/plugins/textpattern' // 快速排版
import 'tinymce/plugins/toc' // 目录生成器
import 'tinymce/plugins/visualblocks' // 显示元素范围
import 'tinymce/plugins/visualchars' // 显示不可见字符
import 'tinymce/plugins/wordcount' // 字数统计
import { uploadImg } from '@/services'

interface Props {
  fetch?: (value: string) => void
  richHeight?: number
  value?: string
  disabled?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  richHeight: 710,
  disabled: false,
})
const emits = defineEmits(['update:value'])
const init = {
  language_url: '/tinymce/langs/zh_CN.js', // 引入语言包文件
  language: 'zh_CN', // 语言类型

  skin_url: '/tinymce/skins/ui/oxide', // 皮肤：浅色
  // skin_url: '/tinymce/skins/ui/oxide-dark',//皮肤：暗色
  // ,/static/css/test.css
  content_css: '/tinymce/skins/content/default/content.css',
  plugins:
    'print preview searchreplace autolink directionality indent2em visualblocks visualchars fullscreen image link media template code codesample table charmap hr pagebreak nonbreaking anchor insertdatetime advlist lists wordcount textpattern autosave ', // 插件配置
  toolbar:
    'formatselect fontselect fontsizeselect | forecolor backcolor bold italic underline strikethrough | alignleft aligncenter alignright alignjustify indent2em lineheight | image media bullist numlist | blockquote subscript superscript removeformat | table charmap hr pagebreak insertdatetime print preview', // 工具栏配置，设为false则隐藏
  // menubar: 'file edit',  //菜单栏配置，设为false则隐藏，不配置则默认显示全部菜单，也可自定义配置--查看 http://tinymce.ax-z.cn/configure/editor-appearance.php --搜索“自定义菜单”

  fontsize_formats:
    '12px 14px 16px 18px 20px 22px 24px 28px 32px 34px 36px 48px 56px 72px', // 字体大小
  font_formats:
    '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;', // 字体样式
  lineheight_formats: '0.5 0.8 1 1.2 1.5 1.75 2 2.5 3 4 5', // 行高配置，也可配置成"12px 14px 16px 20px"这种形式
  height: props.richHeight, // 注：引入autoresize插件时，此属性失效
  placeholder: '在这里输入文字，推荐字体：微软雅黑，字号：18px，行高：2',
  branding: false, // tiny技术支持信息是否显示
  resize: false, // 编辑器宽高是否可变，false-否,true-高可变，'both'-宽高均可，注意引号
  // statusbar: false,  //最下方的元素路径和字数统计那一栏是否显示
  elementpath: false, // 元素路径是否显示

  // content_style: 'img {max-width:100%;}', // 直接自定义可编辑区域的css样式
  content_style: 'body {font-size:20px;font-family: "微软雅黑";line-height:2;}', // 直接自定义可编辑区域的css样式

  // content_css: '/tinycontent.css',  //以css文件方式自定义可编辑区域的css样式，css文件需自己创建并引入

  // images_upload_url: '/apib/api-upload/uploadimg',  //后端处理程序的url，建议直接自定义上传函数image_upload_handler，这个就可以不用了

  // images_upload_base_path: '/demo',  //相对基本路径--关于图片上传建议查看--http://tinymce.ax-z.cn/general/upload-images.php
  paste_data_images: true, // 图片是否可粘贴
  // images_upload_url: `${import.meta.env.VITE_API_HOST}/upms/sys-file/upload`,
  images_upload_handler: (blobInfo: any, success: any, failure: any) => {
    if (blobInfo.blob().size / 1024 / 1024 > 5) {
      failure('上传失败，图片大小请控制在 5M 以内')
    }
    else {
      const formData = new FormData()
      formData.append('file', blobInfo.blob())
      // console.log(formData.get('file'), blobInfo.blob())
      uploadImg(formData).then((res: any) => {
        success(import.meta.env.VITE_API_BASE + res.url)
      })
    }
  },
  // setup(editor) {
  // editor.on('init', (ed) => {
  //     console.log(ed)
  //     ed.target.editorCommands.execCommand('fontName', false, '宋体')
  //     ed.target.editorCommands.execCommand('fontSize', false, '20px')
  //     ed.target.editorCommands.execCommand('lineHeight', false, '1.75')
  //   })
  // },

  setup(editor: any) {
    editor.on('keyup', () => {
      if (editor.getContent() !== '') {
        editor.dom.removeClass(editor.getBody(), 'mce-placeholder')
        editor.getBody().removeAttribute('data-mce-bogus')
      }
    })
  },
}
// 默认格式，一个空格都不要改！
const extraStyle
  = '<div style="font-size: 18px; font-family: \'微软雅黑\'; line-height: 2;">'
const richText = ref(props.value)
const valueComputed = computed(() => props.value)
// const { setRichText } = useRichStore()
watch(valueComputed, (newV) => {
  if (newV === undefined || newV === null) {
    richText.value = ''
  }
  else {
    if (newV.startsWith(extraStyle)) {
      newV = newV.replace(extraStyle, '')
      newV = newV.replace('</div>', '')
    }
    richText.value = newV
  }
})

watch(richText, (newVal) => {
  props.fetch && props.fetch(richText.value as string)
  emits('update:value', newVal)
})
onMounted(() => {
  tinymce.init({})
})
</script>
<template>
  <div>
    <Editor v-model="richText" :init="init" :disabled="disabled" />
  </div>
</template>

<style lang="scss">
.tox-tinymce-aux {
  z-index: 99999 !important;
}

.tinymce.ui.FloatPanel {
  z-index: 99;
}
</style>
