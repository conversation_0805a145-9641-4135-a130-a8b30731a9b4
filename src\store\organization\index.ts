import { defineStore } from 'pinia'
import { cloneDeep } from 'lodash-es'
import { getUserByDepartmentId } from '@/services/organization'

export const useOrganizationStore = defineStore('organization', {
  state: () => ({
    // 所有人
    allPeople: [] as any[],
    // 组织一下的人
    orgPeople1: [] as string[],
    // 拆分组织二下的人
    orgPeople2: [] as string[],
  }),

  getters: {
    getAllPeople(state) {
      return state.allPeople
    },
    getOrgPeople1(state) {
      return state.orgPeople1
    },
    getOrgPeople2(state) {
      return state.orgPeople2
    },
  },

  actions: {
    // 点击拆分按钮重置所有状态
    resetOrgPeople() {
      this.allPeople = []
      this.orgPeople1 = []
      this.orgPeople2 = []
    },
    // 设置组织1下选中的人员
    setOrgPeople1(orgIdList: string[]) {
      this.orgPeople1 = cloneDeep(orgIdList)
    },
    // 设置组织2下选中的人员
    setOrgPeople2(orgIdList: string[]) {
      this.orgPeople2 = cloneDeep(orgIdList)
    },
    // 获取组织下所有用户
    setAllPeople(orgId: string) {
      return new Promise((resolve, reject) => {
        // 获取部门下的用户列表
        getUserByDepartmentId(orgId)
          .then((res) => {
            this.allPeople = res.map((item: any) => {
              return {
                ...item,
                value: item.userId,
                label: item.trueName,
                disabled: false,
              }
            })
            resolve(0)
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
  },
})
