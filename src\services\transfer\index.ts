import type {
  AddTransferItemType,
  RoleItemType,
  SaveApproverType,
  SubmitCheckDataType,
  TransferTableItemType,
} from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/**
 * @module 组织关系转接
 * @description 查询我发起的转接关系表
 * @param {any} params.transferType 调动类型 0:系统内调动 1:系统外调动 2:调出系统外
 * @param {any} params.phaseStatus 审核状态 0:待审批 1:已驳回 2:已撤销 3:已完成
 */
export function getPartyMemberTransferSponsorTableList(
  params: {
    userName: string
    transferType: string | number | null
    phaseStatus: string | number | null
  } & PaginationReq,
) {
  return commonReq.get<PaginationRes<TransferTableItemType>>({
    url: '/org-construction/backend/v1/transfer_flow_approval/submitPage',
    params,
  })
}

/**
 * @module 组织关系转接
 * @description 查询待审核的转接关系表
 * @param {any} params.transferType 调动类型 0:系统内调动 1:系统外调动 2:调出系统外
 * @param {any} params.phaseStatus 审核状态 0:待审批 1:已驳回 2:已撤销 3:已完成
 */
export function getPartyMemberTransferCheckPendingTableList(
  params: {
    userName: string
    transferType: string | number | null
    phaseStatus: string | number | null
  } & PaginationReq,
) {
  return commonReq.get<PaginationRes<TransferTableItemType>>({
    url: '/org-construction/backend/v1/transfer_flow_approval/todoPage',
    params,
  })
}

/**
 * @module 组织关系转接
 * @description 查询已审核的转接关系表
 * @param {any} params.transferType 调动类型 0:系统内调动 1:系统外调动 2:调出系统外
 * @param {any} params.phaseStatus 审核状态 0:待审批 1:已驳回 2:已撤销 3:已完成
 */
export function getPartyMemberTransferCheckedTableList(
  params: {
    userName: string
    transferType: string | number | null
    phaseStatus: string | number | null
  } & PaginationReq,
) {
  return commonReq.get<PaginationRes<TransferTableItemType>>({
    url: '/org-construction/backend/v1/transfer_flow_approval/donePage',
    params,
  })
}

/**
 * @module 组织关系转接
 * @description 添加组织关系转接
 */
export function addPartyMemberTransfer(data: AddTransferItemType) {
  return commonReq.post({
    url: '/org-construction/backend/v1/transfer',
    data,
  })
}

/**
 * @module 组织关系转接
 * @description 查看组织关系转接
 */
export function viewPartyMemberTransfer(id: string) {
  return commonReq.get<AddTransferItemType>({
    url: `/org-construction/backend/v1/transfer/${id}`,
  })
}

/**
 * @module 组织关系转接
 * @description 审核组织关系转接
 */
export function checkPartyMemberTransfer(data: SubmitCheckDataType) {
  return commonReq.post({
    url: '/org-construction/backend/v1/transfer_flow_approval/approve',
    data,
  })
}

/**
 * @module 组织关系转接
 * @description 撤销组织关系转接
 */
export function revocationPartyMemberTransfer(data: { id: string }) {
  return commonReq.post({
    url: '/org-construction/backend/v1/transfer_flow_approval/revoke',
    data,
  })
}

/**
 * @module 组织关系转接
 * @description 保存组织关系转接审批流
 */
export function savePartyMemberTransferApprover(data: SaveApproverType) {
  return commonReq.post({
    url: '/org-construction/backend/v1/transfer_flow_design/saveFlowDesign',
    data,
  })
}

/**
 * @module 组织关系转接
 * @description 查看组织关系转接审批流
 */
export function viewPartyMemberTransferApprover() {
  return commonReq.get({
    url: '/org-construction/backend/v1/transfer_flow_design/getFlowDesign',
  })
}

/**
 * @module 组织关系转接
 * @description 获取角色列表
 */
export function getRoleList() {
  return commonReq.get<RoleItemType[]>({
    url: '/upms/role/list',
  })
}

/**
 * @module 组织关系转接
 * @description 查询审核后下次点击审核按钮是否有权限
 */
export function getPartyMemberTransferCheckStatus(params: {
  id: string
  approvalId: string
}) {
  return commonReq.get({
    url: '/org-construction/backend/v1/transfer/approval/detail/next',
    params,
  })
}
