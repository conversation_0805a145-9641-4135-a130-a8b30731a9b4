import type {
  AllTargetList,
  EvaluationType,
  PartyBuildingAdd,
  PartyBuildingDetail,
  PartyBuildingListItem,
  RelateAdd,
  RelateListItem,
  RelateMark,
  RelateMarkListRow,
  RelateMarkListSearchParams,
  RelateWriteDeadLine,
  SearchRelateList,
  StatisticScoreList,
  partyOrganizationItem,
} from './types'
import { commonReq, downloadReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 获取党建清单列表 */
export function getPartyBuildList(
  params: { year: string; month: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<PartyBuildingListItem>>({
    url: '/party-affairs/backend/v1/building-inventory',
    params,
  })
}

/** 新建党建清单 */
export function postPartyBuildList(data: PartyBuildingAdd) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/building-inventory',
    data,
  })
}

/** 编辑党建清单 */
export function putPartyBuildList(data: PartyBuildingAdd) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/building-inventory',
    data,
  })
}

/** 下发单个清单 */
export function putIssueSingleParty(id: string) {
  return commonReq.put({
    url: `/party-affairs/backend/v1/building-inventory/status/${id}`,
  })
}

/** 删除党建清单 */
export function deletePartyBuildList(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/building-inventory/',
    params: { ids },
  })
}

/** 查看单个党建清单 */
export function getSinglePartyListDetail(id: string) {
  return commonReq.get<PartyBuildingDetail>({
    url: `/party-affairs/backend/v1/building-inventory/${id}`,
  })
}

/** 下发单个党建清单 */
export function putSinglePartyList(id: string) {
  return commonReq.put<PartyBuildingDetail>({
    url: `/party-affairs/backend/v1/building-inventory/status/${id}`,
  })
}

/** 新增考核类别 */
export function postEvaluationTypes(data: EvaluationType) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/building-inventory/category',
    data,
  })
}
/** 修改考核类别 */
export function putEvaluationTypes(data: EvaluationType) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/building-inventory/category',
    data,
  })
}

/** 删除考核类别 */
export function deleteEvaluationTypes(id: string) {
  return commonReq.delete({
    url: `/party-affairs/backend/v1/building-inventory/category/${id}`,
  })
}

/** 查看清单下的所有指标 */
export function getAllTargetList(id: string) {
  return commonReq.get<AllTargetList>({
    url: `/party-affairs/backend/v1/building-inventory/target/${id}`,
  })
}

/** 关联指标项 */
export function postRelate(data: RelateAdd) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/building-inventory/target/relation',
    data,
  })
}

/** 关联指标项列表 */
export function getRelateList(params: SearchRelateList & PaginationReq) {
  return commonReq.get<PaginationRes<RelateListItem>>({
    url: '/party-affairs/backend/v1/building-inventory/target/relation',
    params,
  })
}

/** 关联指标项打分列表 */
export function getRelateMarkList(params: RelateMarkListSearchParams) {
  return commonReq.get<RelateMarkListRow[]>({
    url: '/party-affairs/backend/v1/building-inventory/target/mark',
    params,
  })
}

/** 关联指标项打分 */
export function putRelateMark(data: RelateMark) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/building-inventory/target/mark',
    data,
  })
}

/** 关联指标项填写截止日期 */
export function putRelateDeadLine(data: RelateWriteDeadLine) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/building-inventory/target',
    data,
  })
}

/** 删除关联指标项 */
export function deleteRelate(id: string) {
  return commonReq.delete({
    url: `/party-affairs/backend/v1/building-inventory/target/relation/${id}`,
  })
}

/** 导出指标 */
export function postExportTargetFile(id: string) {
  return downloadReq.post({
    url: `/party-affairs/backend/v1/building-inventory/target/export/${id}`,
    responseType: 'blob',
  })
}

/** 统计得分列表 */
export function getStatisticScoreList(id: string) {
  return commonReq.get<PaginationRes<StatisticScoreList>>({
    url: `/party-affairs/backend/v1/building-inventory/target/score/${id}`,
  })
}

/** 导出得分 */
export function postExportScoreFile(id: string) {
  return downloadReq.post({
    url: `/party-affairs/backend/v1/building-inventory/target/score/export/${id}`,
    responseType: 'blob',
  })
}

/** 获取党组织下拉选择树 */
export function getPartyOrganization() {
  return commonReq.get<partyOrganizationItem[]>({
    url: '/org-construction/organization/tree',
  })
}

/** 获取党组织下拉选择树 */
export function getCurrentPartyOrganization() {
  return commonReq.get<partyOrganizationItem[]>({
    url: '/org-construction/organization/currentTree',
  })
}

/** 获取用户所属党组织下拉树 */
export function getCurrentPartyOrganizationByUser() {
  return commonReq.get<partyOrganizationItem[]>({
    url: '/org-construction/orgconstruction_change/cur_user_manage_depts',
  })
}
