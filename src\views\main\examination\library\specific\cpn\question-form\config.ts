/*
 * @Description: 题目表单相关配置
 */
import type { FormRules } from 'naive-ui'
import type { IExaminationQuestionForm } from '@/services/examination'

export const formRules: FormRules = {
  level: [{ required: true }],
  name: [{ required: true, message: '请输入题干', trigger: 'change' }],
  optionList: [
    {
      required: true,
      validator(rule: any, value: []) {
        if (!value.length) {
          return new Error('请添加选项')
        } else {
          return true
        }
      },
      trigger: 'change',
    },
  ],
  answer: [{ required: true, message: '请选择正确答案' }],
  answerArr: [{ required: true, message: '请选择正确答案' }],
  labelIdList: [
    {
      required: true,
      validator(rule: any, value: []) {
        if (!value.length) {
          return new Error('请选择标签')
        } else {
          return true
        }
      },
    },
  ],
}

export function getFormData(): IExaminationQuestionForm {
  return {
    type: 1,
    level: 1,
    name: '',
    optionList: [],
    answer: '',
    analysis: '',
    source: '',
    labelIdList: [],
    libraryId: 0,
    answerArr: [],
  }
}
