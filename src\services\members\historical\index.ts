import type { UserDetailItem, UserItem } from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 获取用户列表 */
export function getHistoryUserList(
  params: { trueName: string; deptId: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<UserItem>>({
    url: '/upms/portal-user-history',
    params,
  })
}

/** 获取用户详细信息 */
export function getHistoryUserInfo(id: string) {
  return commonReq.get({
    url: `/upms/portal-user-history/${id}`,
  })
}

/** 禁用或启用用户 */
export function putDisabledUser(id: string) {
  return commonReq.put({
    url: `/upms/portal-user/lock/${id}`,
  })
}

/** 获取用户详细信息 */
export function getUserInfo(id: string) {
  return commonReq.get({
    url: `/upms/portal-user/users/${id}`,
  })
}

/** 添加用户 */
export function addUserItem(data: UserDetailItem) {
  return commonReq.post({
    url: '/upms/portal-user/users',
    data,
  })
}

/** 编辑用户 */
export function editorUserItem(data: UserDetailItem) {
  return commonReq.put({
    url: '/upms/portal-user/users',
    data,
  })
}

/** 删除用户 */
export function deleteUsers(ids: string) {
  return commonReq.delete({
    url: '/upms/portal-user/users',
    params: { ids },
  })
}

/** 导入用户文件 */
export function importUserFile(data: FormData) {
  return commonReq.post({
    url: '/upms/portal-user/exports',
    data,
  })
}

/** 重置用户密码 */
export function resetPwd(id: string) {
  return commonReq.put({
    url: `/upms/portal-user/password/${id}`,
  })
}

/** 恢复历史党员 */
export function putRecover(id: string) {
  return commonReq.put({
    url: '/upms/portal-user-history',
    params: { id },
  })
}
