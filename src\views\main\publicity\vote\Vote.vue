<script setup lang="ts">
import { ArrowDropDownFilled } from '@vicons/material'
import SliderMenu from '@/components/SliderMenu.vue'
import { useVoteStore } from '@/store/vote/vote'
import { sessionCache } from '@/utils/cache'
const router = useRouter()
const route = useRoute()
const voteStore = useVoteStore()
const currentOption = ref(1)
const showSubMenu = ref(false)
const title = computed(() => `${voteStore.getCurrentYear}年民主评议`)
const selectOption = (val: number) => {
  currentOption.value = val
  sessionCache.set('vote_currentTab', val)
  const query = {
    reviewId: voteStore.getQueryData.reviewId,
    year: voteStore.getQueryData.year,
  }

  // 党员
  if (val === 1) {
    router.replace({ name: 'talk-about-party-member', query })
  }
  // 支部班子
  if (val === 2) {
    router.replace({ name: 'talk-about-branch', query })
  }
}
// hover标题切换年份
const replacePage = () => {
  router.replace({ name: 'vote-activity' })
}

// 监听当前菜单状态
watch(
  () => voteStore.getShowMenu,
  (newVal) => {
    showSubMenu.value = newVal
  },
  { immediate: true },
)

// 监听投票打分当前模块下的路由变化
watch(
  () => route.fullPath,
  (newVal) => {
    if (newVal === '/main/vote/talk-about') {
      currentOption.value = 1
      sessionCache.set('vote_currentTab', 1)
      voteStore.clearVoteMenuDataAction()
    }
  },
  { immediate: true },
)
</script>
<template>
  <slider-menu v-if="!showSubMenu" />
  <layout-container v-else :side-width="200" :show-collapse-trigger="false">
    <template #side>
      <div class="h-full">
        <div
          class="text-[14px] font-[500] text-[#333] pt-[1px] mb-[28px] flex flex-row items-center justify-center"
        >
          <n-popover placement="bottom" trigger="click">
            <template #trigger>
              <div class="flex items-center cursor-pointer">
                <span class="text-box font-[400] text-[#333]">{{ title }}</span>
                <n-icon color="#7E7E7E" size="16">
                  <ArrowDropDownFilled />
                </n-icon>
              </div>
            </template>
            <div class="w-[100px] flex flex-col items-center justify-center">
              <span class="cursor-pointer" @click="replacePage">切换年份</span>
            </div>
          </n-popover>
        </div>
        <div
          class="flex flex-col items-start justify-start gap-[20px] box-border pl-[20px] text-[12px]"
        >
          <div class="cursor-pointer" @click="selectOption(1)">
            <span class="option" :class="{ active: currentOption === 1 }">民主评议党员</span>
          </div>
          <div class="cursor-pointer" @click="selectOption(2)">
            <span class="option" :class="{ active: currentOption === 2 }">民主评议党支部班子</span>
          </div>
        </div>
      </div>
    </template>
    <template #main>
      <component
        :is="route.matched[route.matched.length - 1].components?.default"
      />
    </template>
  </layout-container>
</template>
<style lang="scss" scoped>
.option {
  display: block;
  min-width: 150px;
  padding: 6px 10px;
  background-color: transparent;
}
.active {
  background-color: rgb(239, 239, 245);
  &:hover {
    color: #ac241d;
  }
}
</style>
