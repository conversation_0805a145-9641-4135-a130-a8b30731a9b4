<script lang="ts" setup>
import { DownloadRound } from '@vicons/material'
import { NButton, type TreeSelectOption } from 'naive-ui'
import { transferType } from './config'
import { getOrganizationalTransferTableColumns } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/config'
import { useRecordBookStore } from '@/store/recordBook'
import { getTransferList } from '@/services/recordBookMaintenance'
import type { OrganizationalTransferType } from '@/services/recordBookMaintenance/types'

const recordBookStore = useRecordBookStore()

const router = useRouter()

const fileLoading = ref<boolean>(false)
const pageNum = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const total = ref(0)
const selectedDeptId = ref<string | null>(null)
const selectedYear = ref<string | null>(null)
const selectedTransferType = ref<string | null>(null)
const transferTimeSort = ref<'' | '0' | '1'>('')
const currentDownloadFileType = ref<'word' | 'pdf'>('word')
const showPopConfirm = ref<boolean>(false)

const calcOrganizationListTree = computed(() => {
  return recordBookStore.getOrganizationListTreeList
})
const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})

// 获取当前年份
const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

const tableColumns: any = getOrganizationalTransferTableColumns((row) => {
  return [
    h(
      'div',
      {
        class: 'flex flex-row gap-x-[10px]',
      },
      {
        default: () => [
          h(
            NButton,
            {
              text: true,
              color: '#AC241D',
              onClick: () => {
                router.push({
                  name: 'checked',
                  query: {
                    id: row.id,
                  },
                })
              },
            },
            {
              default: () => '查看',
            },
          ),
        ],
      },
    ),
  ]
})
const tableData = ref<OrganizationalTransferType[]>([])

const currentYearOptions = computed(() => {
  return recordBookStore.getYearOptions
})

function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  pageNum.value = 1
  selectedDeptId.value = v
  loadData()
  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  pageNum.value = 1
  selectedYear.value = v
  loadData()
  recordBookStore.setCurrentSelectedYear(option.value as string)
}

function handleUpdateTransferTypeSelectedValue(v: string) {
  pageNum.value = 1
  selectedTransferType.value = v
  loadData()
  recordBookStore.setCurrentSelectedTransferType(v)
}

function handleDownLoadFile() {
  fileLoading.value = true
  recordBookStore.fetchDownLoadFile(
    {
      deptId: selectedDeptId.value!,
      year: selectedYear.value!,
      transferType: (selectedTransferType.value as string) || '',
      transferTimeSort: transferTimeSort.value as string,
      type: currentDownloadFileType.value,
    },
    () => {
      fileLoading.value = false
    },
  )
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleCancelShowPopConfirm() {
  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleUpdateSorter({
  columnKey,
  order,
}: {
  columnKey: string
  order: 'ascend' | 'descend' | false
}) {
  if (columnKey === 'transferTime') {
    if (order === 'descend') {
      transferTimeSort.value = '0'
    }
    else if (order === 'ascend') {
      transferTimeSort.value = '1'
    }
    else {
      transferTimeSort.value = ''
    }
  }
  loadData()
}

watch(
  () => pageNum.value,
  () => {
    loadData()
  },
)

watch(
  () => pageSize.value,
  () => {
    pageNum.value = 1
    loadData()
  },
)

function loadData() {
  if (!selectedDeptId.value || !selectedYear.value) {
    return
  }
  loading.value = true
  getTransferList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
    transferType: selectedTransferType.value as string,
    transferTimeSort: transferTimeSort.value as string,
  })
    .then((res) => {
      total.value = res.total || 0
      tableData.value = res.records || []
    })
    .finally(() => {
      loading.value = false
    })
}

onMounted(() => {
  selectedDeptId.value = currentSelectedDept.value.deptId
  selectedYear.value = currentYear.value
  selectedTransferType.value = recordBookStore.getCurrentSelectedTransferType
  loadData()
})
</script>

<template>
  <div>
    <table-container
      v-model:page="pageNum"
      v-model:page-size="pageSize"
      :loading="loading"
      :show-delete="false"
      :show-pagination="true"
      :show-toolbar="false"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      custom-toolbar
      default-expand-all
      title="组织关系转接"
      @update-sorter="handleUpdateSorter"
    >
      <template #filters>
        <div class="">
          <n-popconfirm
            :show="showPopConfirm"
            :show-icon="false"
            placement="left"
          >
            <template #trigger>
              <n-button
                :loading="fileLoading"
                @click="showPopConfirm = !showPopConfirm"
              >
                <template #icon>
                  <n-icon size="16">
                    <DownloadRound />
                  </n-icon>
                </template>
                下载
              </n-button>
            </template>
            <div class="py-[10px]">
              <n-radio-group v-model:value="currentDownloadFileType">
                <n-radio
                  v-for="(fileType, fileTypeIndex) in [
                    { label: 'word', value: 'word' },
                    { label: 'pdf', value: 'pdf' },
                  ]"
                  :key="fileTypeIndex"
                  :value="fileType.value"
                >
                  {{ fileType.label }}
                </n-radio>
              </n-radio-group>
            </div>

            <template #action>
              <div class="w-[100%] flex flex-row justify-center items-center">
                <n-button
                  size="small"
                  type="primary"
                  @click="handleDownLoadFile(item)"
                >
                  确定
                </n-button>
                <n-button size="small" @click="handleCancelShowPopConfirm">
                  取消
                </n-button>
              </div>
            </template>
          </n-popconfirm>
        </div>
      </template>
      <template #btns>
        <div class="flex flex-row items-center justify-between gap-[10px]">
          <n-tree-select
            v-model:value="selectedDeptId"
            :options="calcOrganizationListTree"
            :show-path="false"
            check-strategy="all"
            children-field="children"
            filterable
            key-field="id"
            label-field="name"
            placeholder="请选择所属党组织"
            style="width: 400px"
            value-field="id"
            @update:value="handleUpdateTreeSelectedValue"
          />
          <n-select
            v-model:value="selectedYear"
            :options="currentYearOptions"
            placeholder="请选择年份"
            size="medium"
            style="width: 200px"
            @update:value="handleUpdateYearSelectedValue"
          />
          <n-select
            v-model:value="selectedTransferType"
            :options="transferType"
            clearable
            placeholder="请选择调动类型"
            size="medium"
            style="width: 200px"
            @update:value="handleUpdateTransferTypeSelectedValue"
          />
        </div>
      </template>
    </table-container>
  </div>
</template>

<style lang="scss" scoped></style>
