export interface CultureShowListItem {
  [key: string]: any
  id: string
  username: string
  orgName: string
  title: string
  content: string
  fileList: FileList[]
  publishTime: string
  auditTime: string
  reason: string
  rejectedPerson: string
  topStatus: string
  isRelease?: string
}

export interface CultureShowSearchParams {
  title?: string
  content?: string
  username?: string
  status?: string
  type?: string
}

export interface CultureShowAuditParams {
  id?: string
  /** 驳回原因   */
  reason?: string
  /** 审核状态（1：通过 2：驳回） */
  status?: string
}

export interface FileList {
  id: string
  fileName: string
  original: string
}

export interface CultureShowListItemDetail {
  id: string
  title: string
  content: string
  reason?: string
  fileList: FileList[]
}
