<script setup lang="ts">
import { NSwitch } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import ChangeOrganize from '../../cpn/ChangeOrganize.vue'
import ProjectSecretaryForm from '../cpn/ProjectSecretaryForm.vue'
import { getTableColumns } from './config'
import { useDrawerEdit, useMyTable } from '@/hooks'
import { useOrganizeGardenStore } from '@/store/organize-garden'
import {
  deleteProjectSecretary,
  getProjectSecretaryList,
  postProjectSecretaryHidden,
  putProjectSecretaryTop,
} from '@/services/organize-garden'
import DeleteButton from '@/components/DeleteButton.vue'
const { getDeptId } = useOrganizeGardenStore()
const formDataReactive = ref({
  orgId: '',
  title: null,
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getProjectSecretaryList, formDataReactive, {
  batchDeleteTable: true,
  delApi: deleteProjectSecretary,
})
watch(
  getDeptId,
  async(value) => {
    if (value) {
      formDataReactive.value.orgId = value
      // loadData()
    }
  },
  {
    immediate: true,
  },
)
// 新增/编辑党建清单抽屉
const idEditRef = ref()
const addOrgStyleFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('书记项目', handelConfirmEdit)

/** 确定保存 */
function handelConfirmEdit() {
  addOrgStyleFormRef.value?.validateAndSave()
}

watch(showEditRef, (newV) => {
  if (!newV) {
    addOrgStyleFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

async function switchTop(value: boolean, id: string) {
  await putProjectSecretaryTop(id)
  window.$message.success('修改置顶成功')
  loadData()
}

async function switchHidden(value: boolean, id: string) {
  await postProjectSecretaryHidden(id)
  window.$message.success('操作成功')
  loadData()
}

/** 列表操作 */
const tableColumns = getTableColumns(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
          display: 'flex',
          gap: '15px',
        },
      },
      [
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'view'
              showEditRef.value = true
            },
          },
          { default: () => '查看' },
        ),
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'modify'
              showEditRef.value = true
            },
          },
          { default: () => '编辑' },
        ),
        h(DeleteButton, {
          handleConfirm: () => handleSingleDelete(String(row.id)),
        }),
      ],
    )
  },
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => switchTop(value, row.id),
      value: Boolean(Number(row.isTop)),
    }),
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => switchHidden(value, row.id),
      value: Boolean(Number(row.isHidden)),
    }),
)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
</script>
<template>
  <div class="w-full px-[20px] py-[20px]">
    <ChangeOrganize />

    <table-container
      v-model:page="currentPage"
      v-model:page-size="pageSize"
      style="padding-left: 0"
      title="书记项目"
      :loading="loading"
      :show-toolbar="false"
      custom-toolbar
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :checked-row-keys="checkedRowKeys"
      @click-add="handleClickAdd"
      @click-delete="handleBatchDelete"
      @update-page="onUpdatePage"
      @update-page-size="onUpdatePageSize"
      @update-checked-row-keys="onUpdateCheckedRowKeys"
    >
      <template #btns>
        <n-button size="small" type="primary" @click="handleClickAdd">
          <template #icon>
            <n-icon>
              <plus-round />
            </n-icon>
          </template>
          添加
        </n-button>

        <n-button size="small" @click="handleBatchDelete">
          <template #icon>
            <n-icon>
              <delete-forever-round />
            </n-icon>
          </template>
          删除
        </n-button>
      </template>
      <!-- <template #filters>
        <n-input
          v-model:value="formDataReactive.title"
          style="width: 200px"
          size="small"
          placeholder="请输入创新做法标题"
          clearable
        />
      </template> -->
    </table-container>
  </div>

  <n-drawer v-model:show="showEditRef" :show-mask="false" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <project-secretary-form
        :id="idEditRef"
        ref="addOrgStyleFormRef"
        :org-id="formDataReactive.orgId"
        :enter-type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            v-if="editTypeRef !== 'view'"
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            {{ editTypeRef !== 'view' ? '取消' : '关闭' }}
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
