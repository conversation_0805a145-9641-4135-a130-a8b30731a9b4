import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  userId: [
    {
      required: true,
      message: '用户不能为空',
      trigger: 'blur',
    },
  ],
  // trueName: [
  //   {
  //     required: true,
  //     message: '姓名不能为空',
  //     trigger: 'input',
  //   },
  // ],
  // phone: [
  //   { required: true, message: '手机号不能为空', trigger: 'input' },
  //   {
  //     message: '手机号码格式错误',
  //     trigger: 'blur',
  //     pattern: MOBILE_NUM,
  //   },
  // ],
  // identityId: [
  //   { required: true, message: '身份证号不能为空', trigger: 'input' },
  //   {
  //     message: '身份证号码格式错误',
  //     trigger: 'blur',
  //     pattern: ID_NUM,
  //   },
  // ],

  organizationId: [
    {
      required: true,
      message: '考核组织不能为空',
      trigger: 'blur',
      type: 'string',
    },
  ],
  applyTime: [
    {
      required: true,
      message: '申请时间不能为空',
      trigger: 'blur',
      type: 'string',
    },
  ],
}
