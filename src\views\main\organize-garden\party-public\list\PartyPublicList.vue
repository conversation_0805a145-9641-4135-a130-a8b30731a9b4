<script setup lang="ts">
import { NSwitch } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import AddPublicItemForm from '../cpn/AddPublicItemForm.vue'
import ChangeOrganize from '../../cpn/ChangeOrganize.vue'
import { getTableColumns } from './config'
import { useDrawerEdit, useMyTable } from '@/hooks'
import {
  deletePublic,
  getOrgPublicList,
  postOrgPublicHidden,
  putOrgPublicTop,
} from '@/services/party-public'
import DeleteButton from '@/components/DeleteButton.vue'
import { judgePermission } from '@/directive/permission/ifHasPermi'
import { useOrganizeGardenStore } from '@/store/organize-garden'

const { getDeptId } = useOrganizeGardenStore()

// 筛选项：类别id和资讯标题
const formDataReactive = ref({
  deptId: '',
  annexName: null,
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getOrgPublicList, formDataReactive, {
  batchDeleteTable: true,
  delApi: deletePublic,
})

watch(
  getDeptId,
  async(value) => {
    if (value) {
      formDataReactive.value.deptId = value
    }
  },
  {
    immediate: true,
  },
)

// 新增/编辑党务公开抽屉
const idEditRef = ref()
const addPublicFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('党务公开', handelConfirmEdit)
/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
function handelConfirmEdit() {
  addPublicFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addPublicFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

/** 列表操作 */
const tableColumns = getTableColumns(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
          display: 'flex',
          gap: '15px',
        },
      },
      [
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'view'
              showEditRef.value = true
            },
          },
          { default: () => '查看' },
        ),
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'modify'
              showEditRef.value = true
            },
          },
          { default: () => '编辑' },
        ),
        h(DeleteButton, {
          handleConfirm: () => handleSingleDelete(String(row.id)),
        }),
      ],
    )
  },
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => switchTop(value, row.id),
      value: Boolean(Number(row.isTop)),
    }),
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => switchHidden(value, row.id),
      value: Boolean(Number(row.isHidden)),
    }),
)
async function switchTop(value: boolean, id: string) {
  await putOrgPublicTop(id)
  window.$message.success('修改置顶成功')
  loadData()
}

async function switchHidden(value: boolean, id: string) {
  await postOrgPublicHidden(id)
  window.$message.success('操作成功')
  loadData()
}
</script>
<template>
  <div class="w-full px-[20px] py-[20px]">
    <ChangeOrganize />

    <table-container
      v-model:page="currentPage"
      v-model:page-size="pageSize"
      style="padding-left: 0"
      title="党务公开"
      :loading="loading"
      :show-toolbar="false"
      custom-toolbar
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      :checked-row-keys="checkedRowKeys"
      :show-add="judgePermission('publicity_add_btn')"
      :show-delete="judgePermission('publicity_delete_btn')"
      @click-add="handleClickAdd"
      @click-delete="handleBatchDelete"
      @update-page="onUpdatePage"
      @update-page-size="onUpdatePageSize"
      @update-checked-row-keys="onUpdateCheckedRowKeys"
    >
      <template #btns>
        <n-button size="small" type="primary" @click="handleClickAdd">
          <template #icon>
            <n-icon>
              <plus-round />
            </n-icon>
          </template>
          添加
        </n-button>

        <n-button size="small" @click="handleBatchDelete">
          <template #icon>
            <n-icon>
              <delete-forever-round />
            </n-icon>
          </template>
          删除
        </n-button>
      </template>
      <template #filters>
        <n-input
          v-model:value="formDataReactive.annexName"
          style="width: 200px"
          size="small"
          placeholder="请输入附件名称"
          clearable
        />
      </template>
    </table-container>
  </div>

  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <add-public-item-form
        :id="idEditRef"
        ref="addPublicFormRef"
        :org-id="formDataReactive.deptId"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template v-if="editTypeRef !== 'view'" #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
