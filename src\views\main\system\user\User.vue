<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui'
import { NButton, NSwitch } from 'naive-ui'
import { DocumentImport } from '@vicons/carbon'
import { PlusRound } from '@vicons/material'
import { getTableColumns } from './config'
import NoticeForm from './cpn/NoticeForm.vue'
import { useDrawerEditOrganize, useMyTable } from '@/hooks'
import { useOrganizationListOptions } from '@/hooks/use-select-options'
import ifHasPermi from '@/directive/permission/ifHasPermi'
import {
  deleteUsers,
  getUserList,
  importUserFile,
  putDisabledUser,
  resetPwd,
} from '@/services/system/User'
import DeleteButton from '@/components/DeleteButton.vue'

const filterReactive = ref<{ trueName: string; deptId: string | null }>({
  trueName: '',
  deptId: null,
})
// 有接口后添加：loading,tableData
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleBatchDelete,
  handleSingleDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getUserList, filterReactive, {
  batchDeleteTable: false,
  delApi: deleteUsers,
})
const { organizationListTree } = useOrganizationListOptions()
const handleUpdateValue = (v: string) => {
  filterReactive.value.deptId = v
}

// 新增/编辑党建清单抽屉
const idEditRef = ref()
const addNoticeFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEditOrganize({ name: '用户', confirmFn: handelConfirmEdit })

/** 添加根节点 */
const handleClickAddRootNode = () => {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addNoticeFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns(
  (row) => {
    return [
      h(
        NButton,
        {
          onClick: () => {
            idEditRef.value = row.userId
            editTypeRef.value = 'modify'
            showEditRef.value = true
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
            display: ifHasPermi('portal_user_edit_btn'),
          },
        },
        {
          default: () => '编辑',
        },
      ),
      h(
        NButton,
        {
          onClick: () => {
            window.$dialog.warning({
              title: '提示',
              content: '确定重置为初始密码？',
              positiveText: '确定',
              negativeText: '取消',
              onPositiveClick: () => {
                resetPwd(row.userId)
              },
            })
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '重置密码',
        },
      ),
      h(DeleteButton, {
        handleConfirm: () => handleSingleDelete(String(row.userId)),
      }),
    ]
  },
  (row) => {
    return h(
      NSwitch,
      {
        checkedValue: '0',
        uncheckedValue: '9',
        value: row.lockFlag,
        loading: row.isLoading,
        onUpdateValue(v: any) {
          if (row.lockFlag === '9') {
            row.isLoading = true
            putDisabledUser(row.userId)
              .then((_: any) => {
                row.lockFlag = row.lockFlag === '9' ? '0' : '9'
              })
              .catch(() => {})
              .finally(() => {
                loadData()
                row.isLoading = false
              })
          }
          else {
            window.$dialog.warning({
              title: '提示',
              content: '禁用后用户将无法登录平台，确认禁用？',
              positiveText: '确定',
              negativeText: '取消',
              onPositiveClick: () => {
                row.isLoading = true
                putDisabledUser(row.userId)
                  .then((_: any) => {
                    row.lockFlag = row.lockFlag === '9' ? '0' : '9'
                  })
                  .catch(() => {})
                  .finally(() => {
                    loadData()
                    row.isLoading = false
                  })
              },
            })
          }
        },
      },
      {
        checked: '启用',
        unchecked: '禁用',
      },
    )
  },
)

const showExam = ref(false)
const handelImportFn = () => {
  showExam.value = true
}
/** 导入指标 */
const handleClickImportExcel = (options: {
  file: UploadFileInfo | any
  fileList: Array<UploadFileInfo>
  event?: Event
}) => {
  const formData = new FormData()
  formData.append('file', options.file.file)
  importUserFile(formData)
    .then((res) => {
      window.$message.success('用户导入成功')
      loadData()
      showExam.value = false
    })
    .catch((_) => {})
}

/** 下载模板 */
const handleClickDownloadTemplate = () => {
  const url = '/static/UserTemplate.xls'
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', '用户导入模板.xls')
  link.click()
  showExam.value = false
}
onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="用户管理"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :loading="loading"
    :show-delete="false"
    :checked-row-keys="checkedRowKeys"
    default-expand-all
    @click-add="handleClickAddRootNode"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #filters>
      <n-button
        v-hasPermi="['portal_user_add_btn']"
        size="small"
        type="primary"
        @click="handleClickAddRootNode"
      >
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <n-button size="small" type="info" @click="handelImportFn">
        <template #icon>
          <n-icon>
            <document-import />
          </n-icon>
        </template>
        导入
      </n-button>
    </template>
    <template #btns>
      <div
        class="w-[500px] flex flex-row justify-start items-center gap-[10px]"
      >
        <!-- <n-cascader
          v-model:value="filterReactive.deptId"
          size="small"
          :options="organizationListTree"
          value-field="id"
          label-field="name"
          children-field="children"
          check-strategy="all"
          :show-path="false"
          clearable
          placeholder="请选择所属党组织"
          @update:value="handleUpdateValue"
        /> -->
        <n-tree-select
          v-model:value="filterReactive.deptId"
          :options="organizationListTree"
          value-field="id"
          label-field="name"
          key-field="id"
          children-field="children"
          check-strategy="all"
          placeholder="请选择所属党组织"
          :show-path="false"
          clearable
          filterable
          size="small"
          @update:value="handleUpdateValue"
        />
        <n-input
          v-model:value="filterReactive.trueName"
          size="small"
          placeholder="请输入姓名"
          clearable
        />
      </div>
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle">
      <notice-form
        :id="idEditRef"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
  <custom-dialog v-model:show="showExam" :show-action="false" title="导入">
    <div class="flex justify-center items-center h-[100px]">
      <div class="flex gap-[30px]">
        <n-button size="small" @click="handleClickDownloadTemplate">
          <template #icon>
            <n-icon>
              <document-import />
            </n-icon>
          </template>
          下载模板
        </n-button>
        <n-upload
          accept=".xls,.xlsx"
          :show-file-list="false"
          @change="handleClickImportExcel"
        >
          <n-button size="small">
            <template #icon>
              <n-icon>
                <document-import />
              </n-icon>
            </template>
            导入用户
          </n-button>
        </n-upload>
      </div>
    </div>
  </custom-dialog>
</template>
<style lang="scss" scoped></style>
