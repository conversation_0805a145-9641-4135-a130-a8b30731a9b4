<!--
 * @Description: 考试管理-编辑页 (分为创建、修改)
-->
<template>
  <div class="exam-edit">
    <div class="title">
      {{ title }}
    </div>

    <n-form
      ref="formRef"
      class="form"
      require-mark-placement="left"
      label-width="100"
      label-align="right"
      label-placement="left"
      :rules="formRules"
      :model="formDataReactive"
    >
      <!-- 基本信息 -->
      <div class="area base">
        <div class="area-title">
          <span class="order">1</span>
          <span class="text">基本信息</span>
        </div>
        <n-form-item label="考试名称：" path="name">
          <n-input v-model:value="formDataReactive.name" />
        </n-form-item>
        <n-form-item label="考试类型：" path="examinationType">
          <n-select
            v-model:value="formDataReactive.examinationType"
            :disabled="from === 'relation'"
            :options="[
              { value: 1, label: '普通考试' },
              { value: 2, label: '结业考试' },
              { value: 3, label: '练习自测' },
            ]"
          />
        </n-form-item>
        <n-form-item label="考试说明：">
          <n-input
            v-model:value="formDataReactive.description"
            type="textarea"
            :maxlength="120"
            show-count
          />
        </n-form-item>
      </div>

      <!-- 考试内容 -->
      <div class="area content">
        <div class="area-title">
          <span class="order">2</span>
          <span class="text">考试内容</span>
        </div>
        <n-form-item label="考试内容：" path="examPaperList">
          <paper-selector
            :original-paper-ids="formDataReactive.examPaperList"
            @paper-change="handleExamPaperListChange"
          />
        </n-form-item>
      </div>

      <!-- 考试设置 -->
      <div class="area setup">
        <div class="area-title">
          <span class="order">3</span>
          <span class="text">考试设置</span>
        </div>
        <n-form-item
          v-if="formDataReactive.examinationType !== 3"
          label="通过分数："
          path="passScore"
        >
          <n-input-number
            v-model:value="formDataReactive.passScore"
            :disabled="!passScoreRef"
            :min="0.5"
            :max="passScoreRef"
            :show-button="false"
          />
        </n-form-item>
        <n-form-item
          v-if="formDataReactive.examinationType !== 3"
          label="考试时长："
          path="durationCp"
        >
          <n-radio-group v-model:value="formDataReactive.durationCp[0]">
            <n-radio :value="-1">
              不限制时长
            </n-radio>
            <n-radio :value="0">
              限制时长
            </n-radio>
          </n-radio-group>
          <span
            v-if="formDataReactive.durationCp[0] === 0"
            class="number-input"
          >
            <n-input-number
              v-model:value="formDataReactive.durationCp[1]"
              :min="1"
            />
            <span>分钟</span>
          </span>
        </n-form-item>
        <n-form-item
          v-if="formDataReactive.examinationType !== 3"
          label="开放时间："
        >
          <n-date-picker
            v-model:value="formDataReactive.timeRange"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </n-form-item>
        <n-form-item label="答案和解析：" path="answerAnlysisRule">
          <n-radio-group v-model:value="formDataReactive.answerAnlysisRule">
            <n-radio :value="1">
              交卷后显示
            </n-radio>
            <n-radio :value="2">
              不允许查看
            </n-radio>
            <n-radio :value="3">
              仅可查看对错
            </n-radio>
          </n-radio-group>
        </n-form-item>
        <n-form-item
          v-if="formDataReactive.examinationType !== 3"
          label="补考："
          path="chancesCp"
        >
          <n-radio-group v-model:value="formDataReactive.chancesCp[0]">
            <n-radio :value="0">
              不允许
            </n-radio>
            <n-radio
              :disabled="formDataReactive.examPaperList.length < 2"
              :value="1"
            >
              允许
            </n-radio>
          </n-radio-group>
          <span v-if="formDataReactive.chancesCp[0] === 1" class="number-input">
            <n-input-number
              v-model:value="formDataReactive.chancesCp[1]"
              :min="1"
              :max="formDataReactive.examPaperList.length - 1"
            />
            <span>次</span>
          </span>
        </n-form-item>
        <n-form-item label="顺序打乱：" path="orderRule">
          <n-checkbox-group v-model:value="formDataReactive.orderRuleArr">
            <n-checkbox label="试题顺序打乱" :value="1" />
            <n-checkbox label="选项顺序打乱（单选、多选、填空）" :value="2" />
          </n-checkbox-group>
        </n-form-item>
      </div>

      <!-- 发布设置 -->
      <div v-if="formDataReactive.examinationType === 1" class="area dist">
        <div class="area-title">
          <span class="order">4</span>
          <span class="text">发布设置</span>
        </div>

        <n-form-item label="参与人员：" path="enableRangeCp">
          <n-radio-group v-model:value="formDataReactive.enableRangeCp[0]">
            <div>
              <n-radio :value="0">
                我的管理范围
              </n-radio>
            </div>
            <div style="margin-top: 16px">
              <n-radio :value="1">
                部分范围
              </n-radio>
              <template v-if="formDataReactive.enableRangeCp[0] === 1">
                <n-button size="small" @click="handleClickChooseUser">
                  选择人员
                </n-button>
                <user-selector
                  ref="userSelectorRef"
                  v-model:show="showUserSelectorRef"
                  :tree-data="companyDeptUserList"
                  @confirm="(v: any) => {
                    formDataReactive.enableRangeCp[1] =
                      handleConfirmSelectUser(v)
                  }
                  "
                />

                <n-scrollbar
                  v-if="userIdsRef && userIdsRef.length"
                  style="
                    width: 90%;
                    padding: 10px;
                    margin-top: 10px;
                    max-height: 150px;
                    border: 1px solid #eee;
                  "
                >
                  <n-tag
                    v-for="(user, index) in userIdsRef"
                    :key="user"
                    style="margin-right: 10px; margin-bottom: 10px"
                    type="info"
                    closable
                    @close="
                      () => {
                        formDataReactive.enableRangeCp[1] =
                          handleRemoveUser(index)
                      }
                    "
                  >
                    {{ user.split('&')[0] }}
                  </n-tag>
                </n-scrollbar>
              </template>
            </div>
          </n-radio-group>
        </n-form-item>
      </div>
    </n-form>

    <div style="height: 100px" />

    <div class="btns">
      <n-button type="primary" @click="handleSave(true)">
        发布
      </n-button>
      <n-button @click="handleSave()">
        保存(不发布)
      </n-button>
      <n-button @click="$router.replace({ name: 'examList' })">
        取消
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { NForm } from 'naive-ui'
import {
  NButton,
  NCheckbox,
  NCheckboxGroup,
  NDatePicker,
  NFormItem,
  NInput,
  NInputNumber,
  NRadio,
  NRadioGroup,
  NScrollbar,
  NSelect,
  NTag,
} from 'naive-ui'
import { formRules, getFormData } from './config'
import PaperSelector from './PaperSelector.vue'
import UserSelector from '@/components/user-selector'
import useUserSelector from '@/hooks/use-user-selector'
import useBaseStore from '@/store/base/index'
import type { IExaminationExamForm } from '@/services/examination/index'
import {
  getExaminationExamDetail,
  postExaminationExam,
} from '@/services/examination/index'
import { formatTimeStamp } from '@/utils/date-time-format'

const route = useRoute()
const router = useRouter()
const baseStore = useBaseStore()

const allUserLabels = computed(() => baseStore.allUserLabels)
const editType = route.params.type
const idEditing = route.query.id
const from = route.query.from
const title = editType === 'add' ? '创建考试' : '编辑考试'
const formRef = ref<InstanceType<typeof NForm>>()
const formDataReactive = reactive(getFormData())
const passScoreRef = ref<number>(0)

const {
  companyDeptUserList,
  showUserSelectorRef,
  userSelectorRef,
  userIdsRef,
  handleClickChooseUser,
  handleConfirmSelectUser,
  handleRemoveUser,
} = useUserSelector()

onBeforeMount(() => {
  if (editType === 'modify' && idEditing) {
    // 请求详情接口初始化表单
    getExaminationExamDetail(idEditing).then((res) => {
      formDataReactive.id = res.id
      formDataReactive.name = res.name
      formDataReactive.examinationType = res.examinationType
      formDataReactive.description = res.description
      formDataReactive.passScore = res.passScore
      formDataReactive.timeRange = [
        new Date(res.startTime).getTime(),
        new Date(res.endTime).getTime(),
      ]
      formDataReactive.answerAnlysisRule = res.answerAnlysisRule
      formDataReactive.examPaperList = res.examPaperList

      // 当考试类型为普通考试时，处理参与人员
      if (res.examinationType === 1) {
        if (res.enableRange === 1) {
          formDataReactive.enableRangeCp = [res.enableRange, res.userIds]
          userIdsRef.value = allUserLabels.value.filter(item =>
            res.userIds.includes(Number(item.split('&')[1])),
          )
        } else {
          formDataReactive.enableRangeCp = [res.enableRange, []]
        }
      }

      // 处理考试时长
      if (res.duration !== -1) {
        formDataReactive.durationCp = [0, res.duration]
      }
      // 处理补考
      if (res.chances > 0) {
        formDataReactive.chancesCp = [1, res.chances]
      }
      // 处理顺序打乱
      switch (res.orderRule) {
        case 0:
          formDataReactive.orderRuleArr = []
          break
        case 1:
          formDataReactive.orderRuleArr = [1]
          break
        case 2:
          formDataReactive.orderRuleArr = [2]
          break
        case 3:
          formDataReactive.orderRuleArr = [1, 2]
          break
      }
    })
  }

  baseStore.getCompanyDeptUserListAction()

  if (from === 'relation') {
    formDataReactive.examinationType = Number(route.query.examinationtype)
  }
})

// 试卷变化
function handleExamPaperListChange(v: number[], score: number) {
  passScoreRef.value = score || 0
  if (v.length === 0) {
    formDataReactive.passScore = null
  } else if (v.length <= 1) {
    formDataReactive.chancesCp = [0, null]
  } else if (
    formDataReactive.chancesCp[1]
    && v.length <= formDataReactive.chancesCp[1]
  ) {
    formDataReactive.chancesCp[1] = v.length - 1
  }
  formDataReactive.examPaperList = v
}

// 根据选中的试题顺序生成后端需要的格式
function getMixTypeText(arr: number[]) {
  if (arr.length === 1) {
    if (arr[0] === 1) {
      return '1'
    } else if (arr[0] === 2) {
      return '2'
    }
  } else if (arr.length === 2) {
    return '3'
  }
  return '0'
}

// 保存
function handleSave(isPub = false) {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      const data: IExaminationExamForm = {
        ...formDataReactive,
      }
      data.isPublished = isPub ? 1 : 0

      // 处理考试时长
      const [duration, durationTime] = formDataReactive.durationCp
      if (duration === 0) {
        data.duration = durationTime!
      }

      // 处理开放时间
      if (formDataReactive.timeRange !== null) {
        const [start, end] = formDataReactive.timeRange
        data.startTime = formatTimeStamp(start, 'YYYY-MM-DD HH:mm:ss')
        data.endTime = formatTimeStamp(end, 'YYYY-MM-DD HH:mm:ss')
      }

      // 处理补考
      const [chance, chances] = formDataReactive.chancesCp
      if (chance === 1) {
        data.chances = chances!
      }

      // 处理顺序打乱
      data.orderRule = Number(getMixTypeText(formDataReactive.orderRuleArr))

      // 当考试类型为普通考试时，处理参与人员
      if (formDataReactive.examinationType === 1) {
        const [enableRange, userIds] = formDataReactive.enableRangeCp
        data.enableRange = enableRange
        if (enableRange === 1) {
          data.userIds = userIds
        }
      }

      postExaminationExam(data).then((res) => {
        window.$message.success(res)
        router.replace({ name: 'examList' })
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.exam-edit {
  padding: 27px 20px 50px;

  > .title {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 20px;
  }

  > .form {
    :deep(.n-form-item) {
      margin-left: 35px;
    }

    :deep(.n-form-item-blank) {
      width: 40vw;
    }

    :deep(.n-radio-group),
    :deep(.n-checkbox-group) {
      .n-radio,
      .n-checkbox {
        &:nth-child(n + 2) {
          margin-left: 30px;
        }
      }
    }

    .area {
      margin-bottom: 10px;

      > .area-title {
        height: 48px;
        background: #f6faff;
        margin-bottom: 30px;
        padding-left: 23px;
        display: flex;
        align-items: center;

        > .order {
          font-size: 22px;
          font-weight: 600;
          color: #c5e4ff;
        }

        > .text {
          margin-left: 19px;
          font-size: 14px;
          font-weight: 500;
          color: #333333;
        }
      }

      .number-input {
        display: flex;
        align-items: center;

        > span {
          margin-left: 7px;
        }
      }
    }
  }

  > .btns {
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 30px 0 40px 132px;
    background: #fff;

    > button {
      min-width: 80px;
      height: 30px;
      border-radius: 3px;

      &:nth-child(n + 2) {
        margin-left: 12px;
      }
    }
  }
}
</style>
