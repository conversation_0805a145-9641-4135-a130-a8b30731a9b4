<script setup lang="ts">
import { NForm } from 'naive-ui'
import { reactive } from 'vue'
import { formRules } from './config'
import type {
  uploadFileItem,
  uploadFileItemNew,
} from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
import RichEditor from '@/components/RichEditor.vue'
import { uploadImg } from '@/services'
import {
  getCompanyShowListItem,
  postInsertCompanyShowListItem,
  putEditorCompanyShowListItem,
} from '@/services/publicity/companyShow'
import { formatTimeStamp } from '@/utils/format'
import type { InsertCommonParamsOfCompanyAndRegulation } from '@/services/publicity/companyShow/types'

interface Props {
  type?: string
  id?: string
  categoryId?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
  categoryId: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<InsertCommonParamsOfCompanyAndRegulation>({
  id: '',
  title: '',
  module: [
    {
      id: '0',
      moduleName: '',
      content: '',
    },
  ],
  publishTime: '',
  topStatus: '0',
  cover: { id: '', url: '' },
  appCover: { id: '', url: '' },
  customCover: { id: '', url: '' },
  customAppCover: { id: '', url: '' },
  categoryId: props.categoryId,
  location: '',
  fileIds: [],
  fileList: [],
  activityMemberId: [],
  type: '1',
})

const formRef = ref<InstanceType<typeof NForm>>()

onBeforeMount(() => {
  if (props.type === 'modify' && props.id) {
    getCompanyShowListItem({ id: props.id, type: 1 }).then((res) => {
      formDataReactive.location = res.location
      formDataReactive.categoryId
        = res.categoryId !== '' && res.categoryId
          ? res.categoryId
          : props.categoryId
      formDataReactive.appCover = res.appCover
        ? JSON.parse(JSON.stringify(res.appCover))
        : JSON.parse(JSON.stringify(formDataReactive.appCover))
      formDataReactive.cover = res.cover
        ? JSON.parse(JSON.stringify(res.cover))
        : JSON.parse(JSON.stringify(formDataReactive.cover))
      formDataReactive.customAppCover = res.appCover
        ? JSON.parse(JSON.stringify(res.appCover))
        : JSON.parse(JSON.stringify(formDataReactive.appCover))
      formDataReactive.customCover = res.cover
        ? JSON.parse(JSON.stringify(res.cover))
        : JSON.parse(JSON.stringify(formDataReactive.cover))
      formDataReactive.topStatus = res.topStatus
      formDataReactive.publishTime = res.publishTime
      formDataReactive.activityMemberId = res.activityMemberId || []
      formDataReactive.fileList = res.fileList || []
      formDataReactive.module = res.module || formDataReactive.module
      formDataReactive.title = res.title
      formDataReactive.id = props.id
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      // 新增或编辑最后确定发布时间
      formDataReactive.publishTime = formatTimeStamp(new Date().getTime())
      if (formDataReactive.id) {
        putEditorCompanyShowListItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
      else {
        postInsertCompanyShowListItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// 文件相关
async function handleFileChange(
  fileInfoList: uploadFileItemNew[],
  isDelIDs: string,
) {
  try {
    const fileData = new FormData()
    const customFileIdArr: Array<any> = []
    // 获取所有FileID
    formDataReactive.fileList.forEach((item: any) => customFileIdArr.push(item))
    // 删除动作
    if (isDelIDs) {
      customFileIdArr.forEach((item, index) => {
        if (item.id === isDelIDs) {
          customFileIdArr.splice(index, 1)
        }
      })
      formDataReactive.fileList = [...customFileIdArr]
    }
    else {
      // 新增动作
      const lastFileItem = fileInfoList[fileInfoList.length - 1]?.file
      fileData.append('file', lastFileItem as Blob)
      const data: uploadFileItem = await uploadImg(fileData)
      customFileIdArr.push({
        original: lastFileItem?.name as string,
        fileName: data.url || '',
        id: data.fileId,
      })
      if (data) {
        formDataReactive.fileList = [...customFileIdArr]
      }
    }
  }
  catch (error) {}
}

// PC封面图片上传
const handleCoverDone = async(file: File) => {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formDataReactive.customCover!.id) {
      return
    }
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      formDataReactive.customCover!.id = data.fileId || ''
      formDataReactive.appCover.id = data.fileId || ''
      formDataReactive.appCover.url = ''
    }
  }
  catch (error) {}
}
// PC封面图片删除操作
const handleCoverDelete = () => {
  formDataReactive.customCover!.id = ''
  formDataReactive.appCover.id = ''
  formDataReactive.appCover.url = ''
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="100"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item label="主题：" path="title">
      <n-input
        v-model:value="formDataReactive.title"
        placeholder="请输入主题名称"
        maxlength="60"
        show-count
        clearable
      />
    </n-form-item>
    <n-form-item
      v-for="(item, index) in formDataReactive.module"
      :key="index"
      label="内容："
      path="module[0].content"
    >
      <RichEditor
        v-model:value="item.content"
        style="width: 100%"
        :rich-height="350"
      />
    </n-form-item>
    <n-form-item label="是否置顶：" path="topStatus">
      <n-switch
        v-model:value="formDataReactive.topStatus"
        checked-value="1"
        unchecked-value="0"
      />
    </n-form-item>
    <n-form-item span="24" label="图片：">
      <ImgUploader
        v-model:oldImgUrl="formDataReactive.customCover!.url"
        :width="380"
        :height="200"
        :need-cropper="false"
        @done="handleCoverDone"
        @delete="handleCoverDelete"
      />
    </n-form-item>
    <n-form-item span="24" label="附件：">
      <file-uploader
        :max="100"
        accept=".doc, .docx, .pdf"
        :size-limit="200"
        :original-file-list="(formDataReactive.fileList as any)"
        @file-list-change="handleFileChange"
      >
        <template #tips>
          <span class="tips">
            可上传多个文件，支持扩展名：.doc,docx,大小200M以内
          </span>
        </template>
      </file-uploader>
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
