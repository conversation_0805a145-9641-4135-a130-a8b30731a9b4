<script setup lang="ts">
import { NRate } from 'naive-ui'
import {
  //  DeleteForeverRound,
  PlusRound,
} from '@vicons/material'
import ChangeOrganize from '../cpn/ChangeOrganize.vue'
import BranchStarForm from './cpn/BranchStarForm.vue'
import { getTableColumns } from './config'
import { useMyTable } from '@/hooks/use-my-table'
import { useDrawerEdit } from '@/hooks'
import {
  deleteOrganizeStarResult,
  getOrganizeStarList,
  // postOrganizeStar,
} from '@/services/organize-garden'
import { useOrganizeGardenStore } from '@/store/organize-garden'
import DeleteButton from '@/components/DeleteButton.vue'
const deptId = ref()

const handleChangeOrg = (value: string) => {
  deptId.value = value
  // console.log(value)
}
const { getDeptId } = useOrganizeGardenStore()
const formDataReactive = ref({
  deptId: '',
  // year: null,
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(
  getOrganizeStarList,
  formDataReactive,
  {
    batchDeleteTable: false,
    delApi: deleteOrganizeStarResult,
  },
)

watch(
  getDeptId,
  async(value) => {
    if (value) {
      formDataReactive.value.deptId = value
    }
  },
  {
    immediate: true,
  },
)

const idEditRef = ref()
const addBranchStarFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('党支部星级', handelConfirmEdit)
/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addBranchStarFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addBranchStarFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

const tableColumns = getTableColumns(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
        },
        class: 'flex fel-row gap-x-[15px]',
      },
      [
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'view'
              showEditRef.value = true
            },

          },
          { default: () => '查看' },
        ),
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'modify'
              showEditRef.value = true
            },

          },
          { default: () => '编辑' },
        ),
        h(DeleteButton, {
          handleConfirm: () => handleSingleDelete(String(row.id)),
        }),
      ],
    )
  },
  (row) => {
    return h(NRate, {
      value: row.star,
      count: 5,
      readonly: true,
      // onUpdateValue: (value) => {
      //   const params = {
      //     id: String(row.id),
      //     star: value,
      //     year: row.year,
      //     deptId: formDataReactive.value.deptId,
      //   }
      //   postOrganizeStar(params).then(() => {
      //     window.$message.success('评级成功')
      //     loadData()
      //   })
      // },
    })
  })

</script>
<template>
  <div class="w-full px-[20px] py-[20px]">
    <ChangeOrganize @change-org="handleChangeOrg" />
    <table-container
      v-model:page="currentPage"
      v-model:page-size="pageSize"
      style="padding-left: 0"
      :loading="loading"
      title="党支部星级"
      :show-add="true"
      :show-delete="false"
      :show-toolbar="true"
      :show-pagination="true"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      @click-add="handleClickAdd"
      @click-delete="handleBatchDelete"
      @update-page="onUpdatePage"
      @update-page-size="onUpdatePageSize"
      @update-checked-row-keys="onUpdateCheckedRowKeys"
    >
      <template #btns>
        <n-button size="small" type="primary" @click="handleClickAdd">
          <template #icon>
            <n-icon>
              <plus-round />
            </n-icon>
          </template>
          添加
        </n-button>

        <!-- <n-button size="small" @click="handleBatchDelete">
          <template #icon>
            <n-icon>
              <delete-forever-round />
            </n-icon>
          </template>
          删除
        </n-button> -->
      </template>
      <!-- <template #filters>
        <n-date-picker
          v-model:formatted-value="formDataReactive.year"
          style="width: 260px"
          placeholder="请选择年份"
          clearable
          type="year"
          @update:formatted-value="
            (v:any) => (formDataReactive.year = v)
          "
        />
      </template> -->
    </table-container>
  </div>

  <n-drawer v-model:show="showEditRef" :show-mask="false" :width="400" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <branch-star-form
        :id="idEditRef"
        ref="addBranchStarFormRef"
        :dept-id="formDataReactive.deptId"
        :enter-type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            v-if="editTypeRef !== 'view'"
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            {{ editTypeRef !== 'view' ? '取消' : '关闭' }}
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
