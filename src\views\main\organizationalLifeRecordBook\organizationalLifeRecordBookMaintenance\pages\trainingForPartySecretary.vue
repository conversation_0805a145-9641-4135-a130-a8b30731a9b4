<script lang="ts" setup>
import { DownloadRound } from '@vicons/material'
import type { TreeSelectOption } from 'naive-ui'
import { NButton, NForm, NIcon, useMessage } from 'naive-ui'
import { onMounted, ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { startOfDay } from 'date-fns'
import { useRecordBookStore } from '@/store/recordBook'
import { getPartyPollingListColumns } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/config'
import {
  addPartyPolling,
  deletePartyPolling,
  getPartyPollingDetail,
  getPartyPollingList,
  updatePartyPolling,
} from '@/services/training-for-party-secretary'
import { trainingFormRules } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/formRules'
import type {
  QueryTrainingForPartySecretaryDetailResponseType,
  RecordsItem,
} from '@/services/training-for-party-secretary/types/request'
import {
  deleteHandleStatusItem,
  deletePartyMemberMinutes,
  getHandleStatusList,
  getUserByDepartmentId,
} from '@/services/recordBookMaintenance'
import type { BranchBasicOrganizationConditionUserType } from '@/services/recordBookMaintenance/types'
import { useMyTable } from '@/hooks'

const message = useMessage()
const recordBookStore = useRecordBookStore()
const currentYearOptions = computed(() => {
  return recordBookStore.getYearOptions
})

const userList = ref<BranchBasicOrganizationConditionUserType[]>([])
const confirmLoading = ref<boolean>(false)
const showAddTrainingModal = ref<boolean>(false)
const trainingFormRef = ref<typeof NForm | null>(null)
const fileLoading = ref<boolean>(false)
const pageNum = ref(1)
// const pageSize = ref(10)
// const loading = ref(false)
// const total = ref(0)
const selectedDeptId = ref<string | null>(null)
const selectedYear = ref<string | null>(null)
const currentDownloadFileType = ref<'word' | 'pdf'>('word')
const showPopConfirm = ref<boolean>(false)
// const tableData = ref([])

const drawerTitle = ref('')
const actionType = ref<'add' | 'edit' | 'view'>('add')
const trainingForm = ref<QueryTrainingForPartySecretaryDetailResponseType>({})

const calcOrganizationListTree = computed(() => {
  return recordBookStore.getOrganizationListTreeList
})

const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})
// 获取当前年份
const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

// 筛选项：组织id和年份
const filterRef = ref({
  deptId: '',
  year: '',
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  handleSingleDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateSorter,
  loadData: load,
} = useMyTable(
  getPartyPollingList,
  filterRef,
  {
    batchDeleteTable: true,
    delApi: deleteHandleStatusItem,
  },
  false,
  ref(false),
  undefined,
  {
    decisionDate: {
      descend: 1,
      ascend: 0,
    },
  },
)

const tableColumns = getPartyPollingListColumns((row, index) => {
  return [
    h(
      NButton,
      {
        text: true,
        type: 'primary',
        onClick: () => {
          handleView(row)
        },
      },
      { default: () => '查看' },
    ),
    h(
      NButton,
      {
        text: true,
        type: 'primary',
        style: {
          marginRight: '10px',
          marginLeft: '10px',
        },
        onClick: () => {
          handleUpdate(row)
        },
      },
      { default: () => '编辑' },
    ),
    h(
      NButton,
      {
        text: true,
        type: 'primary',
        onClick: () => {
          window.$dialog.create({
            type: 'default',
            closable: false,
            content: '确认删除该条记录？',
            showIcon: false,
            positiveText: '确认',
            negativeText: '取消',
            onPositiveClick: () => {
              handleDelete(row)
            },
          })
        },
      },
      { default: () => '删除' },
    ),
  ]
})

watch(
  () => trainingForm.value.minutesTime,
  () => {
    if (
      trainingForm.value.minutesTime
      && trainingForm.value.minutesTime.length > 1
    ) {
      const startTime = trainingForm.value.minutesTime[0]
      const endTime = trainingForm.value.minutesTime[1]
      trainingForm.value.startTime = startTime
      trainingForm.value.endTime = endTime
    }
  },
  {
    deep: true,
  },
)

function fetchUserList() {
  // console.log(selectedDeptId.value, 'selectedDeptId.value')
  getUserByDepartmentId({ deptId: selectedDeptId.value }).then((res) => {
    userList.value = res || []
    trainingForm.value.userId
      = res.find(item => item.partyIdentity === '0')?.trueName || null
  })
}

const userOptions = computed(() => {
  return userList.value.map((item) => {
    return {
      label: item.trueName,
      value: item.userId,
    }
  })
})

const isDateDisabled = (
  ts: number,
  type: 'start' | 'end',
  range: [number, number] | null,
): boolean => {
  const date = new Date(ts)
  const tsYear = date.getFullYear()

  // 年份不符合，直接禁用
  if (tsYear !== Number(selectedYear.value)) {
    return true
  }

  // 没有范围值时，不禁用
  if (range === null) {
    return false
  }

  const tsStart = startOfDay(ts).valueOf()
  const rangeStart = startOfDay(range[0]).valueOf()
  const rangeEnd = startOfDay(range[1]).valueOf()
  const oneDay = 86400000 // 一天的毫秒数

  if (type === 'start') {
    // 开始日期不能早于结束日期前一天以上
    return rangeEnd - tsStart >= oneDay
  }

  if (type === 'end') {
    // 结束日期不能晚于开始日期后一天以上
    return tsStart - rangeStart >= oneDay
  }

  return false
}

async function handleConfirm() {
  trainingFormRef.value?.validate(async(errors) => {
    if (!errors) {
      confirmLoading.value = true
      try {
        const userName = userOptions.value.find(
          item => item.value === trainingForm.value.userId,
        )?.label
        const obj = cloneDeep(trainingForm.value)
        delete obj.minutesTime
        obj.deptId = selectedDeptId.value!
        obj.year = selectedYear.value!
        obj.userName = userName
        actionType.value === 'add'
          ? await addPartyPolling({
            ...obj,
          })
          : await updatePartyPolling({
            id: obj.id!,
            ...obj,
          })
        message.success(actionType.value === 'add' ? '新增成功' : '编辑成功')
        closeDrawer()
      }
      catch (error) {
        console.error(error)
        message.error(actionType.value === 'add' ? '新增失败' : '编辑失败')
      }
      finally {
        confirmLoading.value = false
      }
    }
  })
}

async function handleView(row: RecordsItem) {
  actionType.value = 'view'
  drawerTitle.value = '查看'
  const id = row.id
  showAddTrainingModal.value = true
  trainingForm.value = await getPartyPollingDetail({ id })
  trainingForm.value.minutesTime = [
    trainingForm.value.startTime!,
    trainingForm.value.endTime!,
  ]
}

async function handleUpdate(row: RecordsItem) {
  actionType.value = 'edit'
  drawerTitle.value = '编辑'
  const id = row.id
  showAddTrainingModal.value = true
  trainingForm.value = await getPartyPollingDetail({ id })
  trainingForm.value.minutesTime = [
    trainingForm.value.startTime!,
    trainingForm.value.endTime!,
  ]
  // console.log(trainingForm.value, 'trainingForm.value')
}

async function handleDelete(row: RecordsItem) {
  await deletePartyPolling({
    id: row.id!,
  })
  message.success('删除成功')
  await load()
}

function addTrainingData() {
  actionType.value = 'add'
  drawerTitle.value = '新增'
  showAddTrainingModal.value = true
}

function closeDrawer() {
  actionType.value = 'add'
  drawerTitle.value = '新增'
  trainingForm.value = {}
  trainingFormRef.value?.restoreValidation()
  showAddTrainingModal.value = false
  load()
}

function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  pageNum.value = 1
  currentPage.value = 1
  selectedDeptId.value = v
  currentPage.value = 1
  // loadData()
  // load()
  fetchUserList()
  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearSelectedValue(v: string) {
  if (!v) {
    return
  }
  pageNum.value = 1
  currentPage.value = 1
  selectedYear.value = v
  currentPage.value = 1
  // loadData()
  // load()
  fetchUserList()
  recordBookStore.setCurrentSelectedYear(v || '')
}

function handleDownLoadFile() {
  fileLoading.value = true
  recordBookStore.fetchDownLoadFile(
    {
      deptId: selectedDeptId.value!,
      year: selectedYear.value!,
      type: currentDownloadFileType.value,
    },
    () => {
      fileLoading.value = false
    },
  )

  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleCancelShowPopConfirm() {
  nextTick(() => {
    showPopConfirm.value = false
  })
}

async function loadData() {
  if (!selectedDeptId.value || !selectedYear.value) {
    return
  }
  loading.value = true
  getPartyPollingList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
  })
    .then((res) => {
      total.value = res.total || 0
      tableData.value = (res.records as any) || []
    })
    .finally(() => {
      loading.value = false
    })
}

onMounted(() => {
  filterRef.value.deptId = currentSelectedDept.value.deptId
  filterRef.value.year = currentYear.value
  selectedDeptId.value = currentSelectedDept.value.deptId
  selectedYear.value = currentYear.value
  // loadData()
  load()
  fetchUserList()
})
</script>

<template>
  <div>
    <table-container
      v-model:page="currentPage"
      v-model:page-size="pageSize"
      :loading="loading"
      :show-delete="false"
      :show-pagination="true"
      :show-toolbar="false"
      :show-title="true"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      title="党支部书记参加上级党组织集中轮训情况"
      default-expand-all
      @update-page="onUpdatePage"
      @update-page-size="onUpdatePageSize"
      @update-sorter="onUpdateSorter"
    >
      <template #row>
        <div
          class="w-[100%] flex flex-row items-start justify-between gap-[10px] pb-[10px]"
        >
          <div class="flex flex-row gap-x-[10px] justify-start items-start">
            <n-tree-select
              v-model:value="filterRef.deptId"
              :options="calcOrganizationListTree"
              :show-path="false"
              check-strategy="all"
              children-field="children"
              filterable
              key-field="id"
              label-field="name"
              placeholder="请选择所属党组织"
              style="width: 400px"
              value-field="id"
              @update:value="handleUpdateTreeSelectedValue"
            />
            <n-select
              v-model:value="filterRef.year"
              :options="currentYearOptions"
              placeholder="请选择年份"
              size="medium"
              style="width: 200px"
              @update:value="handleUpdateYearSelectedValue"
            />
          </div>

          <div class="flex flex-row gap-x-[10px] justify-start items-start">
            <n-button type="primary" @click="addTrainingData">
              新增
            </n-button>
            <n-popconfirm
              :show="showPopConfirm"
              :show-icon="false"
              placement="bottom"
            >
              <template #trigger>
                <n-button
                  :loading="fileLoading"
                  @click="showPopConfirm = !showPopConfirm"
                >
                  <template #icon>
                    <n-icon size="16">
                      <DownloadRound />
                    </n-icon>
                  </template>
                  下载
                </n-button>
              </template>
              <div class="py-[10px]">
                <n-radio-group v-model:value="currentDownloadFileType">
                  <n-radio
                    v-for="(fileType, fileTypeIndex) in [
                      { label: 'word', value: 'word' },
                      { label: 'pdf', value: 'pdf' },
                    ]"
                    :key="fileTypeIndex"
                    :value="fileType.value"
                  >
                    {{ fileType.label }}
                  </n-radio>
                </n-radio-group>
              </div>

              <template #action>
                <div class="w-[100%] flex flex-row justify-center items-center">
                  <n-button
                    size="small"
                    type="primary"
                    @click="handleDownLoadFile(item)"
                  >
                    确定
                  </n-button>
                  <n-button size="small" @click="handleCancelShowPopConfirm">
                    取消
                  </n-button>
                </div>
              </template>
            </n-popconfirm>
          </div>
        </div>
      </template>
    </table-container>

    <n-drawer
      v-model:show="showAddTrainingModal"
      :width="502"
      placement="right"
      :on-after-leave="closeDrawer"
    >
      <n-drawer-content :title="drawerTitle">
        <n-spin :show="confirmLoading">
          <n-form
            ref="trainingFormRef"
            :disabled="actionType === 'view'"
            :model="trainingForm"
            :rules="trainingFormRules"
            label-placement="left"
            label-width="110"
          >
            <n-form-item label="时间：" path="minutesTime">
              <n-date-picker
                v-model:formatted-value="trainingForm.minutesTime"
                :is-date-disabled="isDateDisabled"
                clearable
                placeholder="请选择时间"
                style="width: 100%"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </n-form-item>

            <n-form-item label="地点：" path="addr">
              <n-input
                v-model:value="trainingForm.addr"
                clearable
                :maxlength="50"
                show-count
                placeholder="请输入地点"
              />
            </n-form-item>
            <n-form-item label="学时：" path="studyHour">
              <n-input-number
                v-model:value="trainingForm.studyHour"
                class="!w-full"
                clearable
                :max="99"
                :min="0"
                placeholder="请输入学时"
              >
                <template #suffix>
                  <span>学时</span>
                </template>
              </n-input-number>
            </n-form-item>
            <n-form-item label="支部书记：" path="userId">
              <n-select
                v-model:value="trainingForm.userId"
                :options="userOptions"
                clearable
                placeholder="请选择支部书记"
              />
            </n-form-item>
            <n-form-item label="培训项目：" path="projectContent">
              <n-input
                v-model:value="trainingForm.projectContent"
                type="textarea"
                :rows="10"
                show-count
                clearable
                :maxlength="500"
                placeholder="请输入培训项目"
              />
            </n-form-item>
          </n-form>
        </n-spin>
        <template #footer>
          <div
            v-if="actionType !== 'view'"
            class="w-[100%] flex flex-row justify-center items-center gap-x-[10px]"
          >
            <n-button
              :disabled="confirmLoading"
              @click="showAddTrainingModal = false"
            >
              取消
            </n-button>
            <n-button
              :disabled="confirmLoading"
              type="primary"
              @click="handleConfirm"
            >
              确定
            </n-button>
          </div>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<style lang="scss" scoped></style>
