import { commonReq } from '../request'
import type { DictItem, OriginItem } from './types'

export function uploadImg(
  data: FormData | File,
  progressCallback?: (progress: number) => void,
) {
  return commonReq.post({
    url: '/upms/sys-file/upload',
    data,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
    onUploadProgress: (progressEvent: ProgressEvent) => {
      if (progressEvent.lengthComputable) {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total,
        )
        // console.log(`上传了: ${percentCompleted}%`)
        if (progressCallback) {
          progressCallback(Number(percentCompleted || 0))
        }
      }
    },
  })
}

/** 根据类型获取对应的枚举 */
export function fetchEnumeration(key: string) {
  return commonReq.get<DictItem[]>({
    url: `/upms/dict/key/${key}`,
  })
}

/** 获取籍贯枚举 */
export function fetchOriginEnumeration() {
  return commonReq.get<OriginItem[]>({
    url: '/upms/backend/v1/origin',
  })
}

/** 获取组织及人员tree */
export function fetchCompanyDeptUserList() {
  return commonReq.get<any[]>({
    url: '/org-construction/organization/user-tree/user',
  })
}
