<script setup lang="ts">
import { NForm } from 'naive-ui'
import { formRules } from './config'

import type { ActiveDemeanorDetail } from '@/services/structure/branch-garden/types'
import {
  getActivityDetail,
  postActivity,
  putActivity,
} from '@/services/structure/branch-garden'
import type { uploadFileItem } from '@/services/affairs/party-building-list/exam-indicators/types'
import { uploadImg } from '@/services'

interface Props {
  branchId: string
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<ActiveDemeanorDetail>({
  branchId: props.branchId,
  id: '',
  title: '',
  sort: null, // 排序
  isMobile: '1', // 是否显示 1显示 0不显示
  imgId: null,
  imgUrl: '',
})

const loading = ref(false)

const imgSizeReactive = reactive<{ width: number; height: number }>({
  width: 340,
  height: 251,
})
const formRef = ref<InstanceType<typeof NForm>>()
const oldImgUrlRef = ref('')
const oldImgUrlFirstFlag = ref(true)
// 图片裁剪完毕
const handleCoverDone = async(file: File) => {
  // 修改的时候 传入旧图片会触发done 此时不能上传 否则与原图id不同 会被视为更改了图片
  if (
    (!oldImgUrlFirstFlag.value && props.type === 'modify')
    || props.type === 'add'
  ) {
    const imgFileData = new FormData()
    imgFileData.append('file', file)
    try {
      if (formDataReactive.imgId) {
        return
      }
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formDataReactive.imgId = data.fileId || ''
      }
    }
    catch (error) {}
  }
  oldImgUrlFirstFlag.value = false
}
// 图片删除
function handleCoverDelete() {
  formDataReactive.imgId = undefined
}
onMounted(async() => {
  loading.value = true
  if (props.type === 'modify' && props.id) {
    try {
      const res = await getActivityDetail(props.id)
      formDataReactive.id = res.id
      formDataReactive.title = res.title
      formDataReactive.imgId = res.imgId
      formDataReactive.isMobile = res.isMobile
      formDataReactive.sort = res.sort

      oldImgUrlRef.value = res.imgUrl ?? ''
      loading.value = false
    }
    catch (error) {}
  }
})

// 验证表单,调用接口
async function validateAndSave() {
  loading.value = true
  const errors = await new Promise((resolve) => {
    formRef.value?.validate((errors: any) => {
      resolve(errors)
    })
  })

  if (!errors) {
    const saveFunction
      = props.type === 'modify' && props.id ? putActivity : postActivity
    const saveData = { ...formDataReactive, fileList: undefined }
    try {
      const res = await saveFunction(saveData)
      if (res) {
        window.$message.success('保存成功')
        emits('saved')
      }
      loading.value = false
    }
    catch (error) {}
  }
}
// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-grid>
      <n-form-item-gi span="24" label="活动名称：" path="title">
        <n-input
          v-model:value="formDataReactive.title"
          placeholder="请输入活动名称"
          clearable
          maxlength="20"
          show-count
        />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="移动端是否展示：" required>
        <n-switch
          v-model:value="formDataReactive.isMobile"
          :checked-value="'1'"
          :unchecked-value="'0'"
        />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="排序：" path="sort">
        <n-input-number
          v-model:value="formDataReactive.sort"
          style="width: 100px"
          :min="1"
          placeholder=""
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi span="14" label="活动图片" path="imgId" required>
        <img-uploader
          :need-cropper="false"
          :width="imgSizeReactive.width"
          :height="imgSizeReactive.height"
          :old-img-url="oldImgUrlRef"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </n-form-item-gi>
    </n-grid>
  </n-form>
</template>

<style lang="scss" scoped></style>
