import type { DataTableColumns, FormRules } from 'naive-ui'
import type { VNodeChild } from 'vue'

export const formRules: FormRules = {
  name: {
    required: true,
    message: '党组织名称不能为空',
    trigger: 'input',
  },
  orgType: {
    required: true,
    message: '党组织类别不能为空',
    trigger: 'input',
  },
  parentId: {
    required: true,
    message: '请选择上级党组织',
    trigger: 'change',
  },
  sortOrder: {
    required: true,
    message: '请添加排序',
    validator(rule: any, value: any) {
      if (value !== null && value >= 1) {
        return true
      }
      else {
        return new Error('请添加排序')
      }
    },
    trigger: 'change',
  },
}

export function getManageLeaderTableColumns(
  operationColumnRenderer: (row: any, index: number) => VNodeChild,
  hidePositionColumn: boolean = false,
): DataTableColumns<any> {
  const columns = [
    {
      key: 'index',
      title: '序号',
      render: (_: any, index: number) => {
        return index + 1
      },
    },
    {
      key: 'trueName',
      title: '姓名',
    },
    {
      key: 'position',
      title: '职务名称',
    },
    {
      key: 'deptName',
      title: '所属党组织',
    },
    {
      key: 'updateTime',
      title: '更新时间',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left' as const,
      width: '100',
      render: (row: any, index: number) => operationColumnRenderer(row, index),
    },
  ]

  // 如果需要隐藏职务名称列，则过滤掉该列
  if (hidePositionColumn) {
    return columns.filter(column => column.key !== 'position')
  }

  return columns
}

export const addManageLeaderRules: FormRules = {
  userId: {
    required: true,
    message: '姓名不能为空',
    trigger: 'change',
  },
  deptId: {
    required: true,
    message: '所属党组织不能为空',
    trigger: 'change',
  },
  position: {
    required: true,
    message: '请输入职务名称',
    trigger: 'input',
  },
}
