import type { Inform, InformReq } from './types'
import { commonReq } from '@/services/request'
import type { PaginationRes } from '@/services/types'

/** 获取通知列表 */
export function getInformList(params: {
  title?: string
  type?: number
  pageNum?: number
  pageSize?: number
}) {
  return commonReq.get<PaginationRes<InformReq>>({
    url: '/sys-operation/backend/v1/notice',
    params: {
      title: params.title,
      type: params.type,
      current: params.pageNum,
      size: params.pageSize,
    },
  })
}

/** 通知新增 */
export function addInform(data: Object) {
  return commonReq.post<string>({
    url: '/sys-operation/backend/v1/notice',
    data,
  })
}
/** 通知修改 */
export function updateInform(data: Object) {
  return commonReq.put<string>({
    url: '/sys-operation/backend/v1/notice',
    data,
  })
}

/** 通知详情 */
export function getInformDetail(id: string) {
  return commonReq.get<Inform>({
    url: `/sys-operation/backend/v1/notice/${id}`,
  })
}

/** 删除通知 */
export function deleteInform(ids: string) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/notice',
    params: {
      ids,
    },
  })
}

/** 推送通知 */
export function pushInform(id: string) {
  return commonReq.put({
    url: `/sys-operation/backend/v1/notice/push/${id}`,
  })
}
