import type { VNodeChild } from 'vue'
import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
// import type { informationType } from '@/services/run/types'
import { NImage } from 'naive-ui'
import type { categoryListType } from '@/services/run/category/types'
export function createColumns(
  commentRender: (row: categoryListType) => VNodeChild,
  operationRender: (row: categoryListType) => VNodeChild,
  topRender: (row: categoryListType) => VNodeChild,
  recommendRender: (row: categoryListType) => VNodeChild,
): TableColumns<categoryListType> {
  return [
    {
      type: 'selection',
      align: 'center',
    },
    {
      title: '封面图',
      key: 'img',
      render: row =>
        h(NImage, {
          src: import.meta.env.VITE_API_BASE + row.coverUrl,
          width: '62',
          style: { height: '40px' },
        }),
    },
    {
      title: '资讯标题',
      key: 'title',
    },
    {
      title: '阅读数',
      key: 'readNum',
      align: 'center',
    },
    // {
    //   title: '评论数',
    //   key: 'comment',
    //   render: commentRender,
    // },
    {
      title: '发布时间',
      key: 'publishTime',
      render: row => row.publishTime ?? '-',
    },
    {
      title: '置顶',
      key: 'top',
      render: topRender,
    },
    {
      title: '推荐',
      key: 'recommend',
      render: recommendRender,
    },
    {
      title: '操作',
      key: 'operation',
      render: operationRender,
    },
  ]
}
