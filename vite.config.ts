import path from 'path'
import { defineConfig, loadEnv } from 'vite'
import Vue from '@vitejs/plugin-vue'
import { createHtmlPlugin } from 'vite-plugin-html'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import svgLoader from 'vite-svg-loader'

export default defineConfig(({ mode }) => {
  // 合并环境变量
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) }
  const { VITE_DEV_PORT, VITE_APP_NAME, VITE_API_HOST, VITE_PREVIEW }
    = process.env

  return {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    // define: {
    // global: 'window',
    // },
    plugins: [
      Vue({
        include: [/\.vue$/],
      }),

      createHtmlPlugin({
        minify: true,
        entry: '/src/main.ts',
        inject: {
          data: {
            title: VITE_APP_NAME,
          },
        },
      }),

      AutoImport({
        imports: ['vue', 'vue-router', '@vueuse/core', '@vueuse/head'],
        dts: 'src/types/auto-imports.d.ts',
      }),

      Components({
        extensions: ['vue'],
        include: [/\.vue$/, /\.vue\?vue/],
        dts: 'src/types/components.d.ts',
        resolvers: [NaiveUiResolver()],
      }),

      svgLoader(),
    ],
    server: {
      host: true,
      port: Number(VITE_DEV_PORT),
      proxy: {
        '/api': {
          target: VITE_API_HOST,
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, ''),
        },
        // 文件预览代理
        '/previews': {
          target: VITE_PREVIEW,
          changeOrigin: true,
        },
      },
    },
  }
})
