<template>
  <div class="layout-padding-auto layout-padding-view">
    <n-scrollbar style="width: 100%; height: calc(100vh - 114px)">
      <div class="mt-[25px] mx-[20px] font-[600]">
        {{ title }}
      </div>
      <table-container
        v-model:page="currentPage"
        v-model:page-size="pageSize"
        :loading="loading"
        class="relation-table"
        :show-title="false"
        :show-add="false"
        :show-delete="false"
        :table-columns="tableColumns"
        :table-data="tableData"
        :total="total"
        @update-page="onUpdatePage"
        @update-page-size="onUpdatePageSize"
      >
        <template #btns>
          <n-button
            icon="folder-add"
            type="primary"
            class="ml10"
            @click="dictformRef.openDialog(null, queryForm)"
          >
            <template #icon>
              <n-icon>
                <IosAdd />
              </n-icon>
            </template>

            添加
          </n-button>
        </template>
      </table-container>
      <dict-form ref="dictformRef" @refresh="loadData" />
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts" name="dict-item">
import type { DataTableColumns } from 'naive-ui'
import { NButton, NPopconfirm } from 'naive-ui'
import { IosAdd } from '@vicons/ionicons4'
import { delItemDict, fetchItemDictList } from '@/services/system/Admin/index'
import { useMyTable } from '@/hooks'
import type { DictItem } from '@/services/system/Admin/types'

const visible = ref(false)
const DictForm = defineAsyncComponent(() => import('./form.vue'))
const dictformRef = ref()
const queryForm = ref({
  dictId: '',
  dictType: '',
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(
  fetchItemDictList,
  queryForm,
  {
    batchDeleteTable: false,
  },
  false,
  ref(true),
)

const title = ref('')

const tableColumns: DataTableColumns<DictItem> = [
  {
    key: 'blank',
    width: 10,
  },
  {
    key: 'dictType',
    title: '类型',
    width: '10%',
  },
  {
    key: 'value',
    title: '数据值',
    width: '10%',
  },
  {
    key: 'label',
    title: '标签名',
    width: '10%',
  },
  {
    key: 'description',
    title: '描述',
    width: '15%',
  },
  {
    key: 'sortOrder',
    title: '排序',
    width: '8%',
  },
  {
    key: 'remarks',
    title: '备注',
    width: '15%',
  },
  {
    key: 'createTime',
    title: '创建时间',
    width: '12%',
  },
  {
    key: 'operation',
    title: '操作',
    width: '20%',
    render(row) {
      return [
        h(
          NButton,
          {
            text: true,
            style: 'margin-right: 16px',
            type: 'primary',
            onClick: () => {
              dictformRef.value.openDialog(row)
            },
          },
          { default: () => '编辑' },
        ),
        h(
          NPopconfirm,
          {
            positiveText: '确定',
            placement: 'left',
            onPositiveClick: () => {
              delItemDict(row.id!).then((res) => {
                window.$message.success(res)
                loadData()
              })
            },
          },
          {
            trigger: () =>
              h(
                NButton,
                {
                  text: true,
                  type: 'primary',
                },
                { default: () => '删除' },
              ),
            default: () => '确定删除吗？',
          },
        ),
      ]
    },
  },
]
const open = (row: any) => {
  queryForm.value.dictId = row.id
  queryForm.value.dictType = row.dictType
  title.value = row.description
  visible.value = true
  loadData()
}
// 暴露变量
defineExpose({
  open,
})
</script>

<style scoped></style>
