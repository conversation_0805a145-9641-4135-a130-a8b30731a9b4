import type { DataTableColumns } from 'naive-ui'

import type { BranchVoteScoreList } from '@/services/affairs/party-building-list-branch/types'

export const tableColumns: DataTableColumns<BranchVoteScoreList> = [
  {
    type: 'selection',
    multiple: true,
  },
  {
    key: 'index',
    title: '序号',
    align: 'center',
    render: (_, i) => i + 1,
  },
  {
    key: 'title',
    title: '投票打分名称',
  },
  {
    key: 'voteType',
    title: '投票打分类型',
  },
  {
    key: 'year',
    title: '年度',
    render: row => `${row.year}年`,
  },
]
