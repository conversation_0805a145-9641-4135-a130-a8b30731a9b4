/** 荣誉墙 */

import { commonReq } from '../request'
import type { PaginationReq } from '../types'
import type { HonorWallDetailItem, HonorWallTableItem } from './types'

/** 获取荣誉墙列表 */
export function honorWallPage(params: PaginationReq) {
  return commonReq.get({
    url: '/org-construction/backend/v1/honorWall/page',
    params,
  })
}

/** 荣誉墙分类查询 */
export function listHonorWallCategory() {
  return commonReq.get<HonorWallTableItem[]>({
    url: '/org-construction/backend/v1/honorwallcategory/listHonorWallCategory',
  })
}

/** 荣誉墙分类新增 */
export function addHonorWallCategory(data: HonorWallTableItem) {
  return commonReq.post({
    url: '/org-construction/backend/v1/honorwallcategory',
    data,
  })
}

/** 荣誉墙分类修改 */
export function putHonorWallCategory(data: HonorWallTableItem) {
  return commonReq.put({
    url: '/org-construction/backend/v1/honorwallcategory',
    data,
  })
}

/** 荣誉墙分类删除 */
export function deleteHonorWallCategory(id: string) {
  return commonReq.delete({
    url: '/org-construction/backend/v1/honorwallcategory',
    params: {
      id,
    },
  })
}

/** 荣誉墙分类移动 */
export function moveHonorWallCategory(data: HonorWallTableItem) {
  return commonReq.put({
    url: '/org-construction/backend/v1/honorwallcategory/move',
    data,
  })
}

/** 删除荣誉墙数据 */
export function deleteHonorWall(ids: string) {
  return commonReq.delete({
    url: '/org-construction/backend/v1/honorWall',
    params: {
      ids,
    },
  })
}

/** 新增荣誉墙数据 */
export function addHonorWall(data: HonorWallDetailItem) {
  return commonReq.post({
    url: '/org-construction/backend/v1/honorWall',
    data,
  })
}

/** 修改荣誉墙数据 */
export function putHonorWall(data: HonorWallDetailItem) {
  return commonReq.put({
    url: '/org-construction/backend/v1/honorWall',
    data,
  })
}
