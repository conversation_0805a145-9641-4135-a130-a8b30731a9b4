<script setup lang="ts">
import ContentBox from '../ContentBox.vue'
import type {
  FileVolist,
  PartyMeetingSummaryDetail,
} from '@/services/affairs/party-meeting/types'
import {
  getMeetingSummaryDetail,
  putMeetingSummary,
} from '@/services/affairs/party-meeting'
import pdf from '@/assets/image/meeting/pdf.png'
import type {
  uploadFileItem,
  uploadFileItemNew,
} from '@/services/affairs/party-building-list/exam-indicators/types'
import { uploadImg } from '@/services'
import usePdfPreview from '@/hooks/use-pdf-preview'

interface Props {
  id: string
}
const props = defineProps<Props>()
const loading = ref(false)
const baseApi = ref(import.meta.env.VITE_API_BASE)
const formDataReactive = reactive({
  content: '',
  fileIds: [] as any,
  fileList: [] as any,
})
const summaryDetail = ref<PartyMeetingSummaryDetail>()
const router = useRouter()
const route = useRoute()
async function handleLoadSummary() {
  try {
    loading.value = true
    summaryDetail.value = await getMeetingSummaryDetail(props.id)
  }
  catch (e) {
  }
  finally {
    loading.value = false
  }
}

const fileIdObj = reactive<{ fileIdsArr: Array<string> }>({ fileIdsArr: [] })

// 文件相关
async function handleFileChange(
  fileInfoList: uploadFileItemNew[],
  isDelIDs: string,
) {
  try {
    const fileData = new FormData()
    // 获取所有FileID
    if (!fileIdObj.fileIdsArr.length) {
      formDataReactive.fileList.forEach((item: any) =>
        fileIdObj.fileIdsArr.push(item.id),
      )
    }
    // 删除动作
    if (isDelIDs || isDelIDs === null) {
      if (isDelIDs === null && fileIdObj.fileIdsArr.length) {
        fileIdObj.fileIdsArr.splice(fileIdObj.fileIdsArr.length - 1, 1)
      }
      else {
        fileIdObj.fileIdsArr.forEach((item, index) => {
          if (item === isDelIDs) {
            fileIdObj.fileIdsArr.splice(index, 1)
          }
        })
      }
      formDataReactive.fileIds = [...fileIdObj.fileIdsArr]
    }
    else {
      // 新增动作
      const lastFileItem = fileInfoList[fileInfoList.length - 1]?.file
      fileData.append('file', lastFileItem as Blob)
      const data: uploadFileItem = await uploadImg(fileData)
      formDataReactive.fileList.push({
        original: lastFileItem?.name as string,
        fileName: data.url || '',
        id: data.fileId,
      })
      if (data) {
        fileIdObj.fileIdsArr.push(data.fileId)
        formDataReactive.fileIds = [...fileIdObj.fileIdsArr]
      }
    }
  }
  catch (error) {}
}
const summaryRef = ref()
const summaryVisible = ref(false)

function handleEditSummaryDialog() {
  // summaryVisible.value = true
  // getMeetingSummaryDetail(props.id).then((res) => {
  //   formDataReactive.content = res.content
  //   formDataReactive.fileList = res.fileVOList.map((file: FileVolist) => {
  //     return {
  //       id: file.id,
  //       original: file.original,
  //       fileName: file.fileName,
  //     }
  //   })
  //   formDataReactive.fileIds = res.fileVOList.map(item => item.id) || []
  // })

  router.push({
    name: 'editSummary',
    query: route.query,
  })
}
async function handleConfirm() {
  try {
    loading.value = true
    if (!formDataReactive.content) {
      window.$message.error('总结内容不能为空')
      return
    }
    const data = {
      meetingId: props.id,
      fileIds: formDataReactive.fileIds,
      content: formDataReactive.content,
    }
    await putMeetingSummary(data)
    window.$message.success('编辑成功')
    formDataReactive.content = ''
    formDataReactive.fileList = []
    formDataReactive.fileIds = []
    fileIdObj.fileIdsArr = []
    summaryVisible.value = false
    handleLoadSummary()
  }
  catch (error) {
  }
  finally {
    loading.value = false
  }
}

function handleCancel() {
  formDataReactive.content = ''
  formDataReactive.fileIds = []
  formDataReactive.fileList = []
  fileIdObj.fileIdsArr = []
  summaryVisible.value = false
}
const { useLoadPdfPreview } = usePdfPreview()

function handlePdfPreview(fileName: string) {
  // const urlAll = `http://${window.location.host}${baseApi.value}${fileName}`
  const urlAll = `${window.$previewHost}${fileName}`
  useLoadPdfPreview(urlAll)
}

onMounted(() => {
  handleLoadSummary()
})
</script>
<template>
  <content-box title="会议内容">
    <template #btn>
      <n-button text type="info" @click="handleEditSummaryDialog">
        编辑
      </n-button>
    </template>

    <div class="w-[85%] pt-[30px] pb-[50px]">
      <div class="flex justify-start items-start">
        <div class="flex gap-x-[10px] text-[#666]">
          <span class="flex-none">内容：</span>
          <span
            v-if="summaryDetail?.content"
            class="flex-1"
            v-html="summaryDetail?.content"
          ></span>
          <span v-else class="flex-1">{{ '暂无内容' }}</span>
        </div>
      </div>
      <div v-if="false" class="pt-[30px] flex justify-start items-center">
        <div class="flex gap-x-[10px] text-[#666]">
          <span class="flex-none">附件：</span>
          <div
            v-if="summaryDetail?.fileVOList.length"
            class="flex flex-col gap-y-[20px]"
          >
            <div
              v-for="(item, index) in summaryDetail?.fileVOList"
              :key="index"
            >
              <div
                v-if="item.fileType !== 'pdf'"
                class="flex items-center gap-x-[10px]"
              >
                <n-image
                  class="border-radius-[10px]"
                  show-toolbar-tooltip
                  object-fit="fill"
                  style="width: 40px; height: 40px; border-radius: 4px"
                  :src="`${baseApi}${item.fileName}`"
                />
                <div class="flex flex-col gap-y-[4px]">
                  <span>{{ item.original }}</span>
                  <span>{{
                    (Number(item.fileSize) / 1024 / 1024).toFixed(2)
                  }}
                    MB</span>
                </div>
              </div>
              <div
                v-else
                class="flex items-center gap-x-[10px] cursor-pointer"
                @click="handlePdfPreview(item.fileName)"
              >
                <img :src="pdf" alt="pdf占位图" class="w-[40px]" />
                <div class="flex flex-col gap-y-[4px]">
                  <span>{{ item.original }}</span>
                  <span>{{
                    (Number(item.fileSize) / 1024 / 1024).toFixed(2)
                  }}
                    MB</span>
                </div>
              </div>
            </div>
          </div>
          <span v-else>暂无附件</span>
        </div>
      </div>
    </div>
  </content-box>
  <custom-dialog
    ref="summaryRef"
    v-model:show="summaryVisible"
    :show-action="true"
    width="600px"
    title="编辑会议总结"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="px-[30px] py-[20px] flex flex-col gap-y-[20px]">
      <div class="flex gap-x-[10px]">
        <span class="flex-none pt-[4px]"><b class="text-[#d03050] font-normal">* </b> 总结：</span>
        <n-input
          v-model:value="formDataReactive.content"
          type="textarea"
          placeholder="请输入会议总结 "
          clearable
          round
          maxlength="200"
          show-count
          :autosize="{ minRows: 6, maxRows: 8 }"
        />
      </div>
      <div class="flex gap-x-[10px]">
        <span class="flex-none pt-[4px]">&emsp;附件：</span>
        <div class="flex flex-wrap flex-col gap-[10px] w-[91%]">
          <file-uploader
            :max="100"
            accept=".jpg, .png, .jpeg, .pdf"
            :size-limit="200"
            :original-file-list="(formDataReactive.fileList as any)"
            @file-list-change="handleFileChange"
          >
            <template #tips>
              <span class="tips">
                可上传多个文件，支持扩展名：.jpg，.jpeg，.png，.pdf，大小200M以内
              </span>
            </template>
          </file-uploader>
        </div>
      </div>
    </div>
  </custom-dialog>
</template>
<style lang="scss" scoped></style>
