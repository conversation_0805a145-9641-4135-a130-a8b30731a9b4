import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey
  = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMeO3PiigdXK0bZZJz6JIyrLsnaFFC2n'
  + 't8aCFjES1j7YThHJFO3mhkwWWR+Feoqa6bSv6Y0/8Fd6qOM5igzarHcCAwEAAQ=='

const privateKey
  = 'MIIBVQIBADANBgkqhkiG9w0BAQEFAASCAT8wggE7AgEAAkEAx47c+KKB1crRtlkn'
  + 'PokjKsuydoUULae3xoIWMRLWPthOEckU7eaGTBZZH4V6iprptK/pjT/wV3qo4zmK'
  + 'DNqsdwIDAQABAkB2JdYb+pKmEhOBQ11hv+2rO7hoRTgCI8dBZulemolv4LEd49AW'
  + 'XJZWsmQOqzuefuYQJJIG32DK5asR37lpNTPpAiEA/t62hr3Qe6cMhdUCO9n1ac2X'
  + 'rg+p8i03UXn65TPdNuUCIQDIcV57kVpAVC88f6mRxVp8ZHYVouFjQsiuvE/cuy1k'
  + 'KwIgZNcljMZJWMNNdx5CVf/007nei6Xy7bjqLDiBBnKw0ekCIQCElznA0623z/hP'
  + 'NMOewcXRc0MJnO0rFApcTAUoJkkocQIhALOPDOrUYPc5qsX6xvky+iXRScPuTko0'
  + 'Gj1m6UumOh+O'

// 加密
export function encrypt(txt: any) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export function decrypt(txt: any) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey) // 设置私钥
  return encryptor.decrypt(txt) // 对数据进行解密
}
