<script setup lang="ts">
interface Props {
  infoText?: string
  btnTexted?: boolean
  btnDisabled?: boolean
  text?: string
  btnType?: 'primary' | 'warning' | 'error'
  handleConfirm: () => Promise<void>
}
const props = withDefaults(defineProps<Props>(), {
  infoText: '删除后无法恢复，确认删除？',
  btnTexted: true,
  text: '删除',
  btnType: 'primary',
  btnDisabled: false,
})

const loading = ref(false)
const onPositiveClick = async() => {
  loading.value = true
  try {
    await props.handleConfirm()
  } catch {
    const msg = '删除失败'
    window.$message.error(msg)
    throw new Error(msg)
  } finally {
    loading.value = false
  }
}
</script>
<template>
  <n-popconfirm
    :positive-button-props="{ loading: loading }"
    @positive-click="onPositiveClick"
  >
    <template #trigger>
      <n-button :type="btnType" :text="btnTexted" :disabled="btnDisabled">
        {{ props.text }}
      </n-button>
    </template>
    <span>{{ infoText }}</span>
  </n-popconfirm>
</template>
<style lang="scss" scoped></style>
