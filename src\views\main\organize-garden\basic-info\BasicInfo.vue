<script setup lang='ts'>
import type { FormRules } from 'naive-ui'
import ChangeOrganize from '../cpn/ChangeOrganize.vue'
import { useOrganizeGardenStore } from '@/store/organize-garden'
import { getOrganizeById, putOrganizeIntroduce } from '@/services/organize-garden'
const { getDeptId } = useOrganizeGardenStore()

const formDataReactive = reactive({
  summary: '',
  deptId: '',
})

async function handleOrgSummaryByDeptId(deptId: string) {
  const res = await getOrganizeById(deptId)
  formDataReactive.summary = res.summary
}

watch(getDeptId, async(value) => {
  if (value) {
    await handleOrgSummaryByDeptId(value)
    formDataReactive.deptId = value
  }
}, {
  immediate: true,
})

const formRules: FormRules = {
  summary: [
    {
      required: true,
      message: '组织简介不能为空',
      trigger: 'input',
    },
  ],
}
const baseInfoFormRef = ref()
/** 重置 */
function handleReset() {
  formDataReactive.summary = ''
  baseInfoFormRef.value?.restoreValidation()
}
/** 保存 */
function handleConfirm() {
  baseInfoFormRef.value?.validate((errors: any) => {
    if (!errors) {
      const data = {
        summary: formDataReactive.summary,
        deptId: formDataReactive.deptId,
      }
      putOrganizeIntroduce(data).then(async() => {
        window.$message.success('保存成功')
        await handleOrgSummaryByDeptId(String(formDataReactive.deptId))
      })
    }
  })
}
</script>
<template>
  <div class="w-full px-[20px] py-[20px]">
    <ChangeOrganize />
    <div class="py-[25px]">
      <span class="text-[14px] font-[600] text-[#333] leading-[20px] mb-[26px]">组织基本信息</span>
    </div>
    <div>
      <n-form
        ref="baseInfoFormRef"
        :model="formDataReactive"
        :rules="formRules"
        label-placement="left"
        require-mark-placement="left"
        label-width="auto"
        class="w-[100%]"
      >
        <n-form-item path="summary" label="组织简介：">
          <n-input
            v-model:value="formDataReactive.summary"
            type="textarea"
            clearable
            :autosize="{
              minRows: 26
            }"
            maxlength="10000"
            show-count
          />
        </n-form-item>
      </n-form>
    </div>
    <div class="flex justify-center items-center gap-[20px]">
      <n-button size="large" @click="handleReset">
        重置
      </n-button>
      <n-button size="large" type="primary" @click="handleConfirm">
        确定
      </n-button>
    </div>
  </div>
</template>
<style lang='scss' scoped>
</style>
