import type { FormRules } from 'naive-ui'

export const formRulesForOrganization: FormRules = {
  orgName: [
    {
      required: true,
      message: '组织名称不能为空',
      trigger: 'input',
    },
  ],
  epithet: {
    required: true,
    message: '荣誉名称不能为空',
    trigger: 'input',
  },
  certificateId: {
    required: true,
    message: '请上传奖状图片',
    trigger: 'change',
  },
  recommandStatus: {
    required: true,
    message: '请选择是否置顶',
    trigger: 'change',
  },
  content: [
    {
      required: true,
      message: '请输入先进事迹',
      trigger: 'change',
    },
  ],
}
export const formRulesForPerson: FormRules = {
  deptId: {
    required: true,
    message: '请选择组织',
    trigger: 'change',
  },
  userId: {
    required: true,
    message: '请选择党员',
    trigger: 'change',
  },
  epithet: {
    required: true,
    message: '请输入荣誉名称',
    trigger: 'input',
  },
  year: {
    required: true,
    message: '请输入年份',
    trigger: 'input',
  },
  topStatus: {
    required: true,
    message: '请选择是否置顶',
    trigger: 'change',
  },
  recommandStatus: {
    required: true,
    message: '请选择是否推荐',
    trigger: 'change',
  },
  avatarId: {
    required: true,
    message: '请上传奖状图片',
    trigger: 'change',
  },
  content: [
    {
      required: true,
      message: '请输入先进事迹',
      trigger: 'change',
    },
  ],
}
