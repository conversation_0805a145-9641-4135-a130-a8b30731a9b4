import { commonReq } from '../request'
import type { PaginationReq } from '../types'
import type {
  AddOrEditPublicCategory,
  AddOrEditPublicItem,
  MovePublicCategory,
  PublicCategoryList,
} from './types'

/** 获取党务公开分类列表 */
export function getPublicCategoryList() {
  return commonReq.get<PublicCategoryList[]>({
    url: '/party-affairs/backend/v1/open_category/list_all',
  })
}

/** 新增党务公开分类 */
export function postPublicCategory(data: AddOrEditPublicCategory) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/open_category',
    data,
  })
}

/** 编辑党务公开分类 */
export function putPublicCategory(data: AddOrEditPublicCategory) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/open_category',
    data,
  })
}

/** 移动党务公开分类 */
export function movePublicCategory(data: MovePublicCategory) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/open_category/move',
    data,
  })
}

/** 删除党务公开分类 */
export function deletePublicCategory(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/open_category',
    params: {
      ids,
    },
  })
}

/** 获取党务公开列表 */
export function getCategoryList(
  params: {
    categoryId: string
    title?: string
  } & PaginationReq,
) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/open/page',
    params,
  })
}

/** 获取党务公开列表-组织园地使用 */
export function getOrgPublicList(params: {
  orgId: string
  annexName?: string
}) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/open/page',
    params,
  })
}

/** 置顶党务公开 */
export function putOrgPublicTop(id: string) {
  return commonReq.put({
    url: `/org-construction/orgconstructionStyle/top/${id}`,
  })
}

/** 隐藏党务公开 */
export function postOrgPublicHidden(id: string) {
  return commonReq.post({
    url: `/org-construction/orgconstructionStyle/hidden/${id}`,
  })
}

/** 新增党务公开 */
export function postPublic(data: AddOrEditPublicItem) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/open',
    data,
  })
}

/** 编辑党务公开 */
export function putPublic(data: AddOrEditPublicItem) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/open',
    data,
  })
}

/** 查看党务公开详情 */
export function getPublicDetail(id: string) {
  return commonReq.get<AddOrEditPublicItem>({
    url: `/party-affairs/backend/v1/open/${id}`,
  })
}

/** 删除党务公开  */
export function deletePublic(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/open',
    params: {
      ids,
    },
  })
}
