<script setup lang="ts">
import { tableColumns } from './config'
import { useMyTable } from '@/hooks'
import { getVoteScoreList } from '@/services/affairs/party-building-list-branch'
import { VOTESCORE_TYPE } from '@/store/dict'
import { useMeetingVoteChoicesStore } from '@/store/meeting-vote-choice/meeting-vote-choice'
import type { BranchVoteScoreList } from '@/services/affairs/party-building-list-branch/types'

const filterReactive = ref({
  voteType: null,
  year: null,
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(
  getVoteScoreList,
  filterReactive,
  {
    batchDeleteTable: false,
  },
  false,
  ref(true),
)

const meetingVoteStore = useMeetingVoteChoicesStore()
const checkVote = ref<BranchVoteScoreList[]>([])
checkVote.value = meetingVoteStore.checkVote

const allVoteScoreData = ref<BranchVoteScoreList[]>([])
async function loadAllMeetingData() {
  try {
    loading.value = true
    const { records } = await getVoteScoreList({
      voteType: null,
      year: null,
      pageNum: 1,
      pageSize: 9999,
    })

    allVoteScoreData.value = records.map((item) => {
      return {
        ...item,
        relatedStatus: checkVote.value.some(check => check.id === item.id),
      }
    })
    meetingVoteStore.setAllVoteList(allVoteScoreData.value)
  }
  catch (e) {
  }
  finally {
    loading.value = false
  }
}

/** 选中的行id */
const voteCheckedRowKeys = ref<Array<number | string>>(
  checkVote.value.map(item => item.id) || [],
)

// /** 行选中 */
function onUpdateVoteCheckedRowKeys(ids: Array<number | string>) {
  voteCheckedRowKeys.value = ids
}

const emits = defineEmits<{
  (e: 'update:value', value: number[]): void
}>()

watch(voteCheckedRowKeys, (newV: Array<string | number>) => {
  emits('update:value', newV as number[])
})

watch(filterReactive.value, (newV) => {
  if (newV) {
    loadData()
  }
})

onMounted(() => {
  loadData()
  loadAllMeetingData()
})
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :loading="loading"
    class="relation-table"
    :show-title="false"
    :show-add="false"
    :show-delete="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="voteCheckedRowKeys"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateVoteCheckedRowKeys"
  >
    <template #filters>
      <n-select
        v-model:value="filterReactive.voteType"
        style="width: 260px"
        placeholder="请选择投票打分类型"
        filterable
        :options="VOTESCORE_TYPE"
        clearable
      />
      <n-date-picker
        v-model:formatted-value="filterReactive.year"
        style="width: 260px"
        placeholder="请选择年份"
        clearable
        type="year"
        @update:formatted-value="
          (v:any) => (filterReactive.year = v)
        "
      />
    </template>
  </table-container>
</template>
<style lang="scss" scoped>
.relation-table:deep(.n-data-table .n-data-table-thead) {
  display: contents;
}
</style>
