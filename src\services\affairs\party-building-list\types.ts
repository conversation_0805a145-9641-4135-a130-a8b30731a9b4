/**  党建清单表格 */
export interface PartyBuildingListItem {
  [key: string]: any
  id?: string
  /** 清单标题 */
  title: string
  /** 考核年月 */
  evaluationYearAndMonth: string
  /** 考核组织 */
  organizations: string | null
  /** 附件 */
  fileList?: FileList[]
  /** 考核分数 */
  evaluationScore?: string
  /** 考核进度 */
  evaluationProgress?: string
  /** 状态 */
  status?: string
  /** 确认进度 */
  confirmProgress?: string
}

export interface FileList {
  id: string
  fileName: string
  original: string
}

/**  党建清单添加 */
export interface PartyBuildingAdd {
  [key: string]: any
  id?: string
  /** 清单标题 */
  title: string
  /** 考核年月 */
  evaluationYearAndMonth: string | null
  /** 考核组织 */
  organizationList: string[] | null
  // /** 考核类别 */
  // examCategory: CategoryListObj[]
}

export interface CategoryListObj {
  // id: number | undefined
  // label: string
  examNumber: number | undefined | null
  examItem: string | null
}

/** 查看单个党建清单 */
export interface PartyBuildingDetail {
  id: string
  title: string
  evaluationYearAndMonth: string
  organizationVoList: OrganizationList[] | null
}

export interface OrganizationList {
  id: string
  name: string
}

export interface EvaluationTypeList {
  id: string
  name: string
}

/** 考核类别 */
export interface EvaluationType {
  id?: string
  /** 清单id */
  inventoryId?: string
  /** 分类名称 */
  name: string
}

/** 查看清单下的所有指标 */
export interface AllTargetList {
  inventoryId: string
  targetItemForm: TargetItemForm[]
  totalScore: number
}

export interface TargetItemForm {
  id: string
  categoryName: string
  targetItemList: AllTargetItemList[]
}

export interface AllTargetItemList {
  id: string
  matter: string
  evaluationRequirements: string
  evaluationMode: string
  fileList: FileList[]
  evaluationScore: number
  endTime: string
  passNum: number
  totalNum: number
  targetId: string
  relatedStatus: boolean
}

export type AllTargetItemListRow = AllTargetItemList & { editing: boolean }

/** 关联指标项 */
export interface RelateAdd {
  categoryId: string
  targetItemIds: number[]
  inventoryId: string
}

/** 关联指标项打分列表 */
export interface RelateMarkList {
  id: string
  organizationName: string
  performanceDescription: string
  meetinglinkList: meetinglinkListItem[]
  votelinkList: votelinkListItem[]
  fileList: FileList[]
  performance: string
  score: number
  confirmStatus: string
}
export type RelateMarkListRow = RelateMarkList & { editing: boolean }

export interface meetinglinkListItem {
  id: string
  title: string
  orgId: string
  org: string
  hostId: string
  host: any
  startTime: string
  endTime: string
  meetingType: string
  meetingAddr: string
  userId: string
  user: any
  meetingStatus: any
}
export interface votelinkListItem {
  id: string
  title: string
  year: string
  voteType: string
}

/** 关联指标项打分列表搜索参数 */
export interface RelateMarkListSearchParams {
  /** 关联打分Id */
  relationId: string
  /** 清单Id */
  partyListId: string
}

/** 关联指标项打分 */
export interface RelateMark {
  id: string
  /** 完成情况 */
  performance: string
  score: number
}

/**  关联指标项填写截止日期 */
export interface RelateWriteDeadLine {
  id: string
  deadline: string
}

/** 得分统计列表 */
export interface StatisticScoreList {
  organization: string
  totalScore: string
  targetItemList: TargetItemList[]
}

export interface TargetItemList {
  id: string
  evaluationRequirements: string
  evaluationMode: string
  performance: string
  score: number
  evaluationScore: string
}

/** 关联指标项列表 */
export interface SearchRelateList {
  inventoryId: string
  title: string
}

export interface RelateListItem {
  id: string
  title: string
  matter: string
  evaluationRequirements: string
  evaluationMode: string
  evaluationScore: number
  fileIds: FileId[]
  isRelated: boolean
}

export interface FileId {
  id: string
  fileName: string
  original: string
}

/**  考核指标表格 */
export interface ExamIndicatorsListItem {
  [key: string]: any
  id: string
  /** 标题 */
  title: string
  /** 事项 */
  matter: string
  /** 工作要求 */
  evaluationRequirements: string
  /** 考核方式 */
  evaluationMode: string
  /** 附件 */
  fileList: FileList[]
  /** 考核分数 */
  evaluationScore: number
  /** 状态 */
  relatedStatus?: string
}

/**  指标项清单添加 */
export interface ExamIndicatorsAdd {
  [key: string]: any
  id?: number
  /** 清单标题 */
  title: string
  /** 工作要求 */
  jobRequire: string
  /** 考核方式 */
  examMethods: string
  /** 考核分数 */
  examScore: number | null
  /** 附件 */
  attachment: null | File
}

/** 党组织下拉选择树 */
export interface partyOrganizationItem {
  id?: string
  deptId?: string
  parentId?: string
  weight?: number
  name: string
  org_type?: string
  createTime?: string
  code?: string
  children: partyOrganizationItem[] | null
}
