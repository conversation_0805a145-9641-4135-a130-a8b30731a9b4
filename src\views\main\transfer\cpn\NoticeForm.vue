<script lang="ts" setup>
import type { SelectOption, TreeSelectOption } from 'naive-ui'
import { NForm } from 'naive-ui'
import { formRules } from './config'
import type { uploadFileItem } from '@/services/affairs/party-building-list/exam-indicators/types'
import { uploadImg } from '@/services'
import {
  getOrganizationTree,
  getOrganizationTreeByUser,
} from '@/services/system/Admin'
import type { AddTransferItemType } from '@/services/transfer/types'
import {
  addPartyMemberTransfer,
  viewPartyMemberTransfer,
} from '@/services/transfer'
import { putPartyUserList } from '@/services/structure/organization-change-over'
import { getUserInfo } from '@/services/system/User'
import { getCurrentOrganize } from '@/services/organize-garden'

interface Props {
  transferTypeProp?: Array<{ label: string; value: number }>
  type?: string
  id?: string
  parentId?: string | null
  parentName?: string | null
  level: number | null
  uploadFileLoading: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
  parentId: null,
  parentName: null,
  level: null,
  uploadFileLoading: false,
})

const emits = defineEmits<{
  (e: 'saved'): void
  (e: 'update:uploadFileLoading', flag: boolean): void
}>()

const formDataReactive = reactive<{ value: AddTransferItemType }>({
  value: {
    id: null,
    userId: null,
    transferType: null,
    oldDeptId: null,
    newDeptId: null,
    newDeptName: null,
    reason: null,
    phaseStatus: null,
    remark: null,
    paymentTime: null,
    letterId: null,
    proveId: null,
    transferTime: null,
    joinTime: null,
    userName: null,
    fileList: [],
  },
})

const isViewModel = computed(() => props.type === 'view')
const formRef = ref<InstanceType<typeof NForm>>()

/** 禁用时间 */
const handleDateDisabled = (s: number) => {
  return s >= new Date().getTime()
}

/** 获取当前组织 */
const handleGetCurrentOrganize = async() => {
  const res = await getCurrentOrganize()
  formDataReactive.value.oldDeptId = res.deptId
  formDataReactive.value.oldDeptName = res.name

  // 获取当前组织下的人员列表
  if (res.deptId) {
    handleUpdateValue(res.deptId)
  }
}

onBeforeMount(() => {
  if (props.type === 'view' && props.id) {
    viewPartyMemberTransfer(props.id).then((res) => {
      formDataReactive.value = res
      if (!formDataReactive.value.fileList) {
        formDataReactive.value.fileList = []
      }
      // 从详情接口取出的附件需要设置它的百分比为100
      if (formDataReactive.value.fileList.length) {
        formDataReactive.value.fileList.forEach((item) => {
          item.percentage = 100
          item.url = item.fileName
          item.name = item.original
        })
      }
    })
  }
})

// 树结构枚举
const treeData = ref<any[]>([])
// 获取组织结构
const getOrganizationTreeData = () => {
  return getOrganizationTree().then((res: any) => {
    treeData.value = res
  })
}

// 树结构枚举(带数据权限)
const treeDataWithPermission = ref<any[]>([])
// 获取组织结构
const getOrganizationTreeDataByPermission = () => {
  return getOrganizationTreeByUser().then((res: any) => {
    treeDataWithPermission.value = res
  })
}

getOrganizationTreeData()
getOrganizationTreeDataByPermission()

const calcTreeDataFn: any = (arr: any[]) => {
  return arr.map((item: any) => {
    return {
      ...item,
      children:
        item.children && item.children.length
          ? calcTreeDataFn(item.children)
          : undefined,
    }
  })
}
// 计算属性重置树结构的children
const calcTreeData = computed(() => {
  return calcTreeDataFn(treeData.value)
})

// 计算属性重置树结构的children(带数据权限)
const calcTreeDataWithPermission = computed(() => {
  return calcTreeDataFn(treeDataWithPermission.value)
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (formDataReactive.value.id) {
        // 更新
        emits('saved')
      }
      else {
        // 新增操作
        // 介绍信id
        formDataReactive.value.letterId = formDataReactive.value.fileList
          ?.map(file => file.id)
          .join(',') as string
        addPartyMemberTransfer(formDataReactive.value).then(() => {
          window.$message.success('添加成功')
          emits('saved')
        })
      }
    }
  })
}

// 递归给每一项添加disabled属性，党小组和党支部允许被选择
function addDisabledProperty(items: any) {
  items.forEach((item: any) => {
    // 根据org_type设置disabled属性
    item.disabled = item.org_type !== '党小组' && item.org_type !== '党支部'

    // 如果有子项，递归调用
    if (item.children && item.children.length > 0) {
      item.children = addDisabledProperty(item.children)
    }
  })
  return items
}

// 上传党费缴纳证明
const handlePaymentImage = async(file: File) => {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (!isViewModel.value) {
      const data: uploadFileItem = await uploadImg(imgFileData)
      formDataReactive.value.proveId = data.fileId
    }
  }
  catch (error) {}
}
// 党费缴纳证明图片删除操作
const handlePaymentImageDel = () => {
  formDataReactive.value.proveId = ''
}

/** 获取组织ID */
const partyPersonObj = reactive<{
  partyPersonList: Array<{ label: string; value: string }>
}>({ partyPersonList: [] })

/** 返回党员列表 */
const PartyPersonListOptions = computed(() => partyPersonObj.partyPersonList)

function handleUpdateValue(v: string) {
  formDataReactive.value.userId = null
  formDataReactive.value.oldDeptId = v
  if (v === null) {
    return
  }
  putPartyUserList({
    id: v,
    pageNum: 1,
    pageSize: 99999999,
    username: '',
  }).then((res) => {
    partyPersonObj.partyPersonList = res.records.map((item: any) => {
      return {
        label: item.trueName,
        value: item.userId,
      }
    })
  })
}

/** 选择党员id */
const handleUpdatePartyPersonValue = (v: string, option: SelectOption) => {
  formDataReactive.value.userId = v
  formDataReactive.value.userName = option.label as string
  getUserInfo(v).then((res) => {
    formDataReactive.value.joinTime = res.joinTime
  })
}

// 当前组织操作
function getCheckedDataOfCurrentOrganization(
  value: string | number,
  option: TreeSelectOption | null,
) {
  handleUpdateValue(value as string)
  // formDataReactive.value.organizationName = option?.label as string
}

// 获取组织名称
function getCheckedData(
  value: string | number,
  option: TreeSelectOption | null,
) {
  // formDataReactive.value.organizationName = option?.label as string
}

// 转化data为树状结构
function transformTreeData(data: any) {
  if (Array.isArray(data)) {
    data.forEach((item) => {
      item.key = item.id
      item.label = item.name
      item.value = item.id

      if (item.children) {
        item.children.forEach((child: any) => {
          transformTreeData(child)
        })
      }
    })
  }
  else {
    data.key = data.id
    data.label = data.name
    data.value = data.id

    if (data.children) {
      data.children.forEach((child: any) => {
        transformTreeData(child)
      })
    }
  }
  return data
}

/**
 * 上传介绍信
 */
async function handleUpload(
  file: File,
  options: any,
  callBack: (data: any) => void,
  upDatedProgress: (progress: number) => void,
) {
  emits('update:uploadFileLoading', true)
  const fileData = new FormData()
  const fileItem = options.file.file as Blob
  fileData.append('file', fileItem)
  const data: uploadFileItem = await uploadImg(fileData, (progress) => {
    upDatedProgress(progress)
  })
  callBack(data)
  emits('update:uploadFileLoading', false)
}
onMounted(() => {
  handleGetCurrentOrganize()
})

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    :disabled="isViewModel"
    :model="formDataReactive.value"
    :rules="formRules"
    label-align="right"
    label-placement="left"
    label-width="120"
    require-mark-placement="left"
    size="small"
  >
    <n-form-item label="调动类型：" path="transferType">
      <n-select
        v-model:value="formDataReactive.value.transferType"
        :options="transferTypeProp"
        clearable
        placeholder="请选择调动类型"
        size="medium"
      />
    </n-form-item>
    <n-form-item label="调动时间：" path="transferTime">
      <n-input
        v-if="isViewModel"
        v-model:value="formDataReactive.value.transferTime"
        placeholder="请选择调动时间"
      />
      <n-date-picker
        v-else
        v-model:formatted-value="formDataReactive.value.transferTime"
        :is-date-disabled="handleDateDisabled"
        clearable
        placeholder="请选择调动时间"
        style="width: 100%"
        type="datetime"
        @update:formatted-value="
          (v:any) => (formDataReactive.value.transferTime = v)
        "
      />
    </n-form-item>
    <n-form-item
      v-if="formDataReactive.value.transferType !== 3"
      label="当前组织："
      path="oldDeptId"
    >
      <n-input
        v-if="isViewModel"
        v-model:value="formDataReactive.value.oldDeptName"
        placeholder="请选择当前组织"
      />
      <!-- 如果是系统内调出，就控制当前所属组织权限 -->
      <n-tree-select
        v-else-if="
          formDataReactive.value.transferType === 1 ||
            formDataReactive.value.transferType === 2
        "
        v-model:value="formDataReactive.value.oldDeptId"
        :options="transformTreeData(calcTreeDataWithPermission)"
        clearable
        key-field="deptId"
        placeholder="请选择当前组织"
        size="small"
        value-field="deptId"
        @update:value="getCheckedDataOfCurrentOrganization"
      />
      <n-tree-select
        v-else
        v-model:value="formDataReactive.value.oldDeptId"
        :options="transformTreeData(calcTreeData)"
        clearable
        placeholder="请选择当前组织"
        size="small"
        @update:value="getCheckedDataOfCurrentOrganization"
      />
    </n-form-item>
    <n-form-item label="调动人选择：" path="userId">
      <n-input
        v-if="isViewModel"
        v-model:value="formDataReactive.value.userName"
        placeholder="请选择调动人员"
      />
      <n-select
        v-else
        v-model:value="formDataReactive.value.userId"
        :options="PartyPersonListOptions"
        placeholder="选择调动人员"
        @update:value="handleUpdatePartyPersonValue"
      />
    </n-form-item>
    <n-form-item
      v-if="formDataReactive.value.transferType !== 2"
      label="目标组织："
      path="newDeptId"
    >
      <n-input
        v-if="isViewModel"
        v-model:value="formDataReactive.value.newDeptName"
        placeholder="请选择目标组织"
      />
      <!-- 如果是系统内调入，就控制当目标组织权限 -->
      <n-tree-select
        v-else-if="formDataReactive.value.transferType === 0"
        v-model:value="formDataReactive.value.newDeptId"
        :options="
          addDisabledProperty(transformTreeData(calcTreeDataWithPermission))
        "
        clearable
        key-field="deptId"
        placeholder="请选择目标组织"
        size="small"
        value-field="deptId"
        @update:value="getCheckedData"
      />
      <n-tree-select
        v-else-if="formDataReactive.value.transferType === 3"
        v-model:value="formDataReactive.value.newDeptId"
        :options="transformTreeData(calcTreeDataWithPermission)"
        clearable
        key-field="deptId"
        placeholder="请选择目标组织"
        size="small"
        value-field="deptId"
        @update:value="getCheckedData"
      />
      <n-tree-select
        v-else
        v-model:value="formDataReactive.value.newDeptId"
        :options="transformTreeData(calcTreeData)"
        clearable
        placeholder="请选择目标组织"
        size="small"
        @update:value="getCheckedData"
      />
    </n-form-item>
    <n-form-item label="入党时间：" path="joinTime">
      <n-input
        v-if="isViewModel"
        v-model:value="formDataReactive.value.joinTime"
        placeholder="请选择入党时间"
      />
      <n-date-picker
        v-else
        v-model:formatted-value="formDataReactive.value.joinTime"
        clearable
        disabled
        placeholder="请选择入党时间"
        style="width: 100%"
        type="datetime"
        @update:formatted-value="
          (v:any) => (formDataReactive.value.joinTime = v)
        "
      />
    </n-form-item>
    <n-form-item label="党费缴纳时间：" path="paymentTime">
      <n-input
        v-if="isViewModel"
        v-model:value="formDataReactive.value.paymentTime"
        placeholder="请选择党费缴纳时间"
      />
      <n-date-picker
        v-else
        v-model:formatted-value="formDataReactive.value.paymentTime"
        :is-date-disabled="handleDateDisabled"
        clearable
        placeholder="请选择党费缴纳时间"
        style="width: 100%"
        class="w-full"
        type="month"
        value-format="yyyy-MM"
        @update:formatted-value="
          (v:any) => (formDataReactive.value.paymentTime = v)
        "
      />
    </n-form-item>
    <n-form-item label="调动原因：" path="reason" span="24">
      <n-input
        v-model:value="formDataReactive.value.reason"
        maxlength="200"
        placeholder="请填写调动原因"
        rows="5"
        show-count
        type="textarea"
      />
    </n-form-item>
    <n-form-item label="介绍信：" path="letterId" span="24">
      <file-uploader-new
        v-model:original-file-list="formDataReactive.value.fileList"
        :is-readonly="props.type === 'view'"
        :max="1"
        :size-limit="200"
        :upload-method="handleUpload"
        accept=".doc, .docx, .pdf"
        need-progress
      >
        <template #tips>
          <span class="tips">
            只可上传一个文件，支持扩展名：.doc，docx，.pdf，大小200M以内
          </span>
        </template>
      </file-uploader-new>
    </n-form-item>
    <n-form-item label="党费缴纳证明：" path="proveId" span="24">
      <!--v-model:oldImgUrl="formDataReactive.value.proveId"-->
      <ImgUploader
        :height="256"
        :is-readonly="props.type === 'view'"
        :need-cropper="false"
        :width="194"
        @delete="handlePaymentImageDel"
        @done="handlePaymentImage"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
