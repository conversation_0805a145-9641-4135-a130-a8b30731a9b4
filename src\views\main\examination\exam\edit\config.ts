/*
 * @Description: 考试编辑表单配置
 */
import type { FormRules } from 'naive-ui'
import type { IExaminationExamForm } from '@/services/examination/index'

type Cp = [number, number | null]

export const formRules: FormRules = {
  name: { required: true, message: '请输入考试名称', trigger: 'change' },
  examinationType: {
    required: true,
    validator(rule: any, value: number | null) {
      if (!value) {
        return new Error('请选择考试类型')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  examPaperList: {
    required: true,
    validator(rule: any, value: number[]) {
      if (!value || !value.length) {
        return new Error('请选择试卷')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  durationCp: {
    required: true,
    validator(rule: any, value: Cp) {
      if (value[0] === 0 && !value[1]) {
        return new Error('请输入时长')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  passScore: {
    required: true,
    validator(rule: any, value: number) {
      if (!value) {
        return new Error('请输入通过分数')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  answerAnlysisRule: {
    required: true,
    validator() {
      return true
    },
  },
  chancesCp: {
    required: true,
    validator(rule: any, value: Cp) {
      if (value[0] === 1 && !value[1]) {
        return new Error('请输入补考次数')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
  orderRule: {
    required: true,
    validator() {
      return true
    },
  },
  enableRangeCp: {
    required: true,
    validator(rule: any, value: [number, number[]]) {
      if (value[0] === 1 && (!value[1] || !value[1].length)) {
        return new Error('请选择人员')
      } else {
        return true
      }
    },
    trigger: 'change',
  },
}

export function getFormData(): IExaminationExamForm & {
  durationCp: Cp
  chancesCp: Cp
  enableRangeCp: [number, number[]]
  timeRange: [number, number] | null
  orderRuleArr: number[]
} {
  return {
    name: '',
    description: '',
    examPaperList: [],
    examinationType: null,
    duration: -1,
    durationCp: [-1, null],
    passScore: null,
    startTime: '',
    endTime: '',
    timeRange: null,
    answerAnlysisRule: 1,
    chances: 0,
    chancesCp: [0, null],
    orderRule: 1,
    orderRuleArr: [],
    enableRange: 0,
    enableRangeCp: [0, []],
    isPublished: 0,
  }
}
