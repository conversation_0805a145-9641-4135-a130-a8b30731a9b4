export interface TransferTableItemType {
  id: string
  userId: string
  userName: string
  transferType: string
  oldDeptId: string
  newDeptId: string
  oldDeptName: string
  newDeptName: string
  reason: string
  phaseStatus: string
  remark?: any
  paymentTime: string
  letterId?: any
  proveId?: any
  approvalId?: any
  approvalStatus: string
  sort?: any
  transferTime: string
  createTime: string
  approvalTime?: any
}

export interface fileItem {
  fileName: number | string | null
  id: string
  original: number | string | null
  percentage?: number
  url?: string | number | null
  name?: string | number | null
}

// 添加组织关系转接类型
export interface AddTransferItemType {
  id: number | string | null
  userId: number | string | null
  transferType: number | null
  oldDeptId: number | string | null
  oldDeptName?: string | null
  newDeptId: number | string | null
  newDeptName: string | null
  reason: string | null
  phaseStatus: number | null
  remark: string | null
  paymentTime: string | null
  letterId: number | string | null | undefined
  letterName?: number | string | null
  proveId: number | string | null
  transferTime: string | null
  joinTime: string | null
  userName: string | null
  fileIds?: Array<string> | null
  fileList?: Array<fileItem> | null
  continueApprovalStatus?: string | null
  approvalNodes?: ApprovalNodesType[]
}

export interface ApprovalUserNameNode {
  userId: string
  username: string
  nickName: string
  roleName: string
  phone: string
}

export interface ApprovalNodesType {
  id: string
  orgName: string
  approveNode: string
  approveRoleName?: any
  approvalStatus: string
  approvalResult?: any
  approvalUserName?: any
  approvalComment?: any
  approvalTime?: any
  approvalUserNameNodes: ApprovalUserNameNode[]
}

// 组织关系转接审批流类型
export interface RoleList {
  approvalRoleId: string | undefined
  approvalRoleName: string | undefined
}

export interface NodeList {
  approvalNode: string
  sort?: number
  orgTypeId: string
  orgTypeName: string
  roleList: RoleList[]
  selectRoleList?: Array<string | undefined>
}

export interface SaveApproverType {
  orgidList: string[]
  nodeList: NodeList[]
}

// 角色列表类型
export interface RoleItemType {
  roleId: string
  roleName: string
  roleDesc: string
}

// 提交审核需要的数据
export interface SubmitCheckDataType {
  approvalId: string | null
  id: string | null
  approvalResult: string | null
  approvalComment: string | null
  sort: string | null
}
