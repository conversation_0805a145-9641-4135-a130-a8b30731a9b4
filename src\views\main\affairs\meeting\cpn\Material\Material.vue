<script setup lang="ts">
import { NButton } from 'naive-ui'
import ContentBox from '../ContentBox.vue'
import { getTableColumns } from './config'
import {
  deleteMeetingMaterial,
  getMeetingMaterialList,
  postMeetingMaterial,
} from '@/services/affairs/party-meeting'
import DeleteButton from '@/components/DeleteButton.vue'

import type {
  uploadFileItem,
  uploadFileItemNew,
} from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
import { uploadImg } from '@/services'
interface Props {
  id: string
}
const props = defineProps<Props>()

async function handleSingleDelete(ids: string) {
  try {
    await deleteMeetingMaterial({ ids })
    handleLoadMaterial()
    window.$message.success('删除成功')
  } catch {}
}

const tableColumns = getTableColumns((row) => {
  return [
    h(DeleteButton, {
      handleConfirm: () => handleSingleDelete(String(row.meetingFileId)),
    }),
  ]
})
const materialData = ref()
const loading = ref(false)
async function handleLoadMaterial() {
  try {
    loading.value = true
    const params = {
      meetingId: props.id,
    }
    materialData.value = await getMeetingMaterialList(params)
  } catch (e) {
  } finally {
    loading.value = false
  }
}

const materialRef = ref()
const materialAddVisible = ref(false)

function handleAddMaterial() {
  materialAddVisible.value = true
}
const formDataReactive = reactive({
  fileIds: [] as any,
  fileList: [] as any,
})
const fileIdObj = reactive<{ fileIdsArr: Array<string> }>({ fileIdsArr: [] })

// 文件相关
async function handleFileChange(
  fileInfoList: uploadFileItemNew[],
  isDelIDs: string,
) {
  try {
    const fileData = new FormData()
    // 获取所有FileID
    if (!fileIdObj.fileIdsArr.length) {
      formDataReactive.fileList.forEach((item: any) =>
        fileIdObj.fileIdsArr.push(item.id),
      )
    }
    // 删除动作
    if (isDelIDs || isDelIDs === null) {
      if (isDelIDs === null && fileIdObj.fileIdsArr.length) {
        fileIdObj.fileIdsArr.splice(fileIdObj.fileIdsArr.length - 1, 1)
      } else {
        fileIdObj.fileIdsArr.forEach((item, index) => {
          if (item === isDelIDs) {
            fileIdObj.fileIdsArr.splice(index, 1)
          }
        })
      }
      formDataReactive.fileIds = [...fileIdObj.fileIdsArr]
    } else {
      // 新增动作
      const lastFileItem = fileInfoList[fileInfoList.length - 1]?.file
      fileData.append('file', lastFileItem as Blob)
      const data: uploadFileItem = await uploadImg(fileData)
      formDataReactive.fileList.push({
        original: lastFileItem?.name as string,
        fileName: data.url || '',
        id: data.fileId,
      })
      if (data) {
        fileIdObj.fileIdsArr.push(data.fileId)
        formDataReactive.fileIds = [...fileIdObj.fileIdsArr]
        fileIdObj.fileIdsArr = [] // 清空fileIdObj.fileIdsArr数组
      }
    }
  } catch (error) {}
}

async function handleConfirm() {
  try {
    loading.value = true
    await postMeetingMaterial({
      meetingId: props.id,
      fileIds: formDataReactive.fileIds,
      // fileIds: formDataReactive.fileList.map((file: any) => file.id),
    })
    window.$message.success('添加成功')
    formDataReactive.fileList = []
    formDataReactive.fileIds = []
    materialAddVisible.value = false
    handleLoadMaterial()
  } catch (error) {
  } finally {
    loading.value = false
  }
}
function handleCancel() {
  formDataReactive.fileList = []
  formDataReactive.fileIds = []
  materialAddVisible.value = false
}

onMounted(() => {
  handleLoadMaterial()
})
</script>
<template>
  <content-box title="会议资料">
    <template #btn>
      <n-button text type="info" @click="handleAddMaterial">
        添加
      </n-button>
    </template>
    <div class="w-[85%] pt-[30px]">
      <NDataTable
        striped
        :loading="loading"
        :columns="tableColumns"
        :data="materialData"
      />
    </div>
  </content-box>

  <custom-dialog
    ref="materialRef"
    v-model:show="materialAddVisible"
    :show-action="true"
    width="550px"
    title="添加会议资料"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="px-[30px] py-[20px] flex flex-col gap-y-[20px]">
      <div class="flex gap-x-[10px]">
        <span class="flex-none pt-[4px]">附件：</span>
        <div class="flex flex-wrap flex-col gap-[10px] w-full">
          <file-uploader
            :max="100"
            accept=".jpg, .png, .jpeg, .pdf"
            :size-limit="200"
            :original-file-list="(formDataReactive.fileList as any)"
            @file-list-change="handleFileChange"
          >
            <template #tips>
              <span class="tips">
                可上传多个文件，支持扩展名：.jpg，.jpeg，.png，.pdf，大小200M以内
              </span>
            </template>
          </file-uploader>
        </div>
      </div>
    </div>
  </custom-dialog>
</template>
<style lang="scss" scoped></style>
