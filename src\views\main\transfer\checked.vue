<script lang="ts" setup>
import { NButton } from 'naive-ui'
import { PlusRound } from '@vicons/material'
import { getTableColumnsOfCheck, statusOptions, transferType } from './config'
import NoticeForm from './cpn/NoticeForm.vue'
import viewNoticeForm from './cpn/viewNoticeForm.vue'
import ApproverSetting from './cpn/ApproverSetting.vue'
import { useDrawerEditOrganize } from '@/hooks'
import { getPartyMemberTransferCheckedTableList } from '@/services/transfer'
import type { TransferTableItemType } from '@/services/transfer/types'

const route = useRoute()
const userNameRef = ref('')
const transferTypeRef = ref<number | null>(null)
const phaseStatusRef = ref<null | string>(null)
const pageNum = ref(1)
const pageSize = ref(10)
const loading = ref(false)

const currentFormStatus = ref('add')

const total = ref(0)
const tableData = ref<TransferTableItemType[]>([])
const approverSettingRef = ref(false)
const approverSettingFormRef = ref()

const uploadFileLoading = ref(false)

/** 获取由我发起列表 */
function getTableList() {
  tableData.value.length = 0
  loading.value = true
  const params = {
    userName: userNameRef.value,
    transferType: transferTypeRef.value,
    phaseStatus: phaseStatusRef.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }
  getPartyMemberTransferCheckedTableList(params)
    .then((res: any) => {
      total.value = res.total || 0
      tableData.value = res.records || []
    })
    .finally(() => {
      loading.value = false
    })
}

// 新增/查看党组织转接抽屉
const idEditRef = ref()
const idEditParentID = ref()
const idEditParentName = ref()
const idEditLevel = ref()
const addNoticeFormRef = ref()
const checkFormRef = ref()

/**
 * 保存操作
 */
const handelConfirmEdit = () => {
  if (['check'].includes(currentFormStatus.value)) {
    checkFormRef.value?.validateAndSave()
  }
  else {
    addNoticeFormRef.value?.validateAndSave()
  }
}

const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  editTitle,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEditOrganize({
  name: '组织关系转接',
  confirmFn: handelConfirmEdit,
  isNotTips: true,
})

/**
 * @description 添加组织关系转接
 */
const addOrganizationChangeOver = () => {
  editTitle.value = '组织关系转接'
  editTypeRef.value = 'add'
  currentFormStatus.value = 'add'
  showEditRef.value = true
}

// 审批人设置
// function approverSetting() {
//   approverSettingRef.value = true
// }

watch(showEditRef, (newV) => {
  if (!newV) {
    if (['check'].includes(currentFormStatus.value)) {
      checkFormRef.value?.resetForm()
    }
    else {
      addNoticeFormRef.value?.resetForm()
    }
  }
})

/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  getTableList()
}

// 修改和删除按钮渲染
const tableColumns = getTableColumnsOfCheck((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          editTitle.value = '组织关系转接'
          editTypeRef.value = 'view'
          currentFormStatus.value = 'view'
          showEditRef.value = true
          idEditRef.value = row.id
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '查看',
      },
    ),
  ]
})

watch(
  () => pageNum.value,
  () => {
    getTableList()
  },
)

watch(
  () => pageSize.value,
  () => {
    pageNum.value = 1
    getTableList()
  },
)

// 审批人设置确定按钮
function handleClickApproverSettingConfirm() {
  approverSettingFormRef.value.saveApprover()
  approverSettingRef.value = false
}

// 审批人设置取消按钮
function handleClickApproverSettingCancel() {
  approverSettingRef.value = false
}

// 过滤条件筛选时
function handelFilterFn() {
  pageNum.value = 1
  getTableList()
}

onMounted(() => {
  if (route.query.id) {
    editTitle.value = '组织关系转接'
    editTypeRef.value = 'view'
    currentFormStatus.value = 'view'
    showEditRef.value = true
    idEditRef.value = route.query.id
  }
  getTableList()
})
</script>
<template>
  <table-container
    v-model:page="pageNum"
    v-model:page-size="pageSize"
    :loading="loading"
    :show-delete="false"
    :show-pagination="true"
    :show-toolbar="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    custom-toolbar
    default-expand-all
    title="组织关系转接"
    @click-add="addOrganizationChangeOver"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="addOrganizationChangeOver">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <!-- <n-button size="small" type="primary" @click="approverSetting">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        审批人设置
      </n-button> -->
    </template>
    <template #filters>
      <div
        class="flex flex-row items-center justify-between gap-[10px] w-[600px]"
      >
        <n-select
          v-model:value="transferTypeRef"
          :options="transferType"
          clearable
          placeholder="请选择调动类型"
          size="medium"
          @update:value="handelFilterFn"
        />
        <n-select
          v-model:value="phaseStatusRef"
          :options="statusOptions"
          clearable
          placeholder="请选择状态"
          size="medium"
          @update:value="handelFilterFn"
        />
        <n-input
          v-model:value="userNameRef"
          clearable
          placeholder="请输入党员姓名"
          size="medium"
          @input="handelFilterFn"
        />
      </div>
    </template>
  </table-container>

  <!-- 新增组织关系转接 -->
  <n-drawer v-model:show="showEditRef" :mask-closable="false" :width="700">
    <n-drawer-content :title="drawerTitle" closable>
      <!-- <notice-form
        v-show="['add', 'view'].includes(currentFormStatus)"
        :id="idEditRef"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        :transfer-type-prop="transferType"
        :parent-id="idEditParentID"
        :parent-name="idEditParentName"
        :level="idEditLevel"
        @saved="handleListSaved"
      /> -->
      <template v-if="['view'].includes(currentFormStatus)">
        <viewNoticeForm
          :id="idEditRef"
          ref="addNoticeFormRef"
          :level="idEditLevel"
          :parent-id="idEditParentID"
          :parent-name="idEditParentName"
          :transfer-type-prop="transferType"
          type="view"
          @saved="handleListSaved"
        />
      </template>
      <template v-if="['add'].includes(currentFormStatus)">
        <notice-form
          :id="idEditRef"
          ref="addNoticeFormRef"
          v-model:upload-file-loading="uploadFileLoading"
          :level="idEditLevel"
          :parent-id="idEditParentID"
          :parent-name="idEditParentName"
          :transfer-type-prop="transferType"
          :type="editTypeRef"
          @saved="handleListSaved"
        />
      </template>
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            :loading="uploadFileLoading"
            style="width: 80px"
            type="primary"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>

  <!-- 审批人设置 -->
  <n-drawer
    v-model:show="approverSettingRef"
    :mask-closable="false"
    :width="700"
  >
    <n-drawer-content closable title="审批人设置">
      <ApproverSetting ref="approverSettingFormRef" @saved="handleListSaved" />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            style="width: 80px"
            type="primary"
            @click="handleClickApproverSettingConfirm"
          >
            确定
          </n-button>
          <n-button
            style="width: 80px"
            @click="handleClickApproverSettingCancel"
          >
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
