import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { VNodeChild } from 'vue'
import type { Inform } from '@/services/run/inform/types'
import { NOTICE_MANAGE_STATUS, getDictLabelByValue } from '@/store/dict'

export function getTableColumns(
  optionColumnRenderer: (row: Inform) => VNodeChild,
  IndexRender: (row: Inform, index: number) => VNodeChild,
): TableColumns<Inform> {
  return [
    {
      title: '序号',
      key: 'id',
      render: IndexRender,
    },
    {
      title: '通知类型',
      key: 'type',
    },
    {
      title: '通知标题',
      key: 'title',
      width: 220,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '通知内容',
      key: 'content',
      width: 220,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '发布人',
      key: 'userName',
    },
    {
      title: '更新时间',
      key: 'updateTime',
    },
    {
      title: '通知发送时间',
      key: 'noticeTime',
      render: row => row.noticeTime ?? '--',
    },
    {
      title: '状态',
      key: 'noticeStatus',
      render: (row) => {
        return getDictLabelByValue(NOTICE_MANAGE_STATUS, row.noticeStatus)
      },
    },
    {
      title: '操作',
      key: 'action',
      align: 'left',
      width: 140,
      render: optionColumnRenderer,
    },
  ]
}
