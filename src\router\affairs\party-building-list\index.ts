import { RouterView } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const partyBuildingListRoute: RouteRecordRaw = {
  path: 'party-building-list',
  name: 'partyBuildingList',
  meta: { title: '党建清单', icon: 'authority', category: 'affairs', order: 9 },
  component: () =>
    import('@/views/main/affairs/party-building-list/partyBuildingList.vue'),
  redirect: { name: 'ptList' },
  children: [
    {
      path: 'party-build',
      name: 'partyBuild',
      meta: { title: '党建清单', icon: 'role' },
      component: RouterView,
      redirect: { name: 'ptList' },
      children: [
        {
          path: 'pt-list',
          name: 'ptList',
          component: () =>
            import(
              '@/views/main/affairs/party-building-list/pt-list/PtList.vue'
            ),
        },
        {
          path: 'target/:partyListId',
          name: 'ptListTarget',
          component: () =>
            import(
              '@/views/main/affairs/party-building-list/pt-list/target/Target.vue'
            ),
        },
      ],
    },
    {
      path: 'exam-indicators',
      name: 'examIndicators',
      meta: { title: '考核指标项', icon: 'admin' },
      component: () =>
        import(
          '@/views/main/affairs/party-building-list/exam-indicators/examList.vue'
        ),
    },
  ],
}
export default partyBuildingListRoute
