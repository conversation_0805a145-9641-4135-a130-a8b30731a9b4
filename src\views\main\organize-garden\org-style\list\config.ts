import type { VNodeChild } from 'vue'
import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import { NImage } from 'naive-ui'
import type { OrgStyleListItem } from '@/services/organize-garden/types'
export function getTableColumns(
  operationRender: (row: OrgStyleListItem) => VNodeChild,
  topRender: (row: OrgStyleListItem) => VNodeChild,
  hiddenRender: (row: OrgStyleListItem) => VNodeChild,
): TableColumns<OrgStyleListItem> {
  return [
    {
      type: 'selection',
      align: 'center',
    },
    {
      title: '创新做法标题',
      key: 'title',
    },
    {
      title: '封面图',
      key: 'img',
      render: row =>
        h(NImage, {
          src: import.meta.env.VITE_API_BASE + row.coverUrl,
          width: '62',
          style: { height: '40px' },
        }),
    },
    {
      title: '发表时间',
      key: 'createTime',
      render: row => row.createTime ?? '-',
    },
    {
      title: '是否置顶',
      key: 'isTop',
      render: topRender,
    },

    {
      title: '是否隐藏',
      key: 'isHidden',
      render: hiddenRender,
    },

    {
      title: '操作',
      key: 'operation',
      width: '10%',
      render: operationRender,
    },
  ]
}
