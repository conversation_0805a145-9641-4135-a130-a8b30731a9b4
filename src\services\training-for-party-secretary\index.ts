import type { RequestTypes } from './types'
import { commonReq } from '@/services/request'

/** 党组织集中轮训分页查询 */
export function getPartyPollingList(
  query: RequestTypes.QueryTrainingForPartySecretaryType,
) {
  return commonReq.get<RequestTypes.QueryTrainingForPartySecretaryResponseType>(
    {
      url: '/party-affairs/work-record-book/party-training/list',
      params: query,
    },
  )
}

/** 查询党组织集中轮训详情 */
export function getPartyPollingDetail(
  query: RequestTypes.QueryTrainingForPartySecretaryDetailType,
) {
  return commonReq.get<RequestTypes.QueryTrainingForPartySecretaryDetailResponseType>(
    {
      url: `/party-affairs/work-record-book/party-training/${query.id}`,
    },
  )
}

/** 新增党组织集中轮训 */
export function addPartyPolling(
  data: RequestTypes.AddTrainingForPartySecretaryType,
) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/party-training',
    data,
  })
}

/** 修改党组织集中轮训 */
export function updatePartyPolling(
  data: RequestTypes.UpdateTrainingForPartySecretaryType,
) {
  return commonReq.put({
    url: '/party-affairs/work-record-book/party-training',
    data,
  })
}

/** 删除党组织集中轮训 */
export function deletePartyPolling(
  query: RequestTypes.DeleteTrainingForPartySecretaryType,
) {
  return commonReq.delete({
    url: `/party-affairs/work-record-book/party-training/${query.id}`,
  })
}
