import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  title: {
    required: true,
    message: '活动名称不能为空',
    trigger: 'input',
  },
  sort: [
    {
      required: true,
      message: '排序不能为空',
      trigger: 'input',
      type: 'number',
    },
  ],
  isMobile: [
    {
      required: true,
      message: '移动端是否展示不能为空',
      trigger: 'change',
      type: 'number',
    },
  ],
  imgId: {
    required: true,
    message: '请上传活动图片',
    trigger: 'change',
    type: 'string',
  },
}
