/** 登录表单 */
export interface UserWholeInfo {
  /** 授权令牌 */
  accessToken: string
  /** 刷新令牌 */
  refreshToken: string
  /** 用户信息 */
  user_info: UserInfo
}

/** 用户信息 */
export interface UserInfo {
  /** 账号是否过期 */
  accountNonExpired: boolean
  /** 账号是否锁定 */
  accountNonLocked: boolean
  /** 用户属性 */
  attributes: Record<string, any>
  /** 权限列表 */
  authorities: AuthorityList[]
  /** 身份是否过期 */
  credentialsNonExpired: boolean
  /** 所属组织id */
  deptId: string
  /** 管理组织id */
  deptIds: any
  /** 是否可以登录 */
  enabled: boolean
  /** 用户id */
  id: string
  /** trueName */
  name: string
  /** 密码 */
  password: string | null
  /** 手机号 */
  phone: string
  /** 用户名 */
  username: string
  permissionCollect: string[]
}

export interface AuthorityList {
  /** 权限 */
  authority: string
}

export interface ILoginReq {
  username: string
  password: string

  [key: string]: string
}

export interface ILoginRes {
  accessToken: string
  refreshToken: string
  user_info: UserInfo
  token_type?: string
  expires_in?: number
  scope?: string
  license?: string
}
