/*
 * @Description:表单配置
 */

import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  name: [{ required: true, message: '请输入试卷名称', trigger: 'change' }],
  paperType: [
    {
      required: true,
      validator(rule: any, value: any) {
        if (value == null) {
          return new Error('请选择试卷类型')
        } else {
          return true
        }
      },
      trigger: 'change',
    },
  ],
}

export function getBaseFormData(): { name: string; paperType: number | null } {
  return {
    name: '',
    paperType: null,
  }
}
