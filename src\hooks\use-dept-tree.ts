/*
 * @Description: 用户管理相关
 */
import { computed, ref, watch } from 'vue'
import type { TreeOption } from 'naive-ui'
import type { IDeptTreeNode } from '@/services/index'
import useAuthorityStore from '@/store/main/authority/index'
import type { ITreeNode } from '@/components/awesome-tree'
import { transformToTreeOptions } from '@/components/awesome-tree'
import { getFirstSelectableNode } from '@/utils/tree-transform'

/**
 * setup hook
 */
export default function() {
  const authorityStore = useAuthorityStore()
  authorityStore.getDeptTreeAction()

  const treeRef = ref()
  const currentDeptRef = ref<TreeOption>()
  const currentDeptIdRef = ref<any>('')

  // 组织树数据
  const treeData = computed<ITreeNode[]>(() => {
    return transformToTreeNodes(authorityStore.deptList)
  })

  watch(treeData, (newV) => {
    const dept = getFirstSelectableNode(newV, false)
    if (dept) {
      currentDeptRef.value = {
        key: dept.nodeId,
        label: dept.nodeName,
        children: transformToTreeOptions(dept.children, 1, false, ''),
      }
      currentDeptIdRef.value = dept.nodeId
      treeRef.value?.setSelectedKeys([dept.nodeId])
      treeRef.value?.setExpandedKeys([dept.nodeId])
    }
  })

  return {
    authorityStore,
    treeRef,
    treeData,
    currentDeptIdRef,

    // 选中部门
    handleSelectDept(dept: TreeOption[]) {
      currentDeptRef.value = dept[0]
      currentDeptIdRef.value = dept[0].key
    },
  }
}

// 将组织树转成ITreeNode
function transformToTreeNodes(data: IDeptTreeNode[]): ITreeNode[] {
  const result: ITreeNode[] = []

  data.forEach((node) => {
    const option: ITreeNode = {
      nodeId: node.id,
      nodeName: node.name,
      pid: node.parentId,
      children: null,
    }
    if (node.children && node.children.length) {
      option.children = transformToTreeNodes(node.children)
    }
    result.push(option)
  })

  return result
}

// 根据选中的deptId获取所有叶子节点id
export function getLeafDeptIdList(node: TreeOption) {
  const idList: number[] = []

  function recursive(node: TreeOption) {
    if (node.children?.length) {
      node.children.forEach((child) => {
        recursive(child)
      })
    } else {
      idList.push(Number(node.key))
    }
  }

  recursive(node)

  return idList
}
