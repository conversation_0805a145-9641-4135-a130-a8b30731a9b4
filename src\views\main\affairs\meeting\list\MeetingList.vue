<script lang="ts" setup>
import { h } from 'vue'
import { NButton, NSelect } from 'naive-ui'
// import { Export } from '@vicons/carbon'
import { DeleteForeverRound } from '@vicons/material'
import { getTableColumns } from './config'
import DeleteButton from '@/components/DeleteButton.vue'
import { useMyTable } from '@/hooks'
import { MEETING_TYPE } from '@/store/dict'
import { useCurrentOrganizationListOptionsNew } from '@/hooks/use-select-options'

import {
  deleteMeetings,
  getMeetingList,
} from '@/services/affairs/party-meeting'

const { organizationCurrentListTree } = useCurrentOrganizationListOptionsNew() // 获取组织列表
const router = useRouter()
const filterRef = ref({
  deptId: null,
  meetingType: null,
  title: null,
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  onUpdateSorter,
  loadData,
} = useMyTable(
  getMeetingList,
  filterRef,
  {
    batchDeleteTable: true,
    delApi: deleteMeetings,
  },
  false,
  ref(false),
  undefined,
  {
    startTime: {
      descend: 1,
      ascend: 0,
    },
  },
)
onMounted(loadData)

const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          router.push({
            name: 'meetingManage',
            query: {
              id: String(row.id),
              status: row.meetingStatus,
            },
          })
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '管理',
      },
    ),

    h(DeleteButton, {
      handleConfirm: () => handleSingleDelete(String(row.id)),
    }),
  ]
})
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    title="党内会议"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
    @update-sorter="onUpdateSorter"
  >
    <template #btns>
      <!-- <n-button size="small">
        <template #icon>
          <n-icon>
            <export />
          </n-icon>
        </template>
        导出
      </n-button> -->
      <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button>
    </template>
    <template #filters>
      <!-- <n-cascader
        v-model:value="filterRef.orgId"
        placeholder="请选择目标组织"
        :options="(organizationCurrentListTree as any)"
        value-field="id"
        label-field="name"
        children-field="children"
        check-strategy="child"
        :show-path="false"
        clearable
        filterable
        style="width: 260px"
        @update:value="(v:any) => (filterRef.orgId= v)"
      /> -->
      <n-tree-select
        v-model:value="filterRef.deptId"
        :options="organizationCurrentListTree"
        :show-path="false"
        check-strategy="all"
        children-field="children"
        clearable
        filterable
        key-field="deptId"
        label-field="name"
        placeholder="请选择所属党组织"
        style="width: 300px"
        value-field="deptId"
        @update:value="(v:any) => (filterRef.deptId= v)"
      />
      <n-select
        v-model:value="filterRef.meetingType"
        :options="MEETING_TYPE"
        clearable
        filterable
        placeholder="请选择会议类型"
        style="width: 260px"
      />
      <n-input
        v-model:value="filterRef.title"
        clearable
        placeholder="请输入会议主题"
        size="large"
        style="width: 260px; height: 32px"
      />
    </template>
  </table-container>
</template>
<style lang="scss" scoped></style>
