# 前端项目模板

## 一、技术栈

### Vue3

使用组合式 API ，业务逻辑编写更加灵活。

- 官方文档：[点我](https://v3.cn.vuejs.org/)

- API 查阅：[点我](https://v3.cn.vuejs.org/api/)

### TypeScript

- 官方文档：[点我](https://www.typescriptlang.org/)

### Vite

- 官方文档：[点我](https://cn.vitejs.dev/)

- 配置说明：[点我](https://cn.vitejs.dev/config/)

### Naive UI

一个精美的，基于 Vue3 的 UI 组件库，对 TypeScript 类型支持很友好。

- 官方文档：[点我](https://www.naiveui.com/zh-CN/os-theme)

- 组件说明：[点我](https://www.naiveui.com/zh-CN/os-theme/components)

### Windicss

CSS 原子化框架，通过框架提供的颗粒化的样式类，进行样式编写。

- 官方文档：[点我](https://cn.windicss.org/)

### 动态路由
1. 在router/index.ts中，在beforeEach进入路由方法中，
    1. 判断是否存在token
        1. 如果存在token，如果要去登录页，则跳转首页，否则，正常跳转，获取动态路由
        2. 如果不存在token，如果要去除登录页外的其他页面，跳转登录页，否则，正常跳转

    2. 动态路由获取逻辑
        1. 具体处理逻辑在store/menuAuth/index.ts中
        2. 第一层不是实际路由，而是在工作台中显示的分类，所以要加入categoryList中，但不实际添加路由
        3. 使用isRoutesGenerated字段判断是否已经获取动态路由，如果已经获取，不再获取新的，但是如果页面刷新/用户手动退出/接口返回token失效，会重置isRoutesGenerated
        4. 接口返回的路由存在按钮权限，暂不处理按钮权限，使用removePermitted方法过滤（permission字段不为空即为按钮）
        5. 在getAsyncRoute方法中将接口返回的路由处理为正式路由格式
        6. 如果存在type为3的，说明是隐藏菜单，则提到上一层，这样才能正常跳转（在数据库中放在列表下一层，是因为要跟列表权限绑定）

3. 数据库添加路由需要字段
    |  数据库字段   | 对应在route中的字段  |
    |  ----  | ----  |
    | name  | meta.title |
    | path  | path |
    | file_path  | component(文件路径，如@/views/main/affairs/dues/statistics/Index.vue 数据库只需要存affairs/dues/statistics/Index) |
    | route_name  | name |
    | icon  | meta.icon |
    | type  | 0-普通路由 3-隐藏路由 |
    | sort_order  | meta.order |

4. 页面处理路由方式
    1. 如果是没有第三层，参考轮播图管理，直接写页面
   
    2. 如果有第三层，但是没有第四层，参考权限管理，在第二层获取侧边路由
     ```
    <script setup lang="ts">
        import SliderMenu from '@/components/SliderMenu.vue'
    </script>
    <template>
        <slider-menu />
    </template>
    ```
    3. 如果有第四层，参考学习管理，与2相似，只是在数据库中第三层下面加了隐藏菜单（添加页面）


    