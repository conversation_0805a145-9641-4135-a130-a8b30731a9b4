/** 评议项各个项目（题目） */
export interface ReviewItem {
  id?: string
  questionnaireId?: string
  renderId?: string
  /** 题目 */
  subjectName: string
  // 类型 0 - 单选，1 - 多选， 2 - 填空
  subjectType: string
  /** 选项列表 */
  reviewSubjectItemArr?: ChoiceItem[]
  // 填空题回答
  subjectItemContent?: string
  /** 是否必填， 1 - 是， 0 - 否 */
  isRequired: string
  /** 顺序 */
  sort: number
  editing?: boolean
}

/** 评议项题目选项 */
export interface ChoiceItem {
  id?: string
  subjectId?: string
  renderId?: string
  /** 选项分值 */
  subjectItemScore: number | null
  /** 选项顺序 */
  sort: number
  /** 选项内容 */
  subjectItemName: string
  /** 是否填写理由 1 - 是，0 - 否 */
  isReason: string
  // 是否选择 1-是 0-否
  checked?: string
}

/** 评议项 - 总分项设置 */
export interface RuleItem {
  id?: string
  scoreItemName?: string // 各项名称
  /** 选项分值 */
  scoreUpper?: string | number // 分数上限
  scoreLower?: string | number // 分数下限
  questionnaireRuleId?: string
  questionnaireId?: string
  sort?: number
}

/** 评议项 - 总分项设置 */
export interface Rules {
  id?: string
  ruleScoreName?: string // 各项名称
  questionnaireId?: string
  questionnaireRuleDetailArr?: RuleItem[]
}

// 评议项问卷
export interface Questionnaire {
  questionnaireType?: string
  isTemplate?: string
  isDisplay?: string
  isScoreItem?: string
  ruleScoreName?: string
  questionnaireScoreUpper?: number
  reviewSubjectArr: ReviewItem[]
  questionnaireRuleObj?: Rules
}

// 整个评议项
export interface Test {
  [index: string]: any
  reviewItemTitle: string
  reviewItemRemark: string
  reviewItemType: string
  selfQuestionnaireObj?: Questionnaire
  eachQuestionnaireObj?: Questionnaire
  branchQuestionnaireObj?: Questionnaire
  partyGroupQuestionnaireObj?: Questionnaire
  partyBranchQuestionnaireObj?: Questionnaire
}

// 整个评议项
export interface Template {
  [index: string]: any
  selfQuestionnaireObj?: TemplateItem[]
  eachQuestionnaireObj?: TemplateItem[]
  branchQuestionnaireObj?: TemplateItem[]
}

// 整个评议项
export interface TemplateItem {
  [index: string]: any
  id?: string
  isReason: string
  reviewTemplateId?: string
  sort?: number
  templateItemName: string
  templateItemScore: number
}
