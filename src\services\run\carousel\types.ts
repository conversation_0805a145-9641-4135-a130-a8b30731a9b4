export interface ICarousel {
  [key: string]: any
  id?: number
  title: string
  imgId?: string | null // 图片地址
  sort: number | null // 排序
  isMobile: number | string | boolean | undefined // 是否显示 1显示 0不显示
  isPc: number | string | boolean | undefined // 是否显示 1显示 0不显示
  location: number
  isRelease?: string
  // locked: number
}

// 新的轮播图管理
export interface ICarouselNewType {
  [key: string]: any
  id?: number
  type: number
  title: string
  coverUrl: string
  linkUrl: string | null
  sort: number
  isMobile: number
  belongingFunction?: number // 99 自定义
}

export interface ICarouselReq {
  pageNo: number
  pageSize: number
  id?: number
  resourceName?: string
  resourceStatus?: number
}

export interface ICarouselRes {
  pageNum: number
  pageSize: number
  totalPage: number
  total: string
  records: ICarousel[]
}

export interface IRowData {
  /** 图片id */
  id?: number
  resourceName: string
  /** 是否显示 1-显示 0-不显示 */
  resourceStatus: number | null
  /** 图片位置 */
  layout: number | null
  /** 图片地址 */
  pictureUrl: string
  /** 跳转地址 */
  jumpUrl: string
  /** 排序 */
  sortOrder: string | null
  /** 图片文件 */
  file?: File
}
