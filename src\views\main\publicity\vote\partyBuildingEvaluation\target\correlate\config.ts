import type { DataTableColumns } from 'naive-ui'
import type { ExamIndicatorsListItem } from '@/services/publicity/vote/evaluation/type'

export const tableColumns: DataTableColumns<ExamIndicatorsListItem> = [
  {
    type: 'selection',
    multiple: true,
  },
  {
    key: 'index',
    title: '序号',
    align: 'center',
    render: (_, i) => i + 1,
  },
  {
    key: 'title',
    title: '标题',
  },
  {
    key: 'matter',
    title: '考核指标',
    render: (row) => {
      return h('span', {
        innerHTML: row.matter,
      })
    },
  },
  {
    key: 'evaluationRequirements',
    title: '计分标准',
    render: (row) => {
      return h('span', {
        innerHTML: row.evaluationRequirements,
      })
    },
  },
  {
    key: 'evaluationMode',
    title: '检查材料',
    render: (row) => {
      return h('span', {
        innerHTML: row.evaluationMode,
      })
    },
  },
  {
    key: 'dept',
    title: '检查部门',
  },
  {
    key: 'evaluationScore',
    title: '分值',
  },
]
