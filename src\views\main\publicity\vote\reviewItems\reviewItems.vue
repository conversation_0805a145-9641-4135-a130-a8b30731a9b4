<template>
  <div :class="`card ${data.editing ? 'active' : ''}`" @click="onClick">
    <div class="text-13px text-[#333] font-500 leading-26px mb-8px">
      <span class="questionTitle">
        <span
          v-if="data.isRequired === BOOLEANTYPE.YES"
          class="n-form-item-label__asterisk"
        />{{ index + 1 }}、{{ data.subjectName.trim() || '请输入题目信息' }}
      </span>
    </div>
    <div
      class="absolute top-4 right-5 w-[110px] h-[24]px rounded-xl flex items-center justify-evenly bg-[#EDEEF1] p-[5px]"
    >
      <NButton
        text
        :disabled="index === 0"
        @click="(e:any) => {
          e.stopPropagation()
          props.onUpward()
        }"
      >
        <NIcon :size="14">
          <ArrowUp />
        </NIcon>
      </NButton>
      <NButton
        text
        :disabled="index === total - 1"
        @click="
          (e:any) => {
            e.stopPropagation()
            props.onDownward()
          }
        "
      >
        <NIcon size="{14}">
          <ArrowDown />
        </NIcon>
      </NButton>
      <NButton
        text
        @click="
          (e:any) => {
            e.stopPropagation()
            props.onDelete()
          }
        "
      >
        <NIcon :size="14">
          <Delete />
        </NIcon>
      </NButton>
      <NButton
        text
        @click="
          (e:any) => {
            e.stopPropagation()
            props.onCopy()
          }
        "
      >
        <NIcon :size="14">
          <Copy />
        </NIcon>
      </NButton>
    </div>
    <div v-if="data.subjectType === QUESTIONTYPE.FILLED">
      <NInput placeholder="请输入意见内容" :resizable="false" type="textarea" />
    </div>
    <div
      v-else-if="
        data.subjectType === QUESTIONTYPE.MULTI &&
          data.reviewSubjectItemArr?.length
      "
    >
      <NCheckboxGroup :value="[]" size="small">
        <div
          v-for="(item, index) in data.reviewSubjectItemArr"
          :key="item.subjectId ?? item.renderId"
        >
          <NCheckbox
            class="leading-32px"
            :label="formatChoiceTitle(index, item.subjectItemName)"
          />
        </div>
      </NCheckboxGroup>
    </div>
    <div
      v-else-if="
        data.subjectType === QUESTIONTYPE.RADIO &&
          data.reviewSubjectItemArr?.length
      "
    >
      <NRadioGroup :value="1" size="small">
        <div v-for="(item, index) in data.reviewSubjectItemArr" :key="index">
          <NRadio
            :label="formatChoiceTitle(index, item.subjectItemName)"
            class="!text-sm !leading-8"
          />
        </div>
      </NRadioGroup>
    </div>
    <div v-else>
      <span class="text-12px text-[#999]">请添加选项</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { ArrowDown, ArrowUp, Copy, Delete } from '@vicons/carbon'
import type { ReviewItem } from './type'
import { BOOLEANTYPE, QUESTIONTYPE } from '@/constant'

const props = defineProps({
  data: {
    type: Object as PropType<ReviewItem>,
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
  total: {
    type: Number,
    required: true,
  },
  onClick: {
    type: Function as PropType<() => void>,
  },
  onUpward: {
    type: Function as PropType<() => void>,
    required: true,
  },
  onDownward: {
    type: Function as PropType<() => void>,
    required: true,
  },
  onDelete: {
    type: Function as PropType<() => void>,
    required: true,
  },
  onCopy: {
    type: Function as PropType<() => void>,
    required: true,
  },
})

function formatChoiceTitle(i: number, str: string) {
  let trimmed = str.trim()
  trimmed = trimmed ? `${trimmed}` : '请输入选项内容'
  return `${trimmed}` // `${String.fromCharCode(65 + i)}. ${trimmed}`
}
// onMounted(loadData)
</script>

<style lang="scss" scoped>
.card {
  padding: 17px 24px 24px;
  margin-bottom: 20px;
  transition: all 250ms;
  position: relative;
  border-radius: 3px;
  cursor: pointer;

  &:hover {
    box-shadow: 0 0 5px 0px rgba(0, 0, 0, 0.2) inset;
  }

  & .questionTitle {
    font-weight: 600;
    line-height: 26px;
  }

  &.active {
    box-shadow: 0px 0px 10px 0px rgba(172, 36, 29, 0.5);
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 2px;
      background: rgb(172, 36, 29);
      border-radius: 3px;
    }
  }
  .n-form-item-label__asterisk {
    position: relative;
    &::after {
      content: '*';
      position: absolute;
      left: -12px;
      color: #d63434;
    }
  }

  .btns {
    position: absolute;
    top: 16px;
    right: 19px;
    width: 110px;
    height: 24px;
    background: #f6f7f8;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}
</style>
