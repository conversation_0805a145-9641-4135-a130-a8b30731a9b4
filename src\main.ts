import { createApp } from 'vue'

import vue3videoPlay from 'vue3-video-play' // 引入组件
import 'vue3-video-play/dist/style.css' // 引入css

import router from './router'
import pluginRegister from './plugins'
import '@/styles/index.scss'
import directive from './directive' // directive

import NaiveConfig from './NaiveConfig.vue'

const meta = document.createElement('meta')
meta.name = 'naive-ui-style'
document.head.appendChild(meta)

const app = createApp(NaiveConfig)

directive(app)

app.use(router).use(pluginRegister).use(vue3videoPlay).mount('#app')
