<script setup lang="ts">
import { NForm } from 'naive-ui'
import { formRules } from './config'
import {
  addInform,
  getInformDetail,
  updateInform,
} from '@/services/run/inform/inform'
import useBaseStore from '@/store/base/index'
import UserSelector from '@/components/user-selector'
import type { InformReq } from '@/services/run/inform/types'
import { NOTICE_MANAGE_TYPES } from '@/store/dict'

const baseStore = useBaseStore()

const emits = defineEmits<{
  (e: 'saved'): void
}>()
interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const sendTypeOptions = ref([
  {
    label: '立刻发送',
    value: 0,
  },
  {
    label: '定时发送',
    value: 1,
  },
])

let oriFormDataReactive = reactive<InformReq>({
  title: '', // 图片名称
  type: '0', // 通知类型
  sendType: 0,
  noticeTime: null,
  content: '', // 通知内容
  userIdList: [], // 接收人员
  id: '',
})
const formDataReactive = reactive<InformReq>({
  title: '', // 图片名称
  type: '0', // 通知类型
  sendType: 0,
  noticeTime: null,
  content: '', // 通知内容
  userIdList: [], // 接收人员
  id: '',
})

const formRef = ref<InstanceType<typeof NForm>>()
const showUserSelectorRef = ref(false)
const userSelectorRef = ref()
const userIdsRef = ref<string[]>([])
const userList = ref<any[]>([])
const companyDeptUserList = computed(() => baseStore.companyDeptUserList)
// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
// 点击选择人员
function handleClickChooseUser() {
  showUserSelectorRef.value = true
  // userSelectorRef.value?.setCheckedKeys(userIdsRef.value)
  userSelectorRef.value?.calcCheckedKeys(userList.value)
}
// 确定选择
function handleConfirmSelectUser(v: any[]) {
  userList.value = v
  // userIdsRef.value = v
  // formDataReactive.userIdList = userIdsRef.value.map(
  //   item => (item ?? '').split('&')[1],
  // )
}
// 删除用户
function handleRemoveUser(index: number) {
  userList.value.splice(index, 1)

  // userIdsRef.value.splice(index, 1)
  // userSelectorRef.value?.setCheckedKeys(userIdsRef.value)
  // formDataReactive.userIdList = userIdsRef.value.map(
  //   item => (item ?? '').split('&')[1],
  // )
}
// 关闭时判断是否存在改变了的数据
function getChangedFlag() {
  return deepEqual(oriFormDataReactive, formDataReactive)
}
function deepEqual(obj1: any, obj2: any) {
  if (Object.keys(obj1).length !== Object.keys(obj2).length) {
    return false
  }

  for (const key in obj1) {
    if (!Object.prototype.hasOwnProperty.call(obj2, key)) {
      return false
    }
    if (typeof obj1[key] === 'object') {
      if (JSON.stringify(obj1[key]) !== JSON.stringify(obj2[key])) {
        return false
      }
    }
    else {
      if (obj1[key] !== obj2[key]) {
        return false
      }
    }
  }

  return true
}

// 动态取出选中人员的idList
watch(
  () => userList.value,
  () => {
    if (userList.value.length) {
      formDataReactive.userIdList = userList.value.map(
        (item: any) => item.userId,
      )
    }
  },
  { deep: true },
)

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (props.id === '0') {
        addInform(formDataReactive).then((res) => {
          window.$message.success('保存成功')
          emits('saved')
        })
      }
      else {
        updateInform(formDataReactive).then((res) => {
          window.$message.success('保存成功')
          emits('saved')
        })
      }
    }
  })
}
async function getDetail() {
  if (props.id !== '0') {
    const data = await getInformDetail(props.id)
    if (data) {
      formDataReactive.id = data.id
      formDataReactive.content = data.content ?? ''
      formDataReactive.title = data.title ?? ''
      formDataReactive.type = data.type ?? 0
      formDataReactive.sendType = data.sendType
      if (data.sendType !== 0) {
        formDataReactive.noticeTime = data.noticeTime
      }
      formDataReactive.userIdList = data.userListList.map(
        (item: any) => item.userId,
      )
      oriFormDataReactive = { ...oriFormDataReactive, ...formDataReactive }
      userIdsRef.value = data.userListList.map(
        (item: any) => `${item.userName}&${item.userId}`,
      )

      // 转换userList
      userList.value = data.userListList.map((item: any) => {
        return {
          userId: item.userId,
          userName: item.userName,
          deptId: item.deptId,
        }
      })

      userSelectorRef.value?.setCheckedKeys(userIdsRef.value)
    }
  }
  else {
    formDataReactive.id = ''
    formDataReactive.content = ''
    formDataReactive.title = ''
    formDataReactive.type = '0'
    formDataReactive.sendType = 0
    formDataReactive.noticeTime = null
    formDataReactive.userIdList = []
    userIdsRef.value = []
    userSelectorRef.value?.setCheckedKeys(userIdsRef.value)
  }
}
onMounted(() => {
  getDetail()
})
defineExpose({
  validateAndSave,
  resetForm,
  getChangedFlag,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-grid>
      <n-form-item-gi span="24" label="通知标题：" path="title">
        <n-input
          v-model:value="formDataReactive.title"
          placeholder="请输入通知标题"
          clearable
          maxlength="100"
          show-count
          :disabled="type == 'view' ? true : false"
        />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="通知类型：" required>
        <n-select
          v-model:value="formDataReactive.type"
          :options="NOTICE_MANAGE_TYPES"
          :disabled="type == 'view' ? true : false"
        />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="推送方式：" required>
        <n-input placeholder="APP通知" disabled />
      </n-form-item-gi>
      <n-form-item-gi span="24" label="发送时间：" required>
        <n-radio-group
          v-model:value="formDataReactive.sendType"
          :disabled="type == 'view' ? true : false"
        >
          <n-space>
            <n-radio
              v-for="item in sendTypeOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </n-radio>
          </n-space>
        </n-radio-group>
        <n-date-picker
          v-if="formDataReactive.sendType === 1"
          v-model:formatted-value="formDataReactive.noticeTime"
          style="width: 250px; margin-left: 10px"
          placeholder="请选择定时发送时间"
          clearable
          type="datetime"
          @update:formatted-value="
            (v:any) => (formDataReactive.noticeTime = v)
          "
        />
      </n-form-item-gi>

      <n-form-item-gi
        class="w-[550px]"
        label="接收人员："
        required
        path="userIdList"
      >
        <div class="receivers">
          <n-button v-if="type !== 'view'" @click="handleClickChooseUser">
            选择人员
          </n-button>
          <n-scrollbar
            v-if="userList && userList.length"
            style="
              width: 90%;
              padding: 10px;
              margin-top: 10px;
              max-height: 150px;
              border: 1px solid #eee;
            "
          >
            <n-tag
              v-for="(user, index) in userList"
              :key="user"
              style="margin-right: 10px; margin-bottom: 10px"
              type="info"
              :closable="type == 'view' ? false : true"
              @close="() => handleRemoveUser(index)"
            >
              {{ user.userName }}
            </n-tag>
          </n-scrollbar>
        </div>

        <user-selector
          ref="userSelectorRef"
          v-model:show="showUserSelectorRef"
          :tree-data="companyDeptUserList"
          @confirm="handleConfirmSelectUser"
        />
      </n-form-item-gi>

      <n-form-item-gi span="24" label="通知内容：" required path="content">
        <n-input
          v-model:value="formDataReactive.content"
          type="textarea"
          style="height: 120px"
          placeholder="请输入，不超过500字"
          maxlength="500"
          show-count
          :disabled="type == 'view' ? true : false"
        />
      </n-form-item-gi>
    </n-grid>
  </n-form>
</template>
<style lang="scss" scoped></style>
