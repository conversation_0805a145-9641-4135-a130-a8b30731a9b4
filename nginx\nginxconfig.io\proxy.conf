proxy_http_version                 1.1;
proxy_cache_bypass                 $http_upgrade;

# Proxy headers
proxy_set_header Upgrade           $http_upgrade;
proxy_set_header Connection        "upgrade";
proxy_set_header Host              $host;
proxy_set_header X-Real-IP         $remote_addr;
proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_set_header X-Forwarded-Host  $host;
proxy_set_header X-Forwarded-Port  $server_port;
proxy_set_header HTTP_X_FORWARDED_FOR $remote_addr; #在多级代理的情况下，记录每次代理之前的客户端真实ip

# Proxy timeouts
proxy_connect_timeout              3600s;
proxy_send_timeout                 3600s;
proxy_read_timeout                 3600s;
