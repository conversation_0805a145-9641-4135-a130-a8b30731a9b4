<script setup lang="ts">
import { NButton, NSwitch } from 'naive-ui'
import { getTableColumns } from './config'
import type { CultureShowListItem } from '@/services/run/interaction/types'
import {
  deleteInteractionSingleData,
  getCultureShowList,
  putInteractionSingleDataTop,
} from '@/services/run/interaction'
import { useMyTable } from '@/hooks'
import DeleteButton from '@/components/DeleteButton.vue'
import Scan from '@/views/main/run/interaction/cpn/Scan.vue'
interface Props {
  secondMenuStatus: string
}
const props = defineProps<Props>()

const filterRef = ref({
  title: null,
  username: null,
  status: props.secondMenuStatus,
  type: 2,
})

const idRef = ref()
const showScanDialog = ref(false)
function handleScan(id: string) {
  idRef.value = id
  showScanDialog.value = true
}

const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getCultureShowList, filterRef, {
  batchDeleteTable: true,
  delApi: deleteInteractionSingleData,
})

const tableColumns = getTableColumns(
  (row: CultureShowListItem) => {
    return h(NSwitch, {
      checkedValue: '1',
      uncheckedValue: '0',
      value: row.topStatus,
      onUpdateValue(v) {
        row.topStatus = v
        putInteractionSingleDataTop(row.id)
          .then((res) => {
            loadData()
          })
          .catch(() => {})
      },
    })
  },
  (row: CultureShowListItem) => {
    return [
      h(
        NButton,
        {
          onClick: () => handleScan(row.id),
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '查看',
        },
      ),
      h(DeleteButton, {
        style: {
          marginRight: '10px',
        },
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    ]
  },
)

onMounted(() => {
  loadData()
})
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title=""
    :show-title="false"
    :loading="loading"
    :show-toolbar="true"
    :show-add="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #filters>
      <n-input
        v-model:value="filterRef.username"
        style="width: 200px; margin-right: 10px"
        size="small"
        placeholder="请输入姓名"
        clearable
      />
      <n-input
        v-model:value="filterRef.title"
        style="width: 200px"
        size="small"
        placeholder="请输入主题"
        clearable
      />
    </template>
  </table-container>
  <custom-dialog
    v-model:show="showScanDialog"
    :show-action="false"
    width="800px"
    title="查看"
  >
    <scan
      :id="idRef"
      :show-content="false"
      title-text="主题"
      description-text="学习心得"
    />
  </custom-dialog>
</template>
<style lang="scss" scoped></style>
