import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  name: { required: true, message: '请输入版本标题', trigger: 'change' },
  file: {
    required: true,
    validator(rule: any, value: any) {
      if (!value) {
        return new Error('请上传安装包')
      }
      return true
    },
    trigger: 'change',
  },
  version: { required: true, message: '请输入版本号', trigger: 'change' },
  updateInfo: { required: true, message: '请输入升级信息', trigger: 'change' },
  downloadAddress: {
    required: true,
    message: '请输入下载地址',
    trigger: 'change',
  },
}
