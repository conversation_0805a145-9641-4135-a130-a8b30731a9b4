<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { NForm } from 'naive-ui'
import { formRules } from './config'
import {
  addBirthdayById,
  getBirthdayById,
  updateBirthday,
} from '@/services/structure/political-birthday/index'
import { uploadImg } from '@/services'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
})

const fileList = ref<any[]>([])

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive({
  id: '',
  blessing: '', // 祝福语
  imgUrl: null, // 图片
})

const loading = ref(false)
const formRef = ref<InstanceType<typeof NForm>>()

const beforeUpload = (file: any) => {
  const regex = /\.(jpg|jpeg|png)$/
  if (!regex.test(file?.file.name)) {
    window.$message.error('请上传.jpg,.jpeg,.png格式的文件')
    return false
  }
  return true
}

// 验证表单,调用接口
async function validateAndSave() {
  loading.value = true
  const errors = await new Promise((resolve) => {
    formRef.value?.validate((errors: any) => {
      resolve(errors)
    })
  })

  if (!errors) {
    const saveFunction
      = props.type === 'modify' && props.id ? updateBirthday : addBirthdayById
    const saveData = {
      ...formDataReactive,
    }
    try {
      const res = await saveFunction(saveData)
      if (res) {
        window.$message.success('保存成功')
        emits('saved')
      }
      loading.value = false
    }
    catch (error) {}
  }
}
// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
// 获取生日详情
function getDetail() {
  getBirthdayById(props.id).then((res) => {
    formDataReactive.id = res.id
    formDataReactive.blessing = res.blessing
    formDataReactive.imgUrl = res.imgUrl
    fileList.value = [
      {
        id: '1',
        name: '祝福图片',
        status: 'finished',
        url: res.imgUrl,
      },
    ]
  })
}

/**
 * 上传图片
 * @param {any} file:File
 */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formDataReactive.imgUrl === '' || formDataReactive.imgUrl === null) {
      const data = await uploadImg(imgFileData)
      if (data) {
        formDataReactive.imgUrl = data.url
      }
    }
  }
  catch (error) {}
}

/**
 * 删除图片
 */
function handleCoverDelete() {
  formDataReactive.imgUrl = null
}

onMounted(() => {
  if (props.type === 'modify' && props.id) {
    getDetail()
  }
})
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="auto"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item span="24" label="默认祝福语：" path="blessing">
      <n-input
        v-model:value="formDataReactive.blessing"
        :disabled="props.type === 'view'"
        placeholder="请输入祝福语"
        clearable
      />
    </n-form-item>
    <n-form-item span="24" label="图片：" path="imgUrl">
      <ImgUploader
        v-model:oldImgUrl="formDataReactive.imgUrl"
        :width="220"
        :height="150"
        :need-cropper="false"
        accept=".jpg,.jpeg,.png"
        :before-upload="beforeUpload"
        @done="handleCoverDone"
        @delete="handleCoverDelete"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped>
:deep(.n-upload-file-list .n-upload-file.n-upload-file--image-card-type) {
  width: 106px;
  height: 106px;
}

:deep(.n-upload-file-list .n-image) {
  width: 106px;
  height: 106px;
}
</style>
