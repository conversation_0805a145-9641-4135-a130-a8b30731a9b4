import type { DefineComponent } from 'vue'
import { defineComponent } from 'vue'

const modules = import.meta.globEager('./*.svg')

const iconComponents: Record<string, DefineComponent> = {} // 保存需要注册的组件

for (const path in modules) {
  const fileName = path.split('/').pop()?.split('.').shift()
  if (fileName) {
    // 根据SVG的文件名生成对应的Vue组件名
    const iconName = `icon-${fileName}`
    iconComponents[iconName] = defineComponent(
      Object.assign({ name: iconName }, modules[path]),
    )
  }
}

export default iconComponents
