<script setup lang="ts">
import LoginForm from './ctn/LoginForm.vue'

const APP_NAME = import.meta.env.VITE_APP_NAME
const APP_LOGO = import.meta.env.VITE_APP_LOGO
</script>
<template>
  <div>
    <div class="login">
      <div class="formCtn">
        <div class="left"></div>
        <div class="flex-1 flex flex-col items-center pt-[62px] ml-[65px]">
          <div class="flex gap-x-[10px]">
            <img class="h-[40px] mb-[33px] rounded-[50%]" :src="APP_LOGO" />
            <span
              class="text-[#051138] text-[20px] leading-[40px] font-[600]"
            >{{ APP_NAME }}</span>
          </div>
          <login-form />
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
// .form-bg {
//   background-image: url(@/assets/image/login-bg.png);
//   background-repeat: no-repeat;
//   background-position: center;
//   background-size: cover;
// }

.login {
  min-width: 1200px;
  min-height: 660px;
  height: 100vh;
  // background: url('@/assets/image/login/top.webp') left 26px top 0 / 273px 80px no-repeat,
  //   url('@/assets/image/login/right.webp') right 0 bottom 0 / 899px 707px no-repeat,
  //   #F1F5F8;
  background: #f8f1f1;

  display: flex;
  justify-content: center;
  align-items: center;

  .formCtn {
    width: 947px;
    height: 509px;
    transform: translateY(-6%);
    background: #ffffff;
    border-radius: 25px;
    padding: 0 100px 60px 66px;
    display: flex;

    .left {
      width: 397px;
      background: url('@/assets/image/login/left.png') top 0 left 0 / contain
        no-repeat;
    }
  }
}
</style>
