# security headers
add_header X-Frame-Options         "SAMEORIGIN" always;
add_header X-XSS-Protection        "1; mode=block" always;
add_header X-Content-Type-Options  "nosniff" always;
add_header Referrer-Policy         "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline' 'unsafe-eval'; connect-src *" always;

# . files
location ~ /\.(?!well-known) {
    deny all;
}
