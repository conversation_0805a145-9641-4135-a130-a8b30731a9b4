import type { DataTableColumns } from 'naive-ui'
import type { VNodeChild } from 'vue'

export function createColumns(
  operationRender: (row: any) => VNodeChild,
): DataTableColumns {
  return [
    {
      type: 'selection',
      fixed: 'left',
    },
    {
      title: '姓名',
      key: 'name',
    },
    {
      title: '手机号',
      key: 'phoneNum',
    },
    {
      title: '所在组织',
      key: 'organization',
    },
    {
      title: '职务',
      key: 'post',
    },
    {
      title: '月份',
      key: 'months',
    },
    {
      title: '应缴金额',
      key: 'payableNum',
    },
    {
      title: '审核状态',
      key: 'auditStatus',
    },
    {
      title: '操作',
      key: 'operation',
      render: row => operationRender(row),
    },
  ]
}
