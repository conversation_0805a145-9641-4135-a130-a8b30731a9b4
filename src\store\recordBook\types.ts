import type { CatalogType } from '@/services/recordBookMaintenance/types'

export interface Department {
  deptId: string
  deptName: string
}

export interface StateType {
  currentSelectedDept: Department
  currentSelectedYear: string
  currentSelectedMeetingType: string | null
  currentSelectedTransferType: string | null
  recordBookList: CatalogType[]
  currentSelectedChapter: { value: string; title: string }
  organizationListTreeList: Array<any>
}
