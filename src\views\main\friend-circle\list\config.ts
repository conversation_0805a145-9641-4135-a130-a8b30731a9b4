import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { CultureShowListItem } from '@/services/run/interaction/types'

export function getTableColumns(
  optionColumnRenderer: (row: CultureShowListItem) => VNodeChild,
  handleUpdateValueRender: (row: CultureShowListItem) => VNodeChild,
): DataTableColumns<CultureShowListItem> {
  return [
    {
      type: 'selection',
    },
    {
      key: 'index',
      title: '序号',
      width: '100',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'username',
      title: '姓名',
    },
    {
      key: 'orgName',
      title: '组织名称',
      render: row => (row.orgName ? row.orgName : '--'),
    },
    {
      key: 'content',
      title: '内容',
    },
    {
      key: 'fileList',
      title: '附件数量',
      render: row => (row.fileList.length ? row.fileList.length : '--'),
    },

    {
      key: 'publishTime',
      title: '发布时间',
    },
    {
      key: 'isRelease',
      title: '是否审核通过',
      render: row => handleUpdateValueRender(row),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
