import type { DataTableColumns } from 'naive-ui'
import type { VNodeChild } from 'vue'

// 通知类型字典
export const MESSAGETYPESTATUS = {
  // 通知类型状态（1-待审核 2-通过 3-未通过）
  WAIT: '1',
  PASS: '2',
  NOT: '3',
}

// 通知类型选项
export const MESSAGETYPEOPTIONS = [
  {
    label: '待审核',
    value: MESSAGETYPESTATUS.WAIT,
  },
  {
    label: '通过',
    value: MESSAGETYPESTATUS.PASS,
  },
  {
    label: '未通过',
    value: MESSAGETYPESTATUS.NOT,
  },
]

// 积分列表
export function getIntegralColumns(
  optionColumnRenderer: (row: any) => VNodeChild,
): DataTableColumns {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'orderCode',
      title: '姓名',
    },
    {
      key: 'userName',
      title: '手机号',
    },
    {
      key: 'userPhone',
      title: '人员类别',
    },
    {
      key: 'unitName',
      title: '所属党组织',
    },
    {
      key: 'unitName',
      title: '积分',
    },
    {
      key: 'returnTime',
      title: '更新时间',
      render: (row: any) => {
        if (row.returnTime) {
          return row.returnTime.slice(0, 7)
        } else {
          return ''
        }
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '300',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 积分明细列表
export function getIntegralDetailColumns(): DataTableColumns {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'orderCode',
      title: '积分变动',
    },
    {
      key: 'userName',
      title: '总积分',
    },
    {
      key: 'userPhone',
      title: '模块',
    },
    {
      key: 'unitName',
      title: '内容项',
    },
    {
      key: 'unitName',
      title: '操作',
    },
    {
      key: 'returnTime',
      title: '时间',
      render: (row: any) => {
        if (row.returnTime) {
          return row.returnTime.slice(0, 7)
        } else {
          return ''
        }
      },
    },
  ]
}
