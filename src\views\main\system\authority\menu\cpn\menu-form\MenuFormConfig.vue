<!--
 * @Description: 菜单编辑表单
-->
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="80"
    label-align="right"
    label-placement="left"
    :rules="formRules"
    :model="formDataReactive"
  />
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { onBeforeMount, reactive, ref } from 'vue'
import type { NForm } from 'naive-ui'
import { MENU_DICT } from '../../config'
import {
  getMenuDetail,
  postMenu,
  updateMenu,
} from '../../../../../../../services'
import { btnRules, directoryRules, getFormData, menuFormRules } from './config'

const props = defineProps({
  editType: { type: String as PropType<'add' | 'modify'>, required: true },
  id: { type: Number },
  parentId: { type: Number },
  parentList: { type: Array },
})
const emits = defineEmits(['saved'])

const formRef = ref<InstanceType<typeof NForm>>()
const formDataReactive = reactive(getFormData())
const oldImgUrlRef = ref('')

const formRules = computed(() => {
  if (formDataReactive.type === MENU_DICT.MENU) {
    // 菜单规则
    return menuFormRules
  } else if (formDataReactive.type === MENU_DICT.BTN) {
    // 按钮规则
    return btnRules
  } else {
    // 目录规则
    return directoryRules
  }
})

onBeforeMount(() => {
  const { editType, id } = props

  if (editType === 'modify' && id) {
    getMenuDetail(id).then((res) => {
      formDataReactive.type = String(res.type)
      formDataReactive.name = res.name
      formDataReactive.path = res.path
      formDataReactive.sortOrder = res.sortOrder
      formDataReactive.parentId = res.parentId
      formDataReactive.permission = res.permission || ''
      formDataReactive.filePath = res.filePath || ''
      formDataReactive.routeName = res.routeName || ''
      formDataReactive.visible = res.visible
      formDataReactive.redirect = res.redirect
      formDataReactive.icon = res.icon
      oldImgUrlRef.value = res.icon
    })
  } else {
    // 初始化父级菜单
    formDataReactive.parentId = Number(props.parentId)
  }
})

function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // 处理formData
      const formData = new FormData()
      // 如果当前是编辑，添加menuId字段
      if (props.editType === 'modify') {
        formData.append('menuId', String(props.id))
      }
      formData.append(
        'parentId',
        String(formDataReactive.parentId ?? props.parentId ?? -1),
      )
      formData.append('type', String(formDataReactive.type))
      formData.append('name', formDataReactive.name)
      formData.append('sortOrder', String(formDataReactive.sortOrder))

      // 菜单独有字段
      if (formDataReactive.type === MENU_DICT.MENU) {
        formData.append('visible', String(formDataReactive.visible))
        formData.append('icon', formDataReactive.icon!)
        formData.append('path', formDataReactive.path!)
        formData.append('permission', formDataReactive.permission!)
        formData.append('filePath', formDataReactive.filePath!)
        formData.append('routeName', formDataReactive.routeName!)
        formData.append('redirect', formDataReactive.redirect!)
      }

      // 目录独有字段
      if (formDataReactive.type === MENU_DICT.DIRECTORY) {
        formData.append('visible', String(formDataReactive.visible))
        formData.append('icon', formDataReactive.icon!)
        formData.append('path', formDataReactive.path!)
        formData.append('redirect', formDataReactive.redirect!)
      }

      // 按钮独有字段
      if (formDataReactive.type === MENU_DICT.BTN) {
        formData.append('permission', formDataReactive.permission!)
      }

      if (props.editType === 'modify') {
        // 编辑菜单
        updateMenu(formData).then((res) => {
          window.$message.success(res)
          emits('saved')
        })
      } else {
        // 新增菜单
        postMenu(formData).then((res) => {
          window.$message.success(res)
          emits('saved')
        })
      }
    }
  })
}

defineExpose({
  validateAndSave,
})
</script>
