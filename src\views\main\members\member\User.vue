<script lang="ts" setup>
import type { UploadFileInfo } from 'naive-ui'
import { NButton, NIcon, NSwitch } from 'naive-ui'
import { DocumentImport } from '@vicons/carbon'
import { IosArrowDropdown, IosArrowDropup } from '@vicons/ionicons4'
import { PlusRound } from '@vicons/material'
import { debounce } from 'lodash-es'
import { getTableColumns } from './config'
import NoticeForm from './cpn/NoticeForm.vue'
import HistoryForm from './cpn/HistoryForm.vue'
import { useDrawerEditOrganize, useMyTable } from '@/hooks'
import {
  useFetchEnumerationOptions,
  useOrganizationListOptionsNew,
} from '@/hooks/use-select-options'
import ifHasPermi from '@/directive/permission/ifHasPermi'
import {
  deleteUsers,
  getUserList,
  importUserFile,
  putDisabledUser,
  resetPwd,
  sortUserItem,
} from '@/services/system/User'
import DeleteButton from '@/components/DeleteButton.vue'
import type { UserItem } from '@/services/system/User/types'
import { USER_ENABLE_STATUS } from '@/store/dict'

const politicalType = ref(useFetchEnumerationOptions('political')) // 政治面貌

// 用户筛选条件接口
interface UserFilterParams {
  trueName: string
  deptId: string | null
  phone: string
  political: string | null
  lockFlag: string | null
}

const filterReactive = ref<UserFilterParams>({
  trueName: '',
  deptId: null,
  phone: '',
  political: null,
  lockFlag: null,
})
// 有接口后添加：loading,tableData
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleBatchDelete,
  handleSingleDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getUserList, filterReactive, {
  batchDeleteTable: false,
  delApi: deleteUsers,
})
const { organizationListTree } = useOrganizationListOptionsNew()
const handleUpdateValue = (v: string) => {
  filterReactive.value.deptId = v
}

// 新增/编辑党建清单抽屉
const partyIdentityRef = ref()
const idEditRef = ref()
const addNoticeFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEditOrganize({ name: '用户', confirmFn: handelConfirmEdit })

/** 添加根节点 */
const handleClickAddRootNode = () => {
  editTypeRef.value = 'add'
  showEditRef.value = true
}

/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}

watch(showEditRef, (newV) => {
  if (!newV) {
    addNoticeFormRef.value?.resetForm()
  }
})

/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

// 转至历史
const historyFormRef = ref()
const showHistoryRef = ref(false)

function handleHistorySaved() {
  showHistoryRef.value = false
  loadData()
}

function handleHistoryClickCancel() {
  window.$dialog.warning({
    title: '提示',
    content: '关闭后本次编辑内容不保存，是否继续？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      showHistoryRef.value = false
    },
  })
}

function handleHistoryClickConfirm() {
  historyFormRef.value?.validateAndSave()
}
const handleSortUserItem = debounce(
  (row, direction) => {
    sortUserItem({
      id: row.userId,
      direction,
      deptId: filterReactive.value.deptId!,
    })
      .then(() => {
        window.$message.success(direction === 'up' ? '上移成功' : '下移成功')
        loadData() // 你自己的数据刷新函数
      })
      .catch(() => {
        console.error(direction === 'up' ? '上移失败' : '下移失败')
        // window.$message.error(direction === 'up' ? '上移失败' : '下移失败')
      })
  },
  300,
  { leading: true, trailing: false }, // 立即触发，防止频繁点击
)

function isDisabledRow(
  row: UserItem,
  index: number,
  type: 'down' | 'up',
) {
  const isFirstItem = index === 0
  const isLastItem = index === tableData.value.length - 1
  const isFirstPage = currentPage.value === 1
  const isLastPage = pageSize.value * currentPage.value >= total.value

  // 如果是唯一一行数据，禁用所有操作
  if (tableData.value.length === 1) {
    return true
  }

  // 处理上移按钮的禁用逻辑
  if (type === 'up') {
    // 第一页的第一个元素禁用上移
    if (isFirstPage && isFirstItem) {
      return true
    }
    // 非第一页的第一个元素不禁用
    return false
  }

  // 处理下移按钮的禁用逻辑
  if (type === 'down') {
    // 最后一页的最后一个元素禁用下移
    if (isLastPage && isLastItem) {
      return true
    }
    // 非最后一页的最后一个元素不禁用
    return false
  }

  return false
}

const onMove = (row: UserItem, direction: string) => {
  handleSortUserItem(row, direction)
}
// 修改和删除按钮渲染
const tableColumns = getTableColumns(
  (row, index) => {
    const moveButtons = []

    // 如果 filterReactive.value.deptId 有值，才添加上下移按钮
    if (filterReactive.value.deptId) {
      moveButtons.push(
        h(
          NIcon,
          {
            size: 20,
            color: isDisabledRow(row, index, 'up') ? '#ccc' : '#AC241D',
            class: isDisabledRow(row, index, 'up')
              ? 'cursor-not-allowed'
              : 'cursor-pointer',
            onClick: () => {
              if (isDisabledRow(row, index, 'up')) {
                return
              }
              onMove(row, 'up')
            },
          },
          { default: () => h(IosArrowDropup) },
        ),
        h(
          NIcon,
          {
            size: 20,
            color: isDisabledRow(row, index, 'down') ? '#ccc' : '#AC241D',
            class: isDisabledRow(row, index, 'down')
              ? 'cursor-not-allowed'
              : 'cursor-pointer',
            onClick: () => {
              if (isDisabledRow(row, index, 'down')) {
                return
              }
              onMove(row, 'down')
            },
          },
          { default: () => h(IosArrowDropdown) },
        ),
      )
    }
    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
        },
      },
      [
        h(
          NButton,
          {
            onClick: () => {
              idEditRef.value = row.userId
              editTypeRef.value = 'modify'
              partyIdentityRef.value = row.partyIdentity
              showEditRef.value = true
            },
            type: 'primary',
            text: true,
            style: {
              display: ifHasPermi('portal_user_edit_btn'),
            },
          },
          { default: () => '编辑' },
        ),
        h(
          NButton,
          {
            onClick: () => {
              window.$dialog.warning({
                title: '提示',
                content: '确定重置为初始密码？',
                positiveText: '确定',
                negativeText: '取消',
                onPositiveClick: () => {
                  resetPwd(row.userId).then(() => {
                    window.$message.success('重置成功！')
                    loadData()
                  })
                },
              })
            },
            type: 'primary',
            text: true,
          },
          { default: () => '重置密码' },
        ),
        row.lockFlag === '9'
        && h(
          NButton,
          {
            onClick: () => {
              idEditRef.value = row.userId
              showHistoryRef.value = true
            },
            type: 'primary',
            text: true,
            style: {
              display: ifHasPermi('portal_user_edit_btn'),
            },
          },
          { default: () => '转至历史' },
        ),
        h(DeleteButton, {
          handleConfirm: () => handleSingleDelete(String(row.userId)),
        }),
        ...moveButtons, // 合并上移下移按钮
      ],
    )
  },

  (row) => {
    return h(
      NSwitch,
      {
        checkedValue: '0',
        uncheckedValue: '9',
        value: row.lockFlag,
        loading: row.isLoading,
        onUpdateValue(v: any) {
          if (row.lockFlag === '9') {
            row.isLoading = true
            putDisabledUser(row.userId)
              .then((_: any) => {
                row.lockFlag = row.lockFlag === '9' ? '0' : '9'
              })
              .catch(() => {})
              .finally(() => {
                loadData()
                row.isLoading = false
              })
          }
          else {
            window.$dialog.warning({
              title: '提示',
              content: '禁用后用户将无法登录平台，确认禁用？',
              positiveText: '确定',
              negativeText: '取消',
              onPositiveClick: () => {
                row.isLoading = true
                putDisabledUser(row.userId)
                  .then((_: any) => {
                    row.lockFlag = row.lockFlag === '9' ? '0' : '9'
                  })
                  .catch(() => {})
                  .finally(() => {
                    loadData()
                    row.isLoading = false
                  })
              },
            })
          }
        },
      },
      {
        checked: '启用',
        unchecked: '禁用',
      },
    )
  },
)

const showExam = ref(false)
const handelImportFn = () => {
  showExam.value = true
}
/** 导入指标 */
const handleClickImportExcel = (options: {
  file: UploadFileInfo | any
  fileList: Array<UploadFileInfo>
  event?: Event
}) => {
  const formData = new FormData()
  formData.append('file', options.file.file)
  importUserFile(formData)
    .then((res) => {
      window.$message.success('用户导入成功')
      loadData()
      showExam.value = false
    })
    .catch((_) => {})
}

/** 下载模板 */
const handleClickDownloadTemplate = () => {
  const url = '/static/UserTemplate.xls'
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', '用户导入模板.xls')
  link.click()
  showExam.value = false
}
onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    :show-delete="false"
    :show-toolbar="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    custom-toolbar
    default-expand-all
    title="用户管理"
    @click-add="handleClickAddRootNode"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #filters>
      <n-button
        v-hasPermi="['portal_user_add_btn']"
        size="small"
        type="primary"
        @click="handleClickAddRootNode"
      >
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <n-button size="small" type="info" @click="handelImportFn">
        <template #icon>
          <n-icon>
            <document-import />
          </n-icon>
        </template>
        导入
      </n-button>
    </template>
    <template #btns>
      <div
        class="w-full flex items-center justify-between gap-[10px]"
      >
        <n-tree-select
          v-model:value="filterReactive.deptId"
          :options="organizationListTree"
          :show-path="false"
          check-strategy="all"
          children-field="children"
          clearable
          filterable
          key-field="deptId"
          label-field="name"
          placeholder="请选择所属党组织"
          size="small"
          value-field="deptId"
          @update:value="handleUpdateValue"
        />

        <n-input
          v-model:value="filterReactive.trueName"
          clearable
          placeholder="请输入姓名"
          size="small"
        />
        <n-input
          v-model:value="filterReactive.phone"
          clearable
          placeholder="请输入手机号"
          size="small"
        />
        <n-select
          v-model:value="filterReactive.political"
          :options="politicalType.enumerationList"
          clearable
          filterable
          placeholder="请选择政治面貌"
        />
        <n-select
          v-model:value="filterReactive.lockFlag"
          :options="USER_ENABLE_STATUS"
          clearable
          filterable
          placeholder="请选择用户状态"
        />
        <!-- <n-tree-select
          v-model:value="filterReactive.deptId"
          class="w-full sm:w-[48%] md:w-[32%] lg:w-[23%] xl:w-[19%]"
          :options="organizationListTree"
          :show-path="false"
          check-strategy="all"
          children-field="children"
          clearable
          filterable
          key-field="deptId"
          label-field="name"
          placeholder="请选择用户类型"
          size="small"
          value-field="deptId"
          @update:value="handleUpdateValue"
        /> -->
      </div>
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :mask-closable="false" :width="700">
    <n-drawer-content :title="drawerTitle">
      <notice-form
        :id="idEditRef"
        ref="addNoticeFormRef"
        :party-identity="partyIdentityRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            style="width: 80px"
            type="primary"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
  <n-drawer v-model:show="showHistoryRef" :mask-closable="false" :width="700">
    <n-drawer-content closable title="转至历史">
      <history-form
        :id="idEditRef"
        ref="historyFormRef"
        @saved="handleHistorySaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            style="width: 80px"
            type="primary"
            @click="handleHistoryClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleHistoryClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
  <custom-dialog v-model:show="showExam" :show-action="false" title="导入">
    <div class="flex justify-center items-center h-[100px]">
      <div class="flex gap-[30px]">
        <n-button size="small" @click="handleClickDownloadTemplate">
          <template #icon>
            <n-icon>
              <document-import />
            </n-icon>
          </template>
          下载模板
        </n-button>
        <n-upload
          :show-file-list="false"
          accept=".xls,.xlsx"
          @change="handleClickImportExcel"
        >
          <n-button size="small">
            <template #icon>
              <n-icon>
                <document-import />
              </n-icon>
            </template>
            导入用户
          </n-button>
        </n-upload>
      </div>
    </div>
  </custom-dialog>
</template>
<style lang="scss" scoped></style>
