<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="近期活动"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :show-delete="false"
    :checked-row-keys="checkedRowKeys"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button>
    </template>
    <template #filters>
      <n-input
        v-model:value="filterReactive.title"
        size="small"
        placeholder="请输入搜索内容"
        clearable
      />
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <AddCompanyShowForm
        :id="idEditRef"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { NButton, NSwitch } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import { getTableColumns } from './config'
import AddCompanyShowForm from './cpn/AddCompanyShowForm.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import {
  delPartyBuildingIndicator,
  getPartyBuildingExamIndIcatorsList,
} from '@/services/affairs/discipline-inspection-list/exam-indicators'
const filterReactive = ref<{ title: string }>({
  title: '',
})
// 有接口后添加：loading,tableData
const {
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getPartyBuildingExamIndIcatorsList, filterReactive, {
  batchDeleteTable: true,
  delApi: delPartyBuildingIndicator,
})

watch(filterReactive.value, (newVal) => {
  loadData()
})

// 新增/编辑党建清单抽屉
const idEditRef = ref()
const addNoticeFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('公司展示', handelConfirmEdit)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addNoticeFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns(
  (row) => {
    return [
      h(
        NButton,
        {
          onClick: () => {
            idEditRef.value = row.id
            editTypeRef.value = 'modify'
            showEditRef.value = true
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '预览',
        },
      ),
      h(
        NButton,
        {
          onClick: () => {
            idEditRef.value = row.id
            editTypeRef.value = 'modify'
            showEditRef.value = true
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '编辑',
        },
      ),
      h(DeleteButton, {
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    ]
  },
  (row) => {
    return h(NSwitch, {
      checkedValue: true,
      uncheckedValue: false,
      value: row.locked,
      loading: row.loading,
      onUpdateValue(v) {
        row.loading = true
        setTimeout(() => {
          row.locked = v
          row.loading = false
        }, 2000)
      },
    })
  },
)

onMounted(loadData)
</script>

<style lang="scss" scoped></style>
