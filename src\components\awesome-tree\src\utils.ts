/*
 * @Description: 树节点转换
 * @Author: 朱备 <zhu<PERSON>@easestrategy.com>
 * @Date: 2021-11-18 17:59:11
 * @LastEditTime: 2021-12-17 17:35:29
 * @LastEditors: 朱备 <zhu<PERSON>@easestrategy.com>
 */

import type { TreeOption } from 'naive-ui'
import type { ITreeNode } from '..'

/**
 * 为节点设置level，并转换成naive-ui的树结构
 */
export function transformToTreeOptions(
  treeData: ITreeNode[] | null,
  level: number,
  leafOnly: boolean,
  maxLevel: number | string,
): TreeOption[] {
  // eslint-disable-next-line eqeqeq
  if (!treeData || treeData.length == 0) {
    return []
  }
  return treeData.map((item, index) => {
    const node: TreeOption = {
      label: item.nodeName,
      key: item.nodeId,
      editing: false,
      pid: item.pid,
      level,
      canUp: index > 0,
      canDown: index < treeData.length - 1,
      // children: [],
    }

    if (leafOnly && level < (maxLevel as number)) {
      node.disabled = true
    }

    if (item.children && item.children.length) {
      if (maxLevel === 'infinite' && leafOnly) {
        node.disabled = true
      }
      node.children = transformToTreeOptions(
        item.children,
        level + 1,
        leafOnly,
        maxLevel,
      )
    }
    return node
  })
}
