import { createRouter, createWebHistory } from 'vue-router'
import useTabsStore from '@/store/tabs/tabs'
import { useMenuAuthStore } from '@/store/menuAuth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: { name: 'login' },
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/login/Login.vue'),
    },
    // {
    //   path: '/newsAdd',
    //   name: 'newsAdd',
    //   component: () => import('@/views/main/news/cpn/AddNewsItemForm.vue'),
    // },
    // {
    //   path: '/specialAdd',
    //   name: 'specialAdd',
    //   component: () => import('@/views/main/special/cpn/AddSpecialItemForm.vue'),
    // },
  ],
})

router.beforeEach((to, from, next) => {
  const token = window.sessionStorage.getItem('access_token')
  if (token) {
    if (to.path === '/login') {
      next({ path: '/main' })
      const tabsStore = useTabsStore()
      if (!tabsStore.tabsOpened.length) {
        tabsStore.addTab({
          fullPath: '/main/workbench',
          title: '工作台',
          icon: 'workbench',
          routeName: 'workbench',
        })
      }

      const parentRoute = to.matched[1]
      tabsStore.addTab({
        fullPath: to.fullPath,
        title: parentRoute.meta.title as string,
        icon: parentRoute.meta.icon as string,
        routeName: parentRoute.name as string,
      })
    }
    else {
      const store = useMenuAuthStore()
      store
        .getDynamicRoute()
        .then((res) => {
          if (res === '1') {
            next({ ...to, replace: true })
          }
          else {
            next()
          }
          const tabsStore = useTabsStore()
          if (!tabsStore.tabsOpened.length) {
            tabsStore.addTab({
              fullPath: '/main/workbench',
              title: '工作台',
              icon: 'workbench',
              routeName: 'workbench',
            })
          }

          const parentRoute = to.matched[1]
          tabsStore.addTab({
            fullPath: to.fullPath,
            title: parentRoute.meta.title as string,
            icon: parentRoute.meta.icon as string,
            routeName: parentRoute.name as string,
          })
        })
        .catch(() => {
          // next()
        })
    }
  }
  else {
    if (to.path !== '/login') {
      next({ path: '/login' })
    }
    else {
      next()
    }
  }

  window.$loadingBar.start()
})

router.afterEach(() => {
  window.$loadingBar.finish()
})
export default router
