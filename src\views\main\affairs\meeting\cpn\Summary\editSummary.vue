<template>
  <div>
    <DetailHeader
      back-name="meetingManage"
      :back-query="route.query"
      header-title="编辑会议内容"
      right-btn-text="保存"
      :is-show-confirm-btn="true"
      :release="publishConfirm"
    />

    <div class="pt-[20px]">
      <n-form
        :model="formData.data"
        label-placement="left"
        label-width="120px"
        :rules="FormRules"
      >
        <n-form-item label="正文：" path="content">
          <RichEditor v-model:value="formData.data.content" />
        </n-form-item>
      </n-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getMeetingSummaryDetail,
  putMeetingSummary,
} from '@/services/affairs/party-meeting'
const route = useRoute()
const router = useRouter()

const meetingId = computed(() => route.query.id as string)

const FormRules = ref({
  content: [
    {
      required: true,
      message: '请填写正文',
      trigger: 'blur',
    },
  ],
})

const formData = ref({
  data: {
    content: '',
  },
})

async function publishConfirm() {
  try {
    if (!formData.value.data.content) {
      window.$message.error('会议内容不能为空')
      return
    }
    const data = {
      meetingId: meetingId.value,
      fileIds: [],
      content: formData.value.data.content,
    }
    await putMeetingSummary(data as any)
    window.$message.success('编辑成功')
    router.replace({
      name: 'meetingDetail',
      params: route.query as any,
    })
  }
  catch (error) {
    console.warn(error)
  }
}

function loadData() {
  getMeetingSummaryDetail(meetingId.value).then((res) => {
    formData.value.data.content = res.content
  })
}

loadData()
</script>

<style scoped lang="scss"></style>
