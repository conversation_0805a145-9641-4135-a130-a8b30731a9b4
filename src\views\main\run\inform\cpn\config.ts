import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  title: [
    {
      required: true,
      message: '通知标题不能为空',
      trigger: 'input',
    },
  ],
  type: [
    {
      required: true,
      message: '通知类型不能为空',
      trigger: 'input',
    },
  ],
  content: [
    {
      required: true,
      message: '通知内容不能为空',
      trigger: 'input',
    },
  ],
  userIdList: [
    {
      required: true,
      message: '接收人员不能为空',
      trigger: 'change',
      validator(rule: any, value: any) {
        if (!value || !value.length) {
          return new Error('接收人员不能为空')
        } else {
          return true
        }
      },
    },
  ],
}

/** 轮播图尺寸 */
export const imgSizes = [
  {
    id: 1, // 首页顶部
    width: 708,
    height: 254,
  },
  {
    id: 2, // 首页中部
    width: 708,
    height: 180,
  },
  {
    id: 3, // 首页底部
    width: 708,
    height: 254,
  },
  {
    id: 4, // 学习页顶部
    width: 708,
    height: 254,
  },
  {
    id: 5, // 学习页中部
    width: 708,
    height: 180,
  },
  {
    id: 6, // 学习页底部
    width: 708,
    height: 254,
  },
  {
    id: 7, // 首页应用
    width: 708,
    height: 254,
  },
]
