import type { VNodeChild } from 'vue'
import { type DataTableColumns, NProgress } from 'naive-ui'
import type { FormRules } from 'naive-ui'
import type {
  ReviewTableItem,
  TalkAboutTableItem,
} from '@/services/publicity/vote/talkAbout/type'
import type { BranchRatingListType } from '@/services/publicity/vote/branchRating/type'
import { REVIEWSTATUSNAME } from '@/constant'

/** 明主评议表格 */
export function columnsOfTalkAbout(
  optionColumnRenderer: (row: TalkAboutTableItem) => VNodeChild,
): DataTableColumns<TalkAboutTableItem> {
  return [
    {
      type: 'selection',
    },
    {
      key: 'index',
      title: '序号',
      width: '50',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'reviewTitle',
      title: '评议名称',
    },
    {
      key: 'reviewYear',
      title: '评议年度',
    },
    {
      key: 'reviewStartTime',
      title: '开始时间',
    },
    {
      key: 'reviewEndTime',
      title: '结束时间',
    },
    {
      key: 'reviewStatus',
      title: '状态',
      render: (row) => {
        return h('span', null, {
          default: () => {
            if (row.reviewStatus === null || row.reviewStatus === undefined) {
              return ''
            }
            return REVIEWSTATUSNAME[
              row.reviewStatus as keyof typeof REVIEWSTATUSNAME
            ]
          },
        })
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}

// 支部星级评定
export function columnsOfBranchRating(
  optionColumnRenderer: (row: BranchRatingListType) => VNodeChild,
): DataTableColumns<BranchRatingListType> {
  return [
    // {
    //   type: 'selection',
    // },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'name',
      title: '评定方案名称',
    },
    {
      key: 'year',
      title: '评定年度',
    },
    {
      key: 'startTime',
      title: '自评开始时间',
    },
    {
      key: 'endTime',
      title: '自评结束时间',
    },
    {
      key: 'declaration',
      title: '申报进度',
      render: (row) => {
        return h('span', {
          style: {
            color:
              row.selfEvaluationNum === row.partyBrandNum
                ? '#00c969'
                : '#AC241D',
          },
          innerHTML: `${row.selfEvaluationNum}/${row.partyBrandNum}`,
        })
      },
    },
    {
      key: 'reEvaluation',
      title: '复评进度',
      width: 200,
      render: (row) => {
        return h(
          NProgress,
          {
            type: 'line',
            percentage: row.reEvaluation,
            color: row.reviewEvaluationNum === row.partyBrandNum ? 'green' : '',
          },
          {
            default: () => {
              if (row.partyBrandNum === 0) {
                return '0%'
              } else {
                return `${(row.reviewEvaluationNum / row.partyBrandNum).toFixed(
                  1,
                )}%`
              }
            },
          },
        )
      },
    },
    {
      key: 'status',
      title: '状态',
      render: (row) => {
        return h('span', null, {
          default: () => {
            if (row.status === null || row.status === undefined) {
              return ''
            }
            const arr = ['未开始', '自评中', '复评中', '已完成']
            return arr[Number(row.status)]
          },
        })
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}

/** 明主评议项党员表格 */
export function columnsOfReviewItems(
  optionColumnRenderer: (row: ReviewTableItem) => VNodeChild,
): DataTableColumns<ReviewTableItem> {
  return [
    {
      type: 'selection',
    },
    {
      key: 'index',
      title: '序号',
      width: '50',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'reviewItemTitle',
      title: '评议项名称',
    },
    {
      key: 'reviewItemRemark',
      title: '评议项说明',
    },
    {
      key: 'reviewItemPromoterName',
      title: '发起人',
    },
    {
      key: 'orgName',
      title: '所属组织',
    },
    {
      key: 'updateTime',
      title: '更新时间',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}

export const reviewItemsPersonDetailRules: FormRules = {
  reviewItemTitle: [
    {
      required: true,
      message: '评议项名称不能为空',
      trigger: 'input',
    },
  ],
  reviewItemRemark: [
    {
      required: true,
      message: '评议项简介不能为空',
      trigger: 'input',
    },
  ],
}
