<script lang="ts" setup>
import { NSwitch } from 'naive-ui'
import {
  DeleteForeverRound,
  PlusRound,
  ViewListOutlined,
} from '@vicons/material'
import { createColumns } from './config'
import addPublicityForm from './addPublicityForm.vue'
import addPublicityCategoryForm from './addPublicityCategoryForm.vue'
import { checkFormRules } from './ruleConfig'
import SideMenuNew from '@/components/SideMenuNew.vue'
import LayoutContainer from '@/components/LayoutContainer.vue'
import { judgePermission } from '@/directive/permission/ifHasPermi'
import {
  addPublicity,
  addPublicityCategory,
  delNews,
  delPublicity,
  delPublicityCategory,
  getNewsShowSetting,
  getPublicityList,
  getShowSettingStyleList,
  joinRotationPool,
  modifyPublicity,
  modifyPublicityCategory,
  movePublicity,
  movePublicityCategory,
  saveNewsShowSetting,
} from '@/services'
import DeleteButton from '@/components/DeleteButton.vue'
import { useMyTable, useTreeMenu } from '@/hooks'
import {
  checkNews,
  getNewsListForPublicity,
  hiddenNews,
  topNews,
} from '@/services/cloud-push/publicity'
import { useAuthStore } from '@/store/auth/auth'
import style1 from '@/assets/image/style1.png'
import style2 from '@/assets/image/style2.png'
import type { StyleItem } from '@/services/cloud-push/publicity/types'

const store = useAuthStore()
const addPublicityFormRef = ref()
const addPublicityCategoryFormRef = ref()
const currentPionnerId = ref('')
const filterRef = ref({ title: '', categoryId: '' })
const defaultSelectedKeys = ref<string[]>([])
const showSettingsDialog = ref<Boolean>(false) // 是否展示设置
const selectedShowModel = ref('propaganda_pionner_01') // 当前展示的设置类型, 默认展示样式一
const showModelOptions = ref<any[]>([]) // 显示类型枚举值
const currentSelectionStyleData = ref() // 当前选中的样式数据
const settingDialogLoading = ref(false)

const { treeData, showModalType, init, moveNode, delNode, saveNode }
  = useTreeMenu({
    menuListApi: getPublicityList,
    moveNodeApi: movePublicity,
    moveChildNodeApi: movePublicityCategory,
    delNodeApi: delPublicity,
    delChildNodeApi: delPublicityCategory,
    addNodeApi: addPublicity,
    modifyNodeApi: modifyPublicity,
    addChildNodeApi: addPublicityCategory,
    modifyChildNodeApi: modifyPublicityCategory,
    refreshTableApi: filterInput,
    multiLevelKey: 'pionnerCategoryVOList',
    labelField: 'pioneerName',
    childLabelField: 'name',
    maxLevel: 2,
    sessionId: 'propagandaId',
    sessionName: 'propagandaTitle',
  })

const {
  loading,
  tableData,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getNewsListForPublicity, filterRef, {
  batchDeleteTable: true,
  delApi: delNews,
})

/** 表格标题 */
const tableTitle = ref('')
const showModal = ref(false)
const modalTitle = ref()
const checkFormData = reactive({ data: { reviewed: 0 } })
const checkFormRef = ref()
const currentCheckId = ref()

function filterInput(res: { id: string; name: string }) {
  defaultSelectedKeys.value = [res.id]
  filterRef.value.categoryId = res.id as string
  tableTitle.value = res.name
}

function switchTop(value: boolean, id: string) {
  topNews(id).then(() => {
    window.$message.success('修改成功')
    loadData()
  })
}

// function switchRecommend(value: boolean, id: string) {
//   recommendNews(id).then(() => {
//     window.$message.success('修改成功')
//     loadData()
//   })
// }

function switchHidden(value: boolean, id: string) {
  hiddenNews(id).then(() => {
    window.$message.success('修改成功')
    loadData()
  })
}

// 是否加入首页轮播图
function switchRotationPool(value: boolean, id: string) {
  joinRotationPool(id).then(() => {
    window.$message.success('修改成功')
    loadData()
  })
}

// 暂定只有账号为admin的，才展示添加
function judgeAdmin() {
  const name = store.wholeUserInfo.username
  return name === 'admin'
}

judgeAdmin()
const router = useRouter()
const columns = createColumns(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
        },
        class: 'flex fel-row gap-x-[15px]',
      },
      [
        h(
          'span',
          {
            onClick: () =>
              router.push({
                name: 'publicityAdd',
                query: { id: row.id, model: 'view' },
              }),
          },
          { default: () => '查看' },
        ),
        h(
          'span',
          {
            onClick: () =>
              router.push({
                name: 'publicityAdd',
                query: { id: row.id, model: 'edit' },
              }),
          },
          { default: () => '编辑' },
        ),
        h(DeleteButton, {
          handleConfirm: () => handleSingleDelete(String(row.id)),
        }),
        // 判断是否有审核权限
        judgePermission('publicity_check_btn')
          ? h(
            'span',
            {
              onClick: () => {
                currentCheckId.value = row.id
                checkFormData.data.reviewed = row.reviewed
                showModal.value = true
                modalTitle.value = '先锋号管理审核'
                showModalType.value = 'check'
              },
            },
            { default: () => '审核' },
          )
          : h('span'),
      ],
    )
  },
  // 是否置顶
  row =>
    h(NSwitch, {
      disabled: row.reviewedStatus !== 1,
      onUpdateValue: (value: any) => switchTop(value, row.id),
      value: Boolean(Number(row.isTop)),
    }),
  // 是否推荐
  // row =>
  //   h(NSwitch, {
  //     disabled: row.reviewedStatus !== 1,
  //     onUpdateValue: (value: any) => switchRecommend(value, row.id),
  //     value: Boolean(Number(row.isRecommand)),
  //   }),
  // 是否隐藏
  row =>
    h(NSwitch, {
      disabled: row.reviewedStatus !== 1,
      onUpdateValue: (value: any) => switchHidden(value, row.id),
      value: Boolean(Number(row.isHidden)),
    }),
  // 是否加入首页轮播池
  row =>
    h(NSwitch, {
      disabled: row.reviewedStatus !== 1,
      onUpdateValue: (value: any) => switchRotationPool(value, row.id),
      value: Boolean(Number(row.sliderFlag || 0)),
    }),
  // 将权限状态传递给createColumns函数
  judgePermission('publicity_check_btn'),
)

/** 添加资讯 */
const addNewsItem = () => {
  router.push({
    name: 'publicityAdd',
    query: { categoryId: filterRef.value.categoryId, model: 'add' },
  })
}

// 处理弹框需要保存的数据及校验弹框必填项
async function handleFormatterParams() {
  if (showModalType.value === 'check') {
    checkFormRef.value?.validate((errors: any) => {
      if (!errors) {
        checkNews(
          currentCheckId.value,
          String(checkFormData.data.reviewed),
        ).then(() => {
          window.$message.success('审核成功')
          loadData()
        })
      }
    })
  }
  else {
    let flag = false
    let pionnerId
    if (showModalType.value === 'root') {
      flag = await addPublicityFormRef.value.handleValidate()
    }
    if (showModalType.value === 'sub') {
      pionnerId = currentPionnerId.value
      flag = await addPublicityCategoryFormRef.value.handleValidate()
    }
    if (!flag) {
      return
    }
    const data
      = showModalType.value === 'root'
        ? addPublicityFormRef.value.formData.data
        : addPublicityCategoryFormRef.value.formData.data
    saveNode({ ...data, type: showModalType.value, pionnerId })
  }
  showModal.value = false
}

async function handleAddChildNode(data: any) {
  // console.log('🚀 ~ handleAddChildNode ~ data:', data)
  if (data.type === 'root') {
    modalTitle.value = '新增先锋号'
    showModalType.value = 'root'
  }
  else {
    modalTitle.value = '新增先锋号资讯类别'
    showModalType.value = 'sub'
    currentPionnerId.value = data.originData.id
    // 先锋号名称回显
    setTimeout(() => {
      addPublicityCategoryFormRef.value.handleSetName(data.label)
    }, 400)
  }
  showModal.value = true
  if (data.model === 'modify') {
    // 编辑状态 需要将数据回显
    if (data.isChild) {
      modalTitle.value = '修改先锋号资讯类别'
      currentPionnerId.value = data.originData.id
      setTimeout(() => {
        addPublicityCategoryFormRef.value.handleSetFormData(data.originData)
        addPublicityCategoryFormRef.value.handleSetName(data.parentName)
      }, 400)
    }
    else {
      modalTitle.value = '修改先锋号'
      setTimeout(() => {
        addPublicityFormRef.value.handleSetFormData(data.originData)
      }, 400)
    }
  }
}

function handleCancel() {
  showModal.value = false
  // addPublicityFormRef.value.handleReset()
  // addPublicityCategoryFormRef.value.handleReset()
}

// 选中菜单触发的事件
function handleChangeTab(data: any) {
  if (data.isChild) {
    tableTitle.value = data.label
    // 根据菜单id去查询详情
    filterRef.value.title = ''
    filterRef.value.categoryId = data.originData.id
    currentPage.value = 1
    window.sessionStorage.setItem('propagandaId', data.originData.id)
    window.sessionStorage.setItem('propagandaTitle', data.label)
  }
}

// 展示设置
function handleDisplaySettings() {
  showSettingsDialog.value = true
  getSettingInfo()
}

// 展示设置-确认
function handleSettingsConfirm() {
  saveNewsShowSetting({
    styleId: currentSelectionStyleData.value.styleId,
    fieldIdentify: 'propaganda_pionner',
  }).then(() => {
    window.$message.success('保存成功')
  })
  showSettingsDialog.value = false
}

// 展示设置-取消
function handleSettingsCancel() {
  showSettingsDialog.value = false
}

// 获取展示设置
function getSettingInfo() {
  settingDialogLoading.value = true
  loadSettingData()
}

function loadSettingData() {
  Promise.all([
    getShowSettingStyleList('propaganda_pionner'),
    getNewsShowSetting('propaganda_pionner'),
  ])
    .then((resArr) => {
      showModelOptions.value = resArr[0]?.map((item: StyleItem) => {
        return {
          id: item.id,
          label: item.title,
          value: item.code,
        }
      })

      if (resArr[1] && resArr[1].length) {
        selectedShowModel.value = resArr[1][0].code
        currentSelectionStyleData.value = resArr[1][0]
      }
      else {
        selectedShowModel.value = 'propaganda_pionner_01'
      }
    })
    .finally(() => {
      settingDialogLoading.value = false
    })
}

// 选择样式触发执行的方法
function handleSelectedStyleData(value: string) {
  currentSelectionStyleData.value = showModelOptions.value.find(
    (item: any) => item.value === value,
  )
  currentSelectionStyleData.value.styleId = currentSelectionStyleData.value.id
}

onMounted(() => {
  init().then((res: any) => {
    defaultSelectedKeys.value = [res.id]
    filterRef.value.categoryId = res.id as string
    tableTitle.value = res.name
    // loadData()
  })
  loadSettingData()
})

onActivated(loadSettingData)
</script>
<template>
  <layout-container style="height: calc(100vh - 114px)">
    <template #side>
      <SideMenuNew
        :default-selected-keys="defaultSelectedKeys"
        :show-root-btn="judgeAdmin()"
        :tree-data="treeData"
        title="先锋号管理"
        @move="moveNode"
        @del-node="delNode"
        @save-tree-node="handleFormatterParams"
        @add-child-node="handleAddChildNode"
        @select-node-key="handleChangeTab"
      />
    </template>
    <template #main>
      <div class="pl-[20px] pt-[25px] pr-[30px]">
        <p class="font-semibold text-[14px]">
          先锋号管理 - {{ tableTitle || '全部资讯' }}
        </p>
        <table-container
          v-model:page="currentPage"
          v-model:page-size="pageSize"
          :checked-row-keys="checkedRowKeys"
          :loading="loading"
          :show-add="judgePermission('publicity_add_btn')"
          :show-delete="judgePermission('publicity_delete_btn')"
          :table-columns="columns"
          :table-data="tableData"
          :total="total"
          style="padding: 0"
          @click-add="addNewsItem"
          @click-delete="handleBatchDelete"
          @update-page="onUpdatePage"
          @update-page-size="onUpdatePageSize"
          @update-checked-row-keys="onUpdateCheckedRowKeys"
        >
          <template #btns>
            <div class="flex flex-row justify-start items-center">
              <n-button
                v-hasPermi="['publicity_add_btn']"
                type="primary"
                @click="addNewsItem"
              >
                <n-icon size="16">
                  <plus-round />
                </n-icon>
                新增
              </n-button>
              <n-button
                v-hasPermi="['publicity_delete_btn']"
                @click="handleBatchDelete"
              >
                <template #icon>
                  <n-icon size="18">
                    <delete-forever-round />
                  </n-icon>
                </template>
                删除
              </n-button>
              <n-button @click="handleDisplaySettings">
                <template #icon>
                  <n-icon size="18">
                    <view-list-outlined />
                  </n-icon>
                </template>
                展示设置
              </n-button>
            </div>
          </template>
          <template #filters>
            <n-input
              v-model:value="filterRef.title"
              placeholder="请输入资讯标题"
            />
          </template>
        </table-container>
      </div>
    </template>
  </layout-container>
  <CustomDialog
    :show="showModal"
    :title="modalTitle"
    width="600px"
    @cancel="handleCancel"
    @confirm="handleFormatterParams"
    @update:show="(v: boolean) => (showModal = v)"
  >
    <div class="p-[20px]">
      <addPublicityForm
        v-show="showModalType === 'root'"
        ref="addPublicityFormRef"
      />
      <addPublicityCategoryForm
        v-show="showModalType === 'sub'"
        ref="addPublicityCategoryFormRef"
      />
      <template v-if="showModalType === 'check'">
        <n-form
          ref="checkFormRef"
          :model="checkFormData.data"
          :rules="checkFormRules"
          label-placement="left"
          label-width="160px"
        >
          <n-form-item label="是否通过审核：" path="reviewed">
            <n-switch
              v-model:value="checkFormData.data.reviewed"
              :checked-value="1"
              :unchecked-value="0"
            />
          </n-form-item>
        </n-form>
      </template>
    </div>
  </CustomDialog>
  <CustomDialog
    :loading="settingDialogLoading"
    :show="showSettingsDialog"
    title="展示设置"
    width="500px"
    @cancel="handleSettingsCancel"
    @confirm="handleSettingsConfirm"
    @update:show="(v: boolean) => (showSettingsDialog = v)"
  >
    <div class="p-[20px] flex flex-col justify-start items-start gap-y-[20px]">
      <div class="flex flex-row justify-start items-center w-[100%]">
        <span class="inline-block w-[60px]">样式：</span>
        <n-select
          v-model:value="selectedShowModel"
          :options="showModelOptions"
          @update:value="handleSelectedStyleData"
        />
      </div>
      <div class="flex flex-row justify-start items-start gap-x-[20px]">
        <span>样式示例图：</span>
        <div>
          <n-image
            v-show="selectedShowModel === 'propaganda_pionner_01'"
            :show-toolbar="false"
            :src="style1"
            alt=""
            class="w-[150px]"
          />
          <n-image
            v-show="selectedShowModel === 'propaganda_pionner_02'"
            :show-toolbar="false"
            :src="style2"
            alt=""
            class="w-[150px]"
          />
        </div>
      </div>
    </div>
  </CustomDialog>
</template>
<style lang="scss" scoped></style>
