<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    loadFn: (pageNum: any, PageSize: any) => void
    total: number
    param?: any
  }>(),
  {
    total: 0,
  },
)
const pageNum = ref(1)
const pageSize = ref(20)
// const changeFn = () => props.changeFn({ pageSize: pageSize.value, pageNum: pageNum.value })
const loadData = () => props.loadFn(pageNum.value, pageSize.value)
const jump2First = () => {
  // changeFn()
  pageNum.value === 1 ? loadData() : (pageNum.value = 1)
}
watch(pageNum, () => {
  // changeFn()
  loadData()
})
watch(pageSize, () => {
  jump2First()
})
// watch(param,()=>{
//   loadData()
// })
onMounted(() => {
  loadData()
})
</script>
<template>
  <div class="flex items-center justify-between mt-40px pr-2px">
    <span class="text-[#BDBDBD] text-12px mr-30px">共
      <span class="text-[#262626] mx-6px">{{ total }}</span>
      条</span>
    <n-pagination
      v-model:page="pageNum"
      v-model:page-size="pageSize"
      :page-sizes="[5, 10, 20, 30, 50]"
      show-quick-jumper
      show-size-picker
      :item-count="total"
    />
  </div>
</template>

<style scoped lang="scss"></style>
