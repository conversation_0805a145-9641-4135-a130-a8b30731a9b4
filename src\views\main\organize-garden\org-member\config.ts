import type { DataTableColumns } from 'naive-ui'
import type { OrganizeMemberListItem } from '@/services/organize-garden/types'

export function getTableColumns(): DataTableColumns<OrganizeMemberListItem> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'name',
      title: '姓名',
    },
    {
      key: 'partyPosition',
      title: '职务',
    },
    {
      key: 'partyAge',
      title: '党龄',
    },
    {
      key: 'joinPartyTime',
      title: '入党时间',
    },
  ]
}
