<script setup lang="ts">
import type { MenuOption } from 'naive-ui'
import type { RouteRecordRaw } from 'vue-router'
import { RouterLink } from 'vue-router'

interface Props {
  menuRoute: RouteRecordRaw
  hasChildren?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  hasChildren: false,
})
const route = useRoute()
const currentRouteName = computed(() => {
  if (props.hasChildren) {
    return (route.matched[2]?.name ?? '') as string
  } else {
    return (route.name ?? '') as string
  }
})
const menuOptions: MenuOption[] = props.menuRoute.children!.map(item => ({
  label: () =>
    h(
      RouterLink,
      {
        to: { name: item.name },
        style: {
          'font-size': '12px',
        },
      },
      { default: () => item.meta?.title },
    ),
  key: item.name! as string,
  icon: () => h(resolveComponent(`icon-${item.meta?.icon}` as string)),
}))
</script>
<template>
  <div>
    <div
      class="text-[14px] font-[500] text-[#333] pt-[1px] pl-[11px] mb-[28px]"
    >
      {{ menuRoute.meta?.title }}
    </div>
    <n-menu :value="currentRouteName" :options="menuOptions" />
  </div>
</template>
<style lang="scss" scoped>
:deep(.n-menu-item-content) {
  padding-left: 13px !important;
}

:deep(.n-menu .n-menu-item) {
  margin-top: 5px;
  margin-bottom: 15px;

  &::before {
    left: 0;
    right: 0;
  }
}
</style>
