<template>
  <div>
    <n-form
      ref="publicityCategoryRef"
      label-placement="left"
      label-width="90px"
      :model="formData.data"
      :rules="publicityCategoryFormRules"
    >
      <n-form-item label="先锋号名称" path="pionnerName">
        <n-input v-model:value="formData.data.pionnerName" disabled />
      </n-form-item>
      <n-form-item label="分类名称" path="name">
        <n-input v-model:value="formData.data.name" clearable show-count :maxlength="50" />
      </n-form-item>
      <!-- <n-form-item label="学习可得的学分" path="configStudyScore">
        <n-input-number
          v-model:value="formData.data.configStudyScore"
          style="width: 100%"
          clearable
        />
      </n-form-item> -->
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { publicityCategoryFormRules } from './ruleConfig'
const publicityCategoryRef = ref()
const formData = reactive({
  data: {
    pionnerName: '',
    name: '',
    // configStudyScore: '',
  },
})

function handleValidate() {
  return new Promise((resolve, reject) => {
    publicityCategoryRef.value?.validate((errors: any) => {
      if (!errors) {
        resolve(true)
      }
      else {
        resolve(false)
      }
    })
  })
}

function handleSetFormData(data: any) {
  formData.data = cloneDeep(data)
}
// function handleReset() {
//   console.log('handleReset')
//   publicityCategoryRef.value?.reset()
// }

// 自动带出先锋号名称
function handleSetName(data: any) {
  formData.data.pionnerName = data
}

defineExpose({
  formData,
  handleValidate,
  handleSetName,
  handleSetFormData,
  // handleReset,
})
</script>

<style scoped lang="scss"></style>
