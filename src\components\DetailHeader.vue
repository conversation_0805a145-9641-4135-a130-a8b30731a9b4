<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { ArrowBackIosNewRound } from '@vicons/material'
import { useRichStore } from '@/store/components/richText'
import PreviewModal from '@/components/PreviewModal.vue'

interface Props {
  release: () => void
  headerTitle?: string
  rightBtnText?: string
  isShowConfirmBtn?: boolean
  needShowDialog?: boolean
  backName?: string
  backQuery?: any
}
const props = withDefaults(defineProps<Props>(), {
  headerTitle: '发布资讯',
  rightBtnText: '发布',
  isShowConfirmBtn: true,
  needShowDialog: false,
  backName: '',
  backQuery: undefined,
})

const emits = defineEmits<{
  (e: 'emit-back'): void
}>()

const { richText } = storeToRefs(useRichStore())
const router = useRouter()
const showModal = ref(false)
function handleJumpBack() {
  emits('emit-back')
  if (props.needShowDialog) {
    window.$dialog.create({
      type: 'default',
      closable: false,
      content: '当前内容还未保存，确认返回吗？',
      showIcon: false,
      positiveText: '确认',
      negativeText: '取消',
      onPositiveClick: () => {
        if (props.backName) {
          if (props.backQuery) {
            router.push({ name: props.backName, query: props.backQuery })
          } else {
            router.push({ name: props.backName })
          }
        } else {
          router.back()
        }
      },
    })
  } else {
    if (props.backName) {
      if (props.backQuery) {
        router.push({ name: props.backName, query: props.backQuery })
      } else {
        router.push({ name: props.backName })
      }
    } else {
      router.back()
    }
  }
}
</script>
<template>
  <div
    class="flex justify-between px-[40px] h-[70px] items-center bg-[#fff] border-b-[3px] sticky top-[0px] z-[100]"
  >
    <n-button @click="handleJumpBack">
      <n-icon size="16">
        <arrow-back-ios-new-round />
      </n-icon>
      返回
    </n-button>
    <div class="flex flex-1 items-center justify-center">
      <span class="text-[18px] font-semibold">{{ props.headerTitle }}</span>
    </div>
    <div v-show="isShowConfirmBtn" class="flex gap-[10px]">
      <!-- <n-button round style="marginright: 10px" @click="showModal = true">
        预览
      </n-button> -->
      <n-button round type="primary" @click="release">
        {{ props.rightBtnText }}
      </n-button>
    </div>
  </div>
  <PreviewModal :rich-text="richText" :show-modal="showModal">
    <div
      v-if="showModal"
      class="absolute left-[0] top-[0] bottom-0 right-0 z-10 mask"
      @click="showModal = false"
    />
  </PreviewModal>
</template>

<style scoped lang="scss">
.modal-enter-active,
.modal-leave-active {
  transition: all 0.5s ease;
}

.modal-enter-from,
.modal-leave-to {
  width: 0;
  height: 0;
  opacity: 0;
}
.mask {
  background-color: rgba(0, 0, 0, 0.47);
}
</style>
