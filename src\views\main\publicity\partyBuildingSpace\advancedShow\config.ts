import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import { NImage } from 'naive-ui'
import type {
  AdvanCedPartyOrganizationItem,
  AdvancedPersonItem,
} from '@/services/publicity/advancedShow/type'

/** 先进基层党组织tab 列名 */
export function columnsOfPartyOrganization(
  optionColumnRenderer: (row: AdvanCedPartyOrganizationItem) => VNodeChild,
  handleUpdateValueRender: (row: AdvanCedPartyOrganizationItem) => VNodeChild,
): DataTableColumns<AdvanCedPartyOrganizationItem> {
  return [
    { type: 'selection' },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'orgName',
      title: '组织名称',
    },
    {
      key: 'epithet',
      title: '荣誉名称',
    },
    {
      key: 'certificate',
      title: '奖状',
      render: (row) => {
        return h(NImage, {
          src: import.meta.env.VITE_API_BASE + row.certificate,
          style: { width: '100px' },
        })
      },
    },
    {
      key: 'publishTime',
      title: '发布时间',
    },
    {
      key: 'recommandStatus',
      title: '是否推荐',
      render: row => handleUpdateValueRender(row),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}

/** 优秀党员tab 列名 */
export function columnsOfPartyMember(
  optionColumnRenderer: (row: AdvancedPersonItem) => VNodeChild,
  handleUpdateToTop: (row: AdvancedPersonItem) => VNodeChild,
  handleUpdateToRecommend: (row: AdvancedPersonItem) => VNodeChild,
): DataTableColumns<AdvancedPersonItem> {
  return [
    { type: 'selection' },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'userName',
      title: '姓名',
    },
    {
      key: 'year',
      title: '评议年度',
    },
    {
      key: 'orgName',
      title: '所属组织',
      render: (row) => {
        return h('span', {
          innerHTML: row.orgName,
        })
      },
    },
    {
      key: 'epithet',
      title: '荣誉名称',
      render: (row) => {
        return h('span', {
          innerHTML: row.epithet,
        })
      },
    },
    {
      key: 'avatarUrl',
      title: '照片',
      render: (row) => {
        return h(NImage, {
          src: import.meta.env.VITE_API_BASE + row.avatarUrl,
          style: { height: '100px' },
        })
      },
    },
    {
      key: 'publishTime',
      title: '发布时间',
      render: (row) => {
        return h('span', {
          innerHTML: row.publishTime,
        })
      },
    },
    {
      key: 'topStatus',
      title: '是否置顶',
      render: row => handleUpdateToTop(row),
    },
    {
      key: 'recommandStatus',
      title: '是否推荐',
      render: row => handleUpdateToRecommend(row),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
