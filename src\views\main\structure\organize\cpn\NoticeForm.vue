<script lang="ts" setup>
import { NForm } from 'naive-ui'
import { formRules } from './config'
import {
  getOrganizationDetail,
  postOrganizationItem,
  putOrganizationItem,
} from '@/services/organization'
import type { OrganizationDetailItem } from '@/services/organization/types'
import { useFetchEnumerationOptions, useOrganizationListOptions } from '@/hooks/use-select-options'
import type { uploadFileItem } from '@/services/types'
import { uploadImg } from '@/services'

interface Props {
  type?: string
  id?: string
  parentId?: string | null
  level: number | null
  data: Array<any>
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
  parentId: null,
  level: null,
  data() {
    return []
  },
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()
const { organizationListTree } = useOrganizationListOptions()

const { enumerationList } = useFetchEnumerationOptions('org_type')

const formDataReactive = reactive<OrganizationDetailItem>({
  parentId: '',
  name: '',
  orgType: null,
  code: '',
  coverUrl: '',
  id: '',
  parentName: null,
  summary: '',
  sortOrder: 0,
})

const formRef = ref<InstanceType<typeof NForm>>()

onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getOrganizationDetail(props.id).then((res) => {
      formDataReactive.id = res.id
      formDataReactive.parentId = res.parentId
      formDataReactive.name = res.name
      formDataReactive.orgType = res.orgType || null
      formDataReactive.code = res.code
      formDataReactive.coverUrl = res.coverUrl
      formDataReactive.summary = res.summary
      formDataReactive.sortOrder = res.sortOrder
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (formDataReactive.id) {
        putOrganizationItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
      else {
        // 新增组织
        postOrganizationItem(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

/**
 * 处理封面图片上传完成的异步函数。
 * @param file 需要上传的文件对象。
 * @returns {Promise<void>} 不返回任何内容。
 */
const handleCoverDone = async(file: File) => {
  const imgFileData = new FormData() // 创建一个FormData对象用于存放文件数据
  imgFileData.append('file', file) // 将文件添加到FormData对象中
  try {
    if (formDataReactive.coverUrl) {
      return // 如果已存在封面URL，则直接返回，不进行上传
    }
    // 异步上传图片文件
    const data: uploadFileItem = await uploadImg(imgFileData)
    if (data) {
      // 上传成功后，更新formData中的封面URL
      formDataReactive.coverUrl = data.url || ''
    }
  }
  catch (error) {
    // 捕获并处理上传过程中的错误
  }
}

// PC封面图片删除操作
const handleCoverDelete = () => {
  formDataReactive.coverUrl = ''
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

// onMounted(() => {
//   getTableList()
// })
defineExpose({
  validateAndSave,
  resetForm,
})
</script>

<template>
  <n-form
    ref="formRef"
    :model="formDataReactive"
    :rules="formRules"
    label-align="right"
    label-placement="left"
    label-width="120"
    require-mark-placement="left"
    size="small"
  >
    <n-form-item label="组织编码：" path="code">
      <n-input
        v-model:value="formDataReactive.code"
        :disabled="props.type === 'view'"
        clearable
        maxlength="40"
        placeholder="请输入组织编码"
        show-count
      />
    </n-form-item>
    <n-form-item label="组织名称：" path="name">
      <n-input
        v-model:value="formDataReactive.name"
        :disabled="props.type === 'view'"
        clearable
        maxlength="30"
        placeholder="请输入组织名称"
        show-count
      />
    </n-form-item>
    <n-form-item label="上级组织名称：" path="parentId">
      <!-- <n-tree-select
        v-model:value="formDataReactive.parentId"
        :disabled="props.type === 'view'"
        :options="transformTreeData(organizationOptions)"
        placeholder="请选择上级组织"
      /> -->

      <n-tree-select
        v-model:value="formDataReactive.parentId"
        :options="organizationListTree"
        :show-path="false"
        :disabled="props.type === 'view'"
        check-strategy="all"
        children-field="children"
        clearable
        filterable
        key-field="id"
        label-field="name"
        placeholder="请选择上级组织"
        value-field="id"
        @update:value="(v:any) => (formDataReactive.parentId= v)"
      />
    </n-form-item>

    <n-form-item label="组织类型：" path="orgType">
      <n-select
        v-model:value="formDataReactive.orgType"
        :disabled="props.type === 'view'"
        :options="enumerationList"
        clearable
        placeholder="请选择党组织类别"
      />
    </n-form-item>

    <n-form-item label="图片：" span="24">
      <ImgUploader
        v-model:old-img-url="formDataReactive.coverUrl"
        :height="200"
        :need-cropper="false"
        :width="380"
        @delete="handleCoverDelete"
        @done="handleCoverDone"
      />
    </n-form-item>
  </n-form>
</template>

<style lang="scss" scoped></style>
