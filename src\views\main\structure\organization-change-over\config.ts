import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { TableItem } from '@/services/structure/organization-change-over/type'

// 调动类型
export const transferType = ref([
  {
    label: '系统内调动',
    value: 0,
  },
  {
    label: '系统外调动',
    value: 1,
  },
  {
    label: '调出系统外',
    value: 2,
  },
])

// 状态枚举
export const statusOptions = ref([
  {
    label: '待审批',
    value: 0,
  },
  {
    label: '已驳回',
    value: 1,
  },
  {
    label: '已撤销',
    value: 2,
  },
  {
    label: '已完成',
    value: 3,
  },
])

export function getTableColumns(
  optionColumnRenderer: (row: TableItem) => VNodeChild,
): DataTableColumns<TableItem> {
  return [
    {
      key: 'transferType',
      title: '调动类型',
    },
    {
      key: 'transferTime',
      title: '调动时间',
    },
    {
      key: 'userName',
      title: '姓名',
    },
    {
      key: 'oldDeptName',
      title: '原组织',
    },
    {
      key: 'newDeptName',
      title: '接收组织',
    },
    {
      key: 'joinTime',
      title: '入党时间',
    },
    {
      key: 'paymentTime',
      title: '党费缴纳时间',
    },
    {
      key: 'phaseStatus',
      title: '状态',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
