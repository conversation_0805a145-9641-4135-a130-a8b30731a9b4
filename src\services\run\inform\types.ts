// export interface Inform {
//   [key: string]: any
//   id: string
//   title: string
//   type: number | null // 通知类型
//   content: string | null // 推送内容
//   userIdList?: Array<string> // 接收列表
//   // locked: number
// }

export interface Inform {
  id: string
  type: string
  title: string
  content: string
  userName: string
  noticeTime: any
  businessType: number
  businessId: string
  noticeStatus: string
  sendType: number
  updateTime: string
  userListList: any
}

export interface InformReq {
  id?: string
  type: string
  title: string
  content: string
  sendType: number
  noticeTime?: string | null
  userIdList: Array<string>
}

export interface InformRes {
  pageNum: number
  pageSize: number
  totalPage: number
  total: string
  records: Inform[]
}

export interface IRowData {
  /** 图片id */
  id?: number
  resourceName: string
  /** 是否显示 1-显示 0-不显示 */
  resourceStatus: number | null
  /** 图片位置 */
  layout: number | null
  /** 图片地址 */
  pictureUrl: string
  /** 跳转地址 */
  jumpUrl: string
  /** 排序 */
  sortOrder: string | null
  /** 图片文件 */
  file?: File
}
