<!--
 * @Description: 标签管理页
-->
<template>
  <div class="label-management">
    <div class="add-bar">
      <div class="filter">
        <span>名称</span>
        <n-input
          v-model:value="nameFilterRef"
          style="width: 230px"
          size="small"
          placeholder="请输入关键词"
          clearable
        />
      </div>

      <n-button size="small" type="primary" @click="() => handleEnterEditing()">
        添加标签
      </n-button>
    </div>

    <div class="table">
      <div class="head row">
        <div class="name cell">
          标签名称
        </div>
        <div class="operation cell">
          操作
        </div>
      </div>
      <n-scrollbar style="width: 100%; max-height: calc(100vh - 230px)">
        <div class="body">
          <div v-if="isAddRef" class="row">
            <div class="cell">
              <n-input
                v-model:value="inputValueRef"
                size="small"
                @blur="(e: any) => handleInputBlur(e)"
                @keypress="(e: any) => handleKeyPress(e.key)"
              />
            </div>
            <div class="cell">
              <n-button
                text
                type="primary"
                @click="() => handleQuitEditing(true)"
              >
                保存
              </n-button>
              <n-button
                text
                type="primary"
                @click="() => handleQuitEditing(false)"
              >
                取消
              </n-button>
            </div>
          </div>
          <div
            v-for="(item, index) in examinationLibraryLabelList"
            :key="item.id"
            class="row"
          >
            <div class="cell">
              <n-input
                v-if="item.editing"
                v-model:value="inputValueRef"
                size="small"
                @blur="(e: any) => handleInputBlur(e, index)"
                @keypress="(e: any) => handleKeyPress(e.key, index)"
              />
              <span v-else>{{ item.name }}</span>
            </div>
            <div class="cell">
              <template v-if="item.editing">
                <n-button
                  text
                  type="primary"
                  @click="() => handleQuitEditing(true, index)"
                >
                  保存
                </n-button>
                <n-button
                  text
                  type="primary"
                  @click="() => handleQuitEditing(false, index)"
                >
                  取消
                </n-button>
              </template>
              <template v-else>
                <n-button
                  text
                  type="primary"
                  @click="() => handleEnterEditing(index)"
                >
                  编辑
                </n-button>
                <n-popconfirm
                  placement="left"
                  positive-text="确定"
                  @positive-click="() => handleDeleteLabel(index)"
                >
                  <template #trigger>
                    <n-button text type="primary">
                      删除
                    </n-button>
                  </template>
                  确定删除吗？
                </n-popconfirm>
              </template>
            </div>
          </div>
        </div>
      </n-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { NButton, NInput, NPopconfirm, NScrollbar } from 'naive-ui'
import useExaminationStore from '@/store/examination/index'
import {
  deleteExaminationLibraryLabel,
  postExaminationLibraryLabel,
  putExaminationLibraryLabel,
} from '@/services/examination/index'

const examinationStore = useExaminationStore()

const isAddRef = ref(false)
const inputValueRef = ref('')
const nameFilterRef = ref('')
const examinationLibraryLabelList = computed(() =>
  examinationStore.examinationLibraryLabelList.filter(item =>
    item?.name?.includes(nameFilterRef.value),
  ),
)

function loadData() {
  examinationStore.getExaminationLibraryLabelListAction()
}

// 进入编辑
function handleEnterEditing(index?: number) {
  isAddRef.value = false
  examinationLibraryLabelList.value.forEach((item) => {
    item.editing = false
  })
  if (index !== undefined) {
    const target = examinationLibraryLabelList.value[index]
    inputValueRef.value = target.name
    target.editing = true
  }

  if (index === undefined) {
    inputValueRef.value = ''
    isAddRef.value = true
  }
}

// 退出编辑
function handleQuitEditing(isSave: boolean, index?: number) {
  if (index !== undefined) {
    const target = examinationLibraryLabelList.value[index]
    if (isSave) {
      if (!inputValueRef.value.trim().length) {
        window.$message.warning('标签名称不能为空')
        return
      }
      // 调用修改接口
      const value = inputValueRef.value
      putExaminationLibraryLabel(target.id, value).then((res) => {
        target.editing = false
        window.$message.success(res)
        examinationStore.getExaminationLibraryLabelListAction()
      })
    } else {
      target.editing = false
    }
  }
  if (index === undefined) {
    if (isSave) {
      if (!inputValueRef.value.trim().length) {
        window.$message.warning('标签名称不能为空')
        return
      }
      // 调用新增接口
      postExaminationLibraryLabel(inputValueRef.value).then((res) => {
        isAddRef.value = false
        window.$message.success(res)
        examinationStore.getExaminationLibraryLabelListAction()
      })
    } else {
      isAddRef.value = false
    }
  }
}

// 删除标签
function handleDeleteLabel(index: number) {
  const id = examinationLibraryLabelList.value[index].id
  deleteExaminationLibraryLabel(id).then((res) => {
    window.$message.success(res)
    examinationStore.getExaminationLibraryLabelListAction()
  })
}

// 输入框失去焦点
function handleInputBlur(event: FocusEvent, index?: number) {
  // 如果点击保存失去焦点，return
  const relatedEl = event.relatedTarget as HTMLElement
  if (relatedEl && relatedEl.innerHTML.includes('保存')) {
    return
  }

  if (index !== undefined) {
    examinationLibraryLabelList.value[index].editing = false
  }
  if (index === undefined) {
    isAddRef.value = false
  }
  inputValueRef.value = ''
}

// 输入框按键
function handleKeyPress(key: string, index?: number) {
  if (key === 'Enter') {
    handleQuitEditing(true, index)
  }
}

loadData()
</script>

<style lang="scss" scoped>
.label-management {
  padding: 20px 0;

  > .add-bar {
    display: flex;
    justify-content: space-between;

    > .filter {
      > span {
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        margin-right: 9px;
      }
    }
  }

  > .table {
    margin-top: 20px;
    width: 100%;

    .row {
      display: flex;
      align-items: center;

      &.head {
        height: 42px;
        background: #fafafa;

        .cell {
          text-align: left;
          font-size: 12px;
          font-weight: 500;
          color: #333333;
        }
      }

      .cell {
        padding-left: 28px;

        &:first-child {
          width: 75%;
        }

        &:last-child {
          width: 25%;
        }
      }
    }

    .body {
      .row {
        line-height: 45px;
        border-bottom: 1px solid #f2f3f6;

        .cell {
          > span {
            font-size: 12px;
            font-weight: 400;
            color: #333333;
          }

          .n-button:nth-child(n + 2) {
            margin-left: 16px;
          }
        }
      }
    }
  }
}
</style>
