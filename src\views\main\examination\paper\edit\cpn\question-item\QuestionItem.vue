<!--
 * @Description: 题目
-->
<template>
  <div class="question-item">
    <div class="top">
      <div class="left">
        <span>{{ index }}、{{ question.type }}</span>
        <span>题库：{{ question.libName }}</span>
      </div>
      <div class="right">
        <!-- 下移 -->
        <n-button text :disabled="!canDown" @click="emits('down')">
          <n-icon size="16">
            <arrow-down16-regular />
          </n-icon>
        </n-button>
        <!-- 上移 -->
        <n-button text :disabled="!canUp" @click="emits('up')">
          <n-icon size="16">
            <arrow-up16-regular />
          </n-icon>
        </n-button>
        <!-- 删除 -->
        <n-button text type="error" @click="emits('delete')">
          <n-icon size="16">
            <delete-outline-round />
          </n-icon>
        </n-button>
      </div>
    </div>

    <div class="name">
      {{ question.name }}
    </div>

    <div v-if="question.type != '填空题'" class="options">
      <div
        v-for="option in question.optionList"
        :key="option.id"
        class="option"
      >
        {{ option.name }}. {{ option.content }}
      </div>
    </div>

    <div class="answer">
      <span>正确答案：<span v-if="question.type == '填空题'">{{
        question.optionList.map((item) => item.content).join('；')
      }}</span><span v-else>{{ question.answerName }}</span></span>
      <span v-if="question.analysis?.length" class="analysis">
        <n-popover :width="200">
          <template #trigger>
            <n-button text type="primary">
              <n-icon style="margin-right: 3px" size="16">
                <notepad-edit16-regular />
              </n-icon>
              答案解析
            </n-button>
          </template>
          {{ question.analysis }}
        </n-popover>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { NButton, NIcon, NPopover } from 'naive-ui'
import { DeleteOutlineRound } from '@vicons/material'
import {
  ArrowDown16Regular,
  ArrowUp16Regular,
  NotepadEdit16Regular,
} from '@vicons/fluent'
import type { IFilteredExaminationQuestion } from '@/services/examination/index'

defineProps({
  index: {
    type: Number,
    required: true,
  },
  question: {
    type: Object as PropType<IFilteredExaminationQuestion>,
    required: true,
  },
  canUp: {
    type: Boolean,
    default: true,
  },
  canDown: {
    type: Boolean,
    default: true,
  },
})
const emits = defineEmits(['up', 'down', 'delete'])
</script>

<style lang="scss" scoped>
.question-item {
  border-bottom: 1px solid #eff0f1;
  margin-bottom: 20px;

  > .top {
    height: 26px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    > .left {
      > span {
        &:first-child {
          font-size: 14px;
          font-weight: 500;
          color: #333333;
          display: inline-block;
          width: 90px;
        }

        &:last-child {
          font-size: 12px;
          font-weight: 400;
          color: #666666;
          margin-left: 62px;
        }
      }
    }

    > .right {
      > button:nth-child(n + 2) {
        margin-left: 10px;
      }
    }
  }

  > .name {
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 26px;
    margin: 10px 0;
  }

  > .options {
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 34px;
    margin-bottom: 7px;
    padding-left: 15px;
  }

  > .answer {
    height: 40px;
    background: #f7fbff;
    padding-left: 16px;
    font-size: 12px;
    font-weight: 400;
    color: #333333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    > .analysis {
      height: 18px;
      line-height: 18px;
      margin-left: 40px;
    }
  }
}
</style>
