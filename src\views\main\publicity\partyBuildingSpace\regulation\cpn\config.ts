import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  title: [
    {
      required: true,
      message: '主题不能为空',
      trigger: 'input',
    },
  ],
  module: {
    '0': {
      content: {
        required: true,
        message: '内容不能为空',
        trigger: 'input',
      },
    },
  },
  topStatus: [
    {
      required: true,
      message: '请选择是否置顶',
      trigger: 'change',
    },
  ],
  customCover: {
    id: {
      required: true,
      message: '请上传图片',
      trigger: 'change',
    },
  },

  fileList: {
    required: true,
    validator(rule: any, value: any) {
      if (value === null) {
        return new Error('请选择文件')
      }
      return true
    },
    trigger: 'change',
  },
}
