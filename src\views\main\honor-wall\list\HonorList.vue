<script lang="ts" setup>
import { DeleteForeverRound, PlusRound } from '@vicons/material'
// import AddHonorRootForm from '../cpn/AddHonorRootForm.vue'
import AddHonorSubForm from '../cpn/AddHonorSubForm.vue'
import AddHonorItemForm from '../cpn/AddHonorItemForm.vue'
import { getTableColumns } from './config'
import { useDrawerEdit, useMyTable, useTreeMenu } from '@/hooks'
import {
  addHonorWallCategory,
  deleteHonorWall,
  deleteHonorWallCategory,
  honorWallPage,
  listHonorWallCategory,
  moveHonorWallCategory,
  putHonorWallCategory,
} from '@/services/honor-wall'
import DeleteButton from '@/components/DeleteButton.vue'
import { useCurrentOrganizationListOptionsNew } from '@/hooks/use-select-options'
import { HONORTYPE } from '@/constant'
import type { HonorWallDetailItem } from '@/services/honor-wall/types'

// const { organizationCurrentListTree } = useCurrentOrganizationListOptions() // 获取组织列表

const { organizationCurrentListTree } = useCurrentOrganizationListOptionsNew() // 获取组织列表

const honorSubFormRef = ref()
const parentPionnerId = ref('')
const parentType = ref()
const pageHonorType = ref()
// 选中菜单名称
const selectName = ref()

// 确认按钮loading
const confirmLoadingRef = ref(false)

// 筛选项：类别id和资讯标题
const filterRef = ref({
  categoryId: '',
  serviceName: null,
  deptId: '',
})

const { treeData, showModalType, moveNode, delNode, saveNode, init }
  = useTreeMenu({
    menuListApi: listHonorWallCategory,
    moveNodeApi: moveHonorWallCategory,
    moveChildNodeApi: moveHonorWallCategory,
    delNodeApi: deleteHonorWallCategory,
    delChildNodeApi: deleteHonorWallCategory,
    addNodeApi: addHonorWallCategory,
    modifyNodeApi: putHonorWallCategory,
    addChildNodeApi: addHonorWallCategory,
    modifyChildNodeApi: putHonorWallCategory,
    refreshTableApi: filterInput,
    customPackDataFunc: packDataFunc,
    multiLevelKey: 'children',
    labelField: 'name',
    childLabelField: 'name',
    maxLevel: 3,
    sessionId: 'honorListCategoryId',
    sessionName: 'honorTitle',
    sessionData: 'honorData',
  })

const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(honorWallPage, filterRef, {
  batchDeleteTable: true,
  delApi: deleteHonorWall,
})

const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('荣誉', handelConfirmEdit)

const addHonorFormRef = ref()
const honorWallItem = reactive<{ value: HonorWallDetailItem }>({
  value: {
    fileList: [],
    title: '',
  },
}) // 点击的数据的id
/** 列表操作 */
const tableColumns = ref()

const showModal = ref(false)
const modalTitle = ref()
// const specialRootFormRef = ref()

// 处理弹框需要保存的数据及校验弹框必填项
async function handleFormatterParams() {
  let flag = false
  let parentId
  let honorType
  // if (showModalType.value === 'root') {
  //   flag = await specialRootFormRef.value.handleValidate()
  // }
  if (showModalType.value === 'sub') {
    parentId = parentPionnerId.value
    honorType = parentType.value
    flag = await honorSubFormRef.value.handleValidate()
  }
  if (!flag) {
    return
  }
  // const data =
  //   showModalType.value === 'root'
  //     ? specialRootFormRef.value.formData.data
  //     : honorSubFormRef.value.formData.data
  const data = honorSubFormRef.value.formData.data
  // saveNode({ ...data, type: showModalType.value, categoryId })
  saveNode({ ...data, type: showModalType.value, parentId, honorType })
  showModal.value = false
}

/** 新增子节点 */
const parentName = ref()

async function handleAddChildNode(data: any) {
  modalTitle.value = '新增荣誉墙分类'
  showModalType.value = 'sub'
  parentPionnerId.value = data.originData.id
  parentName.value = data.name
  parentType.value = data.honorType
  showModal.value = true
  if (data.model === 'modify') {
    // 编辑状态 需要将数据回显
    modalTitle.value = '修改荣誉墙分类'
    parentName.value = data.parentName
    parentPionnerId.value = data.parentId
    nextTick(() => {
      honorSubFormRef.value.handleSetFormData({
        ...data.originData,
        parentName: parentName.value,
      })
    })
  }
}

function handelConfirmEdit() {
  confirmLoadingRef.value = true
  addHonorFormRef.value?.validateAndSave()
}

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}

function handleCancel() {
  showModal.value = false
}

// 选中菜单触发的事件
function handleChangeTab(data: any) {
  // console.log('data: ', data) isChild
  const { label, clickExpand } = data
  if (!clickExpand) {
    filterRef.value.categoryId = data.originData.id
    filterRef.value.serviceName = null
    selectName.value = label
    pageHonorType.value = data.honorType
    currentPage.value = 1
    tableColumns.value = generateColumns()
    window.sessionStorage.setItem('honorListCategoryId', data.originData.id)
    window.sessionStorage.setItem('honorTitle', label)
    window.sessionStorage.setItem('honorData', JSON.stringify(data))
  }
}

const defaultSelectedKeys = ref<string[]>([])

function filterInput(res: { id: string; name: string }) {
  defaultSelectedKeys.value = [res.id]
  filterRef.value.categoryId = res.id
  selectName.value = res.name
}

// 包装数据
function packDataFunc(res: any[]) {
  res.forEach((item) => {
    if (item.parentId === '0') {
      item.hiddleDelete = true
      item.hiddleEdit = true
      item.hiddleUp = true
      item.hiddleDown = true
    }
  })
}

function generateColumns() {
  return getTableColumns((row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
          display: 'flex',
          gap: '15px',
        },
      },
      [
        h(
          'span',
          {
            onClick: () => {
              honorWallItem.value = row as HonorWallDetailItem
              editTypeRef.value = 'view'
              showEditRef.value = true
            },
          },
          { default: () => '查看' },
        ),
        h(
          'span',
          {
            onClick: () => {
              honorWallItem.value = row as HonorWallDetailItem
              editTypeRef.value = 'modify'
              showEditRef.value = true
            },
          },
          { default: () => '编辑' },
        ),

        h(DeleteButton, {
          handleConfirm: () => handleSingleDelete(String(row.id)),
        }),
      ],
    )
  }, pageHonorType.value)
}

/** 保存成功 */
function handleListSaved() {
  confirmLoadingRef.value = false
  showEditRef.value = false
  loadData()
}

onMounted(() => {
  init().then((res: any) => {
    defaultSelectedKeys.value = [res.id]
    filterRef.value.categoryId = res.id
    selectName.value = res.name
    pageHonorType.value = res.data.honorType
    tableColumns.value = generateColumns()
  })
})
/** 点击添加按钮 */
// function handleClickAdd() {}
</script>
<template>
  <layout-container style="height: calc(100vh - 114px)">
    <template #side>
      <SideMenuNew
        v-model:show-modal="showModal"
        :default-selected-keys="defaultSelectedKeys"
        :modal-title="modalTitle"
        :show-root-btn="false"
        :tree-data="treeData"
        title="荣誉墙管理"
        @move="moveNode"
        @del-node="delNode"
        @save-tree-node="handleFormatterParams"
        @add-child-node="handleAddChildNode"
        @select-node-key="handleChangeTab"
      />
    </template>
    <template #main>
      <table-container
        v-model:page="currentPage"
        v-model:page-size="pageSize"
        :checked-row-keys="checkedRowKeys"
        :loading="loading"
        :show-toolbar="false"
        :table-columns="tableColumns"
        :table-data="tableData"
        :title="selectName"
        :total="total"
        custom-toolbar
        @click-add="handleClickAdd"
        @click-delete="handleBatchDelete"
        @update-page="onUpdatePage"
        @update-page-size="onUpdatePageSize"
        @update-checked-row-keys="onUpdateCheckedRowKeys"
      >
        <template #btns>
          <n-button size="small" type="primary" @click="handleClickAdd">
            <template #icon>
              <n-icon>
                <plus-round />
              </n-icon>
            </template>
            添加
          </n-button>

          <n-button size="small" @click="handleBatchDelete">
            <template #icon>
              <n-icon>
                <delete-forever-round />
              </n-icon>
            </template>
            删除
          </n-button>
        </template>
        <template #filters>
          <!-- <n-cascader
            v-model:value="filterRef.deptId"
            placeholder="请选择目标组织"
            :options="(organizationCurrentListTree as any)"
            value-field="id"
            label-field="name"
            children-field="children"
            check-strategy="child"
            :show-path="false"
            clearable
            filterable
            style="width: 260px"
            @update:value="(v:any) => (filterRef.deptId= v)"
          /> -->
          <!-- <n-tree-select
            v-model:value="filterRef.deptId"
            :options="organizationCurrentListTree"
            :show-path="false"
            check-strategy="child"
            children-field="children"
            clearable
            filterable
            key-field="id"
            label-field="name"
            placeholder="请选择所属党组织"
            size="small"
            style="width: 300px"
            value-field="id"
            @update:value="(v:any) => (filterRef.deptId= v)"
          /> -->
          <n-tree-select
            v-model:value="filterRef.deptId"
            :options="organizationCurrentListTree"
            :show-path="false"
            check-strategy="all"
            children-field="children"
            clearable
            filterable
            key-field="deptId"
            label-field="name"
            placeholder="请选择所属党组织"
            style="width: 300px"
            value-field="deptId"
            @update:value="(v:any) => (filterRef.deptId= v)"
          />

          <n-input
            v-if="pageHonorType !== HONORTYPE.ORGHONOR"
            v-model:value="filterRef.serviceName"
            clearable
            placeholder="请输入党员名称"
            size="small"
            style="width: 200px"
          />
        </template>
      </table-container>
    </template>
  </layout-container>
  <CustomDialog
    :show="showModal"
    :title="modalTitle"
    width="600px"
    @cancel="handleCancel"
    @confirm="handleFormatterParams"
    @update:show="(v:boolean) => (showModal = v)"
  >
    <div class="p-[20px]">
      <!-- <AddHonorRootForm
        v-show="showModalType === 'root'"
        ref="honorRootFormRef"
      /> -->
      <AddHonorSubForm
        v-show="showModalType === 'sub'"
        ref="honorSubFormRef"
        :parent-name="parentName"
      />
    </div>
  </CustomDialog>
  <n-drawer v-model:show="showEditRef" :mask-closable="false" :width="700">
    <n-drawer-content :title="drawerTitle" closable>
      <add-honor-item-form
        ref="addHonorFormRef"
        :category-id="filterRef.categoryId"
        :honor-type="pageHonorType"
        :item="honorWallItem.value"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            :loading="confirmLoadingRef"
            style="width: 80px"
            type="primary"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
