<template>
  <div class="h-[50px] pt-[25px] pl-[30px]">
    <span class="text-[14px] font-[600] text-[#333] leading-[20px] mb-[26px]">法规制度</span>
  </div>
  <n-divider />
  <layout-container class="pl-[20px]">
    <template #side>
      <side-tree-ctn tree-title="分类列表" @add-root="handleAddRoot">
        <template #tree>
          <div style="margin-bottom: 16px">
            <n-input
              v-model:value="patternRef"
              size="small"
              placeholder="搜索分类"
            >
              <template #prefix>
                <n-icon>
                  <search-round />
                </n-icon>
              </template>
            </n-input>
          </div>
          <n-scrollbar
            :style="`max-height: ${maxHeight}; ${
              maxHeight.length > 0
                ? 'box-sizing: border-box;padding-right: 20px;'
                : ''
            }`"
            :class="{ pr: maxHeight.length > 0 }"
          >
            <n-tree
              block-line
              expand-on-click
              cascade
              default-expand-all
              :data="treeData"
              :pattern="patternRef"
              :default-expanded-keys="defaultExpandedKeys"
              :render-suffix="renderSuffix"
              :render-label="labelRenderer"
              @update-selected-keys="handleSelectedKeysChange"
            />
          </n-scrollbar>
        </template>
      </side-tree-ctn>
    </template>

    <template v-if="tableColumns" #main>
      <table-container
        v-model:page="currentPage"
        v-model:page-size="pageSize"
        style="padding-top: 0"
        title=""
        :show-toolbar="false"
        custom-toolbar
        :table-columns="tableColumns"
        :table-data="tableData"
        :total="total"
        :loading="loading"
        :show-delete="false"
        :checked-row-keys="checkedRowKeys"
        @click-add="handleClickAdd"
        @click-delete="handleBatchDelete"
        @update-page="onUpdatePage"
        @update-page-size="onUpdatePageSize"
        @update-checked-row-keys="onUpdateCheckedRowKeys"
      >
        <template #btns>
          <n-button size="small" type="primary" @click="handleClickAdd">
            <template #icon>
              <n-icon>
                <plus-round />
              </n-icon>
            </template>
            添加
          </n-button>
          <n-button size="small" @click="handleBatchDelete">
            <template #icon>
              <n-icon>
                <delete-forever-round />
              </n-icon>
            </template>
            删除
          </n-button>
        </template>
        <template #filters>
          <n-input
            v-model:value="filterReactive.title"
            size="small"
            placeholder="请输入搜索主题"
            clearable
          />
          <n-input
            v-model:value="filterReactive.content"
            size="small"
            placeholder="请输入搜索内容"
            clearable
          />
        </template>
      </table-container>
    </template>
  </layout-container>
  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <AddRegulationForm
        :id="idEditRef"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        :category-id="filterReactive.categoryId"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { h, ref } from 'vue'
import type { TreeOption } from 'naive-ui'
import {
  NButton,
  NEllipsis,
  NIcon,
  NInput,
  NPopselect,
  NSwitch,
} from 'naive-ui'
import {
  CheckRound,
  CloseRound,
  DeleteForeverRound,
  MoreVertRound,
  PlusRound,
  SearchRound,
} from '@vicons/material'
import { getTableColumns } from './config'
import AddRegulationForm from './cpn/AddRegulationForm.vue'
import LayoutContainer from '@/components/LayoutContainer.vue'
import SideTreeCtn from '@/components/SideTreeCtn.vue'
import TableContainer from '@/components/TableContainer.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import type { CommonParamsOfCompanyAndRegulation } from '@/services/publicity/companyShow/types'
import {
  delCompanyShowItem,
  getCompanyShowList,
  putUpdateCompanyShowItemTop,
} from '@/services/publicity/companyShow'
import DeleteButton from '@/components/DeleteButton.vue'
import {
  delCategory,
  getCategoryList,
  postCategorySort,
  postInsertCategory,
} from '@/services/publicity/regulation'
import type { ParentCategoryItem } from '@/services/publicity/regulation/types'
const categoryNameRef = ref('')
const filterReactive = ref<CommonParamsOfCompanyAndRegulation>({
  title: '',
  content: '',
  categoryId: '',
  type: '1',
})

const patternRef = ref()
const defaultExpandedKeys = reactive<string[]>([])

const categoryObj = reactive<{ categoryList: Array<ParentCategoryItem> }>({
  categoryList: [],
})
const findCategoryList = async() => {
  categoryObj.categoryList = (await getCategoryList(
    categoryNameRef.value,
  )) as any
  if (
    categoryObj.categoryList.length
    && categoryObj.categoryList[0]?.child?.length
  ) {
    filterReactive.value.categoryId = categoryObj.categoryList[0].child[0].id
    defaultExpandedKeys.push(categoryObj.categoryList[0].child[0].id)
  }
}
const treeData = computed(() =>
  categoryObj.categoryList.map((item, index) => {
    return {
      key: item.id,
      label: item.name,
      editing: !!item.editing,
      renderId: item.renderId,
      parentId: item.parentId,
      canUp: index !== 0,
      canDown: index !== categoryObj.categoryList.length - 1,
      isChild: false,
      children: item.child!.map((ite, idx) => {
        return {
          key: ite.id,
          label: ite.name,
          editing: !!ite.editing,
          renderId: ite.renderId,
          parentId: ite.parentId,
          canUp: idx !== 0,
          canDown: idx !== item.child!.length - 1,
          isChild: true,
        }
      }),
    }
  }),
)

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getCompanyShowList, filterReactive, {
  batchDeleteTable: true,
  delApi: delCompanyShowItem,
  delType: 0,
})

watch(
  filterReactive,
  () => {
    loadData()
  },
  { deep: true },
)

// 新增/编辑制度法规抽屉
const idEditRef = ref()
const addNoticeFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('制度法规', handelConfirmEdit)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addNoticeFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns(
  (row) => {
    return [
      h(
        NButton,
        {
          onClick: () => {
            idEditRef.value = row.id
            editTypeRef.value = 'modify'
            showEditRef.value = true
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '编辑',
        },
      ),
      h(DeleteButton, {
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    ]
  },
  (row) => {
    return h(NSwitch, {
      checkedValue: '1',
      uncheckedValue: '0',
      value: row.topStatus,
      loading: row.loading,
      onUpdateValue() {
        row.loading = true
        putUpdateCompanyShowItemTop({ id: row.id })
          .then((res) => {
            row.topStatus = res
          })
          .catch(() => {})
          .finally(() => {
            loadData()
            row.loading = false
          })
      },
    })
  },
)

/** 树组件右边标签 */
const maxHeight = ref('')
const editingValueRef = ref('')
const labelInputRef = ref<InstanceType<typeof NInput>>()
const addDisabledRef = ref(false)
const renderIdRef = ref('')
function uuid() {
  const s: string[] = []
  const hexDigits = '0123456789abcdef'
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4'
  s[19] = hexDigits.substr((Number(s[19]) & 0x3) | 0x8, 1)
  s[8] = s[13] = s[18] = s[23] = '-'

  return s.join('')
}
/** 上移 */
const upFn = (id: string) => {
  postCategorySort(id, -1).then((res) => {
    findCategoryList()
  })
}
/** 下移 */
const downFn = (id: string) => {
  postCategorySort(id, +1).then((res) => {
    findCategoryList()
  })
}
/** 编辑节点 */
const editFn = (node: TreeOption) => {
  addDisabledRef.value = true
  node.editing = true
  editingValueRef.value = node.label ?? ''
  nextTick(() => {
    labelInputRef.value?.focus()
  })
}
/** 删除节点 */
const delFn = (id: string) => {
  delCategory(id).then((res) => {
    findCategoryList()
  })
}
const addChildFn = (node: TreeOption) => {
  const currentNode = categoryObj.categoryList.find(
    item => item.id === node.key,
  )
  addDisabledRef.value = true
  renderIdRef.value = uuid()
  editingValueRef.value = ''
  if (!currentNode!.child) {
    currentNode!.child = []
  }
  if (Array.isArray(currentNode!.child)) {
    currentNode?.child.unshift({
      editing: true,
      pid: 0,
      id: renderIdRef.value,
      renderId: renderIdRef.value,
      parentId: node.key as string,
      name: '',
    })
  }
  nextTick(() => {
    labelInputRef.value?.focus()
  })
}
// 下拉选择选中
function handleSelectValueChange(
  value: 'up' | 'down' | 'top' | 'bottom' | 'edit' | 'delete' | 'add-child',
  node: TreeOption,
) {
  switch (value) {
    case 'up':
      upFn(node.key as string)
      break
    case 'down':
      downFn(node.key as string)
      break
    case 'edit':
      editFn(node)
      break
    case 'delete':
      // 删除
      window.$dialog.warning({
        title: '提示',
        content: '确定删除吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          delFn(node.key as string)
        },
      })
      break
    case 'add-child':
      addChildFn(node)
      break
    default:
  }
}
// 取消新增，根据renderId删除刚添加的节点
function cancelAdd(renderId: string, treeOptions: TreeOption[]) {
  const index = treeOptions.findIndex(item => item.renderId === renderId)
  if (index < 0) {
    treeOptions.forEach((sub) => {
      if (sub.child) {
        cancelAdd(renderId, sub.child as any)
      }
    })
  } else {
    treeOptions.splice(index, 1)
  }
}
// 保存树节点
function saveTreeNode(node: TreeOption) {
  node.editing = false
  addDisabledRef.value = false
  postInsertCategory(node.parentId as string, node.name as string).then(
    (res) => {
      findCategoryList()
    },
  )
}
// 点击节点后缀图标
function handleClickSuffix(e: Event) {
  e.stopPropagation()
}
// 获取后缀下拉选项
function getSuffixOptions(option: TreeOption) {
  const options = [
    // { label: '上移', value: 'up', disabled: !option.canUp },
    // { label: '下移', value: 'down', disabled: !option.canDown },
    // { label: true ? '编辑' : '重命名', value: 'edit' },
    { label: '删除', value: 'delete' },
    { label: '添加子级', value: 'add-child', disabled: option.isChild },
  ]
  return options
}
function renderSuffix(info: {
  option: TreeOption
  checked: boolean
  selected: boolean
}) {
  if (info.option.editing) {
    return null
  }
  return h(
    'div',
    { onClick: handleClickSuffix },
    h(
      NPopselect,
      {
        trigger: 'click',
        placement: 'bottom-start',
        options: getSuffixOptions(info.option),
        onUpdateValue: (v: any) => handleSelectValueChange(v, info.option),
      },
      {
        default: () =>
          h(
            NIcon,
            { size: 20, color: '#999' },
            { default: () => h(MoreVertRound) },
          ),
      },
    ),
  )
}
// 树节点内容渲染函数
function labelRenderer(info: {
  option: TreeOption
  checked: boolean
  selected: boolean
}) {
  if (info.option.editing) {
    return h(
      'span',
      {
        style: 'display: flex; align-items: center',
      },
      [
        h(NInput, {
          ref: labelInputRef,
          size: 'small',
          value: editingValueRef.value,
          onUpdateValue: v => (editingValueRef.value = v),
          onKeyup: (e) => {
            if (e.key === 'Enter') {
              const label = editingValueRef.value.trim()
              if (label) {
                saveTreeNode({
                  name: editingValueRef.value,
                  parentId: info.option.parentId,
                })
              } else {
                window.$message.warning('请输入名称')
                return false
              }
            }
          },
        }),
        h(
          NButton,
          {
            text: true,
            type: 'primary',
            style: 'margin-left: 8px',
            onClick: (e: Event) => {
              const label = editingValueRef.value.trim()
              if (label) {
                saveTreeNode({
                  name: editingValueRef.value,
                  parentId: info.option.parentId,
                })
              } else {
                window.$message.warning('请输入名称')
                return false
              }
              e.stopPropagation()
            },
          },
          {
            default: () =>
              h(
                NIcon,
                {
                  size: 22,
                },
                { default: () => h(CheckRound) },
              ),
          },
        ),
        h(
          NButton,
          {
            text: true,
            type: 'primary',
            style: 'margin-left: 8px',
            onClick: (e: Event) => {
              info.option.editing = false
              // 如果是新增的，取消新增
              if (info.option.renderId) {
                cancelAdd(
                  info.option.renderId as string,
                  categoryObj.categoryList,
                )
              }
              addDisabledRef.value = false
              e.stopPropagation()
            },
          },
          {
            default: () =>
              h(
                NIcon,
                {
                  size: 22,
                },
                { default: () => h(CloseRound) },
              ),
          },
        ),
      ],
    )
  } else {
    return h(
      NEllipsis,
      { style: 'max-width: 130px' },
      { default: () => info.option.label },
    )
  }
}

// 树节点选中
function handleSelectedKeysChange(
  keys: Array<string | number>,
  option: Array<TreeOption | null>,
) {
  if (option[0] && !option[0].editing) {
    filterReactive.value.categoryId = keys[0] as string
  }
}

/** 添加分类 */
const handleAddRoot = () => {
  addDisabledRef.value = true
  renderIdRef.value = uuid()
  editingValueRef.value = ''
  categoryObj.categoryList.unshift({
    editing: true,
    pid: 0,
    id: renderIdRef.value,
    renderId: renderIdRef.value,
    parentId: '0',
    name: '',
    child: [],
  })
  nextTick(() => {
    labelInputRef.value?.focus()
  })
}

onMounted(() => {
  findCategoryList()
})
</script>
