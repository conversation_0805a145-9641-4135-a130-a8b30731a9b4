import { defineStore } from 'pinia'
import { sessionCache } from '@/utils/cache'
// import { getPreviewHost } from '@/services/system/preview'

export const useSystemStore = defineStore('system', () => {
  const previewHost = ref(sessionCache.get('previewHost') || null)

  async function handleGetPreviewHost() {
    try {
      if (previewHost.value) {
        return
      }
      // 这块儿文件预览地址的拼接，需要用本地启动的ip地址服务打开，localhost打开无效,http://localhost:8100/xxxxx
      previewHost.value = window.location.origin + import.meta.env.VITE_API_BASE
      sessionCache.set('previewHost', previewHost.value)
    }
    catch (err) {}
  }

  handleGetPreviewHost()

  function getPreviewHostData() {
    previewHost.value = sessionCache.get('previewHost')
    return previewHost.value
  }

  // handleGetPreviewHost()

  return {
    getPreviewHostData,
  }
})
