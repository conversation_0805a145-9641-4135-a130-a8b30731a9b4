<script setup lang="ts">
import { NButton } from 'naive-ui'
// import { debounce } from 'lodash-es'
import { getTableColumns } from './config'
import InformForm from './cpn/InformForm.vue'
import { useDrawerEdit } from '@/hooks/use-drawer-edit'
import { NOTICE_MANAGE_TYPES } from '@/store/dict'
import {
  deleteInform,
  getInformList,
  pushInform,
} from '@/services/run/inform/inform'
import useBaseStore from '@/store/base/index'
import { useMyTable } from '@/hooks/use-my-table'
const baseStore = useBaseStore()
const filterRef = ref({
  type: null,
  title: null,
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(getInformList, filterRef, {
  batchDeleteTable: true,
  delApi: deleteInform,
})

/** 图片名称过滤 */
// function handleResourceNameChange() {
//   currentPage.value = 1
//   filterRef.value.title = filterRef.value.title?.trim()
//   loadData()
// }
// watch(filterRef, debounce(handleResourceNameChange, 500))

// 抽屉
const idEditRef = ref()
const informFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('通知', handelConfirmEdit)
/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  idEditRef.value = '0'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  informFormRef.value?.validateAndSave()
}
/** 保存成功 */
function handleCarouselSaved() {
  showEditRef.value = false
  loadData()
}
// 推送
function handlePush(id: string) {
  window.$dialog.warning({
    title: '提示',
    content: '确认发布该通知?',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      pushInform(id)
        .then(() => {
          window.$message.success('发布成功')
        })
        .finally(loadData)
    },
  })
}
function handleUpdateDialog(id: string) {
  editTypeRef.value = 'modify'
  idEditRef.value = id
  showEditRef.value = true
}
function handleShow(id: string) {
  editTypeRef.value = 'view'
  idEditRef.value = id
  showEditRef.value = true
}
function handleSingleDelete(id: string, type: string) {
  window.$dialog.warning({
    title: '提示',
    content:
      type === '已发布'
        ? '删除后无法恢复，确认删除？'
        : '删除后该通知将不会发布，确认删除？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async() => {
      await deleteInform(id)
      window.$message.success('删除')
      loadData()
    },
  })
}
function handleOuterClickCancel() {
  if (!informFormRef.value?.getChangedFlag()) {
    window.$dialog.warning({
      title: '提示',
      content:
        editTypeRef.value === 'modify'
          ? '更新数据未发布，关闭后本次编辑内容不保存，是否继续？'
          : '关闭后本次内容不保存，是否继续？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        handleClickCancel()
      },
    })
  }
  else {
    handleClickCancel()
  }
}
watch(showEditRef, (newV) => {
  if (!newV) {
    informFormRef.value?.resetForm()
  }
})
// 修改和删除按钮渲染
const tableColumns = getTableColumns(
  (row) => {
    return h(
      'div',
      { style: { color: '#AC241D' } },
      (row.noticeStatus !== '0'
        ? [
          h(
            'span',
            {
              style: { cursor: 'pointer' },
              onClick: () => handleShow(row.id),
            },
            { default: () => '查看' },
          ),
        ]
        : [
          h(
            'span',
            {
              style: { cursor: 'pointer' },
              onClick: () => handlePush(row.id),
            },
            { default: () => '发布' },
          ),
          h(
            'span',
            {
              style: { cursor: 'pointer', margin: '0 0 0 20px' },
              onClick: () => handleUpdateDialog(row.id),
            },
            { default: () => '编辑' },
          ),
        ]
      ).concat([
        h(
          'span',
          {
            style: { cursor: 'pointer', margin: '0 0 0 20px' },
            onClick: () => handleSingleDelete(row.id, row.noticeStatus),
          },
          { default: () => '删除' },
        ),
      ]),
    )
  },
  (row, index) => {
    return (currentPage.value - 1) * pageSize.value + index + 1
  },
)
onMounted(() => {
  loadData()
  baseStore.getCompanyDeptUserListAction()
})
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="通知管理"
    :loading="loading"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :show-delete="false"
    @click-add="handleClickAdd"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
  >
    <template #filters>
      <n-select
        v-model:value="filterRef.type"
        style="width: 260px"
        placeholder="请选择通知类型"
        filterable
        :options="NOTICE_MANAGE_TYPES"
        clearable
      />
      <n-input
        v-model:value="filterRef.title"
        clearable
        style="width: 260px; height: 32px"
        placeholder="请输入通知标题/内容"
      />
    </template>
  </table-container>
  <n-drawer v-model:show="showEditRef" :width="600" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <Inform-form
        :id="idEditRef"
        ref="informFormRef"
        :type="editTypeRef"
        @saved="handleCarouselSaved"
      />
      <template #footer>
        <div v-if="editTypeRef != 'view'">
          <n-button
            type="primary"
            style="margin-right: 10px"
            @click="handleClickConfirm"
          >
            保存
          </n-button>
          <n-button @click="handleOuterClickCancel">
            取消
          </n-button>
        </div>
        <div v-else>
          <n-button @click="handleClickCancel">
            关闭
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
