<script setup lang="ts">
import type { CascaderOption, NForm } from 'naive-ui'
import { formRules } from './config'
import { useOrganizationListOptions } from '@/hooks/use-select-options'
import type { BranchDetail } from '@/services/structure/branch-garden/types'
import {
  getBranchDetail,
  postBranch,
  putBranch,
} from '@/services/structure/branch-garden'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const { organizationListTree } = useOrganizationListOptions() // 获取支部列表
const formDataReactive = reactive<BranchDetail>({
  id: '',
  orgId: null,
  orgName: '',
  content: '',
  introduce: '',
})
const handleUpdateValue = (v: string, option: CascaderOption) => {
  formDataReactive.orgId = v
  formDataReactive.orgName = option.name as string
}
const loading = ref(false)
const formRef = ref<InstanceType<typeof NForm>>()
onMounted(async() => {
  loading.value = true
  if (props.type === 'modify' && props.id) {
    try {
      const res = await getBranchDetail(props.id)
      formDataReactive.id = res.id
      formDataReactive.orgId = res.orgId
      formDataReactive.orgName = res.orgName
      formDataReactive.content = res.content
      formDataReactive.introduce = res.introduce
      loading.value = false
    } catch (error) {}
  }
})

// 验证表单,调用接口
async function validateAndSave() {
  loading.value = true
  const errors = await new Promise((resolve) => {
    formRef.value?.validate((errors: any) => {
      resolve(errors)
    })
  })

  if (!errors) {
    const saveFunction
      = props.type === 'modify' && props.id ? putBranch : postBranch
    const saveData = { ...formDataReactive }
    try {
      const res = await saveFunction(saveData)
      if (res) {
        window.$message.success('保存成功')
        emits('saved')
      }
      loading.value = false
    } catch (error) {}
  }
}
// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="80"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-grid>
      <n-form-item-gi label="选择支部" :span="24" path="orgId">
        <!-- <n-cascader
          v-model:value="formDataReactive.orgId"
          :options="organizationListTree"
          placeholder="请选择支部"
          label-field="name"
          value-field="id"
          children-field="children"
          check-strategy="child"
          :show-path="false"
          clearable
          @update:value="handleUpdateValue"
        /> -->
        <n-tree-select
          v-model:value="formDataReactive.orgId"
          :options="organizationListTree"
          value-field="id"
          label-field="name"
          key-field="id"
          children-field="children"
          check-strategy="all"
          placeholder="请选择所属党组织"
          :show-path="false"
          clearable
          filterable
          @update:value="handleUpdateValue"
        />
      </n-form-item-gi>
      <n-form-item-gi label="简介" :span="24" path="content">
        <n-input
          v-model:value="formDataReactive.content"
          type="textarea"
          rows="3"
          placeholder="请输入简介"
          maxlength="40"
          show-count
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi label="支部介绍" :span="24" path="introduce">
        <RichEditor
          v-model:value="formDataReactive.introduce"
          style="width: 100%"
          :rich-height="350"
        />
      </n-form-item-gi>
    </n-grid>
  </n-form>
</template>

<style lang="scss" scoped></style>
