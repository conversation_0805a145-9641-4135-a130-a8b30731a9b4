/*
 * @Description: 资讯分类树相关逻辑
 * @Author: 朱备 <<EMAIL>>
 * @Date: 2021-11-22 15:10:49
 * @LastEditTime: 2021-12-09 16:07:17
 * @LastEditors: 朱备 <<EMAIL>>
 */
import { computed, onBeforeUnmount, ref, watch } from 'vue'
import type { TreeOption } from 'naive-ui'
import { useNewsStore } from '@/store'
import { getFirstSelectableNode } from '@/utils/tree-transform'
import type { INewsColumn } from '@/service'
import { deleteNewsColumn, postNewsColumn, putNewsColumn } from '@/service'
import { sessionCache } from '@/utils/cache'

interface ICurrentNewsColumn {
  id: number
  name: string
  pid: number
}

/**
 * 返回资讯分类树的通用数据逻辑
 * @param callback 点击树节点需要执行的函数
 */
function useNewsColumnTree(callback: () => void) {
  const newsStore = useNewsStore()
  newsStore.getNewsColumnListAction()

  const treeTitle = '分类列表'
  const treeFilterPlaceholder = '搜索'
  const treeRef = ref()
  const treeData = ref()
  const newsColumnList = computed(() => newsStore.newsColumnList)
  // 当前选中的资讯类别
  const currentNewsColumn = ref<ICurrentNewsColumn | undefined>()

  // 添加根节点
  function handleAddRoot() {
    treeRef.value?.addRoot()
  }

  // 选中节点
  function handleSelectNode(node: TreeOption[]) {
    const cate = node[0]
    if (cate) {
      const current = {
        id: cate.key as number,
        name: cate.label ?? '',
        pid: cate.pid as number,
      }
      currentNewsColumn.value = current
      sessionCache.set('current_news_column', current)
      callback()
    }
  }

  // 保存资讯类别
  function handleSaveNode(node: TreeOption) {
    const data: INewsColumn = {
      columnName: node.label ?? '',
      pid: node.pid as number,
    }
    if ((node.key ?? 0) > 0) {
      data.id = node.key as number
    }
    postNewsColumn(data).then(() => {
      newsStore.getNewsColumnListAction()
      window.$message.success('保存成功')
    })
  }

  // 删除资讯类别
  function handleDeleteNode(id: number) {
    deleteNewsColumn(id).then((res) => {
      if (res) {
        window.$message.success('删除成功')
        newsStore.getNewsColumnListAction()
      }
    })
  }

  // 清空当前
  function handleClearCurrent() {
    currentNewsColumn.value = undefined
  }

  // 上移/下移
  function handleMoveNode(id: number, upOrDown: 1 | 2) {
    putNewsColumn(id, { upOrDown }).then((res) => {
      window.$message.success(res)
      newsStore.getNewsColumnListAction()
    })
  }

  watch(newsColumnList, (newV, oldV) => {
    treeData.value = newV
    if (!oldV.length && newV.length) {
      const current = sessionCache.get('current_news_column')
      if (current) {
        currentNewsColumn.value = current
        treeRef.value?.setSelectedKeys([current.id])
      } else {
        const first = getFirstSelectableNode(newV, false)
        if (first) {
          currentNewsColumn.value = {
            id: first.nodeId,
            pid: first.pid ?? 0,
            name: first.nodeName,
          }
          treeRef.value?.setSelectedKeys([first.nodeId])
        }
      }
      callback()
    }
  })

  onBeforeUnmount(() => {
    newsStore.newsColumnList = []
  })

  return {
    treeTitle,
    treeFilterPlaceholder,
    treeRef,
    treeData,
    currentNewsColumn,

    handleAddRoot,
    handleSelectNode,
    handleSaveNode,
    handleDeleteNode,
    handleMoveNode,
    handleClearCurrent,
  }
}

export default useNewsColumnTree
