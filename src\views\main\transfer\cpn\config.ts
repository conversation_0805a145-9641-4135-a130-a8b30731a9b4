import type { FormRules } from 'naive-ui'
import type { VNodeChild } from 'vue'
import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { NodeList } from '@/services/transfer/types'

export const formRules: FormRules = {
  transferType: {
    required: true,
    message: '调动类型不能为空',
    trigger: 'change',
    type: 'number',
  },
  transferTime: {
    required: true,
    message: '调动时间不能为空',
    trigger: 'change',
  },
  paymentTime: {
    required: true,
    message: '党费缴纳时间不能为空',
    trigger: 'change',
  },
  oldDeptId: {
    required: true,
    message: '当前组织不能为空',
    trigger: 'change',
  },
  userId: {
    required: true,
    message: '调动人选择不能为空',
    trigger: 'change',
  },
  newDeptId: {
    required: true,
    message: '目标组织不能为空',
    trigger: 'change',
  },
  reason: {
    required: true,
    message: '调动原因不能为空',
    trigger: 'input',
  },
}

// 审批设置
export const approvalSettingRules: FormRules = {
  orgidList: {
    required: true,
    validator(rule: any, value: any) {
      if (value !== null && value.length > 0) {
        return true
      }
      else {
        return new Error('审批组织不能为空')
      }
    },
    trigger: 'change',
  },

  nodeList: {
    required: true,
    validator(rule: any, value: any) {
      if (value !== null && value.length > 0) {
        if (
          value.some(
            (item: any) =>
              !item.approvalNode
              || item.approvalNode.trim() === ''
              || !item.orgTypeId
              || item.orgTypeId.trim() === ''
              || !item.selectRoleList
              || item.selectRoleList.length === 0,
          )
        ) {
          return new Error('请完整填写审批节点信息')
        }

        return true
      }
      else {
        return new Error('请添加审批人设置')
      }
    },
    trigger: ['input', 'change'],
  },
}

export const formRulesForCheck: FormRules = {
  phaseStatus: {
    required: true,
    message: '审核操作不能为空',
    trigger: 'change',
    type: 'number',
  },
  remark: {
    required: true,
    message: '审核说明不能为空',
    trigger: 'input',
  },
}

// 适用于 审批人设置列
export function getTableColumnsOfApprover(
  approverNodeColumnRenderer: (row: NodeList) => VNodeChild,
  approverRoleColumnRenderer: (row: NodeList) => VNodeChild,
  approverRoleOrganizationRenderer: (row: NodeList) => VNodeChild,
  optionColumnRenderer: (row: NodeList, index: number) => VNodeChild,
): TableColumns<NodeList> {
  return [
    {
      title: '序号',
      key: 'index',
      width: 50,
      render: (_, i) => i + 1,
    },
    {
      key: 'approvalNode',
      title: '审批节点',
      width: 100,
      render: approverNodeColumnRenderer,
    },
    {
      key: 'roleList',
      title: '审批角色',
      width: 300,
      render: approverRoleColumnRenderer,
    },
    {
      key: 'orgTypeId',
      title: '审批角色所属组织',
      width: 120,
      render: approverRoleOrganizationRenderer,
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '60',
      render: optionColumnRenderer,
    },
  ]
}
