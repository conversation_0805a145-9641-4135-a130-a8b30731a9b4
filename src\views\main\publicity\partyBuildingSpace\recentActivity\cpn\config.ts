import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  title: [
    {
      required: true,
      message: '清单标题不能为空',
      trigger: 'input',
    },
  ],
  matter: [
    {
      required: true,
      message: '事项不能为空',
      trigger: 'input',
    },
  ],
  evaluationRequirements: [
    {
      required: true,
      message: '请输入考核要求',
      trigger: 'change',
    },
  ],
  evaluationMode: [
    {
      required: true,
      message: '请输入考核方式',
      trigger: 'change',
    },
  ],
  evaluationScore: [
    {
      required: true,
      validator(rule: any, value: any) {
        if (value && value >= 0) {
          return true
        } else {
          return new Error('请输入考核得分')
        }
      },
      trigger: 'change',
    },
  ],

  fileList: {
    required: true,
    validator(rule: any, value: any) {
      if (value === null) {
        return new Error('请选择文件')
      }
      return true
    },
    trigger: 'change',
  },
}
