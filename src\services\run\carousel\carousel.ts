import type { ICarousel } from './types'
import { commonReq } from '@/services/request'
import type { PaginationRes } from '@/services/types'

/** 获取轮播图列表 */
export function getCarouselList(params: any) {
  return commonReq.get<PaginationRes<ICarousel>>({
    url: '/sys-operation/backend/v1/slider',
    params: {
      title: params.title,
      current: params.pageNum,
      size: params.pageSize,
    },
  })
}

/** 轮播图新增 */
export function addCarousel(data: Object) {
  return commonReq.post<string>({
    url: '/sys-operation/backend/v1/slider',
    data,
  })
}
/** 轮播图修改 */
export function updateCarousel(data: Object) {
  return commonReq.put<string>({
    url: '/sys-operation/backend/v1/slider',
    data,
  })
}

/** 轮播图详情 */
export function getCarouselDetail(id: number) {
  return commonReq.get<ICarousel>({
    url: `/sys-operation/backend/v1/slider/${id}`,
  })
}

/** 删除轮播图 */
export function deleteCarousel(ids: string) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/slider',
    params: {
      ids,
    },
  })
}

/** 轮播图是否发布 */
export function postPublishCarousel(params: { id: string; isRelease: number }) {
  return commonReq.post({
    url: '/sys-operation/backend/v1/slider/release',
    params,
  })
}

/** 移动端是否展示 */
export function postMobileIsShow(params: { id: string; isRelease: number }) {
  return commonReq.post({
    url: '/sys-operation/backend/v1/slider/app_show',
    params,
  })
}

/** 移动端是否展示 */
export function postChangeSort(data: { id: string; sort: number }) {
  return commonReq.post({
    url: '/sys-operation/backend/v1/slider/update-order',
    data,
  })
}
