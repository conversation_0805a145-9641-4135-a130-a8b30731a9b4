<script setup lang="ts">
import { NSwitch } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import AddSpecialRootForm from '../cpn/AddSpecialRootForm.vue'
import AddSpecialSubForm from '../cpn/AddSpecialSubForm.vue'
import { getTableColumns } from './config'
import { useMyTable, useTreeMenu } from '@/hooks'
import {
  deleteSpecialActivity,
  deleteSpecialCategory,
  deleteSpecialColumn,
  getSpecialActivityList,
  getSpecialCategoryList,
  postHiddenSpecialActivity,
  // postRecommendSpecialActivity,
  postSpecialActivityTop,
  postSpecialCategory,
  postSpecialColumn,
  putMoveSpecialCategory,
  putMoveSpecialColumn,
  putSpecialCategory,
  putSpecialColumn,
  updatedJoinRotationPool,
} from '@/services/special'
import DeleteButton from '@/components/DeleteButton.vue'
const router = useRouter()

// 选中菜单名称
const selectName = ref()

// 筛选项：类别id和资讯标题
const filterRef = ref({
  columnId: '',
  title: null,
})

const { treeData, showModalType, moveNode, delNode, saveNode, init }
  = useTreeMenu({
    menuListApi: getSpecialCategoryList,
    moveNodeApi: putMoveSpecialCategory,
    moveChildNodeApi: putMoveSpecialColumn,
    delNodeApi: deleteSpecialCategory,
    delChildNodeApi: deleteSpecialColumn,
    addNodeApi: postSpecialCategory,
    modifyNodeApi: putSpecialCategory,
    addChildNodeApi: postSpecialColumn,
    modifyChildNodeApi: putSpecialColumn,
    refreshTableApi: filterInput,
    multiLevelKey: 'themeColumnVOList',
    labelField: 'name',
    childLabelField: 'columnName',
    maxLevel: 2,
    sessionId: 'specialListCategoryId',
    sessionName: 'specialTitle',
  })

const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getSpecialActivityList, filterRef, {
  batchDeleteTable: true,
  delApi: deleteSpecialActivity,
})

async function switchTop(value: boolean, id: string) {
  await postSpecialActivityTop(id)
  window.$message.success('修改置顶成功')
  loadData()
}

// async function switchRecommend(value: boolean, id: string) {
//   await postRecommendSpecialActivity(id)
//   window.$message.success('修改推荐成功')
//   loadData()
// }
async function switchHidden(value: boolean, id: string) {
  await postHiddenSpecialActivity(id)
  window.$message.success('隐藏成功')
  loadData()
}

// 是否加入首页轮播池
async function joinRotationPool(value: boolean, id: string) {
  await updatedJoinRotationPool(id)
  window.$message.success('操作成功')
  loadData()
}

/** 列表操作 */
const tableColumns = getTableColumns(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
          display: 'flex',
          gap: '15px',
        },
      },
      [
        h(
          'span',
          {
            onClick: () =>
              router.push({
                name: 'specialAdd',
                query: { id: row.id, model: 'view' },
              }),
          },
          { default: () => '查看' },
        ),
        h(
          'span',
          {
            onClick: () =>
              router.push({
                name: 'specialAdd',
                query: { id: row.id, model: 'modify' },
              }),
          },
          { default: () => '编辑' },
        ),

        h(DeleteButton, {
          handleConfirm: () => handleSingleDelete(String(row.id)),
        }),
        // h(
        //   'span',
        //   {
        //     style: { margin: '0 15px' },
        //     // onClick: () =>
        //     //   router.push({
        //     //     name: 'noticeAdd',
        //     //     query: { id: row.id },
        //     //   }),
        //   },
        //   { default: () => '审核' },
        // ),
      ],
    )
  },
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => switchTop(value, row.id),
      value: Boolean(Number(row.isTop)),
    }),
  // row =>
  //   h(NSwitch, {
  //     onUpdateValue: (value: any) => switchRecommend(value, row.id),
  //     value: Boolean(Number(row.isRecommend)),
  //   }),
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => switchHidden(value, row.id),
      value: Boolean(Number(row.isHidden)),
    }),
  // 是否加入轮播池
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => joinRotationPool(value, row.id),
      value: Boolean(Number(row.sliderFlag || 0)),
    }),
)

const showModal = ref(false)
const modalTitle = ref()
const specialRootFormRef = ref()
const specialSubFormRef = ref()
const currentPionnerId = ref('')

// 过滤不想要的字段
// function filterField(obj: any, excludedField: string) {
//   const { [excludedField]: omittedField, ...rest } = obj
//   return rest
// }
// filterField(specialSubFormRef.value.formData.data, 'name')
// 处理弹框需要保存的数据及校验弹框必填项
async function handleFormatterParams() {
  let flag = false
  let categoryId
  if (showModalType.value === 'root') {
    flag = await specialRootFormRef.value.handleValidate()
  }
  if (showModalType.value === 'sub') {
    categoryId = currentPionnerId.value
    flag = await specialSubFormRef.value.handleValidate()
  }
  if (!flag) {
    return
  }
  const data
    = showModalType.value === 'root'
      ? specialRootFormRef.value.formData.data
      : specialSubFormRef.value.formData.data
  saveNode({ ...data, type: showModalType.value, categoryId })
  showModal.value = false
}

/** 新增子节点 */
const parentName = ref()
async function handleAddChildNode(data: any) {
  if (data.type === 'root') {
    modalTitle.value = '新增专题活动'
    showModalType.value = 'root'
  }
  else {
    modalTitle.value = '新增专题栏目'
    showModalType.value = 'sub'
    currentPionnerId.value = data.originData.id
    parentName.value = data.name
  }
  showModal.value = true
  if (data.model === 'modify') {
    // 编辑状态 需要将数据回显
    if (data.isChild) {
      modalTitle.value = '修改专题栏目'
      currentPionnerId.value = data.categoryId
      parentName.value = data.parentName
      nextTick(() => {
        specialSubFormRef.value.handleSetFormData({
          ...data.originData,
          name: parentName.value,
        })
      })
    }
    else {
      modalTitle.value = '修改专题活动'
      nextTick(() => {
        specialRootFormRef.value.handleSetFormData(data.originData)
      })
    }
  }
}

function handleCancel() {
  showModal.value = false
}

// 选中菜单触发的事件
function handleChangeTab(data: any) {
  // console.log('data: ', data)
  const { isChild, label } = data
  if (isChild) {
    filterRef.value.columnId = data.originData.id
    filterRef.value.title = null
    selectName.value = label
    currentPage.value = 1
    window.sessionStorage.setItem('specialListCategoryId', data.originData.id)
    window.sessionStorage.setItem('specialTitle', label)
  }
}

const defaultSelectedKeys = ref<string[]>([])
function filterInput(res: { id: string; name: string }) {
  defaultSelectedKeys.value = [res.id]
  filterRef.value.columnId = res.id
  selectName.value = res.name
}
onMounted(() => {
  init().then((res: any) => {
    defaultSelectedKeys.value = [res.id]
    filterRef.value.columnId = res.id
    selectName.value = res.name
  })
})
/** 点击添加按钮 */
function handleClickAdd() {
  router.push({
    name: 'specialAdd',
    query: { columnId: filterRef.value.columnId, model: 'add' },
  })
}
</script>
<template>
  <layout-container style="height: calc(100vh - 114px)">
    <template #side>
      <SideMenuNew
        v-model:show-modal="showModal"
        title="专题管理"
        :default-selected-keys="defaultSelectedKeys"
        :tree-data="treeData"
        :modal-title="modalTitle"
        @move="moveNode"
        @del-node="delNode"
        @save-tree-node="handleFormatterParams"
        @add-child-node="handleAddChildNode"
        @select-node-key="handleChangeTab"
      />
    </template>
    <template #main>
      <table-container
        v-model:page="currentPage"
        v-model:page-size="pageSize"
        :title="selectName"
        :loading="loading"
        :show-toolbar="false"
        custom-toolbar
        :table-columns="tableColumns"
        :table-data="tableData"
        :total="total"
        :checked-row-keys="checkedRowKeys"
        @click-add="handleClickAdd"
        @click-delete="handleBatchDelete"
        @update-page="onUpdatePage"
        @update-page-size="onUpdatePageSize"
        @update-checked-row-keys="onUpdateCheckedRowKeys"
      >
        <template #btns>
          <n-button size="small" type="primary" @click="handleClickAdd">
            <template #icon>
              <n-icon>
                <plus-round />
              </n-icon>
            </template>
            添加
          </n-button>

          <n-button size="small" @click="handleBatchDelete">
            <template #icon>
              <n-icon>
                <delete-forever-round />
              </n-icon>
            </template>
            删除
          </n-button>
        </template>
        <template #filters>
          <n-input
            v-model:value="filterRef.title"
            style="width: 200px"
            size="small"
            placeholder="请输入活动标题"
            clearable
          />
        </template>
      </table-container>
    </template>
  </layout-container>
  <CustomDialog
    :show="showModal"
    :title="modalTitle"
    width="600px"
    @confirm="handleFormatterParams"
    @cancel="handleCancel"
    @update:show="(v:boolean) => (showModal = v)"
  >
    <div class="p-[20px]">
      <AddSpecialRootForm
        v-show="showModalType === 'root'"
        ref="specialRootFormRef"
      />
      <AddSpecialSubForm
        v-show="showModalType === 'sub'"
        ref="specialSubFormRef"
        :parent-name="parentName"
      />
    </div>
  </CustomDialog>
</template>
<style lang="scss" scoped></style>
