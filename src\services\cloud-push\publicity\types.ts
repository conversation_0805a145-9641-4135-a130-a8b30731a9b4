export interface PublicityListTypes {
  id: string | undefined
  pioneerName: string
  orgId: string
  briefDesc: string
  iconUrl: string
  sort: number
  followers: number
  isHidden: number
  isRecommand: number
  newsNum: number
  pionnerCategoryVOList: PioneerCategoryVOListTypes[]
  // 自定义的一些字段
  key: string
  name: string
  label: string
  editing: boolean
  renderId: string
  parentId: string
  canUp: boolean
  canDown: boolean
  isChild: boolean
  children: any[]
}

export interface PioneerCategoryVOListTypes {
  id: string | undefined
  name: string
  sort: number
  pionnerId: string
  configStudyScore: number
  configCommentScore: number
  categoryDesc?: any
  // 自定义的一些字段
  key: string
  label: string
  editing: boolean
  renderId: string
  parentId: string
  canUp: boolean
  canDown: boolean
  isChild: boolean
}

export interface newsInfoDetailType {
  id?: string
  title: string
  shortTitle: string
  content: string
  coverUrl: string
  categoryId: string
  isTop: number
  isRecommand: number
  sort: number
  reviewed: number
  reviewedUser: string
  reviewedTime: string
}

export interface viewNewsDetailType {
  id?: number | string
  title: string
  shortTitle: string
  // author: string
  publishTime: string | null
  content: string
  coverUrl: string
  categoryId: number | string
  isTop: number
  isRecommand: number
  isHidden: number
  readNum: number | string | null
  commentNum: number
  likeNum: number
  sort: number
  reviewed: number
  reviewedUser: string
  reviewedTime: string
  isOutside: string
  linkUrl?: string
}

export interface newsTableListItem {
  id: string
  title: string
  shortTitle: string
  author: string
  publishTime: string
  content: string
  coverUrl: string
  categoryId: string
  isTop: number
  isRecommand: number
  isHidden?: any
  readNum?: any
  commentNum?: any
  likeNum?: any
  collectNum?: any
  sort: number
  reviewed: number
  reviewedUser: string
  reviewedTime?: any
  isOutside: string
  linkUrl?: string
  reviewedStatus: number
  sliderFlag?: number // 是否加入轮播池
}

// 单个样式类型
export interface StyleItem {
  id: number
  title: string
  code: string
  fieldIdentify: string
  styleContent: string
}

// 当前生效的样式类型
export interface CurrentStyleItem {
  id: number
  title: string
  code: string
  fieldIdentify: string
}
