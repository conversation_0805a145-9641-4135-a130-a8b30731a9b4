import type { VNodeChild } from 'vue'
import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import { NImage } from 'naive-ui'
import type { newsTableListItem } from '@/services/cloud-push/publicity/types'
import { REVIEWED_STATUS, getDictLabelByValue } from '@/store/dict'
export function createColumns(
  operationRender: (row: newsTableListItem) => VNodeChild,
  topRender: (row: newsTableListItem) => VNodeChild,
  // recommendRender: (row: newsTableListItem) => VNodeChild,
  hiddenRender: (row: newsTableListItem) => VNodeChild,
  // 加入首页轮播池
  rotationPool: (row: newsTableListItem) => VNodeChild,
  hasCheckPermission: boolean = true,
): TableColumns<newsTableListItem> {
  const columns: TableColumns<newsTableListItem> = [
    {
      type: 'selection',
      align: 'center',
    },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      title: '资讯标题',
      key: 'title',
      width: '15%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      title: '图片',
      key: 'img',
      width: '100px',
      render: row =>
        row.coverUrl
          ? h(NImage, {
            src: import.meta.env.VITE_API_BASE + row.coverUrl,
            width: '62',
            style: { height: '40px' },
          })
          : '--',
    },
    {
      title: '是否外链',
      key: 'isOutside',
      render: row => (row.isOutside === '1' ? '是' : '否'),
    },
    {
      title: '外链地址',
      key: 'linkUrl',
      width: '10%',
      render: (row) => {
        return row.linkUrl
          ? h(
            'a',
            {
              href: row.linkUrl,
              target: '_blank',
              style: {
                color: '#3f7ee8',
              },
            },
            row.linkUrl,
          )
          : '--'
      },
    },
    {
      title: '阅读量',
      key: 'readNum',
      align: 'center',
    },
    {
      title: '发表时间',
      key: 'publishTime',
      width: '12%',
      render: row => row.publishTime ?? '-',
    },
    {
      title: '是否置顶',
      key: 'isTop',
      render: topRender,
    },
    // {
    //   title: '是否推荐',
    //   key: 'isRecommand',
    //   render: recommendRender,
    // },
    {
      title: '是否隐藏',
      key: 'isHidden',
      render: hiddenRender,
    },
    {
      title: '审核状态',
      // key: 'reviewed',
      key: 'reviewed',
      // render: row => (row.reviewedStatus === 1 ? '已审核' : '未审核'),
      render: (row) => {
        return getDictLabelByValue(REVIEWED_STATUS, row.reviewed)
      },
    },

    {
      title: '操作',
      key: 'operation',
      width: '180px',
      render: operationRender,
    },
  ]

  // 如果有权限，添加"是否加入首页轮播池"列
  if (hasCheckPermission) {
    columns.splice(columns.length - 2, 0, {
      title: '是否加入首页轮播池',
      key: 'sliderFlag',
      render: rotationPool,
    })
  }

  return columns
}
