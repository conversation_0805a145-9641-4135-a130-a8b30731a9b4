import type { UploadFileInfo } from 'naive-ui'

/**
 * @PartyBuildingExamAdd
 * 新增党建清单考核指标项--接口入参
 */
export interface PartyBuildingExamAdd {
  id?: string
  title?: string
  evaluationRequirements: string
  evaluationMode: string
  evaluationScore: number | null
  matter: string
  fileIds: string[]
}

/**
 * @ExamIndIcatorsIds
 * 删除单个党建清单考核指标项--接口入参
 */
export interface ExamIndIcatorsIds {
  ids: string[]
}

/**
 * @EditExamIndIcatorsIdsItem
 * 编辑党建清单考核指标项--接口入参
 */
export interface EditExamIndIcatorsIdsItem {
  id: string
  matter: string
  title?: string
  evaluationRequirements: string
  evaluationMode: string
  evaluationScore: number
  relatedStatus?: boolean
  fileList?: fileItem[]
  fileIds: string[]
}

export interface fileItem {
  fileName: string
  id: string
  original: string
}
export interface uploadFileItem {
  fileId: string
  bucketName: string
  url: string
  fileName: string
}

export interface addAndEditParams {
  id: string
  title?: string
  evaluationRequirements: string
  evaluationMode: string
  evaluationScore: number
  relatedStatus?: boolean
  matter: string
  fileIds: Array<string>
  fileList: Array<fileItem>
}

/** 新增编辑考核事项文件字段定义返回参数 */
export interface uploadFileItemNew extends UploadFileInfo {
  fileName?: string | undefined
  original?: string | undefined
  percent?: number
}
