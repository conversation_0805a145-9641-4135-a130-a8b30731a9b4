import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import { NImage } from 'naive-ui'
import type { CompanySHowItem } from '@/services/publicity/companyShow/types'

export function getTableColumns(
  optionColumnRenderer: (row: CompanySHowItem) => VNodeChild,
  handleUpdateValueRender: (row: CompanySHowItem) => VNodeChild,
): DataTableColumns<CompanySHowItem> {
  return [
    { type: 'selection' },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'appCoverId',
      title: 'APP封面图片',
      render: (row) => {
        const { url } = row.appCover || ''
        return h(NImage, {
          src: import.meta.env.VITE_API_BASE + url,
          style: { width: '100px' },
        })
      },
    },
    // {
    //   key: 'coverId',
    //   title: 'PC封面图片',
    //   render: (row) => {
    //     const { url } = row.cover || ''
    //     return h(NImage, {
    //       src: import.meta.env.VITE_API_BASE + url,
    //       style: { width: '100px' },
    //     })
    //   },
    // },
    {
      key: 'title',
      title: '公司名称',
      render: (row) => {
        return h('span', {
          innerHTML: row.title,
        })
      },
    },
    {
      key: 'publishTime',
      title: '发布时间',
      render: (row) => {
        return h('span', {
          innerHTML: row.publishTime,
        })
      },
    },
    {
      key: 'topStatus',
      title: '置顶',
      render: row => handleUpdateValueRender(row),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
