<script setup lang="ts">
import { NForm } from 'naive-ui'
import { formRules } from './config'
import RichEditor from '@/components/RichEditor.vue'
import {
  addPartyBuildingExamIssue,
  editorPartyBuildingExamIssue,
  getExamIssueDetailInfo,
} from '@/services/publicity/vote/evaluation'
import type { IssueDetailType } from '@/services/publicity/vote/evaluation/type'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<IssueDetailType>({
  matter: '',
  title: '',
  evaluationMode: '',
  evaluationRequirements: '',
  evaluationScore: '',
  dept: '',
  relatedStatus: false,
})
const formRef = ref<InstanceType<typeof NForm>>()

onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getExamIssueDetailInfo(props.id).then((res) => {
      formDataReactive.evaluationMode = res.evaluationMode
      formDataReactive.evaluationRequirements = res.evaluationRequirements
      formDataReactive.evaluationScore = res.evaluationScore
      formDataReactive.matter = res.matter
      formDataReactive.title = res.title
      formDataReactive.id = props.id
      formDataReactive.relatedStatus = res.relatedStatus
      formDataReactive.dept = res.dept
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (formDataReactive.id) {
        editorPartyBuildingExamIssue(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
      else {
        addPartyBuildingExamIssue(formDataReactive).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="90"
    label-align="right"
    label-placement="left"
    :disabled="formDataReactive.relatedStatus"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item label="标题：" path="title">
      <n-input
        v-model:value="formDataReactive.title"
        placeholder="请输入标题内容"
        clearable
      />
    </n-form-item>
    <n-form-item label="考核指标：" path="matter">
      <RichEditor
        v-model:value="formDataReactive.matter"
        style="width: 100%"
        :rich-height="350"
        :disabled="formDataReactive.relatedStatus"
      />
    </n-form-item>
    <n-form-item span="24" label="计分标准：" path="evaluationRequirements">
      <RichEditor
        v-model:value="formDataReactive.evaluationRequirements"
        style="width: 100%"
        :rich-height="350"
        :disabled="formDataReactive.relatedStatus"
      />
    </n-form-item>

    <n-form-item span="24" label="检查材料：" path="evaluationMode">
      <RichEditor
        v-model:value="formDataReactive.evaluationMode"
        style="width: 100%"
        :rich-height="350"
        :disabled="formDataReactive.relatedStatus"
      />
    </n-form-item>
    <n-form-item span="24" label="分值：" path="evaluationScore">
      <n-input-number
        v-model:value="formDataReactive.evaluationScore"
        :min="-10"
        :max="100"
        :show-button="true"
      />
    </n-form-item>
    <n-form-item span="24" label="检查部门：">
      <n-input
        v-model:value="formDataReactive.dept"
        placeholder="请输入检查部门"
        clearable
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
