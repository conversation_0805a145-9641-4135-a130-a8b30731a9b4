<script lang="ts" setup>
import Basic from '../cpn/Basic/Basic.vue' // 会议基本信息
import Material from '../cpn/Material/Material.vue' // 会议资料
// import Notes from '../cpn/Notes/Notes.vue' // 心得笔记
import MeetingSummary from '../cpn/Summary/Summary.vue' // 会议总结
import { emitter } from '@/utils/event-bus'
import unStart from '@/assets/image/meeting/unstart-detail.png'
import doing from '@/assets/image/meeting/doing-detail.png'
import end from '@/assets/image/meeting/end-detail.png'

const route = useRoute()
const id = computed(() => route.query.id as string)
const meetingStatus = computed(() => route.query.status as string)

// 锚点相关
const anchorList = reactive([
  {
    title: '基本信息',
    anchor: 'basic',
  },
  {
    title: '会议资料',
    anchor: 'materials',
  },
  // {
  //   title: '心得笔记',
  //   anchor: 'notes',
  // },
  {
    title: '会议内容',
    anchor: 'summary',
  },
])

const basicRef = ref<HTMLElement | null>(null)
const materialsRef = ref<HTMLElement | null>(null)
const notesRef = ref<HTMLElement | null>(null)
const summaryRef = ref<HTMLElement | null>(null)

const activeAnchor = ref<string | null>('basic')

const scrollTo = (refName: string) => {
  activeAnchor.value = refName
  const targetRef
    = refName === 'basic'
      ? basicRef
      : refName === 'materials'
        ? materialsRef
        : refName === 'notes'
          ? notesRef
          : summaryRef

  if (targetRef.value) {
    targetRef.value.style.scrollMarginTop = '100px'
    targetRef.value.scrollIntoView({ block: 'start', behavior: 'smooth' })
  }
}
const isActive = (anchor: string) => {
  return activeAnchor.value === anchor
}
const emitScrollTop = ref()

emitter.on('my-scroller', (v: any) => (emitScrollTop.value = v))
watch(
  () => emitScrollTop.value,
  (newV) => {
    anchorList.forEach((item) => {
      const element = document.getElementById(item.anchor)
      if (element) {
        const offsetTop = element.offsetTop - 60
        const offsetBottom = offsetTop + element.offsetHeight
        if (newV >= offsetTop && newV < offsetBottom) {
          activeAnchor.value = item.anchor // 更新临时变量
        }
      }
    })
  },
)

watchEffect(() => {
  // 遍历锚点位置，找到当前视口位置应激活的锚点
})

onBeforeUnmount(() => {
  emitter.off('my-scroller')
})
</script>
<template>
  <div>
    <DetailHeader
      :is-show-confirm-btn="false"
      back-name="meetingList"
      header-title="会议管理"
    />

    <div class="flex pb-[500px] relative">
      <div class="fixed left-[100px] top-[220px]">
        <div class="flex flex-col">
          <div class="flex items-start gap-[76px]">
            <div></div>
            <div class="flex flex-col items-center cursor-pointer">
              <div
                class="w-[12px] h-[12px] rounded-[50%] border-2 border-[#d3d8df]"
              ></div>
              <div class="w-[2px] h-[25px] bg-[#f2f3f4]"></div>
            </div>
          </div>
          <div
            v-for="(item, index) in anchorList"
            :key="index"
            class="flex items-start gap-[20px]"
            @click="scrollTo(item.anchor)"
          >
            <div class="mt-[-4px] cursor-pointer">
              <span
                :class="{ 'text-[#AC241D]': isActive(item.anchor) }"
                class="text-[#b1b1b1]"
              >
                {{ item.title }}
              </span>
            </div>
            <div class="flex flex-col items-center cursor-pointer">
              <div
                :class="{ 'bg-[#AC241C]': isActive(item.anchor) }"
                class="w-[12px] h-[12px] rounded-[50%] bg-[#d3d8df]"
              ></div>
              <div
                v-if="index < anchorList.length - 1"
                class="w-[2px] h-[44px] bg-[#f2f3f4]"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex-1 pl-[300px]">
        <div id="basic" ref="basicRef" class="relative">
          <basic :id="id" />
          <div class="absolute top-[10%] right-[10%] w-[130px] h-[130px]">
            <img
              v-if="meetingStatus === '未开始'"
              :src="unStart"
              alt="未开始"
            />
            <img
              v-else-if="meetingStatus === '进行中'"
              :src="doing"
              alt="进行中"
            />
            <img v-else :src="end" alt="已结束" />
          </div>
        </div>
        <div id="materials" ref="materialsRef">
          <material :id="id" />
        </div>
        <!-- <div id="notes" ref="notesRef">
          <notes :id="id" />
        </div> -->
        <div id="summary" ref="summaryRef">
          <meeting-summary :id="id" />
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped></style>
