import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import { NCollapse, NCollapseItem, NIcon, NImage } from 'naive-ui'
import { KeyboardArrowDownSharp } from '@vicons/material'
import type { CompanySHowItem } from '@/services/publicity/companyShow/types'
import { downloadFile } from '@/utils/downloader'

function truncatedText(str: string) {
  const cleanedText = str.replace(/<.*?>/g, '')
  return cleanedText.slice(0, 20)
}

export function getTableColumns(
  optionColumnRenderer: (row: CompanySHowItem) => VNodeChild,
  handleUpdateValueRender: (row: CompanySHowItem) => VNodeChild,
): DataTableColumns<CompanySHowItem> {
  return [
    { type: 'selection' },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'title',
      title: '主题',
    },
    {
      key: 'module',
      title: '内容',
      render: (row) => {
        return h('span', {
          innerHTML: `${truncatedText(row.module)}...`,
        })
      },
      //  ellipsis: {
      //   tooltip: {
      //     contentStyle: { width: '400px', 'word-break': 'break-all' },
      //   },
      // },
    },
    {
      key: 'cover',
      title: '图片',
      render: (row) => {
        const { url } = row.cover || ''
        return h(NImage, {
          src: url ? import.meta.env.VITE_API_BASE + url : '',
          style: { width: '100px' },
        })
      },
    },
    {
      key: 'fileList',
      title: '附件',
      render: (row) => {
        return row.fileList?.length
          ? h(
            NCollapse,
            {
              arrowPlacement: 'right',
            },
            [
              h(
                NCollapseItem,
                {
                  disabled: row.fileList.length === 1,
                },
                {
                  header: () =>
                    h(
                      'div',
                      {
                        style: {
                          marginBottom: '2px',
                          cursor: 'pointer',
                          color: '#3f7ee8',
                        },
                      },
                      h(
                        'span',
                        {
                          onClick: (e: Event) => {
                            downloadFile(
                              row.fileList?.[0]?.url,
                              row.fileList?.[0]?.original,
                            )
                            e.stopPropagation()
                          },
                        },
                        row.fileList?.[0]?.original,
                      ),
                    ),
                  arrow: () =>
                    h(
                      NIcon,
                      row.fileList?.length === 1
                        ? ''
                        : () => h(KeyboardArrowDownSharp),
                    ),
                  default: () =>
                    row.fileList?.slice(1)
                      && row.fileList?.slice(1).map((item) => {
                        return h(
                          'div',
                          {
                            style: {
                              marginBottom: '2px',
                              cursor: 'pointer',
                              color: '#3f7ee8',
                            },
                          },
                          h(
                            'span',
                            {
                              onClick: (e: Event) => {
                                downloadFile(item.url, item.original)
                                e.stopPropagation()
                              },
                            },
                            item?.original,
                          ),
                        )
                      }),
                },
              ),
            ],
          )
          : h('span', {}, { default: () => '--' })
      },
    },
    {
      key: 'publishTime',
      title: '发布时间',
      render: (row) => {
        return h('span', {
          innerHTML: row.publishTime,
        })
      },
    },
    {
      key: 'topStatus',
      title: '是否置顶',
      render: row => handleUpdateValueRender(row),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
