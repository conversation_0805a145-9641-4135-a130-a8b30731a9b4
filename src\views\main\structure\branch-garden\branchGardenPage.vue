<script setup lang="ts">
import { NButton } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import { getTableColumns } from './config'
import BranchForm from './cpn/BranchForm.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import {
  deleteBranchGardenItem,
  getBranchGardenTableList,
} from '@/services/structure/branch-garden'

const router = useRouter()
const filterReactive = ref<{ content: string }>({
  content: '',
})
// 有接口后添加：loading,tableData
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getBranchGardenTableList, filterReactive, {
  batchDeleteTable: true,
  delApi: deleteBranchGardenItem,
  delType: 0,
})

// 新增/编辑党建清单抽屉
const idEditRef = ref()
const addListFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('支部', handelConfirmEdit)
/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addListFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addListFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

watch(filterReactive.value, () => {
  loadData()
})

/** 点击添加按钮 */
// function handleClickAdd() {
//   router.push({ name: 'addBranchGardenIndex', query: { id: '-1' } })
// }

/** 跳转至支部展示 */
function handleJumpBranchShow(id: string, orgName: string) {
  router.push({
    name: 'my-branch-show',
    query: {
      id,
      orgName,
    },
  })
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          idEditRef.value = row.id
          editTypeRef.value = 'modify'
          showEditRef.value = true
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '编辑',
      },
    ),
    h(
      NButton,
      {
        onClick: () => handleJumpBranchShow(String(row.id), row.orgName),
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '支部展示',
      },
    ),
    h(DeleteButton, {
      handleConfirm: () => handleSingleDelete(String(row.id)),
    }),
  ]
})

onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="支部园地"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :show-delete="false"
    :loading="loading"
    :checked-row-keys="checkedRowKeys"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button>
    </template>
    <template #filters>
      <n-input
        v-model:value="filterReactive.content"
        size="small"
        placeholder="请输入简介"
        clearable
      />
    </template>
  </table-container>

  <!-- 新增清单抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="600" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <branch-form
        :id="idEditRef"
        ref="addListFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<style lang="scss" scoped></style>
