<script setup lang='ts'>
import { NButton, NSwitch } from 'naive-ui'
import { PlusRound } from '@vicons/material'
import { getTableColumns } from './config'
import VersionForm from './cpn/VersionForm.vue'
import { useMyTable } from '@/hooks'
import { deleteVersion, getVersionList, postChangeStatus } from '@/services/system/Version'
import DeleteButton from '@/components/DeleteButton.vue'
const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getVersionList, ref({}), {
  batchDeleteTable: false,
  delApi: deleteVersion,
})
const versionFormRef = ref()
const showDialogRef = ref(false)
const editTypeRef = ref<'add' | 'modify'>('add')
const dialogTitle = computed(() =>
  editTypeRef.value === 'add' ? '发布版本' : '编辑版本',
)
const idEditRef = ref()
function handleClickAdd() {
  editTypeRef.value = 'add'
  showDialogRef.value = true
}
// 点击编辑
function handleClickEdit(id: string) {
  idEditRef.value = id
  editTypeRef.value = 'modify'
  showDialogRef.value = true
}
// 点击保存
function handleConfirm() {
  versionFormRef.value?.validateAndSave()
}
// 保存成功
function handleSaved() {
  showDialogRef.value = false
  loadData()
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          handleClickEdit(String(row.id))
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '编辑',
      },
    ),
    h(DeleteButton, {
      style: {
        marginRight: '10px',
      },
      handleConfirm: () => handleSingleDelete(String(row.id)),
    }),

  ]
}, (row) => {
  return h(NSwitch, {
    checkedValue: 1,
    uncheckedValue: 0,
    value: row.status,
    loading: row.loading,
    onUpdateValue() {
      row.loading = true
      const params = {
        id: String(row.id),
        updateStatus: row.status === 1 ? 0 : 1,
      }
      postChangeStatus(params)
        .then((res) => {
          row.status = res
        })
        .catch(() => {})
        .finally(() => {
          loadData()
          row.loading = false
        })
    },
  })
})

onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :loading="loading"
    title="版本管理"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        新增
      </n-button>
    </template>
  </table-container>

  <custom-dialog
    v-model:show="showDialogRef"
    :title="dialogTitle"
    width="550px"
    @confirm="handleConfirm"
  >
    <version-form
      :id="idEditRef"
      ref="versionFormRef"
      :type="editTypeRef"
      @saved="handleSaved"
    />
  </custom-dialog>
</template>
<style lang='scss' scoped>

</style>
