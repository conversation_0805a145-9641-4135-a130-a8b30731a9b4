<script setup lang="ts">
import { tableColumns } from './config'
import { useMyTable } from '@/hooks'
import { getPartyMeetingList } from '@/services/affairs/party-building-list-branch'
import type { BranchPartyMeetingList } from '@/services/affairs/party-building-list-branch/types'
import { MEETING_TYPE } from '@/store/dict'
import { useMeetingVoteChoicesStore } from '@/store/meeting-vote-choice/meeting-vote-choice'

const filterReactive = ref({
  meetingType: null,
  startTime: null,
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(
  getPartyMeetingList,
  filterReactive,
  {
    batchDeleteTable: false,
  },
  false,
  ref(true),
)
const meetingVoteStore = useMeetingVoteChoicesStore()
const checkMeeting = ref<BranchPartyMeetingList[]>([])
checkMeeting.value = meetingVoteStore.checkMeeting

const allMeetingData = ref<BranchPartyMeetingList[]>([])
async function loadAllMeetingData() {
  try {
    loading.value = true
    const { records } = await getPartyMeetingList({
      meetingType: null,
      startTime: null,
      pageNum: 1,
      pageSize: 9999,
    })

    allMeetingData.value = records.map((item) => {
      return {
        ...item,
        relatedStatus: checkMeeting.value.some(check => check.id === item.id),
      }
    })
    meetingVoteStore.setAllMeetingList(allMeetingData.value)
  }
  catch (e) {
  }
  finally {
    loading.value = false
  }
}

/** 选中的行id */
const meetingCheckedRowKeys = ref<Array<number | string>>(
  checkMeeting.value.map(item => item.id) || [],
)

// /** 行选中 */
function onUpdateMeetingCheckedRowKeys(ids: Array<number | string>) {
  meetingCheckedRowKeys.value = ids
}

const emits = defineEmits<{
  (e: 'update:value', value: number[]): void
}>()

watch(meetingCheckedRowKeys, (newV: Array<string | number>) => {
  emits('update:value', newV as number[])
})

watch(filterReactive.value, (newV) => {
  if (newV) {
    loadData()
  }
})

onMounted(() => {
  loadData()
  loadAllMeetingData()
})
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :loading="loading"
    class="relation-table"
    :show-title="false"
    :show-add="false"
    :show-delete="false"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="meetingCheckedRowKeys"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateMeetingCheckedRowKeys"
  >
    <template #filters>
      <n-select
        v-model:value="filterReactive.meetingType"
        style="width: 260px"
        placeholder="请选择会议类型"
        filterable
        :options="MEETING_TYPE"
        clearable
      />
      <n-date-picker
        v-model:formatted-value="filterReactive.startTime"
        style="width: 260px"
        placeholder="请选择开始时间"
        clearable
        type="datetime"
        @update:formatted-value="
          (v:any) => (filterReactive.startTime = v)
        "
      />
    </template>
  </table-container>
</template>
<style lang="scss" scoped>
.relation-table:deep(.n-data-table .n-data-table-thead) {
  display: contents;
}
</style>
