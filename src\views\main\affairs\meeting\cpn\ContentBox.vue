<script setup lang="ts">
import { defineProps } from 'vue'
const props = defineProps<{
  title: string
}>()
</script>
<template>
  <div class="flex flex-row mt-[40px] mb-[20px] justify-start items-center">
    <span class="text-[14px] font-bold leading-[14px]">{{ props.title }}</span>
    <span class="line" />
    <slot name="btn" />
  </div>
  <slot />
</template>
<style lang="scss" scoped>
.line {
  margin-left: 20px;
  width: 80%;
  display: inline-block;
  border-top: 0.5px solid #ccc;
  font-weight: 100;
  margin-right: 10px;
}
</style>
