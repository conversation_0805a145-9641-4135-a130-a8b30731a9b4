<script setup lang='ts'>
import ChangeOrganize from '../cpn/ChangeOrganize.vue'
import { getTableColumns } from './config'
import { useMyTable } from '@/hooks/use-my-table'
import { getOrganizeMemberList } from '@/services/organize-garden'
const deptId = ref()

const handleChangeOrg = (value: string) => {
  deptId.value = value
  // console.log(value)
}
const filterReactive = ref({
  name: '',
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  onUpdatePage,
  onUpdatePageSize,
  loadData,
} = useMyTable(getOrganizeMemberList, filterReactive, {
  batchDeleteTable: false,
})

const tableColumns = getTableColumns()

const mockData = [
  {
    name: '张三',
    partyAge: '10',
    partyPosition: '支部书记',
    joinPartyTime: '2023.2',
  },
  {
    name: '张三',
    partyAge: '10',
    partyPosition: '支部书记',
    joinPartyTime: '2023.2',
  },
  {
    name: '张三',
    partyAge: '10',
    partyPosition: '支部书记',
    joinPartyTime: '2023.2',
  },
]

onMounted(() => {
  loadData()
})
</script>
<template>
  <div class="w-full px-[20px] py-[20px]">
    <ChangeOrganize @change-org="handleChangeOrg" />
    <table-container
      v-model:page="currentPage"
      v-model:page-size="pageSize"
      style="padding:25px 0"
      :loading="loading"
      title="组织成员"
      :show-add="false"
      :show-delete="false"
      :table-columns="tableColumns"
      :table-data="mockData"
      :total="total"
      @update-page="onUpdatePage"
      @update-page-size="onUpdatePageSize"
    >
      <template #btns>
        <n-input v-model:value="filterReactive.name" placeholder="请输入姓名" clearable />
      </template>
    </table-container>
  </div>
</template>
<style lang='scss' scoped>
</style>
