<script setup lang="ts">
import OptionsBar from '../cpns/OptionsBar.vue'
import { useData } from '../cpns/use-data'
import { columns } from './config'

const { getData, total, pageNum, pageSize } = useData(apiFn)
function apiFn() {}
// import { usePagination } from '@/hooks/use-pagination'
// const { total, pageNum, pageSize } = usePagination(loadData, 10)
// const targetObj = ref<any>({})

// function getData(value: string, key: string) {
//   targetObj.value[key] = value
//   for (const key in targetObj.value) {
//     if (targetObj.value[key] === null) {
//       delete targetObj.value[key]
//     }
//   }
//   loadData()
// }
// function loadData() {

// }
</script>
<template>
  <div class="pt-[25px] px-[20px]">
    <p class="text-[14px] font-[600] leading-[20px]">
      缴费记录
    </p>
    <p
      class="bg-[#FFFAE1] rounded-[3px] text-[12px] font-[400] py-[11px] pl-[13px] text-[#7C5125] border-[1px] border-[#F5DD8C] mt-[26px]"
    >
      当前已选数据中，已缴费金额<span>500</span>元
    </p>
    <OptionsBar :get-fn="getData" :default-button="true" />
    <n-data-table :columns="columns" class="mt-[20px]" />
    <div class="flex items-center justify-between mt-[30px] pr-[2px]">
      <span class="text-[#BDBDBD] text-[12px] mr-[30px]">共
        <span class="text-[#262626] mx-[6px]">{{ total }}</span>
        条</span>
      <n-pagination
        v-model:page="pageNum"
        v-model:page-size="pageSize"
        :page-sizes="[5, 10, 20, 30, 50]"
        show-quick-jumper
        show-size-picker
        :item-count="total"
      />
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
