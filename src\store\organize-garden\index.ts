import { defineStore } from 'pinia'

export const useOrganizeGardenStore = defineStore('organizeGarden', () => {
  const deptId = ref()
  const deptName = ref()

  function setDeptId(id: string) {
    deptId.value = id
  }

  function getDeptId() {
    return deptId.value
  }

  function setDeptName(name: string) {
    deptName.value = name
  }

  function getDeptName() {
    return deptName.value
  }

  return {
    deptId,
    deptName,
    setDeptId,
    setDeptName,
    getDeptId,
    getDeptName,
  }
})
