import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { VNodeChild } from 'vue'
import { NImage } from 'naive-ui'
import type { VRListItem } from '@/services/vr/types'

export function getTableColumns(
  operationRender: (row: VRListItem) => VNodeChild,
  jumpRender: (row: VRListItem) => VNodeChild,
  hiddenRender: (row: VRListItem) => VNodeChild,
  topRender: (row: VRListItem) => VNodeChild,
): TableColumns<VRListItem> {
  return [
    {
      type: 'selection',
      align: 'center',
    },
    {
      title: '线上展馆标题',
      key: 'title',
    },
    {
      title: '图片',
      key: 'img',
      render: row =>
        h(NImage, {
          src: import.meta.env.VITE_API_BASE + row.coverUrl,
          width: '62',
          style: { height: '40px' },
        }),
    },
    {
      title: '链接地址',
      key: 'linkUrl',
      width: '15%',
      render: (row) => {
        return row.linkUrl
          ? h(
            'a',
            {
              href: row.linkUrl,
              target: '_blank',
              style: {
                color: '#3f7ee8',
              },
            },
            row.linkUrl,
          )
          : '--'
      },
    },
    {
      title: '发表时间',
      key: 'createTime',
      render: row => row.createTime ?? '-',
    },
    {
      title: '跳转到外部网页',
      key: 'isOutside',
      render: jumpRender,
    },
    {
      title: '是否隐藏',
      key: 'isHidden',
      render: hiddenRender,
    },
    {
      title: '是否置顶',
      key: 'top',
      render: topRender,
    },
    {
      title: '操作',
      key: 'operation',
      width: '15%',
      render: operationRender,
    },
  ]
}
