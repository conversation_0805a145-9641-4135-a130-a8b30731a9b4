/*
 * @Description: 日期时间格式化
 */
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import customParseFormat from 'dayjs/plugin/customParseFormat'

dayjs.extend(utc)
dayjs.extend(customParseFormat)

const DEFAULT_FORMAT = 'YYYY-MM-DD HH:mm'

/**
 * 格式化时间戳
 * @param timeStamp 时间戳
 * @param format 目标格式，默认为'YYYY-MM-DD HH:mm'
 * @returns 格式化后的时间字符串
 */
export function formatTimeStamp(
  timeStamp: number,
  format: string = DEFAULT_FORMAT,
): string {
  return dayjs(timeStamp).format(format)
}

/**
 * 格式化UTC时间
 * @param utcTimeString utc时间字符串
 * @param format 目标格式，默认为'YYYY-MM-DD HH:mm'
 * @returns 格式化后的时间字符串
 */
export function formatUTCString(
  utcTimeString: string,
  format: string = DEFAULT_FORMAT,
): string {
  return dayjs.utc(utcTimeString).format(format)
}

/**
 * 格式化自定义格式的时间
 * @param timeString 要转换的时间字符串
 * @param originalFormat 原先的格式
 * @param format 目标格式，默认为'YYYY-MM-DD HH:mm'
 * @returns 格式化后的时间字符串
 */
export function formatCustomTimeString(
  timeString: string,
  originalFormat: string,
  format: string = DEFAULT_FORMAT,
): string {
  if (!timeString || timeString.length !== originalFormat.length) {
    return ''
  }
  return dayjs(timeString, originalFormat).format(format)
}
