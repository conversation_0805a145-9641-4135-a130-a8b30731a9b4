import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import { NCollapse, NCollapseItem, NEllipsis, NIcon } from 'naive-ui'
import { KeyboardArrowDownSharp } from '@vicons/material'
import { downloadFile } from '@/utils/downloader'
import type { AllTargetItemListRowBranch } from '@/services/affairs/discipline-inspection-list-branch/types'

export function getTableColumns(
  optionColumnRenderer: (row: AllTargetItemListRowBranch) => VNodeChild,
): DataTableColumns<AllTargetItemListRowBranch> {
  return [
    {
      key: 'index',
      title: '序号',
      width: '4%',
      align: 'center',
      render: (_, i) => i + 1,
    },
    // {
    //   key: 'title',
    //   title: '标题',
    //   width: '12%',
    //   ellipsis: {
    //     tooltip: {
    //       contentStyle: { width: '400px', 'word-break': 'break-all' },
    //     },
    //   },
    // },
    {
      key: 'matter',
      title: '事项',
      width: '14%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'evaluationRequirements',
      title: '工作要求',
      width: '14%',
      render: (row) => {
        return h(
          NEllipsis,
          {
            expandTrigger: 'click',
            lineClamp: '1',
            tooltip: {
              contentStyle: { width: '400px', 'word-break': 'break-all' },
            },
          },
          {
            default: () => {
              return h('div', {
                style: {
                  width: '100%',
                },
                innerHTML: row.evaluationRequirements,
              })
            },
          },
        )
      },
    },
    {
      key: 'evaluationMode',
      title: '考核方式',
      width: '14%',
      render: (row) => {
        return h(
          NEllipsis,
          {
            expandTrigger: 'click',
            lineClamp: '1',
            tooltip: {
              contentStyle: { width: '400px', 'word-break': 'break-all' },
            },
          },
          {
            default: () => {
              return h('div', {
                style: {
                  width: '100%',
                },
                innerHTML: row.evaluationMode,
              })
            },
          },
        )
      },
    },
    {
      key: 'fileList',
      title: '附件',
      width: '14%',
      render: (row) => {
        return row.fileList?.length
          ? h(
            NCollapse,
            {
              arrowPlacement: 'right',
            },
            [
              h(
                NCollapseItem,
                {
                  disabled: row.fileList.length === 1,
                },
                {
                  header: () =>
                    h(
                      'div',
                      {
                        style: {
                          marginBottom: '2px',
                          cursor: 'pointer',
                          color: '#3f7ee8',
                        },
                      },
                      h(
                        'span',
                        {
                          onClick: (e: Event) => {
                            downloadFile(
                              row.fileList?.[0]?.fileName,
                              row.fileList?.[0]?.original,
                            )
                            e.stopPropagation()
                          },
                        },
                        row.fileList?.[0]?.original,
                      ),
                    ),
                  arrow: () =>
                    h(
                      NIcon,
                      row.fileList?.length === 1
                        ? ''
                        : () => h(KeyboardArrowDownSharp),
                    ),
                  default: () =>
                    row.fileList?.slice(1)
                      && row.fileList?.slice(1).map((item) => {
                        return h(
                          'div',
                          {
                            style: {
                              marginBottom: '2px',
                              cursor: 'pointer',
                              color: '#3f7ee8',
                            },
                          },
                          h(
                            'span',
                            {
                              onClick: (e: Event) => {
                                downloadFile(item.fileName, item.original)
                                e.stopPropagation()
                              },
                            },
                            item.original,
                          ),
                        )
                      }),
                },
              ),
            ],
          )
          : h('span', {}, { default: () => '--' })
      },
    },
    {
      key: 'evaluationScore',
      title: '考核得分',
      width: '8%',
      align: 'center',
    },
    {
      key: 'endTime',
      title: '截止日期',
      width: '8%',
      render: row => row.endTime ?? '--',
    },
    {
      key: 'submitStatus',
      title: '状态',
      width: '14%',
      align: 'center',
      render: (row) => {
        return h(
          'span',
          {
            style: {
              color:
                row.submitStatus === '未提交'
                  ? 'red'
                  : row.submitStatus === '已提交'
                    ? 'green'
                    : 'gray',
            },
          },
          row.submitStatus,
        )
      },
    },
    {
      key: 'performance',
      title: '完成情况',
      width: '7%',
      align: 'center',
      render: row => row.performance ?? '--',
    },
    {
      key: 'score',
      title: '得分',
      width: '7%',
      align: 'center',
      render: row => row.score ?? '--',
    },
    {
      key: 'action',
      title: '操作',
      align: 'center',
      width: '10%',
      render: row => optionColumnRenderer(row),
    },
  ]
}
