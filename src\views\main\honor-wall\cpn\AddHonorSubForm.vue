<script setup lang="ts">
import { honorSubFormRules } from './ruleConfig'
import { uploadImg } from '@/services'
import type { uploadFileItem } from '@/services/types'
// import type { uploadFileItem } from '@/services/types'
// import { uploadImg } from '@/services'
interface Props {
  parentName: string
}
const props = defineProps<Props>()

const honorSubRef = ref()
const formData = reactive({
  data: {
    name: '',
    parentName: props.parentName,
    briefDesc: '',
    coverUrl: '',
    configStudyScore: '',
  },
})

function handleValidate() {
  return new Promise((resolve, reject) => {
    honorSubRef.value?.validate((errors: any) => {
      if (!errors) {
        resolve(true)
      }
      else {
        resolve(false)
      }
    })
  })
}

function handleSetFormData(data: any) {
  formData.data = data
}

// /**
//  * 上传图片
//  * @param {any} file:File
//  */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (!formData.data.coverUrl) {
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formData.data.coverUrl = data.url
      }
    }
  }
  catch (error) {}
}

// /**
//  * 删除图片
//  */
function handleCoverDelete() {
  formData.data.coverUrl = ''
}

defineExpose({
  formData,
  handleValidate,
  handleSetFormData,
})
</script>
<template>
  <div>
    <n-form
      ref="honorSubRef"
      label-placement="left"
      label-width="110px"
      require-mark-placement="left"
      :model="formData.data"
      :rules="honorSubFormRules"
    >
      <n-form-item label="父级分类名称" path="parentName">
        <n-input v-model:value="formData.data.parentName" disabled clearable />
      </n-form-item>

      <n-form-item label="荣誉墙分类名称" path="name">
        <n-input v-model:value="formData.data.name" clearable />
      </n-form-item>

      <n-form-item label="专题栏目图片：" path="coverUrl">
        <ImgUploader
          v-model:oldImgUrl="formData.data.coverUrl"
          :width="350"
          :height="278"
          :need-cropper="false"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </n-form-item>
      <!-- <n-form-item label="荣誉墙分类简介" path="briefDesc">
        <n-input
          v-model:value="formData.data.briefDesc"
          type="textarea"
          show-count
          clearable
        />
      </n-form-item> -->
      <!-- 学习学分 -->
      <!-- <n-form-item label="学习学分：" path="configStudyScore">
        <n-input-number
          v-model:value="formData.data.configStudyScore"
          clearable
          style="width: 100%"
        />
      </n-form-item> -->
    </n-form>
  </div>
</template>

<style scoped lang="scss"></style>
