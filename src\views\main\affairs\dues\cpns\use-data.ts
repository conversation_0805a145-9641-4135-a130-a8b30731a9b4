import { usePagination } from '@/hooks/use-pagination'
export function useData(apiFn: any) {
  const { total, pageNum, pageSize } = usePagination(loadData, 20)
  const targetObj = ref<any>({})
  const data = ref<any>({})
  function getData(value: string, key: string) {
    targetObj.value[key] = value
    for (const key in targetObj.value) {
      if (targetObj.value[key] === null) {
        delete targetObj.value[key]
      }
    }
    loadData()
  }
  async function loadData() {
    const res = await apiFn()
    data.value = res
  }

  return {
    getData,
    data,
    total,
    pageNum,
    pageSize,
  }
}
