export interface OrganizationDetailItem {
  id: string
  deptId?: string
  parentName: string | null
  parentId?: string
  name: string
  abbreviation?: string
  orgType: string | null
  orgTypeName?: string
  code: string
  summary: string
  sortOrder: number
  coverUrl: string
}

export interface OrganizationItemTableItem {
  id: string
  parentId: string
  afterParentDeptId: string
  parentName: string
  weight: number
  orgType: String
  level: number
  name: string
  org_type: string
  createTime: string
  code: string
  children?: OrganizationItemTableItem[]
}

export interface ManageLeaderDto {
  id?: string
  userId: string | null
  trueName: string
  position: string
  deptId: string
  deptName: string
  updateTime?: string
}

// 领导班子用户types
export interface ManageLeaderUsersTypes {
  id: string
  roleCode: string
  userId: string
  trueName: string
  position: string
  deptId: string
  deptName: string
  updateTime: string
}

// 领导班子详情types
export interface ManageLeaderDetailTypes {
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
  id: string
  delFlag: string
  orgId: string
  roleCode: string
  users: ManageLeaderUsersTypes[]
}

// 添加成员时，需要该组织下的所有成员
export interface OrganizationUsersTypes {
  userId: string
  deptId: string
  trueName: string
  partyIdentity: string
  deptName: string
}
