<template>
  <div class="library-specific">
    <edit-top>
      <template #left>
        <n-button
          size="small"
          @click="() => $router.replace({ name: 'questionList' })"
        >
          <template #icon>
            <n-icon size="16">
              <arrow-back-ios-new-round />
            </n-icon>
          </template>
          返回
        </n-button>
      </template>
      <template #mid>
        题库：{{ libraryName }}
      </template>
    </edit-top>

    <table-container
      :show-title="false"
      custom-toolbar
      :show-toolbar="false"
      :total="totalRef"
      :loading="loadingRef"
      :table-columns="tableColumns"
      :table-data="examinationQuestionList"
      @row-checked="handleRowChecked"
      @page-change="handlePageChange"
    >
      <template #btns>
        <n-button size="small" type="primary" @click="handleClickAdd">
          <template #icon>
            <n-icon size="16">
              <add />
            </n-icon>
          </template>
          添加
        </n-button>
        <n-button size="small" @click="handleClickDelete">
          <template #icon>
            <n-icon size="16">
              <delete />
            </n-icon>
          </template>
          删除
        </n-button>
        <n-button size="small" @click="handelImportFn">
          <template #icon>
            <n-icon>
              <document-import />
            </n-icon>
          </template>
          导入
        </n-button>
        <n-button size="small" @click="handleExport">
          <n-icon style="margin-right: 6px" size="14">
            <export />
          </n-icon>
          导出
        </n-button>
      </template>
      <template #filters>
        <div class="item">
          <n-input
            v-model:value="questionNameRef"
            style="width: 15vw"
            size="small"
            clearable
            placeholder="请输入题干名称"
            @input="handleFilter"
            @clear="handleClearQuestionName"
          >
            <template #suffix>
              <n-icon style="cursor: pointer" @click="handleFilter">
                <search />
              </n-icon>
            </template>
          </n-input>
        </div>
        <div class="item">
        </div>
      </template>
    </table-container>

    <n-drawer v-model:show="showEditRef" :width="668" :mask-closable="false">
      <n-drawer-content closable>
        <template #header>
          <n-radio-group v-model:value="questionTypeRef">
            <n-radio
              v-for="item in questionTypes"
              :key="item.label"
              class="question-type"
              :value="item.value"
            >
              {{ item.label }}
            </n-radio>
          </n-radio-group>
        </template>

        <n-scrollbar
          style="max-height: calc(100vh - 155px); padding-right: 20px"
        >
          <question-form
            :id="idEditingRef"
            :key="formKeyRef"
            ref="questionFormRef"
            v-model:question-type="questionTypeRef"
            :edit-type="editTypeRef"
            @saved="handleQuestionSaved"
          />
        </n-scrollbar>

        <template #footer>
          <n-button type="primary" @click="() => handleClickSave()">
            保存
          </n-button>
          <n-button
            v-if="editTypeRef === 'add'"
            @click="() => handleClickSave(true)"
          >
            保存并继续
          </n-button>
          <n-button @click="handleClickCancel">
            取消
          </n-button>
        </template>
      </n-drawer-content>
    </n-drawer>
    <custom-dialog
      v-model:show="showTopic"
      :show-action="false"
      title="导入题目"
    >
      <div class="flex justify-center items-center h-[100px]">
        <div class="flex gap-[30px]">
          <n-button size="small" @click="handleClickDownloadTemplate">
            <template #icon>
              <n-icon>
                <document-import />
              </n-icon>
            </template>
            下载模板
          </n-button>
          <n-upload
            accept=".xls,.xlsx"
            :show-file-list="false"
            @change="handleQuestionFileChange"
          >
            <n-button size="small">
              <template #icon>
                <n-icon>
                  <document-import />
                </n-icon>
              </template>
              导入题目
            </n-button>
          </n-upload>
        </div>
      </div>
    </custom-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, h, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import type { DataTableColumns, UploadFileInfo } from 'naive-ui'
import {
  NButton,
  NDrawer,
  NDrawerContent,
  NIcon,
  NInput,
  NPopconfirm,
  NRadio,
  NRadioGroup,
  NScrollbar,
  NUpload,
} from 'naive-ui'
import { ArrowBackIosNewRound } from '@vicons/material'
import { Add, Delete, DocumentImport, Export, Search } from '@vicons/carbon'
import TableContainer from '../../library/list/TableContainer.vue'
import QuestionForm from './cpn/question-form/QuestionForm.vue'
import EditTop from '@/components/EditTop.vue'
import useExaminationStore from '@/store/examination/index'
import useTableContainer from '@/hooks/use-table-container'
import type {
  IExaminationQuestion,
  IExaminationQuestionQuery,
} from '@/services/examination/index'
import {
  deleteExaminationQuestion,
  exportExaminationQuestion,
  importExaminationQuestion,
} from '@/services/examination/index'
import { formatCustomTimeString } from '@/utils/date-time-format'
import { questionTypes } from '@/views/main/examination/config'
import { useDrawerEdit } from '@/hooks/use-drawer-edit'
import uuid from '@/utils/uuid'
import { downloadArrayBuffer } from '@/utils/downloader'

const route = useRoute()
const examinationStore = useExaminationStore()
const examinationQuestionList = computed(
  () => examinationStore.examinationQuestionList,
)
const formKeyRef = ref(uuid())
const isContinueRef = ref(false)
const libraryId = computed(() => route.params.libraryId)
const libraryName = decodeURIComponent(route.query.lib as string)
const questionFormRef = ref()
const questionTypeRef = ref(1)
const idEditingRef = ref(0)
const questionNameRef = ref('')
const needRefreshRef = ref(false) // 是否需要刷新列表

const {
  loadingRef,
  totalRef,
  currentPageRef,
  pageSizeRef,
  handlePageChange,
  handleRowChecked,
  handleClickDelete,
  idsChecked,
} = useTableContainer(loadExaminationQuestionList, deleteExaminationQuestion)
const { showEditRef, editTypeRef, handleClickCancel } = useDrawerEdit(
  '',
  () => {
    // console.log('confirm')
  },
)

// 加载题目列表
function loadExaminationQuestionList() {
  if (!loadingRef.value && currentPageRef.value) {
    loadingRef.value = true
    // 生成参数
    const params: IExaminationQuestionQuery = {
      libraryId: libraryId.value,
      pageNo: currentPageRef.value,
      pageSize: pageSizeRef.value,
    }
    if (questionNameRef.value.length) {
      params.name = questionNameRef.value
    }
    examinationStore.getExaminationQuestionListAction(params).then((res) => {
      totalRef.value = Number(res)
      loadingRef.value = false
    })
  }
}

async function handleExport() {
  if (idsChecked.value.length === 0) {
    window.$message.warning('请先选择数据')
    return
  }
  try {
    loadingRef.value = true
    const res = await exportExaminationQuestion({
      subjectIdList: idsChecked.value,
    })
    downloadArrayBuffer(res, '题目列表.xlsx', 'application/msexcel')
    loadingRef.value = false
  }
  catch (error) {}
}

// 过滤
function handleFilter() {
  currentPageRef.value = 1
  loadExaminationQuestionList()
}
function handleClearQuestionName() {
  questionNameRef.value = ''
  handleFilter()
}

// 点击添加按钮
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
// 点击保存按钮
function handleClickSave(isContinue?: boolean) {
  questionFormRef.value?.validateAndSave()
  isContinueRef.value = !!isContinue
}
// 保存成功
function handleQuestionSaved() {
  needRefreshRef.value = true // 保存成功，需要刷新列表
  if (isContinueRef.value) {
    formKeyRef.value = uuid()
  }
  else {
    showEditRef.value = false
  }
  isContinueRef.value = false
}
const showTopic = ref(false)
const handelImportFn = () => {
  showTopic.value = true
}
/** 下载模板 */
const handleClickDownloadTemplate = () => {
  const url = '/static/topicTemplate.xlsx'
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', '题目导入模版.xlsx')
  link.click()
  showTopic.value = false
}
// 选择文件
function handleQuestionFileChange(options: {
  file: UploadFileInfo
  fileList: Array<UploadFileInfo>
  event?: Event
}) {
  const file = options.file.file
  if (file) {
    const formData = new FormData()
    formData.append('libraryId', libraryId.value as string)
    formData.append('file', file)
    importExaminationQuestion(formData).then(() => {
      window.$message.success('导入成功')
      loadExaminationQuestionList()
      showTopic.value = false
    })
  }
}

watch(showEditRef, (newV) => {
  if (!newV && needRefreshRef.value) {
    loadExaminationQuestionList()
    needRefreshRef.value = false
  }
})

const tableColumns: DataTableColumns<IExaminationQuestion> = [
  { type: 'selection' },
  {
    key: 'name',
    title: '题干',
    width: 400,
    ellipsis: { tooltip: { width: 300 } },
  },
  { key: 'blank', width: 30 },
  {
    key: 'type',
    title: '题型',
    width: 90,
    render(row) {
      return questionTypes.find(item => item.value === row.type)?.label
    },
  },
  {
    key: 'examLabels',
    title: '标签',
    width: 140,
    ellipsis: { tooltip: { width: 200 } },
  },
  {
    key: 'updatedAt',
    title: '更新时间',
    width: 150,
    render(row) {
      return formatCustomTimeString(row.updatedAt, 'YYYY-MM-DD HH:mm:ss')
    },
  },
  {
    key: 'operation',
    title: '操作',
    width: 120,
    render(row) {
      return [
        h(
          NPopconfirm,
          {
            positiveText: '确定',
            placement: 'bottom-end',
            onPositiveClick: () => {
              editTypeRef.value = 'modify'
              idEditingRef.value = row.id
              showEditRef.value = true
            },
          },
          {
            trigger: () =>
              h(
                NButton,
                { text: true, type: 'primary', style: 'margin-right: 20px' },
                { default: () => '编辑' },
              ),
            default: () =>
              h('div', null, [
                h('div', null, '编辑后会同步更新已关联的试卷/考试，'),
                h('div', null, '确认编辑？'),
              ]),
          },
        ),
        h(
          NPopconfirm,
          {
            positiveText: '确定',
            placement: 'left',
            onPositiveClick: () => {
              deleteExaminationQuestion(String(row.id)).then((res) => {
                window.$message.success(res)
                loadExaminationQuestionList()
              })
            },
          },
          {
            trigger: () =>
              h(
                NButton,
                { text: true, type: 'primary' },
                { default: () => '删除' },
              ),
            default: () => '确定删除吗？',
          },
        ),
      ]
    },
  },
]
</script>

<style lang="scss" scoped>
.library-specific {
  border-left: 1px solid #eeeff3;
}

:deep(.question-type) {
  line-height: 26px;
  margin-right: 40px;

  > .n-radio__label {
    font-weight: 400;
    color: #333;
  }

  &.n-radio--checked {
    > .n-radio__label {
      font-weight: 600;
    }
  }
}
</style>
