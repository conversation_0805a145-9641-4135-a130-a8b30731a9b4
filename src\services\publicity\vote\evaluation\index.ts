import type {
  ExamIndicatorsListItem,
  IssueDetailType,
  PartyBuildingEvaluationTableItemType,
  addPartyBuildingEvaluationType,
} from './type'
import type { PaginationReq, PaginationRes } from '@/services/types'
import { commonReq, downloadReq } from '@/services/request'

/** 获取党建考核列表 */
export function getPartyBuildingEvaluationTableList(
  params: { title: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<PartyBuildingEvaluationTableItemType>>({
    url: '/party-affairs/backend/v1/voting-assess',
    params,
  })
}

/** 新建党建考核 */
export function addPartyBuildingEvaluation(
  data: addPartyBuildingEvaluationType,
) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/voting-assess',
    data,
  })
}

/** 编辑党建考核 */
export function editorPartyBuildingEvaluation(
  data: addPartyBuildingEvaluationType,
) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/voting-assess',
    data,
  })
}

/** 查看单个党建考核 */
export function viewPartyBuildingEvaluation(id: string) {
  return commonReq.get({
    url: `/party-affairs/backend/v1/voting-assess/${id}`,
  })
}

/** 删除单个党建考核 */
export function delPartyBuildingEvaluation(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/voting-assess',
    params: { ids },
  })
}

/** 下发单个党建考核 */
export function issuePartyBuildingEvaluation(id: string) {
  return commonReq.put({
    url: `/party-affairs/backend/v1/voting-assess/status/${id}`,
  })
}

/** 新建党建考核类别 */
export function addPartyBuildingEvaluationCategory(data: {
  name: string
  votingAssessId: string
}) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/voting-assess/category',
    data,
  })
}

/** 修改党建考核类别 */
export function editorPartyBuildingEvaluationCategory(data: {
  id: string
  name: string
}) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/voting-assess/category',
    data,
  })
}

/** 删除党建考核类别 */
export function delPartyBuildingEvaluationCategory(id: string) {
  return commonReq.delete({
    url: `/party-affairs/backend/v1/voting-assess/category/${id}`,
  })
}

/** 查看党建考核下的指标 */
export function viewEvaluationIssue(id: string) {
  return commonReq.get({
    url: `/party-affairs/backend/v1/voting-assess/target/${id}`,
  })
}

/** 党建考核下的关联指标项列表 */
export function getAssessIssueList(
  params: {
    assessId: string
    categoryId: string
    title: string
  } & PaginationReq,
) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/voting-assess/target/relation',
    params,
  })
}

/** 添加党建考核下的关联指标项 */
export function addAssessIssue(data: {
  categoryId: string
  assessId: string
  targetItemIds: number[]
}) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/voting-assess/target/relation',
    data,
  })
}

/** 添加党建考核下的关联指标项截止日期 */
export function addAssessIssueExpirationDate(data: {
  id: string
  deadline: string
}) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/voting-assess/target',
    data,
  })
}

/** 添加党建考核下的关联指标项 */
export function delAssessIssueItem(id: string) {
  return commonReq.delete({
    url: `/party-affairs/backend/v1/voting-assess/target/relation/${id}`,
  })
}

/** 获取党建考核指标项TableList */
export function getExamIssueTableList(
  params: { title: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<ExamIndicatorsListItem>>({
    url: '/party-affairs/backend/v1/voting-assess/target-item',
    params,
  })
}

/** 删除党建考核指标项当个或多个 */
export function delExamIssueTableItem(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/voting-assess/target-item',
    params: { ids },
  })
}

/** 获取党建考核下指标项详情 */
export function getExamIssueDetailInfo(id: string) {
  return commonReq.get<IssueDetailType>({
    url: `/party-affairs/backend/v1/voting-assess/target-item/${id}`,
  })
}

/** 添加党建考核指标项 */
export function addPartyBuildingExamIssue(data: IssueDetailType) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/voting-assess/target-item',
    data,
  })
}

/** 编辑党建考核指标项 */
export function editorPartyBuildingExamIssue(data: IssueDetailType) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/voting-assess/target-item',
    data,
  })
}

/** 导入党建考核指标项 */
export function importPartyBuildingExamIssueFile(file: FormData) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/building-inventory/target-item/import',
    data: file,
  })
}

/** 获取关联指标项打分时的信息 */
export function getAssessIssueDetailInfo(params: { relationId: string }) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/voting-assess/target/mark',
    params,
  })
}

/** 给关联指标项打分 */
export function putAssessIssueGrade(data: { id: string; score: number }) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/voting-assess/target/mark',
    data,
  })
}

/** 给关联指标项打分 */
export function getAssessIssueScoreStatistic(id: string) {
  return commonReq.get({
    url: `/party-affairs/backend/v1/voting-assess/target/score/${id}`,
  })
}

/** 导出党建考核指标 */
export function exportPartyBuildingExamTargetFile(id: string) {
  return downloadReq.post({
    url: `/party-affairs/backend/v1/voting-assess/target/export/${id}`,
    responseType: 'blob',
  })
}

/** 导出党建考核得分 */
export function exportPartyBuildingExamScoreFile(id: string) {
  return downloadReq.post({
    url: `/party-affairs/backend/v1/voting-assess/target/score/export/${id}`,
    responseType: 'blob',
  })
}
