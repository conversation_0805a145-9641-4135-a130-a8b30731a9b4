import { commonReq } from '../request'
import type { PaginationReq2 } from '../types'
import type {
  AddOrEditNewListItem,
  AddOrEditNewsCategory,
  MoveNewsCategory,
  NewsCategoryList,
} from './types'

/** 获取资讯分类列表及其子类 */
export function getNewsCategoryList() {
  return commonReq.get<NewsCategoryList[]>({
    url: '/sys-operation/backend/v1/news/category',
  })
}

/** 新增资讯分类 */
export function postNewsCategory(data: AddOrEditNewsCategory) {
  return commonReq.post({
    url: '/sys-operation/backend/v1/news/category',
    data,
  })
}

/** 编辑资讯分类 */
export function putNewsCategory(data: AddOrEditNewsCategory) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/news/category',
    data,
  })
}

/** 移动资讯分类 */
export function moveNewsCategory(data: MoveNewsCategory) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/news/category/move',
    data,
  })
}

/** 删除资讯分类 */
export function deleteNewsCategory(id: string) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/news/category',
    params: {
      id,
    },
  })
}

/** 获取资讯列表 */
export function getNewsList(
  params: {
    categoryId: string
    title?: string
  } & PaginationReq2,
) {
  return commonReq.get({
    url: '/sys-operation/backend/v1/news',
    params,
  })
}

/** 新增资讯 */
export function postNews(data: AddOrEditNewListItem) {
  return commonReq.post({
    url: '/sys-operation/backend/v1/news',
    data,
  })
}

/** 编辑资讯 */
export function putNews(data: AddOrEditNewListItem) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/news',
    data,
  })
}

/** 查看资讯详情 */
export function getNewsDetail(id: string) {
  return commonReq.get<AddOrEditNewListItem>({
    url: `/sys-operation/backend/v1/news/${id}`,
  })
}

/** 删除资讯  */
export function deleteNews(ids: string) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/news',
    params: {
      ids,
    },
  })
}

/** 置顶资讯 */
export function putNewsTop(id: string) {
  return commonReq.put({
    url: `/sys-operation/backend/v1/news/top/${id}`,
  })
}

/** 推荐资讯 */
export function putNewsRecommend(id: string) {
  return commonReq.put({
    url: `/sys-operation/backend/v1/news/recommend/${id}`,
  })
}

/** 隐藏资讯 */
export function postHideNews(id: string) {
  return commonReq.post({
    url: `/sys-operation/backend/v1/news/hidden/${id}`,
  })
}

/** 是否加入首页轮播池 */
export function joinRotationPool(id: string) {
  return commonReq.post({
    url: `/sys-operation/backend/v1/news/slider/${id}`,
  })
}

/** 审核资讯 */
export function postNewsAudit(id: string, reviewed: string) {
  return commonReq.post({
    url: `/sys-operation/backend/v1/news/reviewed/${id}?reviewed=${reviewed}`,
  })
}

/** 查询资讯首页配置 */
export function getNewsOption() {
  return commonReq.get({
    url: '/sys-operation/backend/v1/news/config',
  })
}

/** 保存资讯首页配置 */
export function saveSetting(data: any) {
  return commonReq.post({
    url: '/sys-operation/backend/v1/news/config',
    data,
  })
}
