<template>
  <div>
    <n-form
      ref="publicityRef"
      label-placement="left"
      label-width="90px"
      :model="formData.data"
      :rules="publicityFormRules"
    >
      <n-form-item label="先锋号名称" path="pioneerName">
        <n-input
          v-model:value="formData.data.pioneerName"
          clearable
          :maxlength="50"
          show-count
        />
      </n-form-item>
      <n-form-item label="图片" path="iconUrl">
        <ImgUploader
          v-model:old-img-url="formData.data.iconUrl"
          :width="220"
          :height="150"
          :need-cropper="false"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </n-form-item>
      <n-form-item label="所属组织" path="deptId">
        <n-tree-select
          v-model:value="formData.data.deptId"
          :options="orgType.organizationCurrentListTree"
          value-field="deptId"
          label-field="name"
          key-field="deptId"
          children-field="children"
          check-strategy="all"
          placeholder="请选择所属党组织"
          :show-path="false"
          clearable
          filterable
          @update:value="handleUpdateValue"
        />
      </n-form-item>
      <!-- <n-form-item label="是否推荐" path="isRecommand">
        <n-switch
          v-model:value="formData.data.isRecommand"
          :checked-value="1"
          :unchecked-value="0"
          :rubber-band="false"
        />
      </n-form-item> -->
      <n-form-item label="是否隐藏" path="isHidden">
        <n-switch
          v-model:value="formData.data.isHidden"
          :checked-value="1"
          :unchecked-value="0"
          :rubber-band="false"
        />
      </n-form-item>
      <n-form-item label="是否加入首页轮播池" path="sliderFlag">
        <n-switch
          v-model:value="formData.data.sliderFlag"
          :checked-value="1"
          :unchecked-value="0"
          :rubber-band="false"
        />
      </n-form-item>
      <n-form-item label="先锋号简介" path="briefDesc">
        <n-input
          v-model:value="formData.data.briefDesc"
          type="textarea"
          clearable
          show-count
        />
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { publicityFormRules } from './ruleConfig'
import type { uploadFileItem } from '@/services/affairs/party-building-list/exam-indicators/types'
import { uploadImg } from '@/services'
import { useCurrentOrganizationListOptionsNew } from '@/hooks/use-select-options'
const publicityRef = ref()
const formData = reactive({
  data: {
    pioneerName: '',
    briefDesc: '',
    isRecommand: 0,
    isHidden: 0,
    iconUrl: '',
    deptId: '',
    sliderFlag: 0,
  },
})

const orgType = ref(useCurrentOrganizationListOptionsNew())

/** 获取党组织id */
const handleUpdateValue = (v: string) => {
  formData.data.deptId = v
}

/**
 * 上传图片
 * @param {any} file:File
 */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formData.data.iconUrl === '') {
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formData.data.iconUrl = data.url
      }
    }
  }
  catch (error) {}
}
/**
 * 删除图片
 */
function handleCoverDelete() {
  formData.data.iconUrl = ''
}

function handleValidate() {
  return new Promise((resolve, reject) => {
    publicityRef.value?.validate((errors: any) => {
      if (!errors) {
        resolve(true)
      }
      else {
        resolve(false)
      }
    })
  })
}

function handleSetFormData(data: any) {
  formData.data = cloneDeep(data)
}

// function handleReset() {
//   publicityRef.value?.restoreValidation()
// }

defineExpose({
  formData,
  handleValidate,
  handleSetFormData,
  // handleReset,
})
</script>

<style scoped lang="scss"></style>
