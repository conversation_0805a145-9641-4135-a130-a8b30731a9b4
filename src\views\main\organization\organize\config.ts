import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { EditExamIndIcatorsIdsItem } from '@/services/affairs/discipline-inspection-list/exam-indicators/types'

export function getTableColumns(
  optionColumnRenderer: (row: EditExamIndIcatorsIdsItem) => VNodeChild,
): DataTableColumns<EditExamIndIcatorsIdsItem> {
  return [
    { type: 'selection' },
    {
      key: 'title',
      title: '组织名称',
    },
    {
      key: 'matter',
      title: '排序',
    },
    {
      key: 'evaluationScore',
      title: '更新时间',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
