import EaseRequest from '@easestrategy/ease-request'
import { sessionCache } from '@/utils/cache'
import router from '@/router'
import { useAuthStore } from '@/store/auth/auth'

import { AUTHORIZATION } from '@/services/auth/config'

const redirectToLogin = () => {
  if (router.currentRoute.value.name !== 'login') {
    router.replace({
      name: 'login',
      query: { from: router.currentRoute.value.fullPath },
    })
  }
}

const DEFAULT_ERR_MSG = '服务器错误，请联系管理员'
const CLIENT_ERR_MSG = '请求错误'

let messageFlag = true

export const commonReq = new EaseRequest({
  baseURL: import.meta.env.VITE_API_BASE ?? '/',
  interceptors: {
    requestInterceptor(config) {
      const token = window.sessionStorage.getItem('access_token')
      if (token) {
        config.headers!.Authorization = `Bearer ${token}`
      }
      // else if (router.currentRoute.value.name !== 'login') {
      //   redirectToLogin()
      // }
      return config
    },
    responseInterceptor(res) {
      if (res.data.code !== 0) {
        const errMsg = res.data.msg ?? DEFAULT_ERR_MSG
        window.$message.error(errMsg)
        throw new Error(errMsg)
      }
      if (res.data.additionalData) {
        res.data.data.additionalData = res.data.additionalData
      }
      return res.data.data
    },
    responseInterceptorCatch: (err) => {
      const statusCode = err.response!.status
      let errMsg = ''
      if (statusCode === 403 || statusCode === 424) {
        errMsg = '登录状态已过期，请重新登录'
        if (!messageFlag) {
          setTimeout(() => {
            messageFlag = true
          }, 300)
        } else {
          window.$message.error(errMsg)
          messageFlag = false
        }
        const store = useAuthStore()
        store.handleUserLogout()
        redirectToLogin()
      } else if (statusCode >= 500) {
        errMsg = err.response?.data.msg ?? DEFAULT_ERR_MSG
        window.$message.error(errMsg)
      } else {
        errMsg = err.response?.data.msg ?? CLIENT_ERR_MSG
        window.$message.error(errMsg)
      }
      throw new Error(errMsg)
    },
  },
})

export const authReq = new EaseRequest({
  baseURL: import.meta.env.VITE_API_BASE ?? '/',
  interceptors: {
    requestInterceptor: (config) => {
      config.headers!.Authorization = AUTHORIZATION
      config.headers!['Content-Type'] = 'application/json'
      return config
    },
    responseInterceptor: (res) => {
      if (res.data.code === 1) {
        const errMsg = res.data.msg ?? DEFAULT_ERR_MSG
        window.$message.error(errMsg)
        throw new Error(errMsg)
      }
      return res.data
    },
    responseInterceptorCatch: (err) => {
      const errMsg = err.response?.data.msg ?? DEFAULT_ERR_MSG
      window.$message.error(errMsg)
      throw new Error(errMsg)
    },
  },
})

export const downloadReq = new EaseRequest({
  baseURL: import.meta.env.VITE_API_BASE ?? '/',
  interceptors: {
    requestInterceptor: (config) => {
      // const token = sessionCache.get(import.meta.env.VITE_APP_TOKEN_STORAGE_KEY)
      const token = window.sessionStorage.getItem('access_token')
      if (token) {
        config.headers!.Authorization = `Bearer ${token}`
      }
      // else if (router.currentRoute.value.name !== 'login') {
      //   redirectToLogin()
      // }
      return config
    },
    responseInterceptor: (res) => {
      return res.data
    },
    responseInterceptorCatch: (err) => {
      const statusCode = err.response!.status
      let errMsg = ''
      if (statusCode === 424) {
        errMsg = '登录状态已过期，请重新登录'
        redirectToLogin()
      } else if (statusCode >= 500) {
        errMsg = err.response?.data.msg ?? DEFAULT_ERR_MSG
      } else {
        errMsg = err.response?.data.msg ?? CLIENT_ERR_MSG
      }
      window.$message.error(errMsg)
      throw new Error(errMsg)
    },
  },
})

export const checkReq = new EaseRequest({
  baseURL: import.meta.env.VITE_API_BASE ?? '/',
  interceptors: {
    requestInterceptor: (config) => {
      const token = sessionCache.get('access_token')
      if (token) {
        config.headers!.Authorization = `Bearer ${token}`
      } else if (router.currentRoute.value.name !== 'login') {
        redirectToLogin()
      }
      return config
    },
    responseInterceptor: (res) => {
      return res.data
    },
    responseInterceptorCatch: (err) => {
      const statusCode = err.response!.status
      let errMsg = ''
      if (statusCode === 424) {
        errMsg = '登录状态已过期，请重新登录'
        redirectToLogin()
      } else if (statusCode >= 500) {
        errMsg = err.response?.data.msg ?? DEFAULT_ERR_MSG
      } else {
        errMsg = err.response?.data.msg ?? CLIENT_ERR_MSG
      }
      window.$message.error(errMsg)
      throw new Error(errMsg)
    },
  },
})
