<template>
  <n-drawer v-model:show="visible" :width="600" :mask-closable="false">
    <n-drawer-content :title="dataForm.id ? '编辑' : '添加'" closable>
      <n-form
        ref="dicDialogFormRef"
        :model="dataForm"
        labn-width="90px"
        :rules="dataRules"
      >
        <n-form-item label="类型" path="dictType">
          <n-input
            v-model:value="dataForm.dictType"
            clearable
            disabled
            placeholder="请输入类型"
          />
        </n-form-item>
        <n-form-item label="标签名" path="label">
          <n-input
            v-model:value="dataForm.label"
            placeholder="请输入标签名"
            clearable
          />
        </n-form-item>
        <n-form-item label="数据值" path="value">
          <n-input
            v-model:value="dataForm.value"
            placeholder="请输入数据值"
            clearable
          />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="dataForm.description"
            placeholder="请输入描述"
            clearable
          />
        </n-form-item>
        <n-form-item label="排序" path="sortOrder">
          <n-input-number
            v-model:value="dataForm.sortOrder"
            placeholder="请输入排序"
            clearable
          />
        </n-form-item>
        <n-form-item label="备注" path="remarks">
          <n-input
            v-model:value="dataForm.remarks"
            type="textarea"
            rows="3"
            placeholder="请输入备注"
            maxlength="150"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <span class="dialog-footer">
          <n-button @click="visible = false">取消</n-button>
          <n-button type="primary" :disabled="loading" @click="onSubmit">确认</n-button>
        </span>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts" name="dict-item-form">
import {
  addItemDict,
  getItemDict,
  putItemDict,
  validateDictItemLabel,
} from '@/services/system/Admin'

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh'])

// 定义变量内容
const dicDialogFormRef = ref()

const visible = ref(false)
const loading = ref(false)

const dataForm = reactive({
  id: '',
  dictId: '',
  dictType: '',
  value: '',
  label: '',
  description: '',
  sortOrder: 0,
  remarks: '',
})

const dataRules = reactive({
  value: [{ required: true, message: '数据值不能为空', trigger: 'blur' }],
  label: [
    { required: true, message: '标签不能为空', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        validateDictItemLabel(
          rule,
          value,
          callback,
          dataForm.dictType,
          dataForm.id !== '',
        )
      },
      trigger: 'blur',
    },
  ],
  description: [{ required: true, message: '描述不能为空', trigger: 'blur' }],
  sortOrder: [
    {
      type: 'number',
      required: true,
      message: '排序不能为空',
      trigger: ['blur', 'change'],
    },
  ],
})

// 打开弹窗
const openDialog = (row: any, dictForm: any) => {
  visible.value = true
  dataForm.id = ''

  nextTick(() => {
    dicDialogFormRef.value?.restoreValidation()
  })

  if (row?.id) {
    getItemDict(row.id).then((res) => {
      Object.assign(dataForm, res)
    })
  } else if (dictForm) {
    dataForm.dictId = dictForm.dictId
    dataForm.dictType = dictForm.dictType
  }
}

// 提交
const onSubmit = () => {
  dicDialogFormRef.value?.validate(async(error: any) => {
    if (!error) {
      try {
        loading.value = true
        dataForm.id ? await putItemDict(dataForm) : await addItemDict(dataForm)
        window.$message.success(dataForm.id ? '编辑' : '添加')
        visible.value = false
        emit('refresh')
      } catch (err: any) {
        window.$message.error(err.msg)
      } finally {
        loading.value = false
      }
    } else {
      window.$message.error('请校验表单！')
    }
  })
}

// 暴露变量
defineExpose({
  openDialog,
})
</script>
