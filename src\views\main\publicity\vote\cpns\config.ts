import type { FormRules } from 'naive-ui'

// 添加明主评议表单字段校验
export const talkAboutFormRules: FormRules = {
  reviewTitle: {
    required: true,
    message: '评议名称不能为空',
    trigger: 'input',
  },
  reviewYear: {
    required: true,
    message: '评议年度不能为空',
    trigger: 'change',
  },
  reviewStartTime: {
    required: true,
    message: '开始时间不能为空',
    trigger: 'change',
  },
  reviewEndTime: {
    required: true,
    message: '结束时间不能为空',
    trigger: 'change',
  },
}

export const branchRatingInfoFormRules: FormRules = {
  title: {
    required: true,
    message: '评定方案名称不能为空',
    trigger: 'input',
  },
  year: {
    required: true,
    message: '评议年度不能为空',
    trigger: 'change',
  },
  selfStartTime: {
    required: true,
    message: '自评开始时间不能为空',
    trigger: 'change',
  },
  selfEndTime: {
    required: true,
    message: '自评结束时间不能为空',
    trigger: 'change',
  },
}
