<!-- 积分管理 -->
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="学分管理"
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <div class="flex flex-row justify-start items-center gap-x-[10px]">
        <n-input
          v-model:value="filterReactive.userName"
          size="small"
          placeholder="请输入姓名"
          style="width: 200px"
          clearable
        />
        <n-tree-select
          v-model:value="filterReactive.orgId"
          clearable
          placeholder="请选择所属党组织"
          size="small"
          style="width: 400px"
          :options="transformTreeData(calcTreeData)"
        />
      </div>
    </template>
  </table-container>

  <!-- 明细弹框 -->
  <n-modal
    preset="dialog"
    :show="detailVisible"
    :show-icon="false"
    style="width: 1000px"
    @close="handleDetailClose"
  >
    <template #header>
      <div class="font-bold">
        学分明细
      </div>
    </template>
    <DetailPage :current-row="currentRow.value" />
  </n-modal>
</template>

<script setup lang="ts">
import { h, onMounted, ref, watch } from 'vue'
import { NButton } from 'naive-ui'
import { getIntegralColumns } from './config'
import DetailPage from './detail.vue'
import { getPointList } from '@/services/point-management'
import { useMyTable } from '@/hooks'
import { getOrganizationTree } from '@/services/structure/organize'
import type { PointTableItemType } from '@/services/point-management/types'
// 过滤参数
const filterReactive = ref({
  orgId: '',
  userName: '',
})
// 明细弹框
const detailVisible = ref(false)
// 树结构枚举
const treeData = ref<any[]>([])
const calcTreeDataFn: any = (arr: any[]) => {
  return arr.map((item: any) => {
    return {
      ...item,
      children:
        item.children && item.children.length
          ? calcTreeDataFn(item.children)
          : undefined,
    }
  })
}
// 计算属性重置树结构的children
const calcTreeData = computed(() => {
  return calcTreeDataFn(treeData.value)
})

// 获取组织结构
const getOrganizationTreeData = () => {
  return getOrganizationTree().then((res: any) => {
    treeData.value = res
  })
}

// 当前操作的row
const currentRow = reactive<{ value: PointTableItemType }>({
  value: {
    id: '',
    userId: '',
    userName: '',
    phone: '',
    political: '',
    orgName: '',
    creditSum: 0,
    updateTime: '',
  },
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getPointList, filterReactive, {})

// 修改和删除按钮渲染
const tableColumns = getIntegralColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          handleDetail(row)
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '明细',
      },
    ),
  ]
})

// 明细
function handleDetail(row: PointTableItemType) {
  currentRow.value = row
  detailVisible.value = true
}

// 转化data为树状结构
function transformTreeData(data: any) {
  if (Array.isArray(data)) {
    data.forEach((item) => {
      item.key = item.id
      item.label = item.name
      item.value = item.id

      if (item.children) {
        item.children.forEach((child: any) => {
          transformTreeData(child)
        })
      }
    })
  }
  else {
    data.key = data.id
    data.label = data.name
    data.value = data.id

    if (data.children) {
      data.children.forEach((child: any) => {
        transformTreeData(child)
      })
    }
  }
  return data
}

// 明细取消
function handleDetailClose() {
  detailVisible.value = false
}

watch(filterReactive.value, () => {
  loadData()
})

onMounted(() => {
  loadData()
  getOrganizationTreeData()
})
</script>

<style lang="scss" scoped>
.operate-btn {
  width: 62px;
  height: 28px;
  background: #ffffff;
  border-radius: 3px;
  border: 1px solid #d8d9da;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
}

:deep(.filter-operate .n-input) {
  width: 220px !important;
  height: 28px;
}

:deep(.filter-operate .n-select) {
  width: 184px !important;
  height: 28px !important;
}

:deep(.filter-operate .n-base-selection) {
  width: 184px !important;
  height: 28px !important;
  box-sizing: border-box !important;
}

:deep(.filter-operate .n-base-selection-label) {
  width: 184px !important;
  height: 28px !important;
  box-sizing: border-box !important;
}

:deep(.filter-operate .n-base-selection__state-border) {
  width: 184px !important;
  height: 28px !important;
  box-sizing: border-box !important;
}

.filter-operate {
  @apply flex gap-x-1;
  width: 410px;
}

.dialog-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;

  .sure-btn {
    width: 80px;
    height: 30px;
    background: #cb0000;
    border-radius: 3px;
    font-size: 14px;
    font-weight: 400;
    color: #ffffff;
  }

  .cancel-btn {
    width: 80px;
    height: 30px;
    background: #ffffff;
    border-radius: 3px;
    border: 1px solid #d8d9da;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    margin-left: 12px;
  }
}

.filter-detail-operate {
  width: 330px;
}
</style>
