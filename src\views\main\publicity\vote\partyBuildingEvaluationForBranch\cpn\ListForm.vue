<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { formRules } from './config'
import { useOrganizationListOptions } from '@/hooks/use-select-options'
import type { PartyBuildingAdd } from '@/services/affairs/discipline-inspection-list/types'
import {
  getSinglePartyListDetail,
  postPartyBuildList,
  putPartyBuildList,
} from '@/services/affairs/discipline-inspection-list'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()
const { organizationListTree } = useOrganizationListOptions()

const formDataReactive = reactive<PartyBuildingAdd>({
  title: '', // 清单标题
  evaluationYearAndMonth: null, // 考核时间
  organizationList: null, // 考核组织

  // examCategory: [
  //   {
  //     examNumber: 1,
  //     examItem: '',
  //   },
  // ],
})
// 新增组
// function addCategory() {
//   formDataReactive.examCategory!.push({
//     examNumber: formDataReactive.examCategory!.length + 1,
//     examItem: '',
//   })
// }
// // 删除组
// function deleteCategory(index: number) {
//   formDataReactive.examCategory!.splice(index, 1)
// }
const loading = ref(false)
const formRef = ref<InstanceType<typeof NForm>>()
onMounted(async() => {
  loading.value = true
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    try {
      const res = await getSinglePartyListDetail(props.id)
      formDataReactive.title = res.title
      formDataReactive.evaluationYearAndMonth = res.evaluationYearAndMonth
      formDataReactive.organizationList
        = res.organizationVoList?.map(item => item.id) ?? null
      formDataReactive.id = props.id
      loading.value = false
    } catch (error) {}
  }
})

// 验证表单,调用接口
async function validateAndSave() {
  loading.value = true
  const errors = await new Promise((resolve) => {
    formRef.value?.validate((errors) => {
      resolve(errors)
    })
  })

  if (!errors) {
    const saveFunction
      = props.type === 'modify' && props.id
        ? putPartyBuildList
        : postPartyBuildList
    const saveData = { ...formDataReactive }
    try {
      const res = await saveFunction(saveData)
      if (res) {
        window.$message.success('保存成功')
        emits('saved')
      }
      loading.value = false
    } catch (error) {}
  }
}
// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="90"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-grid>
      <n-form-item-gi span="24" label="清单标题：" path="title">
        <n-input
          v-model:value="formDataReactive.title"
          :disabled="props.type === 'view'"
          placeholder="请输入清单标题"
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi
        span="24"
        label="考核时间："
        path="evaluationYearAndMonth"
      >
        <n-date-picker
          v-model:formatted-value="formDataReactive.evaluationYearAndMonth"
          :disabled="props.type === 'view'"
          style="width: 100%"
          placeholder="请选择考核时间"
          clearable
          type="month"
          @update:formatted-value="
            (v:any) => (formDataReactive.evaluationYearAndMonth = v)
          "
        />
      </n-form-item-gi>

      <n-form-item-gi span="24" label="考核组织：" path="organizationList">
        <n-cascader
          v-model:value="formDataReactive.organizationList"
          :disabled="props.type === 'view'"
          :options="(organizationListTree as any)"
          value-field="id"
          label-field="name"
          children-field="children"
          check-strategy="child"
          :show-path="false"
          clearable
          multiple
          @update:value="(v:any) => (formDataReactive.organizationList = v)"
        />
      </n-form-item-gi>
      <!-- <n-form-item-gi span="24" label="考核类别：" path="title">
        <div class="flex flex-col" style="width: 100%">
          <div v-for="(_, index) in formDataReactive.examCategory" :key="index">
            <div class="flex flex-col">
              <div class="flex items-center mb-[10px]">
                <div class="w-[20px] px-[4px] py-[2px] mr-[2px]">
                  {{ index + 1 }}
                </div>
                <n-input
                  v-model:value="formDataReactive.examCategory[index].examItem"
                  placeholder="请输入考核类别"
                  clearable
                  maxlength="100"
                />
                <n-button
                  v-show="formDataReactive.examCategory.length !== 1"
                  quaternary
                  circle
                  class="ml-[6px]"
                  @click="() => deleteCategory(index)"
                >
                  <template #icon>
                    <n-icon size="{16}" color="#e45649">
                      <Delete />
                    </n-icon>
                  </template>
                </n-button>
              </div>
              <n-button
                v-show="formDataReactive.examCategory.length - 1 === index"
                quaternary
                circle
                class="text-center"
                @click="addCategory"
              >
                <template #icon>
                  <n-icon size="{16}" color="blue">
                    <ios-add-circle />
                  </n-icon>
                </template>
              </n-button>
            </div>
          </div>
        </div>
      </n-form-item-gi> -->
    </n-grid>
  </n-form>
</template>
<style lang="scss" scoped></style>
