<script lang="ts" setup>
import { NButton, NIcon, NInput } from 'naive-ui'
import { CheckRound, CloseRound } from '@vicons/material'
import { useDelete } from '@/hooks'

const { delItem } = useDelete(deleteClassication)

interface itemType {
  id: string
  name: string
  sort: number
  categoryFlag: string
  rename?: boolean
}

interface Props {
  value: itemType[]
  operateFn: (args: any, operateName: string) => void
  title: string
  searchId: string
  top?: string
  activatedID: string
}

const props = withDefaults(defineProps<Props>(), { top: '110px' })
const emit = defineEmits<{
  (e: 'select', item: itemType | '0' | undefined): void
}>()

const hoverId = ref('-1')
const activatedId = ref('')

const onlyAllowText = (value: string) =>
  !value || /^[\u4e00-\u9fa5]+$/.test(value)

// 党群资讯列表
const newsListAboutParty = ref(
  props.value.filter(item => item.categoryFlag === '1'),
)
// 消金说廉列表
const newsListAboutHonest = ref(
  props.value.filter(item => item.categoryFlag === '2'),
)

/** 搜索资讯 */
const searchVal = ref('')
watchEffect(() => {
  newsListAboutParty.value = props.value.filter(
    item => item.categoryFlag === '1' && item.name.includes(searchVal.value),
  )
  newsListAboutHonest.value = props.value.filter(
    item => item.categoryFlag === '2' && item.name.includes(searchVal.value),
  )
})

watch(
  () => props.activatedID,
  (newVal) => {
    activatedId.value = newVal
  },
)

function handleHover(id: string) {
  hoverId.value = id
}

function handleClick(id: string, item?: itemType) {
  activatedId.value = id
  if (id === '-1' || id === '-2') {
    // emit('select', { id: '0', name: '', sort: 0 })
  } else {
    emit('select', item)
  }
}

// 菜单箭头
const expandedMenus = [ref(true), ref(true)]

const handleArrow = (index: number) => {
  expandedMenus[index].value = !expandedMenus[index].value
}

// 操作弹框
const maskVisible = ref(false)

function closeMask() {
  maskVisible.value = false
  document.removeEventListener('click', closeMask)
}

function showMenu(id: string) {
  closeMask()

  maskVisible.value = true
  activatedId.value = id

  setTimeout(() => {
    document.addEventListener('click', closeMask)
  }, 100)
}

// 添加类别弹窗
const modalVisible = ref(false)

// 类别标识
const categoryFlag = ref('1')

// 添加类别方法
function handleAddCategory(flag: string) {
  modalVisible.value = true
  categoryFlag.value = flag
}
const categoryInput = ref()

function addClassication() {
  props.operateFn(
    {
      name: categoryInput.value,
      sort: 0,
      categoryFlag: categoryFlag.value,
    },
    'add',
  )
  modalVisible.value = false
}

// 上下移动
function moveClassication(
  id: string,
  move: string,
  sort: number,
  name: string,
) {
  props.operateFn({ id, move, sort, name }, 'move')
}

// 第一个禁用上移
const isFirstItem = (id: string, flag: string) => {
  const currentIndex = props.value
    .filter(item => item.categoryFlag === flag)
    .findIndex(category => category.id === id)
  if (currentIndex === 0) {
    return true
  }
}

// 最后一个禁用下移
const isLastItem = (id: string, flag: string) => {
  const currentIndex = props.value
    .filter(item => item.categoryFlag === flag)
    .findIndex(category => category.id === id)
  if (
    currentIndex
    === props.value.filter(item => item.categoryFlag === flag).length - 1
  ) {
    return true
  }
}
/** 清除模态框中的input框的值 */
watch(
  () => modalVisible.value,
  (newVal) => {
    if (!newVal) {
      categoryInput.value = ''
    }
  },
)

/** 删除类别 */
function deleteClassication(id: string) {
  props.operateFn({ id }, 'delete')
}
/** 重命名 */
const inputRef = ref<HTMLInputElement>()
const renameFn = (item: itemType) => {
  if (props.value.some(item => item.rename)) {
    window.$dialog.create({
      type: 'default',
      closable: false,
      content: '您有重命名项未填写完整，请继续填写！',
      showIcon: false,
      positiveText: '确认',
      negativeText: '取消',
    })
  } else {
    nextTick(() => {
      item.rename = true
    })
  }
}
const currentCategoryName = ref('')
const confirmRename = (item: itemType) => {
  if (currentCategoryName.value.trim().length) {
    item.rename = false
    const payload = { name: currentCategoryName.value, id: item.id, move: '0' }
    props.operateFn(payload, 'rename')
    currentCategoryName.value = ''
  } else {
    window.$message.error('请填写资讯类别名称')
  }
}
const cancelRename = (item: itemType) => {
  item.rename = false
  currentCategoryName.value = ''
}
</script>
<template>
  <div class="fixed left-0 w-[259px] bg-[#F9FAFB] bottom-0 px-[15px] root">
    <div class="px-[8px] pt-[25px] flex justify-between text-[14px] font-[500]">
      <span>{{ title }}</span>
    </div>
    <div class="mt-[20px]">
      <n-input v-model:value="searchVal" placeholder="搜索栏目" />
    </div>
    <div class="mt-[10px] text-[18px] overflow-scroll h-[calc(100vh-300px)]">
      <div
        :class="{ activated: activatedId === '-1' }"
        class="flex items-center hover:bg-[#E4E8F0] rounded-[6px] ml-[12px] pr-[10px]"
      >
        <icon-right-arrow
          class="w-[12px] cursor-pointer transform"
          :class="[expandedMenus[0].value ? 'rotate-[90deg]' : 'rotate-[0]']"
          @click="handleArrow(0)"
        />
        <span
          class="font-[500] text-[12px] cursor-pointer pl-[10px] leading-[40px] w-full"
          @click="handleClick('-1')"
        >
          党群资讯
        </span>
        <span class="cursor-pointer" @click="() => handleAddCategory('1')">+</span>
      </div>
      <div>
        <div
          v-for="item in newsListAboutParty"
          v-show="expandedMenus[0].value"
          :key="item.id"
          :class="{ activated: activatedId === item.id }"
          class="pl-[30px] my-[5px] text-[12px] relative ml-[10px] font-[400] hover:bg-[#E4E8F0] hover:transition-all cursor-pointer rounded-[6px] flex justify-between items-center pr-[20px] leading-[40px]"
          @mouseleave="() => (hoverId = '-1')"
          @mouseover="handleHover(item.id)"
        >
          <div
            v-show="!item.rename"
            class="w-[140px]"
            @click="handleClick(item.id, item)"
          >
            {{ item.name }}
          </div>
          <div
            v-show="item.rename"
            class="w-[100%] flex flex-row justify-between items-center"
          >
            <NInput
              ref="inputRef"
              v-model:value="currentCategoryName"
              maxlength="10"
              :allow-input="onlyAllowText"
              show-count
              clearable
            />
            <div class="w-[50px] h-[32px] flex flex-row">
              <NButton
                style="margin-left: 8px"
                text
                type="primary"
                size="small"
                @click="confirmRename(item)"
              >
                <NIcon><CheckRound size="22" /></NIcon>
              </NButton>
              <NButton
                style="margin-left: 8px"
                text
                type="primary"
                size="medium"
                @click="cancelRename(item)"
              >
                <NIcon><CloseRound :size="22" /></NIcon>
              </NButton>
            </div>
          </div>
          <div
            v-show="hoverId === item.id && !item.rename"
            @click="showMenu(item.id)"
          >
            <icon-more class="w-[20px]" />
          </div>
          <div
            v-show="activatedId === item.id && maskVisible"
            class="font-[400] flex flex-col bg-[white] text-center rounded-[6px] cursor-pointer absolute top-[50px] right-[0] z-[20] text-[12px] leading-[30px] py-[8px] px-[19px]"
          >
            <n-button @click="renameFn(item)">
              重命名
            </n-button>
            <n-button
              :disabled="isFirstItem(item.id, '1')"
              @click="moveClassication(item.id, '-1', item.sort, item.name)"
            >
              上移
            </n-button>
            <n-button
              :disabled="isLastItem(item.id, '1')"
              @click="moveClassication(item.id, '+1', item.sort, item.name)"
            >
              下移
            </n-button>
            <n-button @click="delItem(item.id)">
              删除
            </n-button>
          </div>
        </div>
      </div>
      <div
        :class="{ activated: activatedId === '-2' }"
        class="flex items-center hover:bg-[#E4E8F0] rounded-[6px] ml-[12px] pr-[10px]"
      >
        <icon-right-arrow
          class="w-[12px] cursor-pointer"
          :class="[expandedMenus[1].value ? 'rotate-[90deg]' : 'rotate-[0]']"
          @click="handleArrow(1)"
        />
        <span
          class="font-[500] text-[12px] cursor-pointer pl-[10px] leading-[40px] w-full"
          @click="handleClick('-2')"
        >
          消金说廉
        </span>
        <span class="cursor-pointer" @click="() => handleAddCategory('2')">+</span>
      </div>
      <div class="overflow-scroll h-[calc(100vh-300px)]">
        <div
          v-for="item in newsListAboutHonest"
          v-show="expandedMenus[1].value"
          :key="item.id"
          :class="{ activated: activatedId === item.id }"
          class="pl-[30px] my-[5px] text-[12px] relative ml-[10px] font-[400] hover:bg-[#E4E8F0] hover:transition-all cursor-pointer rounded-[6px] flex justify-between items-center pr-[20px] leading-[40px]"
          @mouseleave="() => (hoverId = '-1')"
          @mouseover="handleHover(item.id)"
        >
          <div
            v-show="!item.rename"
            class="w-[140px]"
            @click="handleClick(item.id, item)"
          >
            {{ item.name }}
          </div>
          <div
            v-show="item.rename"
            class="w-[100%] flex flex-row justify-between items-center"
          >
            <NInput
              ref="inputRef"
              v-model:value="currentCategoryName"
              maxlength="10"
              :allow-input="onlyAllowText"
              show-count
              clearable
            />
            <div class="w-[50px] h-[32px] flex flex-row">
              <NButton
                style="margin-left: 8px"
                text
                type="primary"
                size="small"
                @click="confirmRename(item)"
              >
                <NIcon><CheckRound size="22" /></NIcon>
              </NButton>
              <NButton
                style="margin-left: 8px"
                text
                type="primary"
                size="medium"
                @click="cancelRename(item)"
              >
                <NIcon><CloseRound :size="22" /></NIcon>
              </NButton>
            </div>
          </div>
          <div
            v-show="hoverId === item.id && !item.rename"
            @click="showMenu(item.id)"
          >
            <icon-more class="w-[20px]" />
          </div>
          <div
            v-if="activatedId === item.id && maskVisible"
            class="font-[400] flex flex-col bg-[white] text-center rounded-[6px] cursor-pointer absolute top-[50px] right-[0] z-[20] text-[12px] leading-[30px] py-[8px] px-[19px]"
          >
            <n-button @click="renameFn(item)">
              重命名
            </n-button>
            <n-button
              :disabled="isFirstItem(item.id, '2')"
              @click="moveClassication(item.id, '-1', item.sort, item.name)"
            >
              上移
            </n-button>
            <n-button
              :disabled="isLastItem(item.id, '2')"
              @click="moveClassication(item.id, '+1', item.sort, item.name)"
            >
              下移
            </n-button>
            <n-button @click="delItem(item.id)">
              删除
            </n-button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <n-modal
    v-model:show="modalVisible"
    :mask-closable="false"
    preset="card"
    style="width: 400px"
    title="分类名"
  >
    <div>
      <p class="mb-[30px]">
        新建分类默认排在最后，可在分类列表中调整显示顺序
      </p>
      <n-input
        v-model:value="categoryInput"
        maxlength="10"
        show-count
        clearable
        placeholder="请输入名称（限10个字以内，只能输入汉字）"
        :allow-input="onlyAllowText"
      />
      <div class="flex justify-end mt-[30px]">
        <n-button round type="primary" @click="addClassication">
          确定
        </n-button>
        <n-button round @click="modalVisible = false">
          取消
        </n-button>
      </div>
    </div>
  </n-modal>
  <!-- <div v-if="maskVisible" class="absolute top-0 bottom-0 left-0 right-0 z-0" @click="()=>maskVisible=false" /> -->
</template>

<style lang="scss" scoped>
.root {
  top: v-bind(top);

  .activated {
    background-color: #e4e8f0;
  }

  ::-webkit-scrollbar {
    display: none;
  }
}
:deep(.n-button.n-button--disabled .n-button__border) {
  border: none;
  outline: none;
}
:deep(.n-button .n-button__border) {
  border: none;
}
:deep(.n-button .n-button__state-border) {
  border: none;
}
:deep(.n-button:not(.n-button--disabled):hover .n-button__state-border) {
  border: none;
}
</style>
