<script setup lang="ts">
import { NButton } from 'naive-ui'
import ContentBox from '../ContentBox.vue'
import { getTableColumns } from './config'
import {
  getMeetingNoteDetail,
  getMeetingNoteList,
} from '@/services/affairs/party-meeting'
import type {
  PartyMeetingNoteDetail,
  PartyMeetingNoteItem,
} from '@/services/affairs/party-meeting/types'

interface Props {
  id: string
}
const props = defineProps<Props>()

const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => handleNoteDetail(row.id, row.user),
        type: 'primary',
        text: true,
      },
      {
        default: () => '详情',
      },
    ),
  ]
})
const materialData = ref<PartyMeetingNoteItem>()
const loading = ref(false)
async function handleLoadNotes() {
  try {
    loading.value = true
    const params = {
      meetingId: props.id,
    }
    materialData.value = await getMeetingNoteList(params)
  } catch (e) {
  } finally {
    loading.value = false
  }
}

const noteDetailRef = ref()
const noteDetailVisible = ref(false)
const noteDetailObj = ref<PartyMeetingNoteDetail>()
const noteUser = ref()
async function handleNoteDetail(id: string, user: string) {
  noteDetailVisible.value = true
  noteUser.value = user
  noteDetailObj.value = await getMeetingNoteDetail(id)
}
const baseApi = ref(import.meta.env.VITE_API_BASE)
onMounted(() => {
  handleLoadNotes()
})
</script>
<template>
  <content-box title="心得笔记">
    <div class="w-[85%] pt-[30px]">
      <NDataTable
        striped
        :loading="loading"
        :columns="tableColumns"
        :data="materialData"
      />
    </div>
  </content-box>

  <custom-dialog
    ref="noteDetailRef"
    v-model:show="noteDetailVisible"
    :show-action="false"
    width="600px"
    :title="`${noteUser}的心得笔记`"
  >
    <div class="px-[30px] py-[20px] flex flex-col gap-y-[20px]">
      <div class="flex gap-x-[10px]">
        <span class="flex-none">内容：</span>
        <span>{{ noteDetailObj?.content }}</span>
      </div>
      <div class="flex gap-x-[10px]">
        <span class="flex-none">附件：</span>
        <div class="flex flex-wrap gap-[10px]">
          <n-image-group>
            <n-image
              v-for="(item, index) in noteDetailObj?.fileVOList"
              :key="index"
              :src="baseApi + item.fileName"
              lazy
              class="border-radius-[10px]"
              show-toolbar-tooltip
              object-fit="fill"
              style="width: 112px; height: 112px; border-radius: 4px"
            />
          </n-image-group>
        </div>
      </div>
    </div>
  </custom-dialog>
</template>
<style lang="scss" scoped></style>
