<template>
  <div class="pr-[40px]">
    <n-form
      ref="wisdomCategoryRef"
      require-mark-placement="left"
      label-placement="left"
      label-width="150px"
      :model="formData"
      :rules="formRules"
    >
      <!-- 新增所属专题 -->
      <n-form-item
        v-if="currentType === 'sub'"
        label="所属专题"
        path="parentId"
      >
        <n-select v-model:value="formData.parentId" :options="subjectOptions" />
      </n-form-item>
      <n-form-item label="栏目标题" path="name">
        <n-input
          v-model:value="formData.name"
          placeholder="请输入栏目标题"
          show-count
          maxlength="50"
          clearable
        />
      </n-form-item>
      <n-form-item label="栏目简介" path="introduce">
        <n-input
          v-model:value="formData.introduce"
          placeholder="请输入栏目简介"
          type="textarea"
          show-count
          maxlength="100"
          clearable
        />
      </n-form-item>
      <n-form-item label="栏目图片" path="coverUrl">
        <ImgUploader
          v-if="['add', 'modify'].includes(props.type)"
          v-model:oldImgUrl="formData.coverUrl"
          :width="220"
          :height="150"
          :need-cropper="false"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
        <n-image v-else :src="viewImageSrc" width="100" height="100" />
      </n-form-item>
      <!-- 新版本增加是否加入轮播池 -->
      <n-form-item label="是否加入轮播池" path="isCarouselPool">
        <n-switch
          v-model:value="formData.isCarouselPool"
          :checked-value="1"
          :unchecked-value="0"
        >
          <template #checked>
            是
          </template>
          <template #unchecked>
            否
          </template>
        </n-switch>
      </n-form-item>
      <n-form-item
        v-if="currentType !== 'sub'"
        label="使用默认展示模版"
        path="useDefaultLayout"
      >
        <n-switch
          v-model:value="formData.useDefaultLayout"
          :checked-value="true"
          :unchecked-value="false"
        >
          <template #checked>
            是
          </template>
          <template #unchecked>
            否
          </template>
        </n-switch>
      </n-form-item>
      <n-form-item
        v-if="!formData.useDefaultLayout"
        label="二级栏目展示模版"
        path="categoryLayout"
      >
        <template #label>
          <div class="flex items-center gap-[4px]">
            <span>二级栏目展示模版</span>
            <n-tooltip placement="bottom" trigger="hover">
              <template #trigger>
                <n-icon size="16">
                  <HelpOutlineRound />
                </n-icon>
              </template>
              <div class="flex flex-col">
                <span>提示：该项配置仅影响二级栏目展示效果，如暂无二级栏目，则此配置暂不生效。</span>
              </div>
            </n-tooltip>
          </div>
        </template>
        <n-select
          v-model:value="formData.categoryLayout"
          placeholder="请选择二级栏目展示模版"
          filterable
          :options="categoryLayoutType.enumerationList"
          clearable
        />
      </n-form-item>
      <n-form-item
        v-if="!formData.useDefaultLayout"
        label="列表展示模版"
        path="layout"
      >
        <n-select
          v-model:value="formData.layout"
          placeholder="请选择列表展示模版"
          filterable
          :options="layoutType.enumerationList"
          clearable
        />
      </n-form-item>
      <!-- 新版本增加是否在首页显示 -->
      <n-form-item
        v-if="currentType === 'sub'"
        label="是否在首页展示"
        path="showOnHome"
      >
        <n-switch
          v-model:value="formData.showOnHome"
          :checked-value="true"
          :unchecked-value="false"
        >
          <template #checked>
            是
          </template>
          <template #unchecked>
            否
          </template>
        </n-switch>
      </n-form-item>
      <!-- ---- -->
      <!-- <n-form-item label="学习学分" path="configStudyScore">
        <n-input-number
          v-model:value="formData.configStudyScore"
          style="width: 100%"
        />
      </n-form-item> -->
      <n-form-item
        v-if="currentType === 'root'"
        label="学习计时（秒）"
        path="configReadTime"
      >
        <n-input-number
          v-model:value="formData.configReadTime"
          style="width: 100%"
        />
      </n-form-item>
      <!-- <n-form-item label="点赞学分" path="configCommentScore">
        <n-input-number
          v-model:value="formData.configCommentScore"
          style="width: 100%"
        />
      </n-form-item> -->
      <!-- <n-form-item label="   " path="">
        <span class="text-[12px] text-[#999999] mt-[-30px]">说明：评论即为点赞。</span>
      </n-form-item> -->
    </n-form>
  </div>
</template>

<script setup lang="ts">
import type { FormRules } from 'naive-ui'
import { HelpOutlineRound } from '@vicons/material'
import { uploadImg } from '@/services/common'
import type { uploadFileItem } from '@/services/affairs/party-building-list/exam-indicators/types'
import { useFetchEnumerationOptions } from '@/hooks/use-select-options'
interface Props {
  type?: string
  treeData: any[]
  currentType: string
  parentId: string | null
}
interface IData {
  id?: number | string
  name: string
  introduce: string
  configStudyScore: string
  configCommentScore: string
  configReadTime: number
  coverUrl: string | null
  isCarouselPool: number // 是否加入轮播池
  parentId: string | null // 所属专题
  showOnHome: boolean // 是否显示在首页
  useDefaultLayout: boolean // 是否是默认模板
  categoryLayout: string | null
  layout: string | null
}
const categoryLayoutType = ref(useFetchEnumerationOptions('category_layout'))
const layoutType = ref(useFetchEnumerationOptions('list_layout'))

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  currentType: 'root',
  parentId: null,
})

const formData = ref<IData>({
  id: '',
  name: '',
  configStudyScore: '',
  configCommentScore: '',
  configReadTime: 10,
  coverUrl: null,
  introduce: '',
  isCarouselPool: 0,
  parentId: props.parentId,
  showOnHome: false,
  useDefaultLayout: true,
  categoryLayout: null,
  layout: null,
})

const viewImageSrc = computed(
  () => import.meta.env.VITE_API_BASE + formData.value.coverUrl,
)

const subjectOptions = computed(() => {
  return props.treeData.map((item) => {
    return {
      label: item.name,
      value: item.originData.id,
    }
  })
})

/**
 * 上传图片
 * @param {any} file:File
 */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formData.value.coverUrl === '' || formData.value.coverUrl === null) {
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formData.value.coverUrl = data.url
      }
    }
  }
  catch (error) {}
}

// 图片删除
function handleCoverDelete() {
  formData.value.coverUrl = ''
}
const formRules: FormRules = {
  parentId: {
    required: true,
    message: '请选择所属专题',
    trigger: 'change',
    type: 'string',
  },
  name: {
    required: true,
    // validator(rule: any, value: any) {
    //   if (value === '' || !value) {
    //     return new Error('资讯类别不能为空')
    //   } else if (/^[\u4E00-\u9FA5]+$/.test(value)) {
    //     return true
    //   } else {
    //     return new Error('资讯类别仅支持中文')
    //   }
    // },
    message: '请输入栏目标题',
    trigger: 'input',
    type: 'string',
  },
  introduce: {
    required: true,
    message: '请输入栏目简介',
    trigger: 'input',
    type: 'string',
  },
  coverUrl: {
    required: true,
    message: '请上传图片',
    trigger: 'change',
    type: 'string',
  },
  isCarouselPool: {
    required: true,
    message: '请选择是否加入轮播池',
    trigger: 'change',
    type: 'number',
  },
  useDefaultLayout: {
    required: true,
    message: '请选择是否使用默认展示模版',
    trigger: 'change',
    type: 'boolean',
  },
  categoryLayout: {
    required: true,
    message: '二级栏目展示模版不能为空',
    trigger: 'change',
  },
  layout: {
    required: true,
    message: '列表展示模版不能为空',
    trigger: 'change',
  },
  showOnHome: {
    required: true,
    message: '请选择是否在首页展示',
    trigger: 'change',
    type: 'boolean',
  },
  configStudyScore: {
    required: true,
    message: '请输入学习学分',
    trigger: 'input',
    type: 'number',
  },
  configReadTime: {
    required: true,
    message: '请输入学习计时（秒)',
    trigger: 'input',
    type: 'number',
  },
  configCommentScore: {
    required: true,
    message: '请输入点赞学分',
    trigger: 'input',
    type: 'number',
  },
}
const wisdomCategoryRef = ref()

function handleValidate() {
  return new Promise((resolve, reject) => {
    wisdomCategoryRef.value?.validate((errors: any) => {
      if (!errors) {
        resolve(true)
      }
      else {
        resolve(false)
      }
    })
  })
}

function handleSetFormData(data: any) {
  formData.value = {
    name: data?.name,
    configStudyScore: data?.configStudyScore,
    configCommentScore: data?.configCommentScore,
    configReadTime: data?.configReadTime,
    coverUrl: data?.coverUrl,
    introduce: data?.introduce,
    isCarouselPool: data?.isCarouselPool,
    parentId: String(data?.parentId),
    showOnHome: data?.showOnHome,
    useDefaultLayout: data?.useDefaultLayout,
    categoryLayout: data?.categoryLayout,
    layout: data?.layout,
  }
  if (data.id) {
    formData.value.id = data.originData.id
  }
}

defineExpose({
  formData,
  handleValidate,
  handleSetFormData,
})
</script>

<style scoped lang="scss"></style>
