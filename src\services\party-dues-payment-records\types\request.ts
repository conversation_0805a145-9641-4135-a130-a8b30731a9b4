/**
 * @description: 查询党费缴纳记录列表
 * */
export interface QueryPartyDuesPaymentRecordsType {
  /**
   * 组织ID
   */
  deptId: number | string
  pageNum?: number
  pageSize?: number
  /**
   * 年份
   */
  year: number | string
}

/**
 * @description: 查询党费缴纳记录列表返回类型
 * */
export interface QueryPartyDuesPaymentRecordsResponseType {
  current?: number | null
  pages?: number | null
  records?: DuesPayVO[] | null
  size?: number | null
  total?: number | null
  [property: string]: any
}

/**
 * @description: 党费缴纳记录详情
 * */
export interface DuesPayVO {
  /**
   * 四月缴纳金额
   */
  aprAmount?: number | null
  /**
   * 八月缴纳金额
   */
  augAmount?: number | null
  /**
   * 十二月缴纳金额
   */
  decAmount?: number | null
  /**
   * 二月缴纳金额
   */
  febAmount?: number | null
  /**
   * 主键id
   */
  id?: number | null
  /**
   * 一月缴纳金额
   */
  janAmount?: number | null
  /**
   * 七月缴纳金额
   */
  julAmount?: number | null
  /**
   * 六月缴纳金额
   */
  junAmount?: number | null
  /**
   * 三月缴纳金额
   */
  marAmount?: number | null
  /**
   * 五月缴纳金额
   */
  mayAmount?: number | null
  /**
   * 十一月缴纳金额
   */
  novAmount?: number | null
  /**
   * 十月缴纳金额
   */
  octAmount?: number | null
  /**
   * 九月缴纳金额
   */
  sepAmount?: number | null
  /**
   * 用户ID
   */
  userId?: number | null
  /**
   * 用户姓名
   */
  userName?: null | string
}

/**
 * @description: 编辑党费缴纳记录
 * */
export interface UpdatePartyDuesPaymentRecordsType {
  /**
   * 组织id
   */
  deptId: number | string
  /**
   * 用户缴纳金额列表
   */
  userDuesPayList: DuesPayVO[] | null
  /**
   * 年份
   */
  year: number | string
}
