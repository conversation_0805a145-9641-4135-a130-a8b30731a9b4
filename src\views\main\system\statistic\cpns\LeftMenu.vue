<script lang="ts" setup>
import type { TreeOption } from 'naive-ui'
import { useOrganizationListOptions } from '@/hooks/use-select-options'
interface MenuProps {
  title: string
}
withDefaults(defineProps<MenuProps>(), { title: 'title' })

const emits = defineEmits<{
  (e: 'getCurrentDeptId', value: string): void
}>()
const searchVal = ref('')

const { organizationListTree } = useOrganizationListOptions() // 获取支部列表

watch(organizationListTree, (newVal) => {
  if (newVal[0] && newVal[0].id) {
    emits('getCurrentDeptId', newVal[0].id as string)
  }
})

/**
 * 点击树节点触发的方法
 * @param {any} {option}:{option:TreeOption}
 */
const nodeProps = ({ option }: { option: TreeOption }) => {
  return {
    onClick() {
      if (!option.disabled) {
        emits('getCurrentDeptId', option.id as string)
      }
    },
  }
}

// 递归匹配用户输入的组织名称
const filterOptions = (arr: any[], str: string, result: any[]) => {
  if (!str) {
    return arr
  }
  if (Array.isArray(arr) && arr.length) {
    arr.forEach((item) => {
      if (item.name.includes(str)) {
        result.push(item)
      } else {
        filterOptions(item.children || [], str, result)
      }
    })
  }
  return result
}

// 模糊搜索
const filterOrganTree = computed(() => {
  const arr = filterOptions(organizationListTree.value, searchVal.value, [])
  return arr
})
</script>
<template>
  <div class="fixed left-0 w-[259px] bg-[#F9FAFB] bottom-0 px-[15px] root">
    <div class="px-[8px] pt-[25px] flex justify-between text-[14px] font-[500]">
      <span>{{ title }}</span>
    </div>
    <div class="mt-[20px]">
      <n-input v-model:value="searchVal" placeholder="请输入搜索关键字" />
    </div>
    <div class="mt-[30px] leading-[60px] text-[18px]">
      <div class="overflow-scroll h-[calc(100vh-300px)]">
        <n-tree
          block-line
          :data="filterOrganTree"
          default-expand-all
          label-field="name"
          key-field="id"
          children-field="children"
          check-strategy="child"
          :node-props="nodeProps"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.root {
  top: 110px;

  .activated {
    background-color: #e4e8f0;
  }
  ::-webkit-scrollbar {
    display: none;
  }
}
</style>
