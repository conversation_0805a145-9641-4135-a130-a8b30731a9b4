<script lang="ts" setup>
import MeetingRecord from './pages/meetingRecord.vue'
import PartyDayActivity from './pages/PartyDayActivity.vue' // 主题党日活动情况
import HeartTalkRecord from './pages/HeartTalkRecord.vue' // 谈心谈话情况
import RewardStatus from './pages/RewardStatus.vue' // 奖励情况
import HandleStatus from './pages/HandleStatus.vue' // 处置情况
import OrganizationalTransfer from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/organizationalTransfer.vue'
import PartyMembersRoster from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/partyMembersRoster.vue'
import PartyMembersAttendLectures from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/partyMembersAttendLectures.vue'
import ActiveMembersParty from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/activeMembersParty.vue'
import BranchBasicOrganizationalStatus from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/branchBasicOrganizationalStatus.vue'
import DemocraticEvaluationPartyMembers from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/democraticEvaluationPartyMembers.vue'
import PartyMemberMeetingMinutes from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/partyMemberMeetingMinutes.vue'
import InternalPartyReportingRecords from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/internalPartyReportingRecords.vue'
import ImportantMattersOpinionsRecords from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/importantMattersOpinionsRecords.vue'
import PartyDuesPaymentRecords from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/partyDuesPaymentRecords.vue'
import AnnualPlanPartyBranch from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/annualPlanPartyBranch.vue'
import TrainingForPartySecretary from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/trainingForPartySecretary.vue'
import PartyBranchAnnualSummary from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/partyBranchAnnualSummary.vue'
import PartyMembersActivities from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/partyMembersActivities.vue'
import PartyBranchActivities from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/partyBranchActivities.vue'

const MeetingRecordPage = markRaw(MeetingRecord)
const OrganizationalTransferPage = markRaw(OrganizationalTransfer)
const PartyMembersRosterPage = markRaw(PartyMembersRoster)
const PartyMembersAttendLecturesPage = markRaw(PartyMembersAttendLectures)
const ActiveMembersPartyPage = markRaw(ActiveMembersParty)
const BranchBasicOrganizationalStatusPage = markRaw(
  BranchBasicOrganizationalStatus,
)
const DemocraticEvaluationPartyMembersPage = markRaw(
  DemocraticEvaluationPartyMembers,
)
const PartyMemberMeetingMinutesPage = markRaw(PartyMemberMeetingMinutes)
const InternalPartyReportingRecordsPage = markRaw(InternalPartyReportingRecords)
const ImportantMattersOpinionsRecordsPage = markRaw(
  ImportantMattersOpinionsRecords,
)
const PartyDuesPaymentRecordsPage = markRaw(PartyDuesPaymentRecords)
const AnnualPlanPartyBranchPage = markRaw(AnnualPlanPartyBranch)
const TrainingForPartySecretaryPage = markRaw(TrainingForPartySecretary)
const PartyBranchAnnualSummaryPage = markRaw(PartyBranchAnnualSummary)

const PartyDayActivityPage = markRaw(PartyDayActivity)
const HeartTalkRecordPage = markRaw(HeartTalkRecord)
const RewardStatusPage = markRaw(RewardStatus)
const HandleStatusPage = markRaw(HandleStatus)

const PartyMembersActivitiesPage = markRaw(PartyMembersActivities)
const PartyBranchActivitiesPage = markRaw(PartyBranchActivities)

const route = useRoute()
const router = useRouter()
const currentType = computed(() => route.query.type as string)

const pageMap: {
  [key: string]: any
  meetingRecord: typeof MeetingRecordPage
  partyOrganizationalTransfer: typeof OrganizationalTransferPage
  partyMembersRosterType: typeof PartyMembersRosterPage
  partyMembersAttendLectures: typeof PartyMembersAttendLecturesPage
  activeMembersParty: typeof ActiveMembersPartyPage
  branchBasicOrganizationalStatus: typeof BranchBasicOrganizationalStatusPage
  democraticEvaluationPartyMembers: typeof DemocraticEvaluationPartyMembersPage
  partyMemberMeetingMinutes: typeof PartyMemberMeetingMinutesPage
  internalPartyReportingRecords: typeof InternalPartyReportingRecordsPage
  importantMattersOpinionsRecords: typeof ImportantMattersOpinionsRecordsPage
  partyDayActivity: typeof PartyDayActivityPage
  heartTalkRecord: typeof HeartTalkRecordPage
  rewardStatus: typeof RewardStatusPage
  handleStatus: typeof HandleStatusPage
  partyDuesPaymentRecords: typeof PartyDuesPaymentRecordsPage
  annualPlanPartyBranch: typeof AnnualPlanPartyBranchPage
  trainingForPartySecretary: typeof TrainingForPartySecretaryPage
  partyBranchAnnualSummary: typeof PartyBranchAnnualSummaryPage

  partyMembersActivities: typeof PartyMembersActivitiesPage
  partyBranchActivities: typeof PartyBranchActivitiesPage
} = {
  meetingRecord: MeetingRecordPage,
  partyOrganizationalTransfer: OrganizationalTransferPage,
  partyMembersRosterType: PartyMembersRosterPage,
  partyMembersAttendLectures: PartyMembersAttendLecturesPage,
  activeMembersParty: ActiveMembersPartyPage,
  branchBasicOrganizationalStatus: BranchBasicOrganizationalStatusPage,
  democraticEvaluationPartyMembers: DemocraticEvaluationPartyMembersPage,
  partyMemberMeetingMinutes: PartyMemberMeetingMinutesPage,
  internalPartyReportingRecords: InternalPartyReportingRecordsPage,
  importantMattersOpinionsRecords: ImportantMattersOpinionsRecordsPage,
  partyDuesPaymentRecords: PartyDuesPaymentRecordsPage,
  annualPlanPartyBranch: AnnualPlanPartyBranchPage,
  trainingForPartySecretary: TrainingForPartySecretaryPage,
  partyBranchAnnualSummary: PartyBranchAnnualSummaryPage,
  partyDayActivity: PartyDayActivityPage,
  heartTalkRecord: HeartTalkRecordPage,
  rewardStatus: RewardStatusPage,
  handleStatus: HandleStatusPage,
  partyMembersActivities: PartyMembersActivitiesPage,
  partyBranchActivities: PartyBranchActivitiesPage,
}

const navTitleMap: Record<string, string> = {
  meetingRecord: '会议记录',
  partyOrganizationalTransfer: '组织关系转接',
  partyMembersRosterType: '党员名册',
  partyMembersAttendLectures: '党员听党课情况',
  activeMembersParty: '入党积极分子名册',
  branchBasicOrganizationalStatus: '支部基本组织状况',
  democraticEvaluationPartyMembers: '民主评议党员情况',
  partyMemberMeetingMinutes: '党员议事记录',
  internalPartyReportingRecords: '党内情况通报记录',
  importantMattersOpinionsRecords: '重要事项征求意见记录',
  partyDayActivity: '主题党日活动情况登记',
  partyDuesPaymentRecords: '党员交纳党费记录',
  annualPlanPartyBranch: '党支部年度工作计划',
  trainingForPartySecretary: '党支部书记参加上级党组织集中轮训情况',
  partyBranchAnnualSummary: '党支部年度工作总结',
  heartTalkRecord: '谈心谈话情况',
  rewardStatus: '党员受党内、行政奖励情况',
  handleStatus: '党员受党内、行政处分和不合格党员处置情况',
  partyMembersActivities: '组织入党积极分子学习、活动情况',
  partyBranchActivities: '党支部参加党委（总支）组织的活动情况登记',
}

const currentPage = computed(() => pageMap[currentType.value!] || '')
const currentNavTitle = computed(() => navTitleMap[currentType.value!] || '')

function handleBack() {
  router.replace({
    name: 'organizationalLifeRecordBookMaintenance',
  })
}
</script>

<template>
  <div>
    <div class="p-[20px]">
      <span class="cursor-pointer" @click="handleBack">
        <n-button size="large" text type="primary">组织生活记录本维护</n-button>
      </span>
      /
      <span>
        <n-button size="large" text>{{ currentNavTitle }}</n-button>
      </span>
    </div>
    <component :is="currentPage" />
  </div>
</template>

<style lang="scss" scoped></style>
