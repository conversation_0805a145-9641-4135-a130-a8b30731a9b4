<script lang="ts" setup>
import type { DataTableSortState, PaginationInfo } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'

interface Props {
  loading?: boolean
  title?: string
  tableColumns: any
  tableData: any[]
  rowKey?: (rowData: any) => string

  showAdd?: boolean
  showDelete?: boolean
  showToolbar?: boolean
  customToolbar?: boolean
  showPagination?: boolean
  showTitle?: boolean
  showTabs?: boolean

  tableHeight?: string
  total?: number
  page: number
  pageSize: number
  pageSizes?: number[]
  pageCacheKey?: string

  checkedRowKeys?: Array<string | number> // 被选中的列的 key
  showOther?: boolean
  showPaddingY?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  title: '',
  showAdd: true,
  showDelete: true,
  showToolbar: true,
  customToolbar: false,
  showPagination: true,
  showTitle: true,
  showTabs: false,
  pageCacheKey: '',
  total: 0,
  pageSizes: () => [10, 15, 20, 30, 50],
  checkedRowKeys: () => [],
  rowKey: (row: any) =>
    row.id
    ?? row.nodeId
    ?? row.selfReviewId
    ?? row.deptId
    ?? row.trainStartTime,
  showOther: false,
  showPaddingY: true,
})
const emits = defineEmits<{
  (e: 'click-add'): void
  (e: 'click-delete'): void
  (e: 'update-page'): void
  (e: 'update-page-size'): void
  (e: 'update:page', value: any): void
  (e: 'update:pageSize', value: any): void
  (e: 'update-sorter', value: DataTableSortState | DataTableSortState[]): void
  (e: 'update-checked-row-keys', value: any): void
}>()

function prefixRenderer(info: PaginationInfo) {
  return `共 ${info.itemCount} 条 `
}

/** 新版本分页 */
const _page = ref(props.page)
const _pageSize = ref(props.pageSize)
const _pageComputed = computed(() => props.page)
const _pageSizeComputed = computed(() => props.pageSize)
watch(_pageComputed, newV => (_page.value = newV))
watch(_pageSizeComputed, newV => (_pageSize.value = newV))
watch(_page, newVal => emits('update:page', newVal))
watch(_pageSize, newVal => emits('update:pageSize', newVal))
</script>
<template>
  <div :class="[{ 'py-[25px]': showPaddingY }, 'px-[22px]']">
    <div
      v-if="showTitle"
      class="text-[14px] font-[600] text-[#333] leading-[20px] mb-[26px]"
    >
      {{ props.title }}
    </div>
    <div v-if="showTabs">
      <slot name="tabs"></slot>
    </div>

    <!-- 其他项区域 -->
    <div v-if="showOther" class="others">
      <slot name="others"></slot>
    </div>

    <!-- 按钮区域 -->
    <div v-if="showToolbar" class="tool-bar">
      <div class="btns">
        <slot name="btns">
          <n-button
            v-if="showAdd"
            v-hasPermi="['portal_user_add_btn']"
            size="small"
            type="primary"
            @click="emits('click-add')"
          >
            <template #icon>
              <n-icon>
                <plus-round />
              </n-icon>
            </template>
            添加
          </n-button>
          <n-button
            v-if="showDelete"
            v-hasPermi="['portal_user_delete_btn']"
            size="small"
            @click="emits('click-delete')"
          >
            <template #icon>
              <n-icon>
                <delete-forever-round />
              </n-icon>
            </template>
            删除
          </n-button>
        </slot>
      </div>
      <!-- 筛选 -->
      <div class="filters">
        <slot name="filters"></slot>
      </div>
    </div>

    <div v-else-if="customToolbar" class="tool-bar">
      <div class="btns">
        <slot name="btns"></slot>
      </div>
      <div class="filters">
        <slot name="filters"></slot>
      </div>
    </div>

    <div>
      <slot name="row"></slot>
    </div>
    <!-- 表格 -->
    <n-data-table
      :bordered="false"
      :checked-row-keys="props.checkedRowKeys"
      :columns="props.tableColumns"
      :data="props.tableData"
      :loading="props.loading"
      :row-key="props.rowKey"
      size="small"
      v-bind="$attrs"
      @update-sorter="(e: any) => emits('update-sorter', e)"
      @update-checked-row-keys="(rowKeys: any) => emits('update-checked-row-keys', rowKeys)"
    />
    <!-- 分页 -->
    <div>
      <n-pagination
        v-if="showPagination"
        v-model:page="_page"
        v-model:page-size="_pageSize"
        :item-count="props.total"
        :page-sizes="props.pageSizes"
        :prefix="prefixRenderer"
        class="pagination"
        show-quick-jumper
        show-size-picker
        @update-page="emits('update-page')"
        @update-page-size="emits('update-page-size')"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.tool-bar {
  @apply h-[28px] mb-[20px] flex justify-between;
}

.filters {
  @apply flex gap-x-1;
}

:slotted(.item) {
  display: flex;
  align-items: center;

  &:nth-child(n + 2) {
    margin-left: 14px;
  }

  .n-select,
  .n-input {
    width: 8.5vw;
  }

  .n-date-picker.n-date-picker--range .n-input {
    width: 330px !important;
  }

  .label {
    margin-right: 9px;
    font-size: 12px;
    font-weight: 400;
    color: #333333;
  }
}

.btns {
  display: flex;

  :deep(.n-button:nth-child(n + 2)) {
    margin-left: 8px;
  }
}

.pagination {
  margin-top: 36px;
  font-size: 12px;
}

:deep(.n-data-table-filter) {
  justify-content: flex-start;
}

.n-pagination.pagination {
  justify-content: flex-end;
  position: relative;

  :deep(.n-pagination-prefix) {
    position: absolute;
    left: 0;
    line-height: 28px;
  }
}
</style>
