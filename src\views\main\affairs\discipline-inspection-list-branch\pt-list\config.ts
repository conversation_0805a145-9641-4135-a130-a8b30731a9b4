import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import { NCollapse, NCollapseItem, NIcon } from 'naive-ui'
import { KeyboardArrowDownSharp } from '@vicons/material'
import type { PartyBuildingListBranchItem } from '@/services/affairs/discipline-inspection-list-branch/types'
import { downloadFile } from '@/utils/downloader'

export function getTableColumns(
  optionColumnRenderer: (row: PartyBuildingListBranchItem) => VNodeChild,
): DataTableColumns<PartyBuildingListBranchItem> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      key: 'title',
      title: '清单标题',
      width: '23%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'evaluationYearAndMonth',
      title: '考核时间',
      width: '15%',
      render: row => `${row.evaluationYearAndMonth.replace('-', '年')}月`,
    },
    // {
    //   key: 'organizations',
    //   title: '考核组织',
    //   width: '15%',
    //   ellipsis: {
    //     tooltip: {
    //       contentStyle: { width: '400px', 'word-break': 'break-all' },
    //     },
    //   },
    // },
    {
      key: 'fileList',
      title: '附件',
      width: '23%',
      render: (row) => {
        return row.fileList?.length
          ? h(
            NCollapse,
            {
              arrowPlacement: 'right',
            },
            [
              h(
                NCollapseItem,
                {
                  disabled: row.fileList.length === 1,
                },
                {
                  header: () =>
                    h(
                      'div',
                      {
                        style: {
                          marginBottom: '2px',
                          cursor: 'pointer',
                          color: '#3f7ee8',
                        },
                      },
                      h(
                        'span',
                        {
                          onClick: (e: Event) => {
                            downloadFile(
                              row.fileList?.[0]?.fileName,
                              row.fileList?.[0]?.original,
                            )
                            e.stopPropagation()
                          },
                        },
                        row.fileList?.[0]?.original,
                      ),
                    ),
                  arrow: () =>
                    h(
                      NIcon,
                      row.fileList?.length === 1
                        ? ''
                        : () => h(KeyboardArrowDownSharp),
                    ),
                  default: () =>
                    row.fileList?.slice(1)
                      && row.fileList?.slice(1).map((item) => {
                        return h(
                          'div',
                          {
                            style: {
                              marginBottom: '2px',
                              cursor: 'pointer',
                              color: '#3f7ee8',
                            },
                          },
                          h(
                            'span',
                            {
                              onClick: (e: Event) => {
                                downloadFile(item.fileName, item.original)
                                e.stopPropagation()
                              },
                            },
                            item.original,
                          ),
                        )
                      }),
                },
              ),
            ],
          )
          : h('span', {}, { default: () => '--' })
      },
    },
    {
      key: 'evaluationScore',
      title: '考核分数',
      width: '13%',
      render: row => (row.evaluationScore ? row.evaluationScore : '--'),
    },
    // {
    //   key: 'evaluationProgress',
    //   width: '15%',
    //   title: '考核进度',
    //   render: (row) => {
    //     return h(NProgress, {
    //       type: 'line',
    //       processing: true,
    //       percentage: row.evaluationProgress as any,
    //       // indicatorPlacement: 'inside',
    //     })
    //   },
    // },
    {
      key: 'status',
      title: '状态',
      width: '13%',
      render: (row) => {
        return row.status ? row.status : '--'
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '10%',
      render: row => optionColumnRenderer(row),
    },
  ]
}
