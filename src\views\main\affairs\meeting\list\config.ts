import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { PartyMeetingListItem } from '@/services/affairs/party-meeting/types'
import unstartIcon from '@/assets/image/meeting/unstart.png'
import doingIcon from '@/assets/image/meeting/doing.png'
import endIcon from '@/assets/image/meeting/end.png'

export function getTableColumns(
  optionColumnRenderer: (row: PartyMeetingListItem) => VNodeChild,
): DataTableColumns<PartyMeetingListItem> {
  return [
    {
      type: 'selection',
    },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      key: 'title',
      title: '会议主题',
      width: '15%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'meetingType',
      title: '会议类型',
      width: '10%',
    },
    {
      key: 'meetingAddr',
      title: '会议地点',
      width: '12%',
    },
    {
      key: 'org',
      title: '所在组织',
      width: '10%',
    },
    {
      key: 'user',
      title: '发起人',
      width: '10%',
    },
    {
      key: 'host',
      title: '主持人',
      width: '10%',
    },
    {
      key: 'meetingStatus',
      title: '会议状态',
      width: '10%',
      render: (row) => {
        const statusIconMap: Record<string, string> = {
          未开始: unstartIcon,
          进行中: doingIcon,
          已结束: endIcon,
        }

        const statusText = row.meetingStatus
        const iconSrc = statusIconMap[statusText]

        return h(
          'div',
          {
            class: 'flex items-center gap-x-[7px]',
          },
          [
            h('img', { class: 'w-[12px] h-[12px]', src: iconSrc }),
            h('span', {}, statusText),
          ],
        )
      },
    },
    {
      key: 'startTime',
      title: '开始时间',
      width: '12%',
      // sorter: true,
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '6%',
      render: row => optionColumnRenderer(row),
    },
  ]
}
