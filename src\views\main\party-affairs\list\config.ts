import type { VNodeChild } from 'vue'
import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import { NCollapse, NCollapseItem, NEllipsis, NIcon, NImage } from 'naive-ui'
import { KeyboardArrowDownSharp } from '@vicons/material'
import type { AffairsListItem } from '@/services/party-affairs/types'
import { downloadFile } from '@/utils/downloader'
function truncatedText(str: string) {
  const cleanedText = str.replace(/<.*?>/g, '')
  return cleanedText.slice(0, 20)
}
export function getTableColumns(
  operationRender: (row: AffairsListItem) => VNodeChild,
  handleUpdateValueRender: (row: AffairsListItem) => VNodeChild,
): TableColumns<AffairsListItem> {
  return [
    {
      type: 'selection',
      align: 'center',
    },
    {
      title: '主题',
      key: 'themeName',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      title: '封面图',
      key: 'pictureUrl',
      render: row =>
        h(NImage, {
          src: import.meta.env.VITE_API_BASE + row.pictureUrl,
          width: '62',
          style: { height: '40px' },
        }),
    },
    {
      title: '是否外链',
      key: 'isOutside',
      render: row => row.isOutside === '1' ? '是' : '否',
    },
    {
      title: '外链地址',
      key: 'linkUrl',
      width: '15%',
      render: (row) => {
        return row.linkUrl
          ? h('a', {
            href: row.linkUrl,
            target: '_blank',
            style: {
              color: '#3f7ee8',
            },
          }, row.linkUrl)
          : '--'
      },
    },
    {
      title: '内容',
      key: 'content',
      render: (row) => {
        return row.content
          ? h(NEllipsis, {
            style: {
              maxWidth: '200px',
            },
            tooltip: true,
          }, {
            default: () => {
              return h('span', {
                innerHTML: `${truncatedText(row.content)}`,
              })
            },
            tooltip: () => {
              return h('div', {
                style: {
                  width: '400px',
                },
                innerHTML: row.content,
              })
            },
          })
          : '--'
      },
    },

    {
      key: 'fileList',
      title: '附件',
      width: '18%',
      render: (row) => {
        return row.fileList?.length
          ? h(
            NCollapse,
            {
              arrowPlacement: 'right',
            },
            [
              h(
                NCollapseItem,
                {
                  disabled: row.fileList.length === 1,
                },
                {
                  header: () =>
                    h(
                      'div',
                      {
                        style: {
                          marginBottom: '2px',
                          cursor: 'pointer',
                          color: '#3f7ee8',
                        },
                      },
                      h(
                        'span',
                        {
                          onClick: (e: Event) => {
                            downloadFile(
                              row.fileList?.[0]?.fileName,
                              row.fileList?.[0]?.original,
                            )
                            e.stopPropagation()
                          },
                        },
                        row.fileList?.[0]?.original,
                      ),
                    ),
                  arrow: () =>
                    h(
                      NIcon,
                      row.fileList?.length === 1
                        ? ''
                        : () => h(KeyboardArrowDownSharp),
                    ),
                  default: () =>
                    row.fileList?.slice(1)
                      && row.fileList?.slice(1).map((item) => {
                        return h(
                          'div',
                          {
                            style: {
                              marginBottom: '2px',
                              cursor: 'pointer',
                              color: '#3f7ee8',
                            },
                          },
                          h(
                            'span',
                            {
                              onClick: (e: Event) => {
                                downloadFile(item.fileName, item.original)
                                e.stopPropagation()
                              },
                            },
                            item?.original,
                          ),
                        )
                      }),
                },
              ),
            ],
          )
          : h('span', {}, { default: () => '--' })
      },
    },
    {
      title: '发布时间',
      key: 'createTime',
      render: row => row.createTime ?? '-',
    },
    {
      key: 'topStatus',
      title: '是否置顶',
      render: row => handleUpdateValueRender(row),
    },

    {
      title: '操作',
      key: 'operation',
      width: '10%',
      render: operationRender,
    },
  ]
}
