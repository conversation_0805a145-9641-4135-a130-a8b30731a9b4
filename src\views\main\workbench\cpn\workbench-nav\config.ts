import { storeToRefs } from 'pinia'
import router from '@/router'
import type { categoryListType } from '@/router/type'
import { useMenuAuthStore } from '@/store/menuAuth'

const { categoryList } = storeToRefs(useMenuAuthStore())

const allRoutes = router.getRoutes()
export const menuRoutes = categoryList.value?.map(
  (category: categoryListType) => ({
    category,
    children: allRoutes
      ?.filter(route => route.meta?.category === category.name)
      .sort((a, b) => Number(a.meta?.order) - Number(b.meta?.order)),
  }),
)

export function createMenuRoutes() {
  const MenuList = categoryList.value?.map((category: categoryListType) => ({
    category,
    children: allRoutes!
      .filter(route => route.meta?.category === category.name)
      .sort((a, b) => Number(a.meta?.order) - Number(b.meta?.order)),
  }))
    .filter((item: any) => item.children.length > 0)
  return MenuList
}
