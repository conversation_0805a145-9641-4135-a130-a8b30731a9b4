/*
 * @Description: 规则下拉选项相关
 * @Author: 朱备 <<EMAIL>>
 * @Date: 2021-12-03 16:23:23
 * @LastEditTime: 2021-12-04 10:02:26
 * @LastEditors: 朱备 <<EMAIL>>
 */

import { ref } from 'vue'
import type { SelectOption } from 'naive-ui'
import {
  getExaminationQuestionCount,
  getLabelListHasQuestion,
  getLevelListHasQuestion,
  getLibraryListHasQuestion,
  getTypeListHasQuestion,
} from '@/services/examination/index'
import {
  difficultyOptions,
  questionTypes,
} from '@/views/main/examination/config'

export const loadingRef = ref(false)

export default function(needInit: boolean, rule?: any) {
  const libraryList = ref<SelectOption[]>()
  const typeList = ref<SelectOption[]>()
  const levelList = ref<SelectOption[]>()
  const labelList = ref<SelectOption[]>()

  const libraryIdRef = ref<number>()
  const typeRef = ref<number>()
  const levelRef = ref<number>()
  const labelIdRef = ref<number>()

  const countRef = ref<number>()

  // 加载题库
  function loadLibraryList() {
    countRef.value = undefined
    getLibraryListHasQuestion().then((res) => {
      libraryList.value = res.map(item => ({
        label: item.name,
        value: item.id,
      }))
      if (needInit) {
        loadingRef.value = true
        libraryIdRef.value = rule.libraryId
        loadTypeList()
      }
    })
  }

  // 加载题型
  function loadTypeList() {
    typeList.value = []
    levelList.value = []
    labelList.value = []
    typeRef.value = undefined
    levelRef.value = undefined
    labelIdRef.value = undefined
    countRef.value = undefined
    getTypeListHasQuestion(libraryIdRef.value!).then((res) => {
      typeList.value = questionTypes.filter(item => res.includes(item.value))

      if (needInit) {
        typeRef.value = rule.type
        loadLevelList()
      }
    })
  }

  // 加载难度
  function loadLevelList() {
    levelList.value = []
    labelList.value = []
    levelRef.value = undefined
    labelIdRef.value = undefined
    countRef.value = undefined
    getLevelListHasQuestion(libraryIdRef.value!, typeRef.value!).then((res) => {
      levelList.value = difficultyOptions.filter(item =>
        res.includes(item.value),
      )
      if (needInit) {
        levelRef.value = rule.level
        loadLabelList()
      }
    })
  }

  // 加载标签
  function loadLabelList() {
    labelList.value = []
    labelIdRef.value = undefined
    countRef.value = undefined
    getLabelListHasQuestion(
      libraryIdRef.value!,
      typeRef.value!,
      levelRef.value!,
    ).then((res) => {
      labelList.value = res.map(item => ({
        value: item.id,
        label: item.name,
      }))
      if (needInit) {
        labelIdRef.value = rule.labelId
        loadCount()
      }
    })
  }

  // 加载题量
  function loadCount() {
    countRef.value = undefined
    getExaminationQuestionCount({
      libraryId: libraryIdRef.value!,
      type: typeRef.value!,
      level: levelRef.value!,
      labelId: labelIdRef.value!,
    }).then((res) => {
      countRef.value = res
      needInit = false
      loadingRef.value = false
    })
  }

  loadLibraryList()

  return {
    libraryList,
    typeList,
    levelList,
    labelList,
    libraryIdRef,
    typeRef,
    levelRef,
    labelIdRef,
    countRef,

    loadLibraryList,
    loadTypeList,
    loadLevelList,
    loadLabelList,
    loadCount,
  }
}
