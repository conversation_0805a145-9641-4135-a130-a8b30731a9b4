<script setup lang="ts">
import { formRules } from './config'
import { uploadImg } from '@/services'
import type {
  addAndEditParams,
  uploadFileItem,
  uploadFileItemNew,
} from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
const formDataReactive = reactive<addAndEditParams>({
  id: '',
  title: '',
  evaluationRequirements: '',
  evaluationMode: '',
  evaluationScore: 0,
  matter: '',
  fileIds: [],
  fileList: [],
})

const emits = defineEmits<{
  (e: 'emitsUploadFileItem', value: any[]): void
  (e: 'emitsUploadFile', value: string[]): void
}>()
const fileIdObj = reactive<{ fileIdsArr: string[] }>({ fileIdsArr: [] })
// 文件相关
async function handleFileChange(
  fileInfoList: uploadFileItemNew[],
  isDelIDs: string,
) {
  try {
    const fileData = new FormData()
    // 获取所有FileID
    if (!fileIdObj.fileIdsArr.length) {
      formDataReactive.fileList.forEach(item =>
        fileIdObj.fileIdsArr.push(item.id),
      )
    }
    // 删除动作
    if (isDelIDs || isDelIDs === null) {
      if (isDelIDs === null && fileIdObj.fileIdsArr.length) {
        fileIdObj.fileIdsArr.splice(fileIdObj.fileIdsArr.length - 1, 1)
      } else {
        fileIdObj.fileIdsArr.forEach((item, index) => {
          if (item === isDelIDs) {
            fileIdObj.fileIdsArr.splice(index, 1)
          }
        })
      }
      formDataReactive.fileIds = [...fileIdObj.fileIdsArr]
    } else {
      // 新增动作
      const lastFileItem = fileInfoList[fileInfoList.length - 1]?.file
      fileData.append('file', lastFileItem as Blob)
      const data: uploadFileItem = await uploadImg(fileData)
      formDataReactive.fileList.push({
        original: lastFileItem?.name as string,
        fileName: data.url || '',
        id: data.fileId,
      })
      if (data) {
        fileIdObj.fileIdsArr.push(data.fileId)
        formDataReactive.fileIds = [...fileIdObj.fileIdsArr]
      }
      emits('emitsUploadFileItem', formDataReactive.fileList)
      emits('emitsUploadFile', formDataReactive.fileIds)
    }
  } catch (error) {}
}
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="90"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item span="24" label="附件：" path="fileList">
      <file-uploader
        :max="100"
        accept=".doc, .docx, .pdf"
        :size-limit="200"
        :original-file-list="(formDataReactive.fileList as any)"
        @file-list-change="handleFileChange"
      >
        <template #tips>
          <span class="tips">
            可上传多个文件，支持扩展名：.doc，docx，.pdf，大小200M以内
          </span>
        </template>
      </file-uploader>
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped>
.relation-table:deep(.n-data-table .n-data-table-thead) {
  display: contents;
}
</style>
<style lang="scss" scoped>
.relation-table:deep(.n-data-table .n-data-table-thead) {
  display: contents;
}
</style>
