import type { FormRules } from 'naive-ui'

export const PartyTeamFormRules: FormRules = {
  organizationId: {
    required: true,
    message: '请选择组织',
    trigger: 'change',
    type: 'string',
  },
  appointedName: {
    required: true,
    message: '请输入届期',
    trigger: 'input',
    type: 'string',
  },
  appointedStartTime: {
    required: true,
    message: '请选择届期开始时间',
    trigger: 'change',
    type: 'string',
  },
  appointedEndTime: {
    required: true,
    message: '请选择届期结束时间',
    trigger: 'change',
    type: 'string',
  },
  quotaList: {
    required: true,
    validator(rule: any, value: any) {
      if (value === null || value.length === 0) {
        return new Error('请添加领导班子职务配额')
      }
      return true
    },
    trigger: 'change',
  },
  teamIntroduction: {
    required: true,
    message: '请输入班子简介',
    trigger: 'input',
    type: 'string',
  },
}
