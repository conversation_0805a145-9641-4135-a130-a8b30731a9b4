<script lang="ts" setup>
import type { FormRules } from 'naive-ui'
import { NSwitch } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import AddNewsCategoryForm from './AddNewsCategoryForm.vue'
import { getTableColumns } from './config'
import { useMyTable, useTreeMenu } from '@/hooks'
import {
  deleteNews,
  deleteNewsCategory,
  getNewsCategoryList,
  getNewsList,
  getNewsOption,
  joinRotationPool,
  moveNewsCategory,
  postNewsAudit,
  postNewsCategory,
  putNewsCategory,
  putNewsRecommend,
  putNewsTop,
  saveSetting,
} from '@/services/news'
import DeleteButton from '@/components/DeleteButton.vue'
import { judgePermission } from '@/directive/permission/ifHasPermi'

const router = useRouter()
const { treeData, showModalType, moveNode, delNode, saveNode, init }
  = useTreeMenu({
    menuListApi: getNewsCategoryList,
    moveNodeApi: moveNewsCategory,
    // moveChildNodeApi: movePublicityCategory,
    delNodeApi: deleteNewsCategory,
    // delChildNodeApi: delPublicityCategory,
    addNodeApi: postNewsCategory,
    modifyNodeApi: putNewsCategory,
    // addChildNodeApi: addPublicityCategory,
    // modifyChildNodeApi: modifyPublicityCategory,
    refreshTableApi: filterInput,
    labelField: 'name',
    sessionId: 'newsCategoryId',
    sessionName: 'newsCategoryLabel',
  })

const showModal = ref(false)
const modalTitle = ref()
const addFirstCategoryRef = ref()

const homeModal = ref(false)

/** 新增子节点 */
async function handleAddChildNode(data: any) {
  showModal.value = true
  modalTitle.value = '新增资讯类别'
  // addChildNode(data)
  if (data.model === 'modify') {
    modalTitle.value = '修改资讯类别'
    nextTick(() => {
      addFirstCategoryRef.value.handleSetFormData(data)
    })
  }
}

/** 处理弹框需要保存的数据及校验弹框必填项 */
async function handleFormatterParams() {
  try {
    if (!(await addFirstCategoryRef.value.handleValidate())) {
      return
    }
    const data = addFirstCategoryRef.value.formData
    if (!data.onShow) {
      data.onShow = false
    }
    saveNode({ ...data, type: showModalType.value })
    showModal.value = false
  }
  catch (error) {
    console.error(error)
  }
}

function handleCancel() {
  showModal.value = false
}

// 选中菜单名称
const selectName = ref()

// 筛选项：类别id和资讯标题
const filterRef = ref({
  categoryId: '',
  title: null,
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getNewsList, filterRef, {
  batchDeleteTable: true,
  delApi: deleteNews,
})

// 选中菜单触发的事件
function handleChangeTab(data: any) {
  const { isChild, label } = data
  if (!isChild) {
    filterRef.value.categoryId = data.originData.id
    window.sessionStorage.setItem('newsCategoryId', data.originData.id)
    window.sessionStorage.setItem('newsCategoryLabel', label)
    filterRef.value.title = null
    selectName.value = label
    currentPage.value = 1
  }
}

async function switchTop(value: boolean, id: string) {
  await putNewsTop(id)
  window.$message.success('修改置顶成功')
  loadData()
}

async function switchRecommend(value: boolean, id: string) {
  await putNewsRecommend(id)
  if (value) {
    window.$message.success('精选成功')
  }
  loadData()
}

// async function switchHidden(value: boolean, id: string) {
//   await postHideNews(id)
//   if (value) {
//     window.$message.success('精选成功')
//   }
//   loadData()
// }

// 是否加入首页轮播池
async function switchRotationPool(value: boolean, id: string) {
  await joinRotationPool(id)
  if (value) {
    window.$message.success('操作成功')
  }
  loadData()
}

// 审核相关
const reviewModal = ref(false)
const reviewFormRef = ref()
const checkFormData = ref({
  reviewed: 0,
})
const homeDataFormRef = ref()

// 首页咨询管理相关
const homeFormRef = ref({
  homeType: '',
})

const homeDataRules: FormRules = {
  homeType: {
    required: true,
    message: '请选择展示在首页的分类',
    trigger: 'change',
  },
}

const reviewTitle = ref()
const reviewId = ref()
const checkFormRules: FormRules = {
  reviewed: {
    required: true,
    message: '请选择是否审核通过',
    trigger: 'change',
    type: 'number',
  },
}

function handleReview(id: string, title: string, reviewedStatus: number) {
  reviewId.value = id
  reviewTitle.value = title
  checkFormData.value.reviewed = reviewedStatus
  reviewModal.value = true
}

function handleHomeConfirm() {
  homeDataFormRef.value?.validate((errors: any) => {
    if (!errors) {
      saveSetting({ id: homeFormRef.value.homeType, showOnHome: true }).then(
        () => {
          window.$message.success('操作成功')
          loadData()
          homeModal.value = false
        },
      )
    }
  })
}

function handleHomeCancel() {
  homeModal.value = false
}

function handleReviewConfirm(row: any) {
  reviewFormRef.value?.validate((errors: any) => {
    if (!errors) {
      postNewsAudit(reviewId.value, String(checkFormData.value.reviewed)).then(
        () => {
          window.$message.success('审核成功')
          loadData()
          reviewModal.value = false
        },
      )
    }
  })
}

function handleReviewCancel() {
  reviewModal.value = false
}

function handleClickHome() {
  homeModal.value = true
}

/** 列表操作 */
const tableColumns = getTableColumns(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
          display: 'flex',
          gap: '15px',
        },
      },
      [
        h(
          'span',
          {
            onClick: () =>
              router.push({
                name: 'newsAdd',
                query: { id: row.id, model: 'view' },
              }),
          },
          { default: () => '查看' },
        ),
        h(
          'span',
          {
            onClick: () =>
              router.push({
                name: 'newsAdd',
                query: { id: row.id, model: 'modify' },
              }),
          },
          { default: () => '编辑' },
        ),
        judgePermission('pub_news_check')
          ? h(
            'span',
            {
              onClick: () =>
                handleReview(row.id, row.title, row.reviewedStatus),
            },
            { default: () => '审核' },
          )
          : null,
        judgePermission('pub_news_del')
          ? h(DeleteButton, {
            handleConfirm: () => handleSingleDelete(String(row.id)),
          })
          : null,
      ],
    )
  },
  row =>
    h(NSwitch, {
      disabled: row.reviewedStatus !== 1,
      onUpdateValue: (value: any) => switchTop(value, row.id),
      value: Boolean(Number(row.isTop)),
    }),
  row =>
    h(NSwitch, {
      disabled: row.reviewedStatus !== 1,
      onUpdateValue: (value: any) => switchRecommend(value, row.id),
      value: Boolean(Number(row.isRecommend)),
    }),
  // row =>
  //   h(NSwitch, {
  //     disabled: row.reviewedStatus !== 1,
  //     onUpdateValue: (value: any) => switchHidden(value, row.id),
  //     value: Boolean(Number(row.isHidden)),
  //   }),
  row =>
    h(NSwitch, {
      disabled: row.reviewedStatus !== 1,
      onUpdateValue: (value: any) => switchRotationPool(value, row.id),
      value: Boolean(Number(row.sliderFlag || 0)),
    }),
)

/** 点击添加按钮 */
function handleClickAdd() {
  router.push({
    name: 'newsAdd',
    query: { categoryId: filterRef.value.categoryId, model: 'add' },
  })
}

const defaultSelectedKeys = ref<string[]>([])

function filterInput(res: { id: string; name: string }) {
  // loadData()
  defaultSelectedKeys.value = [res.id]
  filterRef.value.categoryId = res.id
  selectName.value = res.name
}

const settingOptions = ref([])

onMounted(() => {
  init().then((res: any) => {
    defaultSelectedKeys.value = [res.id]
    filterRef.value.categoryId = res.id
    selectName.value = res.name
  })

  getNewsOption().then((res: any) => {
    settingOptions.value = res.map((item: any) => ({
      value: item.id,
      label: item.name,
    }))

    res.forEach((item: any) => {
      if (item.showOnHome) {
        homeFormRef.value.homeType = item.id
      }
    })
  })
})
</script>
<template>
  <layout-container style="height: calc(100vh - 114px)">
    <template #side>
      <SideMenuNew
        v-model:show-modal="showModal"
        :default-selected-keys="defaultSelectedKeys"
        :modal-title="modalTitle"
        :tree-data="treeData"
        title="资讯管理"
        @move="moveNode"
        @del-node="delNode"
        @save-tree-node="handleFormatterParams"
        @add-child-node="handleAddChildNode"
        @select-node-key="handleChangeTab"
      />
    </template>
    <template #main>
      <table-container
        v-model:page="currentPage"
        v-model:page-size="pageSize"
        :checked-row-keys="checkedRowKeys"
        :loading="loading"
        :show-add="judgePermission('pub_news_add')"
        :show-delete="judgePermission('pub_news_del')"
        :show-toolbar="false"
        :table-columns="tableColumns"
        :table-data="tableData"
        :title="selectName"
        :total="total"
        custom-toolbar
        @click-add="handleClickAdd"
        @click-delete="handleBatchDelete"
        @update-page="onUpdatePage"
        @update-page-size="onUpdatePageSize"
        @update-checked-row-keys="onUpdateCheckedRowKeys"
      >
        <template #btns>
          <template v-if="judgePermission('pub_news_home')">
            <n-button size="small" type="primary" @click="handleClickHome">
              首页资讯管理
            </n-button>
          </template>
          <template v-if="judgePermission('pub_news_add')">
            <n-button size="small" type="primary" @click="handleClickAdd">
              <template #icon>
                <n-icon>
                  <plus-round />
                </n-icon>
              </template>
              添加
            </n-button>
          </template>

          <template v-if="judgePermission('pub_news_del')">
            <n-button size="small" @click="handleBatchDelete">
              <template #icon>
                <n-icon>
                  <delete-forever-round />
                </n-icon>
              </template>
              删除
            </n-button>
          </template>
        </template>
        <template #filters>
          <n-input
            v-model:value="filterRef.title"
            clearable
            placeholder="请输入资讯标题"
            size="small"
            style="width: 200px"
          />
        </template>
      </table-container>
    </template>
  </layout-container>
  <CustomDialog
    :show="showModal"
    :title="modalTitle"
    width="600px"
    @cancel="handleCancel"
    @confirm="handleFormatterParams"
    @update:show="(v: boolean) => (showModal = v)"
  >
    <div class="p-[20px]">
      <AddNewsCategoryForm
        v-show="showModalType === 'root'"
        ref="addFirstCategoryRef"
      />
    </div>
  </CustomDialog>

  <CustomDialog
    :show="reviewModal"
    :title="reviewTitle"
    width="400px"
    @cancel="handleReviewCancel"
    @confirm="handleReviewConfirm"
    @update:show="(v: boolean) => (reviewModal = v)"
  >
    <div class="p-[20px]">
      <n-form
        ref="reviewFormRef"
        :model="checkFormData"
        :rules="checkFormRules"
        label-placement="left"
        label-width="120px"
        require-mark-placement="left"
      >
        <n-form-item label="是否通过审核：" path="reviewed">
          <n-switch
            v-model:value="checkFormData.reviewed"
            :checked-value="1"
            :unchecked-value="0"
          />
        </n-form-item>
      </n-form>
    </div>
  </CustomDialog>

  <CustomDialog
    :show="homeModal"
    title="首页资讯管理"
    width="400px"
    @cancel="handleHomeCancel"
    @confirm="handleHomeConfirm"
    @update:show="(v: boolean) => (homeModal = v)"
  >
    <div class="p-[20px]">
      <n-form
        ref="homeDataFormRef"
        :model="homeFormRef"
        :rules="homeDataRules"
        label-placement="left"
        label-width="160px"
        require-mark-placement="left"
      >
        <n-form-item label="展示在首页的分类：" path="homeType">
          <n-select
            v-model:value="homeFormRef.homeType"
            :options="settingOptions"
            clearable
            filterable
            placeholder="请选择展示在首页的分类："
            style="width: 260px"
          />
        </n-form-item>
      </n-form>
    </div>
  </CustomDialog>
</template>
<style lang="scss" scoped></style>
