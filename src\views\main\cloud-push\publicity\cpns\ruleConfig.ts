import type { FormRules } from 'naive-ui'
import { isHttpOrHttpsLink } from '@/utils/utils'

export const publicityFormRules: FormRules = {
  pioneerName: {
    required: true,
    message: '请输入先锋号名称',
    trigger: 'input',
    type: 'string',
  },
  iconUrl: {
    required: true,
    message: '请上传图片',
    trigger: 'change',
    type: 'string',
  },
  isRecommand: {
    required: true,
    message: '请选择是否推荐',
    trigger: 'change',
    type: 'number',
  },
  isHidden: {
    required: true,
    message: '请选择是否隐藏',
    trigger: 'change',
    type: 'number',
  },
  sliderFlag: {
    required: true,
    message: '请选择是否加入首页轮播池',
    trigger: 'change',
    type: 'number',
  },
  deptId: {
    required: true,
    message: '所属党组织不能为空',
    trigger: 'change',
  },
  briefDesc: {
    required: true,
    message: '请输入先锋号简介',
    trigger: 'input',
    type: 'string',
  },
}

export const publicityCategoryFormRules: FormRules = {
  pionnerName: {
    required: true,
    message: '请输入先锋号名称',
    trigger: 'input',
    type: 'string',
  },
  name: {
    required: true,
    message: '请输入分类名称',
    trigger: 'input',
    type: 'string',
  },
  configStudyScore: {
    required: true,
    message: '请输入学习可得的分数',
    trigger: 'input',
    type: 'number',
  },
}

export const newsDetailFormRules: FormRules = {
  title: {
    required: true,
    message: '请输入新闻标题',
    trigger: 'input',
    type: 'string',
  },
  readNum: {
    required: true,
    message: '请输入阅读数量',
    trigger: 'input',
    type: 'number',
  },
  coverUrl: {
    required: true,
    message: '请上传图片',
    trigger: 'change',
    type: 'string',
  },
  isTop: {
    required: true,
    message: '请选择是否置顶',
    trigger: 'change',
    type: 'number',
  },
  isRecommand: {
    required: true,
    message: '请选择是否推荐',
    trigger: 'change',
    type: 'number',
  },
  isHidden: {
    required: true,
    message: '请选择是否隐藏',
    trigger: 'change',
    type: 'number',
  },
  content: {
    required: true,
    message: '请输入正文',
    trigger: 'input',
    type: 'string',
  },
  linkUrl: {
    required: true,
    // message: '请输入正文',
    validator(rule: any, value: any) {
      if (value === null || !isHttpOrHttpsLink(value)) {
        return new Error('请输入合法的链接地址')
      }
      return true
    },
    trigger: 'input',
    type: 'string',
  },
}

export const checkFormRules: FormRules = {
  reviewed: {
    required: true,
    message: '请选择是否审核通过',
    trigger: 'change',
    type: 'number',
  },
}
