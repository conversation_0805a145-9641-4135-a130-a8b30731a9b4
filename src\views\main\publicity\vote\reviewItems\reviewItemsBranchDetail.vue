<template>
  <div>
    <div class="title">
      新增评议项
    </div>
    <div class="greySplit">
      <div class="subTitle">
        基本信息
      </div>
    </div>
    <NForm
      ref="formRef"
      :model="reviewItemDetail"
      :rules="reviewItemsPersonDetailRules"
      label-placement="left"
      require-mark-placement="left"
      class="w-[675px]"
    >
      <NFormItem
        class="text-13px text-highlightBlue mt-4 ml-5 mr-4"
        label="评议项名称："
        path="reviewItemTitle"
      >
        <NInput
          v-model:value="reviewItemDetail.reviewItemTitle"
          class="mx-10px"
          placeholder="请输入评议项名称"
        />
      </NFormItem>

      <NFormItem
        class="text-13px text-highlightBlue mt-4 ml-5 mr-4"
        label="评议项简介："
        path="reviewItemRemark"
      >
        <NInput
          v-model:value="reviewItemDetail.reviewItemRemark"
          class="mx-10px"
          placeholder="请输入评议项简介"
          type="textarea"
        />
      </NFormItem>
    </NForm>
    <div v-for="(type, index) in typeKeyArr" :key="index">
      <reviewItemsListCard
        v-model:questionnaireInfo="reviewItemDetail[type]"
        title="支部班子"
        :init-loaded="initLoaded"
        :template-item-list="templateDetail"
      />
    </div>
    <div class="line" />
    <div class="flex ml-9">
      <NButton
        v-if="route.query.action !== 'view'"
        type="primary"
        class="!w-[80px] !h-[30px]"
        @click="save"
      >
        保存
      </NButton>
      <NButton
        class="!ml-4 !mb-4 !w-[80px] !h-[30px]"
        :on-click="
          () => {
            back()
          }
        "
      >
        {{ route.query.action === 'view' ? '返回' : '取消' }}
      </NButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { NButton, NInput } from 'naive-ui'
import reviewItemsListCard from './reviewItemsListCard.vue'
import type { ReviewItem, RuleItem, Rules, TemplateItem, Test } from './type'
import { reviewItemsPersonDetailRules } from './config'
import { BOOLEANTYPE, QUESTIONTYPE } from '@/constant'
import { emitter } from '@/utils/event-bus'
import {
  addReviewItemDetailBranch,
  getReviewItemDetail,
  getReviewTemplate,
  putReviewItemDetailBranch,
} from '@/services/publicity/vote/talkAbout'
import { checkOverlapIntervals, removeIdProperties } from '@/utils/utils'

const loading = ref(false)
const route = useRoute()
const router = useRouter()
const formRef = ref<InstanceType<typeof NForm>>()
const reviewItemDetail = reactive<Test>({
  reviewItemTitle: '', // 评议项名称
  reviewItemRemark: '', // 评议项简介
  reviewItemType: '', // 评议项类型

  // 支部班子
  branchQuestionnaireObj: {
    questionnaireType: '1', // 问卷类型
    isTemplate: '0', // 是否展示模板
    isDisplay: '1', // 是否展示问卷
    isScoreItem: '1', // 是否开启总分项
    ruleScoreName: '', // 总分项名称
    questionnaireScoreUpper: 0, // 分数上限
    reviewSubjectArr: [] as ReviewItem[], // 题目集合
    questionnaireRuleObj: {
      id: '',
      questionnaireId: '',
      ruleScoreName: '',
      questionnaireRuleDetailArr: [] as RuleItem[],
    } as Rules,
  },
})
// 模板数据
const templateDetail = ref<TemplateItem[]>([])

const typeKeyArr = ['branchQuestionnaireObj']

const initLoaded = ref(false) // 标志初始化完成

getDetails()

function getDetails() {
  getReviewTemplate().then((resTemp) => {
    templateDetail.value = resTemp.templateItemList
    if (route.query.id) {
      getReviewItemDetail({ id: route.query.id as string }).then((res) => {
        Object.assign(reviewItemDetail, res)

        if (route.query.action === 'copy') {
          // 复制新建删除 id
          removeIdProperties(reviewItemDetail)
        }

        initLoaded.value = true
        // 根据监听过滤模板、初次加载不需要根据监听过滤、所以此处nextTick
      })
    } else {
      initLoaded.value = true
    }
  })
}

// /** 排他 */
function clearEditing(type: string) {
  reviewItemDetail[type].reviewSubjectArr.forEach(
    (item: ReviewItem) => (item.editing = false),
  )
}

/** 保存 */
async function save() {
  loading.value = true

  formRef.value?.validate((errors: any) => {
    try {
      if (!errors) {
        const showTip = false
        // 校验表单并设置sort
        for (let i = 0; i < typeKeyArr.length; i++) {
          const type = typeKeyArr[i]
          if (reviewItemDetail[type].isDisplay === BOOLEANTYPE.YES) {
            if (reviewItemDetail[type].isScoreItem === BOOLEANTYPE.YES) {
              if (
                !reviewItemDetail[type].questionnaireRuleObj
                  .questionnaireRuleDetailArr.length
                || !reviewItemDetail[type].questionnaireRuleObj.ruleScoreName
              ) {
                throw new Error('开启总分项，需输入总分项信息！')
              } else if (
                reviewItemDetail[type].questionnaireRuleObj
                  .questionnaireRuleDetailArr.length
              ) {
                for (
                  let i = 0;
                  i
                  < reviewItemDetail[type].questionnaireRuleObj
                    .questionnaireRuleDetailArr.length;
                  i++
                ) {
                  const ruleDetail
                    = reviewItemDetail[type].questionnaireRuleObj
                      .questionnaireRuleDetailArr[i]
                  if (!ruleDetail.scoreItemName) {
                    throw new Error('总分项列表各项名称不可为空！')
                  }

                  if (
                    Number(ruleDetail.scoreUpper)
                    < Number(ruleDetail.scoreLower)
                  ) {
                    throw new Error('总分项列表分数上限不可小于分数下限！')
                  }
                }

                // 分数上下限数组
                const scoreArr = reviewItemDetail[
                  type
                ].questionnaireRuleObj.questionnaireRuleDetailArr.map(
                  (item: RuleItem) => [
                    Number(item.scoreLower),
                    Number(item.scoreUpper),
                  ],
                )

                if (checkOverlapIntervals(scoreArr)) {
                  throw new Error('总分项列表分数区间不可重合！')
                }
              }
            }
            reviewItemDetail[type].reviewSubjectArr.forEach(
              (question: ReviewItem, qi: number) => {
                if (question.id) {
                  // showTip = true
                }
                if (question.subjectType === null) {
                  clearEditing(type)
                  setEditing(qi, type)
                  throw new Error('请选择题型')
                }
                if (!question.subjectName.trim()) {
                  clearEditing(type)
                  setEditing(qi, type)
                  throw new Error('请输入题目信息')
                }
                if (
                  [QUESTIONTYPE.RADIO, QUESTIONTYPE.MULTI].includes(
                    question.subjectType,
                  )
                  && !question.reviewSubjectItemArr?.length
                ) {
                  clearEditing(type)
                  setEditing(qi, type)
                  throw new Error('请添加选项')
                }
                nextTick(() => {
                  emitter.emit('notification-template-evaluation-validate')
                })
                question.sort = qi + 1
                question.reviewSubjectItemArr?.forEach((choice, ci) => {
                  if (!choice.subjectItemName.trim()) {
                    clearEditing(type)
                    setEditing(qi, type)
                    throw new Error('请输入选项内容')
                  }
                  if (choice.subjectItemScore === null) {
                    clearEditing(type)
                    setEditing(qi, type)
                    throw new Error('请输入选项分值')
                  }
                  choice.sort = ci + 1
                })
              },
            )
          }
        }

        const fn = async() => {
          if (route.query.id && route.query.action === 'modify') {
            await putReviewItemDetailBranch(reviewItemDetail)
            window.$message.success('保存成功')
          } else {
            await addReviewItemDetailBranch(reviewItemDetail)
            window.$message.success('保存成功')
          }

          back()
        }
        if (showTip) {
          window.$dialog.info({
            title: '提示',
            content: '已推送的评价问卷不会被更新，是否继续？',
            positiveText: '是',
            negativeText: '否',
            onPositiveClick: fn,
          })
        } else {
          fn()
        }
      } else {
        window.$message.error('请校验表单必填项')
      }
    } catch (e) {
      window.$message.error((e as Error).message)
    } finally {
      loading.value = false
    }
  })
}

function back() {
  router.push({ name: 'review-items-branch' })
}

/** 设置第 n 个题目为编辑中 */
function setEditing(i: number, type: string) {
  reviewItemDetail[type].reviewSubjectArr[i].editing = true
}
</script>

<style lang="scss" scoped>
.greySplit {
  width: calc(100% - 40px);
  height: 48px;
  background: #f5f6f8;
  margin-left: 20px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  .subTitle {
    position: relative;
    padding-left: 28px;
    font-size: 15px;
    font-weight: 600;
  }
  .subTitle::before {
    content: ''; /* 创建伪元素 ::before */
    display: block; /* 将其显示为块级元素 */
    width: 3px; /* 设置竖线的宽度 */
    height: 16px; /* 根据需求调整高度（去除上下边距）*/
    background-color: #cb0000; /* 设置竖线颜色 */
    position: absolute; /* 设置绝对定位 */
    left: 15px;
    top: 3px; /* 根据需求调整与文本之间的距离 */
    border-radius: 2px;
  }
}
.title {
  font-weight: 700;
  margin-left: 20px;
  margin-top: 27px;
}

.line {
  width: 100%;
  border-top: 1px solid #f2f3f6;
  margin: 52px 0 15px 0;
}

:deep(.n-form-item) .n-form-item-label,
:deep(.n-form-item) .n-input {
  font-size: 14px;
}
</style>
