<script setup lang="ts">
import { ArrowBackIosNewRound, AssessmentOutlined } from '@vicons/material'

import { Delete, Edit, Export } from '@vicons/carbon'
import { IosAddCircle } from '@vicons/ionicons4'
import type { DataTableColumns } from 'naive-ui'
import { NButton, NDatePicker, NEllipsis, NIcon } from 'naive-ui'
import NoticeForm from '../../exam-indicators/cpn/NoticeForm.vue'
import Correlate from './correlate/Correlate.vue'
import CategoryForm from './cpn/CategoryForm.vue'
import ScoreStatisticForPartyBuildingExam from '@/components/ScoreStatisticForPartyBuildingExam.vue'
import GradeStatisticForPartyBuildingExam from '@/components/GradeStatisticForPartyBuildingExam.vue'
import type {
  AllTargetItemListRow,
  AllTargetList,
} from '@/services/publicity/vote/evaluation/type'
import { downloadArrayBuffer } from '@/utils/downloader'
import { formatTimeStamp } from '@/utils/format'
import {
  addAssessIssue,
  addAssessIssueExpirationDate,
  delAssessIssueItem,
  delPartyBuildingEvaluationCategory,
  exportPartyBuildingExamScoreFile,
  exportPartyBuildingExamTargetFile,
  viewEvaluationIssue,
} from '@/services/publicity/vote/evaluation'

const route = useRoute()
const router = useRouter()
const title = route.query.title
const status = computed(() => route.query.status)

const partyListId = computed(() => route.query.partyListId)
const targetListRef = ref<AllTargetList>()
const assessId = ref()
const allTargetItemList = ref<AllTargetItemListRow[]>([])

const loading = ref(false)
/** 加载指标数据 */
function loadTargetListData() {
  loading.value = true
  viewEvaluationIssue(String(partyListId.value)).then((res: any) => {
    if (res) {
      targetListRef.value = res
      assessId.value = res.assessId
      allTargetItemList.value = res.targetItemForm?.flatMap(
        (item: any) => item.targetItemList,
      )
    }
  })
  loading.value = false
}

const idEditRef = ref()
const categoryIdEditRef = ref()
const nameEditRef = ref()

const categoryFormRef = ref()
const categoryModalVisible = ref(false)
const editTypeRef = ref<'add' | 'modify' | 'view'>('add')
const dialogTitle = computed(
  () => `${editTypeRef.value === 'add' ? '创建' : '编辑'}考核类别`,
)

/** 添加类别弹窗 */
function handleAddCategory() {
  editTypeRef.value = 'add'
  nameEditRef.value = ''
  idEditRef.value = partyListId.value
  categoryModalVisible.value = true
}
/** 添加类别表单确认 */
function handleCategorySaved() {
  categoryModalVisible.value = false
  loadTargetListData()
}
/** 添加类别弹窗确认 */
function handleConfirm() {
  categoryFormRef.value?.validateAndSave()
}

/** 编辑类别 */
function handleEditCategory(categoryId: string, categoryName: string) {
  categoryIdEditRef.value = categoryId
  nameEditRef.value = categoryName
  editTypeRef.value = 'modify'
  categoryModalVisible.value = true
}
/** 删除类别 */
function handleDeleteCategory(categoryId: string) {
  window.$dialog.info({
    title: '删除考核类别',
    content: '确定删除分类？如已关联指标项，则指标项内容会被同步删除',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      loading.value = true
      delPartyBuildingEvaluationCategory(categoryId).then(() => {
        window.$message.success('删除成功')
        loadTargetListData()
      })
      loading.value = false
    },
  })
}

/** 得分统计弹框状态 */
const ScoreStatisticModalVisible = ref(false)
const scoreStatisticRef = ref()

// 点击得分统计按钮
function handleScoreStatistics() {
  ScoreStatisticModalVisible.value = true
  scoreStatisticRef.value?.loadScoreList(String(partyListId.value))
}

/** 得分统计确认 */
async function ScoreStatisticConfirm() {
  try {
    loading.value = true
    const res = await exportPartyBuildingExamScoreFile(
      String(partyListId.value),
    )
    downloadArrayBuffer(
      res,
      `得分统计-${formatTimeStamp(Date.now(), 'YYYYMMDDHHmmss')}`,
    )
    loading.value = false
    ScoreStatisticModalVisible.value = false
  } catch (error) {}
}

/** 导出指标 */
async function handleExport() {
  try {
    loading.value = true
    const res = await exportPartyBuildingExamTargetFile(
      String(partyListId.value),
    )
    downloadArrayBuffer(
      res,
      `党建考核-${formatTimeStamp(Date.now(), 'YYYYMMDDHHmmss')}`,
    )
    loading.value = false
  } catch (error) {}
}

// 打分
const GradeStatisticModalVisible = ref(false)
// const relationIdRef = ref()
const markRef = ref()
const scoreRef = ref()

/** 点击打分按钮 */

function handleMarking(index: number) {
  const id = allTargetItemList.value[index].id
  scoreRef.value = allTargetItemList.value[index].evaluationScore
  GradeStatisticModalVisible.value = true
  markRef.value?.loadMarkListData(String(id))
}

/** 打分弹窗关闭刷新页面 */
function handleCancel() {
  nextTick(() => {
    markRef.value.clearEditing() // 清除所有行的编辑状态
  })
  loadTargetListData()
}

// 关联指标项
const correlateModalVisible = ref(false)
const correlateRef = ref()
const correlateTableRef = ref()
// 点击关联指标项按钮
// 点击关联指标项按钮
function handleCorrelate(categoryId: string) {
  categoryIdEditRef.value = categoryId
  correlateModalVisible.value = true
}
// function handleRelateCancel() {
// categoryIdEditRef.value = ''
// }
// 选择的key值
const rowKeys = ref<number[]>([])

function handleRowKeys(v: number[]) {
  rowKeys.value = v
}
async function handleCorrelateConfirm() {
  try {
    loading.value = true
    const data = {
      categoryId: String(categoryIdEditRef.value),
      targetItemIds: rowKeys.value,
      assessId: String(assessId.value),
    }
    await addAssessIssue(data)
    window.$message.success('指标关联成功')
    loadTargetListData()
    correlateModalVisible.value = false
    loading.value = false
  } catch (err) {}
}

/** 获取当前索引 */
function getIndex(id: string) {
  return allTargetItemList.value.findIndex(item => item.id === id)
}
/** 截止时间更改 */
async function handleDeadLineTime(id: string, formattedValue: number) {
  try {
    loading.value = true
    const data = {
      id,
      deadline: formatTimeStamp(formattedValue, 'YYYY-MM-DD'),
    }
    await addAssessIssueExpirationDate(data)
    window.$message.success('截止日期更新成功')
    loadTargetListData()
    loading.value = false
  } catch (error) {}
}

const addNoticeFormRef = ref()
const targetEditId = ref()
const showEditRef = ref()

/** 点击编辑按钮 */
function handleEditSingleTarget(id: string, status: boolean) {
  targetEditId.value = id
  editTypeRef.value = status ? 'view' : 'modify'
  showEditRef.value = true
}
const drawerTitle = computed(() =>
  editTypeRef.value === 'add'
    ? '添加指标'
    : editTypeRef.value === 'modify'
      ? '编辑指标'
      : '查看指标',
)

/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadTargetListData()
}

function handleClickConfirm() {
  addNoticeFormRef.value?.validateAndSave()
}

function handleClickCancel() {
  showEditRef.value = false
}
/** 点击删除按钮 */
async function handleDeleteSingleTarget(index: number) {
  try {
    loading.value = true
    const id = allTargetItemList.value[index].id
    await delAssessIssueItem(id)
    window.$message.success('删除成功')
    loadTargetListData()
    loading.value = false
  } catch (error) {}
}

const tableColumns: DataTableColumns<AllTargetItemListRow> = [
  {
    key: 'index',
    title: '序号',
    width: '4%',
    align: 'center',
    render: (_, i) => i + 1,
  },
  {
    key: 'matter',
    width: '12%',
    title: '考核指标',
    render: (row) => {
      return h(
        NEllipsis,
        {
          style: {
            maxWidth: '280px',
          },
          expandTrigger: 'click',
          lineClamp: '1',
          tooltip: true,
        },
        {
          default: () => {
            return h('span', {
              innerHTML: row.matter,
            })
          },
        },
      )
    },
  },
  {
    key: 'evaluationRequirements',
    width: '17%',
    title: '计分标准',
    render: (row) => {
      return h(
        NEllipsis,
        {
          style: {
            maxWidth: '280px',
          },
          expandTrigger: 'click',
          lineClamp: '1',
          tooltip: true,
        },
        {
          default: () => {
            return h('span', {
              innerHTML: row.evaluationRequirements,
            })
          },
        },
      )
    },
  },
  {
    key: 'evaluationMode',
    width: '17%',
    title: '检查材料',
    render: (row) => {
      return h(
        NEllipsis,
        {
          style: {
            maxWidth: '280px',
          },
          expandTrigger: 'click',
          lineClamp: '1',
          tooltip: true,
        },
        {
          default: () => {
            return h('span', {
              innerHTML: row.evaluationMode,
            })
          },
        },
      )
    },
  },
  {
    key: 'dept',
    title: '检测部门',
    width: '14%',
    render: (row) => {
      return h(
        NEllipsis,
        {
          style: {
            maxWidth: '280px',
          },
          expandTrigger: 'click',
          lineClamp: '1',
          tooltip: true,
        },
        {
          default: () => {
            return h('span', {
              innerHTML: row.dept,
            })
          },
        },
      )
    },
  },
  {
    key: 'evaluationScore',
    width: '8%',
    align: 'center',
    title: '分值',
  },
  {
    key: 'endTime',
    width: '12%',
    title: '截止日期',
    render: (row) => {
      return (status.value === '打分中' || status.value === '已完成')
        && !allTargetItemList.value[getIndex(row.id)].endTime
        ? '--'
        : h(NDatePicker, {
          type: 'date',
          disabled: status.value === '打分中' || status.value === '已完成',
          formattedValue: allTargetItemList.value[getIndex(row.id)].endTime,
          onUpdateFormattedValue: (v: any) => handleDeadLineTime(row.id, v),
          // onConfirm: (v: any) => handleDeadLineTime(row.id, v),
        })
    },
  },
  {
    key: 'passNumAndTotalNum',
    width: '8%',
    align: 'center',
    title: '评分进度',
    render: (row) => {
      return `${row.passNum}/${row.totalNum}`
    },
  },
  {
    key: 'options',
    title: '操作',
    width: '10%',
    align: 'center',
    render: (row, i) => {
      return h('div', [
        status.value === '打分中' || status.value === '已完成'
          ? ''
          : h(
            NButton,
            {
              text: true,
              type: 'primary',
              style: 'margin-right:10px',
              onClick: () =>
                handleEditSingleTarget(row.targetId, row.relatedStatus),
            },
            {
              default: () => (row.relatedStatus ? '查看' : '编辑'),
            },
          ),
        status.value === '打分中' || status.value === '已完成'
          ? ''
          : h(
            NButton,
            {
              text: true,
              type: 'primary',
              style: 'margin-right:10px',
              onClick: () => handleDeleteSingleTarget(getIndex(row.id)),
            },
            '删除',
          ),
        (status.value === '打分中' || status.value === '已完成')
          && h(
            NButton,
            {
              text: true,
              type: 'primary',
              onClick: () => handleMarking(getIndex(row.id)),
            },
            '打分',
          ),
      ])
    },
  },
]

onMounted(loadTargetListData)
</script>
<template>
  <edit-top>
    <template #left>
      <n-button size="small" @click="() => router.back()">
        <n-icon size="16">
          <arrow-back-ios-new-round />
        </n-icon>
        返回
      </n-button>
    </template>
    <template #mid>
      {{ title }}
    </template>
    <template #right>
      <n-button
        v-if="status === '打分中' || status === '已完成'"
        @click="handleScoreStatistics"
      >
        <n-icon style="margin-right: 6px" size="16">
          <assessment-outlined />
        </n-icon>
        得分统计
      </n-button>
      <n-button
        v-if="status === '打分中' || status === '已完成'"
        @click="handleExport"
      >
        <n-icon style="margin-right: 6px" size="14">
          <export />
        </n-icon>
        导出
      </n-button>
    </template>
  </edit-top>

  <div class="px-[20px] py-[23px]">
    <div
      class="bg-[#fafafa] pl-[12px] pr-[20px] text-center leading-[42px] text-[12px] font-[500] mb-[11px] text-[#333] grid grid-cols-[1fr,2fr,3fr,3fr,3fr,1fr,2fr,1.5fr,2fr]"
    >
      <span class="text-center">序号</span>
      <span>考核指标</span>
      <span>计分标准</span>
      <span class="text-center ml-[40px]">检查材料</span>
      <span>检查部门</span>
      <span class="ml-[20px]">分值</span>
      <span class="text-center ml-[60px]">截止日期</span>
      <span class="text-right">评分进度</span>
      <span class="text-center ml-[40px]">操作</span>
    </div>
    <n-scrollbar style="width: 100%; height: calc(100vh - 330px)">
      <div
        v-for="(item, index) in targetListRef?.targetItemForm"
        :key="index"
        class="bg-[#fafafa] px-[20px] py-[22px] mb-[20px]"
      >
        <div class="flex justify-between items-center mb-[22px]">
          <div class="flex items-center gap-[20px]">
            <span
              class="text-[12px] text-[#CB0000] font-[500] leading-[17px] ml-[10px]"
            >类别：{{ item.categoryName }}</span>
            <n-button
              v-if="status !== '打分中' && status !== '已完成'"
              text
              @click="handleEditCategory(item.id, item.categoryName)"
            >
              <n-icon size="16" color="#333">
                <Edit />
              </n-icon>
            </n-button>
            <n-button
              v-if="status !== '打分中' && status !== '已完成'"
              text
              @click="handleDeleteCategory(item.id)"
            >
              <n-icon size="16" color="#333">
                <Delete />
              </n-icon>
            </n-button>
          </div>
          <div
            v-if="status !== '打分中' && status !== '已完成'"
            class="rounded-[1px] bg-[#CB0000] w-[80px] h-[24px] text-[12px] text-[#fff] text-center leading-[24px] cursor-pointer"
            @click="handleCorrelate(item.id)"
          >
            关联指标项
          </div>
        </div>
        <div>
          <n-data-table
            id="targetTable"
            :bordered="true"
            :single-line="false"
            :columns="tableColumns"
            :data="item.targetItemList"
          />
        </div>
      </div>

      <div
        v-if="status !== '打分中' && status !== '已完成'"
        class="bg-[#fafafa] px-[20px] py-[22px] mb-[20px] flex justify-center"
      >
        <n-button
          text
          text-color="#4899d0"
          size="large"
          icon-placement="left"
          @click="handleAddCategory"
        >
          <template #icon>
            <n-icon size="30" color="#4899d0" class="mr-[30px]">
              <ios-add-circle />
            </n-icon>
          </template>
          <span class="text-[20px]">创建考核类别</span>
        </n-button>
      </div>
    </n-scrollbar>

    <div class="sticky bottom-0 z-1 bg-[#fff]">
      <div
        class="bg-[#fafafa] px-[20px] leading-[42px] text-[12px] font-[500] text-[#cb0000]"
      >
        总分：{{ targetListRef?.totalScore }}分
      </div>
    </div>
  </div>

  <ScoreStatisticForPartyBuildingExam
    :id="idEditRef"
    ref="scoreStatisticRef"
    v-model:show="ScoreStatisticModalVisible"
    belong="discipline"
    title="得分统计"
    @confirm="ScoreStatisticConfirm"
  />

  <custom-dialog
    ref="correlateRef"
    v-model:show="correlateModalVisible"
    width="1200px"
    title="关联指标项"
    @confirm="handleCorrelateConfirm"
  >
    <correlate
      ref="correlateTableRef"
      :assess-id="assessId"
      :category-id="categoryIdEditRef"
      @update:value="handleRowKeys"
    />
  </custom-dialog>

  <custom-dialog
    v-model:show="categoryModalVisible"
    :title="dialogTitle"
    @confirm="handleConfirm"
  >
    <category-form
      :id="idEditRef"
      ref="categoryFormRef"
      :category-id="categoryIdEditRef"
      :name="nameEditRef"
      :type="editTypeRef"
      @saved="handleCategorySaved"
    />
  </custom-dialog>

  <GradeStatisticForPartyBuildingExam
    ref="markRef"
    v-model:show="GradeStatisticModalVisible"
    :exam-score="scoreRef"
    belong="discipline"
    title="打分"
    @cancel="handleCancel"
  />

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <notice-form
        :id="targetEditId"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped>
:deep(.n-data-table .n-data-table-thead) {
  display: none;
}

:deep(.n-data-table .n-data-table-thead) {
  display: none;
}

.emptyImg {
  width: 100%;
  height: 500px;
  background-image: url('@/assets/image/emptyImg.png');
  background-position: center center;
  background-repeat: no-repeat;
}
</style>
