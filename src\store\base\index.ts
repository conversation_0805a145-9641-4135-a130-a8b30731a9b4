import { defineStore } from 'pinia'
import type { TreeOption } from 'naive-ui'
import { fetchCompanyDeptUserList } from '@/services/common/index'

const useBaseStore = defineStore('base', {
  state: () => ({
    companyDeptUserList: [] as TreeOption[],
    allUserLabels: [] as string[],
  }),

  actions: {
    async getCompanyDeptUserListAction() {
      const res = await fetchCompanyDeptUserList()

      // 将返回的结果转成naive树结构
      const { userLabels, treeOptions } = transformToTreeOptions(res)

      this.allUserLabels = userLabels
      this.companyDeptUserList = treeOptions
    },
  },
})

/**
 * 将返回的部门人员树转成TreeOptions
 */
function transformToTreeOptions(data: any[]) {
  const userLabels: string[] = []

  function recursive(arr: any[]) {
    let treeOptions: TreeOption[] = []
    // for (const item of arr) {
    //   if (
    //     (!item.children || item.children.length === 0)
    //     // && (!item.userList || item.userList.length === 0)
    //   ) {
    //     continue
    //   }
    //   else {
    //     let parent: TreeOption = {}
    //     if (item.children && item.children.length) {
    //       parent = {
    //         key: `dept${item.id}`,
    //         label: item.name,
    //         children: recursive(item.children),
    //       }
    //     }
    //     // else if (item.userList && item.userList.length) {
    //     //   parent = {
    //     //     key: `dept${item.id}`,
    //     //     label: item.name,
    //     //     children: item.userList.map((user: any) => {
    //     //       const label = `${user.trueName}&${user.userId}`
    //     //       userLabels.push(label)
    //     //       return {
    //     //         key: label,
    //     //         label: user.trueName,
    //     //       }
    //     //     }),
    //     //   }
    //     // }
    //     treeOptions.push(parent)
    //   }
    // }
    treeOptions = arr.map((item) => {
      return {
        key: item.id,
        label: item.name,
        userList: item.userList,
        children:
          item.children && item.children.length
            ? recursive(item.children)
            : undefined,
      }
    })
    return treeOptions
  }
  return {
    treeOptions: recursive(data),
    userLabels,
  }
}

export default useBaseStore
