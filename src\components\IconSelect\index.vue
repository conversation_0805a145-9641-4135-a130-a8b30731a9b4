<template>
  <div class="icon-body">
    <n-input
      v-model:value="iconName"
      class="icon-search"
      clearable
      placeholder="请输入图标名称"
      @clear="filterIcons"
      @input="filterIcons"
    >
      <template #suffix>
        <i class="el-icon-search el-input__icon" />
      </template>
    </n-input>
    <div class="icon-list">
      <div class="list-container">
        <div
          v-for="(item, index) in iconList"
          :key="index"
          class="icon-item-wrapper"
          @click="selectedIcon(item)"
        >
          <div :class="['icon-item', { active: props.activeIcon === item }]">
            <Component
              :is="`icon-${item}`"
              class="w-[20px] h-[20px] mr-[10px]"
            />
            <n-popover trigger="hover">
              <template #trigger>
                <span>{{ item }}</span>
              </template>
              <span>{{ item }}</span>
            </n-popover>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import loadIcons from './requireIcons' // 修改导入的文件

const props = defineProps({
  activeIcon: {
    type: String,
  },
})

const iconName = ref('')
const iconList = ref<string[]>([]) // 初始化为空数组
const emit = defineEmits(['selected'])

// 异步加载图标数据
loadIcons().then((loadedIcons) => {
  iconList.value = loadedIcons
})

function filterIcons() {
  if (!iconName.value) {
    // 保持原始数据
    return
  }

  iconList.value = iconList.value.filter(item => item.indexOf(iconName.value) !== -1)
}

function selectedIcon(name: string) {
  emit('selected', name)
  document.body.click()
}

function reset() {
  iconName.value = ''
  loadIcons().then((loadedIcons) => {
    iconList.value = loadedIcons
  })
}

defineExpose({
  reset,
})
</script>

<style lang="scss" scoped>
.icon-body {
  width: 100%;
  padding: 10px;

  .icon-search {
    width: 96%;
    position: relative;
    margin-bottom: 5px;
  }

  .icon-list {
    height: 200px;
    overflow: auto;

    .list-container {
      display: flex;
      flex-wrap: wrap;

      .icon-item-wrapper {
        width: calc(100% / 3);
        height: 28px;
        line-height: 28px;
        cursor: pointer;
        display: flex;
        align-items: center;

        .icon-item {
          display: flex;
          align-items: center;
          max-width: 100%;
          height: 100%;
          padding: 0 5px;

          &:hover {
            background: #ececec;
            border-radius: 5px;
          }

          .icon {
            flex-shrink: 0;
          }

          span {
            display: inline-block;
            vertical-align: -0.15em;
            fill: currentColor;
            padding-left: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .icon-item.active {
          background: #ececec;
          border-radius: 5px;
        }
      }
    }
  }
}
</style>
