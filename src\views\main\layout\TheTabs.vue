<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { CloseRound } from '@vicons/material'
import useTabsStore from '@/store/tabs/tabs'
import type { ITab } from '@/services/tabs/types'
import router from '@/router'

const tabsStore = useTabsStore()
const { tabsOpened, currentTab } = storeToRefs(tabsStore)

// 点击tab
function handleClickTab(tab: ITab) {
  if (tab.routeName === currentTab.value) {
    return
  }
  router.push(tab.fullPath)
  tabsStore.setCurrentTab(tab.routeName)
}

// 关闭tab
function handleClickClose(tab: ITab) {
  tabsStore.deleteTab(tab)
}
</script>
<template>
  <n-scrollbar x-scrollable>
    <div class="tabs-box h-[54px] bg-[#f5f6f8] flex items-center px-[22px]">
      <div
        v-for="tab in tabsOpened"
        :key="tab.routeName"
        class="flex"
        :class="['tabItem', { active: currentTab === tab.routeName }]"
        @click="handleClickTab(tab)"
      >
        <div class="flex items-center">
          <Component
            :is="`icon-${tab.icon}`"
            class="w-[16px] h-[16px] mr-[5px]"
          />
          <span class="ml-[7px]">{{ tab.title }}</span>
        </div>
        <div
          v-if="tab.routeName !== 'workbench'"
          class="cursor-pointer rounded-[50%] flex items-center hover:text-[#fff] hover:bg-[#aaa]"
        >
          <n-icon size="14" @click.stop="handleClickClose(tab)">
            <close-round />
          </n-icon>
        </div>
      </div>
    </div>
  </n-scrollbar>
</template>
<style lang="scss" scoped>
.tabs-box {
  box-shadow: 0px 3px 5px 0px rgba(182, 191, 218, 0.25);
}

.tabItem {
  width: 160px;
  height: 32px;
  border-radius: 16px;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 400;
  color: #7b848e;
  text-shadow: 0px 3px 5px rgba(182, 191, 218, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  &:nth-child(n + 2) {
    margin-left: 12px;
  }

  &.active,
  &:hover {
    color: #333;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(182, 191, 218, 0.2);
  }
}
</style>
