import type {
  AddBranchRatingType,
  BranchRatingListType,
  ReEvaluationDataType,
} from './type'
import type { PaginationReq, PaginationRes } from '@/services/types'
import { commonReq, downloadReq } from '@/services/request'

/** 获取党支部星级评定列表 */
export function getBranchRatingTableList(
  params: { title: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<BranchRatingListType>>({
    url: '/party-affairs/backend/v1/voting-star',
    params,
  })
}

/** 删除党支部星级评定 */
export function delBranchRatingListItem(id: string) {
  return commonReq.delete({
    url: `/party-affairs/backend/v1/voting-star/${id}`,
  })
}

/** 获取党支部星级评定详情 */
export function getBranchRatingDetailInfo(id: string) {
  return commonReq.get<AddBranchRatingType>({
    url: `/party-affairs/backend/v1/voting-star/${id}`,
  })
}

/** 添加党支部星级评定 */
export function addBranchRatingInfoItem(data: AddBranchRatingType) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/voting-star',
    data,
  })
}

/** 修改党支部星级评定 */
export function modifyBranchRatingInfoItem(data: AddBranchRatingType) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/voting-star',
    data,
  })
}

/** 获取党支部星级评定复评 */
export function getBranchRatingReEvaluation(params: {
  starId: string
  deptId: string
}) {
  return commonReq.get<ReEvaluationDataType>({
    url: '/party-affairs/backend/v1/voting-star/evaluation',
    params,
  })
}

/** 新增党支部星级评定复评 */
export function addBranchRatingReEvaluation(data: {
  starId: string
  deptId: string
  star: string
}) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/voting-star/re-evaluation',
    data,
  })
}

/** 更新党支部星级评定复评 */
export function updatedBranchRatingReEvaluation(data: {
  id: string
  star: string
}) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/voting-star/re-evaluation',
    data,
  })
}

/** 公布党支部星级评定结果 */
export function publishRatingResult(id: string) {
  return commonReq.put({
    url: `/party-affairs/backend/v1/voting-star/${id}`,
  })
}

/** 导出党支部星级评定内容 */
export function exportRatingResult(starId: string) {
  return downloadReq.post({
    url: `/party-affairs/backend/v1/voting-star/evaluation?starId=${starId}`,
    responseType: 'blob',
  })
}
