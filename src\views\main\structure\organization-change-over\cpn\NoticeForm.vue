<script setup lang="ts">
import { NForm } from 'naive-ui'
import type { SelectOption, TreeSelectOption } from 'naive-ui'
import { formRules } from './config'
import type {
  uploadFileItem,
  uploadFileItemNew,
} from '@/services/affairs/party-building-list/exam-indicators/types'
import type { AddChangeOverItem } from '@/services/structure/organization-change-over/type'
import { useOrganizationListOptions } from '@/hooks/use-select-options'
import { uploadImg } from '@/services'
import {
  addOrganizationChangeOverItem,
  getOrganizationChangeOverItemDetail,
  putPartyUserList,
} from '@/services/structure/organization-change-over'
import { formatTimeStamp } from '@/utils/format'
import { getOrganizationTree } from '@/services/system/Admin'

interface Props {
  transferTypeProp?: Array<{ label: string; value: number }>
  type?: string
  id?: string
  parentId?: string | null
  parentName?: string | null
  level: number | null
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
  parentId: null,
  parentName: null,
  level: null,
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const { organizationListTree } = useOrganizationListOptions() // 获取支部列表

const formDataReactive = reactive<AddChangeOverItem>({
  id: null,
  userId: null,
  transferType: null,
  oldDeptId: null,
  newDeptId: null,
  newDeptName: null,
  reason: null,
  phaseStatus: null,
  remark: null,
  paymentTime: null,
  letterId: null,
  letterName: '',
  proveId: null,
  transferTime: null,
  joinTime: null,
  userName: null,
  fileIds: [],
  fileList: [],
})
const fileIdObj = reactive<{ fileIdsArr: Array<string> }>({ fileIdsArr: [] })
const formRef = ref<InstanceType<typeof NForm>>()

onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getOrganizationChangeOverItemDetail(props.id).then((res) => {
      formDataReactive.id = res.id
      formDataReactive.joinTime = res.joinTime
      formDataReactive.letterId = res.letterId
      formDataReactive.newDeptName = res.newDeptName
      formDataReactive.oldDeptName = res.oldDeptName
      formDataReactive.paymentTime = res.paymentTime
      formDataReactive.phaseStatus = res.phaseStatus
      formDataReactive.proveId = res.proveId
      formDataReactive.reason = res.reason
      formDataReactive.remark = res.remark
      formDataReactive.transferTime = res.transferTime
      formDataReactive.transferType = res.transferType
      formDataReactive.userName = res.userName
      formDataReactive.fileList = [
        {
          original: res.letterName || '',
          fileName: res.letterId || '',
          id: '',
        },
      ]
    })
  }
})

// 树结构枚举
const treeData = ref<any[]>([])
// 获取组织结构
const getOrganizationTreeData = () => {
  return getOrganizationTree().then((res: any) => {
    treeData.value = res
  })
}
getOrganizationTreeData()
const calcTreeDataFn: any = (arr: any[]) => {
  return arr.map((item: any) => {
    return {
      ...item,
      children:
        item.children && item.children.length
          ? calcTreeDataFn(item.children)
          : undefined,
    }
  })
}
// 计算属性重置树结构的children
const calcTreeData = computed(() => {
  return calcTreeDataFn(treeData.value)
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: boolean) => {
    if (!errors) {
      if (formDataReactive.id) {
        // 更新
        fileIdObj.fileIdsArr = []
        emits('saved')
      }
      else {
        // 新增
        fileIdObj.fileIdsArr = []
        formDataReactive.paymentTime = formatTimeStamp(new Date().getTime())
        addOrganizationChangeOverItem(formDataReactive).then(() => {
          window.$message.success('添加成功')
          emits('saved')
        })
      }
    }
  })
}

// 上传党费缴纳证明
const handlePaymentImage = async(file: File) => {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    const data: uploadFileItem = await uploadImg(imgFileData)
    formDataReactive.proveId = data.fileId
  }
  catch (error) {}
}
// 党费缴纳证明图片删除操作
const handlePaymentImageDel = () => {
  formDataReactive.proveId = ''
}

/**
 * 上传介绍信
 */
async function handleFileChange(
  fileInfoList: uploadFileItemNew[],
  isDelIDs: string,
) {
  try {
    const fileData = new FormData()
    // 获取所有FileID
    if (!fileIdObj.fileIdsArr.length) {
      formDataReactive.fileList!.forEach(item =>
        fileIdObj.fileIdsArr.push(item.id),
      )
    }
    // 删除动作
    if (isDelIDs || isDelIDs === null) {
      if (isDelIDs === null && fileIdObj.fileIdsArr.length) {
        fileIdObj.fileIdsArr.splice(fileIdObj.fileIdsArr.length - 1, 1)
      }
      else {
        fileIdObj.fileIdsArr.forEach((item, index) => {
          if (item === isDelIDs) {
            fileIdObj.fileIdsArr.splice(index, 1)
          }
        })
      }
      formDataReactive.fileIds = [...fileIdObj.fileIdsArr]
    }
    else {
      // 新增动作
      const lastFileItem = fileInfoList[fileInfoList.length - 1]?.file
      fileData.append('file', lastFileItem as Blob)
      const data: uploadFileItem = await uploadImg(fileData)
      formDataReactive.fileList!.push({
        original: lastFileItem?.name as string,
        fileName: data.url || '',
        id: data.fileId,
      })
      if (data) {
        fileIdObj.fileIdsArr.push(data.fileId)
        formDataReactive.fileIds = [...fileIdObj.fileIdsArr]
        formDataReactive.letterId = data.fileId
      }
    }
  }
  catch (error) {}
}

/** 获取组织ID */
const partyPersonObj = reactive<{
  partyPersonList: Array<{ label: string; value: string }>
}>({ partyPersonList: [] })

const handleUpdateValue = (v: string) => {
  formDataReactive.userId = null
  formDataReactive.oldDeptId = v
  if (v === null) {
    return
  }
  putPartyUserList({
    id: v,
    pageNum: 1,
    pageSize: 99999999,
    username: '',
  }).then((res) => {
    partyPersonObj.partyPersonList = res.records.map((item: any) => {
      return {
        label: item.trueName,
        value: item.userId,
      }
    })
  })
}
/** 返回党员列表 */
const PartyPersonListOptions = computed(() => partyPersonObj.partyPersonList)

/** 选择党员id */
const handleUpdatePartyPersonValue = (v: string, option: SelectOption) => {
  formDataReactive.userId = v
  formDataReactive.userName = option.label as string
}

// 获取组织名称
function getCheckedData(
  value: string | number,
  option: TreeSelectOption | null,
) {
  formDataReactive.organizationName = option?.label as string
  // if (editTitle.value === '换届' && (!value || value === '')) {
  //   resetForm()
  // }
}

// 转化data为树状结构
function transformTreeData(data: any) {
  if (Array.isArray(data)) {
    data.forEach((item) => {
      item.key = item.id
      item.label = item.name
      item.value = item.id

      if (item.children) {
        item.children.forEach((child: any) => {
          transformTreeData(child)
        })
      }
    })
  }
  else {
    data.key = data.id
    data.label = data.name
    data.value = data.id

    if (data.children) {
      data.children.forEach((child: any) => {
        transformTreeData(child)
      })
    }
  }
  return data
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :disabled="props.type === 'view'"
    :model="formDataReactive"
    :rules="formRules"
  >
    <template v-if="['view'].includes(type)">
      <n-form-item label="姓名：" path="userName">
        <n-input v-model:value="formDataReactive.userName" />
      </n-form-item>
      <n-form-item label="入党时间：" path="joinTime">
        <n-input v-model:value="formDataReactive.joinTime" />
      </n-form-item>
    </template>
    <n-form-item label="调动类型：" path="transferType">
      <n-select
        v-model:value="formDataReactive.transferType"
        clearable
        size="medium"
        :options="transferTypeProp"
        placeholder="请选择调动类型"
      />
    </n-form-item>
    <n-form-item label="党费缴纳时间：" path="paymentTime">
      <n-date-picker
        v-model:formatted-value="formDataReactive.paymentTime"
        style="width: 100%"
        placeholder="请选择党费缴纳时间"
        clearable
        type="datetime"
        @update:formatted-value="
          (v:any) => (formDataReactive.paymentTime = v)
        "
      />
    </n-form-item>
    <template v-if="['view'].includes(type)">
      <n-form-item label="调动时间：" path="transferTime">
        <n-input v-model:value="formDataReactive.transferTime" clearable />
      </n-form-item>
      <n-form-item label="转接状态：" path="phaseStatus">
        <n-input v-model:value="formDataReactive.phaseStatus" clearable />
      </n-form-item>
      <n-form-item label="原组织：" path="oldDeptName">
        <!-- <n-cascader
          v-model:value="formDataReactive.oldDeptName"
          :options="organizationListTree"
          placeholder="请选择原组织"
          label-field="name"
          value-field="id"
          children-field="children"
          check-strategy="child"
          :show-path="false"
          clearable
        /> -->
        <n-tree-select
          v-model:value="formDataReactive.oldDeptName"
          :options="organizationListTree"
          value-field="id"
          label-field="name"
          key-field="id"
          children-field="children"
          check-strategy="all"
          placeholder="请选择所属党组织"
          :show-path="false"
          clearable
          filterable
        />
      </n-form-item>
      <n-form-item label="接收组织：" path="newDeptName">
        <!-- <n-cascader
          v-model:value="formDataReactive.newDeptName"
          :options="organizationListTree"
          placeholder="请选择接收组织"
          label-field="name"
          value-field="id"
          children-field="children"
          check-strategy="child"
          :show-path="false"
          clearable
        /> -->
        <n-tree-select
          v-model:value="formDataReactive.newDeptName"
          :options="organizationListTree"
          value-field="id"
          label-field="name"
          key-field="id"
          children-field="children"
          check-strategy="all"
          placeholder="请选择所属党组织"
          :show-path="false"
          clearable
          filterable
        />
      </n-form-item>
    </template>
    <template v-if="['add'].includes(type)">
      <n-form-item label="入党时间：" path="joinTime">
        <n-date-picker
          v-model:formatted-value="formDataReactive.joinTime"
          style="width: 100%"
          placeholder="请选择入党时间"
          clearable
          type="datetime"
          @update:formatted-value="
            (v:any) => (formDataReactive.joinTime = v)
          "
        />
      </n-form-item>
      <n-form-item label="当前组织：" path="oldDeptId">
        <n-tree-select
          v-model:value="formDataReactive.oldDeptId"
          clearable
          placeholder="请选择当前组织"
          size="small"
          :options="transformTreeData(calcTreeData)"
          @update:value="getCheckedData"
        />
      </n-form-item>
      <n-form-item label="调动人选择：" path="userId">
        <n-select
          v-model:value="formDataReactive.userId"
          placeholder="选择调动人员"
          :options="PartyPersonListOptions"
          @update:value="handleUpdatePartyPersonValue"
        />
      </n-form-item>
      <n-form-item label="目标组织：" path="newDeptId">
        <n-tree-select
          v-model:value="formDataReactive.newDeptId"
          clearable
          placeholder="请选择目标组织"
          size="small"
          :options="transformTreeData(calcTreeData)"
          @update:value="getCheckedData"
        />
      </n-form-item>
    </template>

    <n-form-item span="24" label="调动原因：" path="reason">
      <n-input
        v-model:value="formDataReactive.reason"
        type="textarea"
        rows="5"
        maxlength="200"
        show-count
      />
    </n-form-item>
    <n-form-item v-if="['view'].includes(type)" span="24" label="审核说明：">
      <n-input
        v-model:value="formDataReactive.remark"
        type="textarea"
        rows="5"
        maxlength="200"
        show-count
      />
    </n-form-item>
    <n-form-item span="24" label="介绍信：" path="letterId">
      <file-uploader
        :max="1"
        accept=".doc, .docx, .pdf"
        :size-limit="200"
        :original-file-list="formDataReactive.fileList"
        :is-readonly="props.type === 'view'"
        @file-list-change="handleFileChange"
      >
        <template #tips>
          <span class="tips">
            只可上传一个文件，支持扩展名：.doc，docx，.pdf，大小200M以内
          </span>
        </template>
      </file-uploader>
    </n-form-item>
    <n-form-item span="24" label="党费缴纳证明：" path="proveId">
      <ImgUploader
        v-model:oldImgUrl="formDataReactive.proveId"
        :width="194"
        :height="256"
        :need-cropper="false"
        :is-readonly="props.type === 'view'"
        @done="handlePaymentImage"
        @delete="handlePaymentImageDel"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
