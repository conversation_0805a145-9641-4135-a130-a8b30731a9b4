<script setup lang="ts">
import { NButton } from 'naive-ui'
import { getTableColumns } from './config'
import ListForm from './cpn/ListForm.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import {
  deletePartyBuildList,
  getPartyBuildList,
  putIssueSingleParty,
} from '@/services/affairs/party-building-list'

const router = useRouter()

// 开始年月-结束年月
const filterRef = ref({
  startTime: '',
  endTime: '',
})

const timeRangeFormattedValue = computed<[string, string] | null>(() => {
  const { startTime, endTime } = filterRef.value
  if (startTime && endTime) {
    return [startTime, endTime]
  }
  return null
})

const handleUpdateMonthRange = (formattedValue: [string, string] | null) => {
  if (formattedValue) {
    filterRef.value.startTime = formattedValue[0]
    filterRef.value.endTime = formattedValue[1]
  } else {
    filterRef.value.startTime = ''
    filterRef.value.endTime = ''
  }
}

const {
  loading,
  tableData,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getPartyBuildList, filterRef, {
  batchDeleteTable: true,
  delApi: deletePartyBuildList,
})
watch(timeRangeFormattedValue, () => {
  loadData()
})

// 新增/编辑党建清单抽屉
const idEditRef = ref()
const addListFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('党建清单', handelConfirmEdit)
/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addListFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addListFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

/** 下发 */
function handleSingleIssue(id: string) {
  loading.value = true
  window.$dialog.warning({
    title: '提示',
    content: '确认考核清单及考核指标项内容无误，下发填报?',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      putIssueSingleParty(id)
        .then(() => {
          window.$message.success('下发成功')
        })
        .finally(loadData)
    },
  })
  loading.value = false
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    row.status !== '已下发'
    && row.status !== '已完成'
    && row.status !== '支部确认中'
      ? h(
        NButton,
        {
          onClick: () => {
            idEditRef.value = row.id
            editTypeRef.value = 'modify'
            showEditRef.value = true
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '编辑',
        },
      )
      : h(
        NButton,
        {
          onClick: () => {
            idEditRef.value = row.id
            editTypeRef.value = 'view'
            showEditRef.value = true
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '查看',
        },
      ),
    h(
      NButton,
      {
        onClick: () => {
          router.push({
            name: 'ptListTarget',
            // params: {
            //   partyListId: row.id,
            // },
            query: {
              title: row.title,
              examTime: row.evaluationYearAndMonth,
              status: row.status,
              partyListId: row.id,
            },
          })
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '指标',
      },
    ),
    row.status !== '已下发'
    && row.status !== '已完成'
    && row.status !== '支部确认中'
      ? h(
        NButton,
        {
          onClick: () => handleSingleIssue(String(row.id)),
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '下发',
        },
      )
      : '',
    h(DeleteButton, {
      handleConfirm: () => handleSingleDelete(String(row.id)),
    }),
  ]
})
onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :loading="loading"
    title="党建清单"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #filters>
      <n-date-picker
        v-model:formatted-value="timeRangeFormattedValue"
        type="monthrange"
        clearable
        start-placeholder="开始年月"
        end-placeholder="结束年月"
        @update:formatted-value="handleUpdateMonthRange"
      />
      <!-- <n-date-picker
        v-model:formatted-value="filterRef.year"
        size="large"
        clearable
        type="year"
        placeholder="请选择年份"
      />
      <n-date-picker
        v-model:formatted-value="filterRef.month"
        size="large"
        clearable
        type="month"
        placeholder="请选择月份"
      /> -->
    </template>
  </table-container>

  <!-- 新增清单抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="600" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <list-form
        :id="idEditRef"
        ref="addListFormRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped>
:deep(.n-progress.n-progress--line .n-progress-icon.n-progress-icon--as-text) {
  width: 46px;
}
</style>
