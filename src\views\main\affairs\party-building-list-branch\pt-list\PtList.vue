<script setup lang="ts">
import { NButton } from 'naive-ui'
import { getTableColumns } from './config'
import { useMyTable } from '@/hooks'
import {
  getPartyBuildListBranch,
  putResultConfirm,
} from '@/services/affairs/party-building-list-branch'

const router = useRouter()

// 开始年月-结束年月
const filterRef = ref({
  startTime: '',
  endTime: '',
})

const timeRangeFormattedValue = computed<[string, string] | null>(() => {
  const { startTime, endTime } = filterRef.value
  if (startTime && endTime) {
    return [startTime, endTime]
  }
  return null
})

const handleUpdateMonthRange = (formattedValue: [string, string] | null) => {
  if (formattedValue) {
    filterRef.value.startTime = formattedValue[0]
    filterRef.value.endTime = formattedValue[1]
  }
  else {
    filterRef.value.startTime = ''
    filterRef.value.endTime = ''
  }
}

const {
  loading,
  tableData,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getPartyBuildListBranch, filterRef, {
  batchDeleteTable: false,
})
watch(timeRangeFormattedValue, () => {
  loadData()
})

/** 结果确认 */
function handleResultConfirm(id: string) {
  loading.value = true
  window.$dialog.warning({
    title: '提示',
    content: '确认后结果无法修改，是否确认?',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      putResultConfirm(id)
        .then(() => {
          window.$message.success('确认成功')
        })
        .finally(loadData)
    },
  })
  loading.value = false
}

// 表格操作按钮
const tableColumns = getTableColumns((row) => {
  return [
    row.status === '考核中'
      && h(
        NButton,
        {
          onClick: () => {
            router.push({
              name: 'ptListTargetBranch',
              query: {
                title: row.title,
                examTime: row.evaluationYearAndMonth,
                status: row.status,
                partyListId: row.id,
              },
            })
          },
          type: 'primary',
          text: true,
        },
        {
          default: () => '指标',
        },
      ),
    row.status !== '考核中'
      && h(
        NButton,
        {
          onClick: () => {
            router.push({
              name: 'ptListTargetBranch',
              query: {
                title: row.title,
                examTime: row.evaluationYearAndMonth,
                status: row.status,
                partyListId: row.id,
              },
            })
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '查看得分',
        },
      ),
    row.status === '待确认'
      && h(
        NButton,
        {
          onClick: () => handleResultConfirm(String(row.id)),
          type: 'primary',
          text: true,
        },
        {
          default: () => '结果确认',
        },
      ),
  ]
})
onMounted(loadData)
</script>
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    :loading="loading"
    :show-add="false"
    :show-delete="false"
    title="党建清单"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #filters>
      <n-date-picker
        v-model:formatted-value="timeRangeFormattedValue"
        type="monthrange"
        clearable
        start-placeholder="开始年月"
        end-placeholder="结束年月"
        @update:formatted-value="handleUpdateMonthRange"
      />
    </template>
  </table-container>
</template>
<style lang="scss" scoped>
:deep(.n-progress.n-progress--line .n-progress-icon.n-progress-icon--as-text) {
  width: 46px;
}
</style>
