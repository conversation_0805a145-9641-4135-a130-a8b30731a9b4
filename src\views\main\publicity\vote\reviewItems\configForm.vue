<template>
  <div class="card">
    <NForm ref="formRef" :rules="formRules" :model="value">
      <NFormItem label="题型" path="subjectType">
        <NSelect
          :options="QUESTION_TYPE"
          :value="value.subjectType"
          :on-update-value="updateQuestionType"
        />
      </NFormItem>
      <NFormItem label="题目" path="subjectName">
        <NInput
          type="textarea"
          placeholder="请输入题目信息"
          :value="value!.subjectName"
          :on-update-value="updateQuestionTitle"
        />
      </NFormItem>

      <NFormItem
        v-if="value.subjectType !== '2'"
        label="选项"
        path="reviewSubjectItemArr"
      >
        <div class="w-full">
          <div v-for="(item, index) in value.reviewSubjectItemArr" :key="index">
            <div
              :key="index"
              class="flex items-center justify-between gap-x-10px"
            >
              <NInput
                placeholder="请输入选项内容"
                :value="item.subjectItemName"
                :on-update-value="
                  (v:any) => updateChoice({ ...item, subjectItemName: v }, index)
                "
                :disabled="haveTemplate"
              />
              <NInputNumber
                class="ml-[8px]"
                placeholder="分值"
                :show-button="false"
                :value="item.subjectItemScore"
                :on-update-value="
                  (v:any) => updateChoice({ ...item, subjectItemScore: v }, index)
                "
                :disabled="haveTemplate"
              />
              <NButton
                class="!ml-[14px]"
                text
                type="error"
                :on-click="() => deleteChoice(index)"
                :disabled="haveTemplate"
              >
                <NIcon :size="14" class="text-black">
                  <Close />
                </NIcon>
              </NButton>
            </div>
            <NCheckbox
              class="mb-[18px] mt-[12px]"
              size="small"
              label="需要填理由"
              :checked-value="'1'"
              :unchecked-value="'0'"
              :checked="item.isReason"
              :on-update-checked="
                (v:any) => updateChoice({ ...item, isReason: v }, index)
              "
              :disabled="haveTemplate"
            />
          </div>
          <n-button
            quaternary
            type="info"
            :on-click="addChoice"
            :disabled="haveTemplate"
          >
            <template #icon>
              <n-icon>
                <AddAlt />
              </n-icon>
            </template>
            添加选项
          </n-button>
        </div>
      </NFormItem>

      <NFormItem label="必填">
        <NSwitch
          :checked-value="'1'"
          :unchecked-value="'0'"
          :value="value.isRequired"
          :on-update-value="updateRequired"
        />
      </NFormItem>
    </NForm>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable vue/prop-name-casing */
import type { FormRules, NForm } from 'naive-ui'
import {
  NButton,
  NCheckbox,
  NFormItem,
  NIcon,
  NInput,
  NInputNumber,
  NSelect,
  NSwitch,
} from 'naive-ui'
import type { PropType } from 'vue'
import { onUnmounted, ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { AddAlt, Close } from '@vicons/carbon'
import type { ChoiceItem, ReviewItem, TemplateItem } from './type'
import { QUESTIONTYPE } from '@/constant'
import { emitter } from '@/utils/event-bus'

const QUESTION_TYPE = [
  { value: QUESTIONTYPE.RADIO, label: '单选题' },
  { value: QUESTIONTYPE.FILLED, label: '填空题' },
]

// const EVALUATION_TYPE = [
//   { value: '0', label: '教师' },
//   { value: '1', label: '课程' },
//   { value: '2', label: '项目' },
// ]

const formRules: FormRules = {
  subjectType: [{ required: true, message: '请选择题型', type: 'string' }],
  subjectName: [
    {
      required: true,
      message: '请输入题目信息',
      type: 'string',
    },
  ],
  reviewSubjectItemArr: [
    { required: true, message: '请添加选项', type: 'array' },
  ],
  // questionObject: [
  //   {
  //     required: true,
  //     message: '请选择评价指标',
  //     type: 'number',
  //   },
  // ],
  // questionDimension: [
  //   {
  //     required: true,
  //     message: '请输入统计展示名称',
  //     type: 'string',
  //   },
  // ],
}

const props = defineProps({
  value: {
    type: Object as PropType<ReviewItem>,
    default: () => {
      return {
        renderId: `question-${Date.now()}`,
        subjectType: '',
        subjectName: '',
        sort: 1,
        isRequired: '1',
        // questionDimension: '',
        editing: true,
        // questionObject: null,
      }
    },
  },
  haveTemplate: {
    type: Boolean,
    default: false,
  },
  templateDetail: {
    type: Array as PropType<TemplateItem[]>,
    default: () => [],
  },
  'onUpdate:value': {
    type: Function as PropType<(value: ReviewItem) => void>,
  },
})

watch(
  () => props.value.subjectType,
  (nval) => {
    if (props.haveTemplate && nval === QUESTIONTYPE.RADIO) {
      const data = cloneDeep(props.value!)
      data.reviewSubjectItemArr = []
      // 有模板且启用了单选
      props.templateDetail.forEach((item) => {
        data.reviewSubjectItemArr = data.reviewSubjectItemArr ?? []
        data.reviewSubjectItemArr.push({
          renderId: `choice-${Date.now()}`,
          sort: data.reviewSubjectItemArr.length + 1,
          subjectItemName: item.templateItemName,
          subjectItemScore: item.templateItemScore,
          isReason: item.isReason,
        })
      })
      doUpdate(data)
    }
  },
)
function doUpdate(data: ReviewItem) {
  const { 'onUpdate:value': _updateValue } = props
  _updateValue?.(data)
}
function updateQuestionType(v: string) {
  const data = { ...props.value! }
  data.subjectType = v
  doUpdate(data)
}
function updateQuestionTitle(v: string) {
  const data = { ...props.value! }
  data.subjectName = v
  doUpdate(data)
}
// function updateDimension(v: string) {
//   const data = { ...props.value! }
//   data.questionDimension = v
//   doUpdate(data)
// }
function updateRequired(v: string) {
  const data = { ...props.value! }
  data.isRequired = v
  doUpdate(data)
}
function addChoice() {
  const data = cloneDeep(props.value!)
  data.reviewSubjectItemArr = data.reviewSubjectItemArr ?? []
  data.reviewSubjectItemArr.push({
    renderId: `choice-${Date.now()}`,
    sort: data.reviewSubjectItemArr.length + 1,
    subjectItemName: '',
    subjectItemScore: null,
    isReason: '',
  })
  doUpdate(data)
}
function deleteChoice(i: number) {
  const data = cloneDeep(props.value!)
  data.reviewSubjectItemArr!.splice(i, 1)
  doUpdate(data)
}
function updateChoice(choice: ChoiceItem, i: number) {
  const data = cloneDeep(props.value!)
  data.reviewSubjectItemArr![i] = choice
  doUpdate(data)
}
// function updateType(v: number) {
//   const data = { ...props.value! }
//   data.questionObject = v
//   doUpdate(data)
// }

const formRef = ref<InstanceType<typeof NForm>>()

emitter.on('notification-template-evaluation-validate', () => {
  formRef.value?.validate().catch(() => {})
})

onUnmounted(() => {
  emitter.off('notification-template-evaluation-validate')
})
// onMounted(loadData)
</script>

<style lang="scss" scoped>
.card {
  padding: 17px 24px 24px;

  transition: all 250ms;
  position: relative;
  border-radius: 3px;
  cursor: pointer;

  &.required .questionTitle {
    position: relative;
    &::after {
      content: '*';
      position: absolute;
      left: -12px;
      top: -6px;
      color: #d63434;
    }
  }

  &.active {
    box-shadow: 0px 0px 10px 0px rgba(66, 133, 247, 0.5);
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 2px;
      background: rgb(66, 133, 247);
      border-radius: 3px;
    }
  }

  .btns {
    position: absolute;
    top: 16px;
    right: 19px;
    width: 110px;
    height: 24px;
    background: #f6f7f8;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

:deep(.n-form-item) .n-form-item-label {
  font-weight: 600;
}

:deep(.n-form-item) .n-base-selection,
:deep(.n-form-item) .n-input {
  font-size: 14px;
}
</style>
