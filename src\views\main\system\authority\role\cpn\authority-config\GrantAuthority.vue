<!-- <script setup lang="ts">
import type { TreeOption } from 'naive-ui'
import { getRootNodes, transformTreeArray } from './treeUtils'

interface Props {
  treeData: TreeOption[]
}
const props = defineProps<Props>()

const patternRef = ref('')
const allCheckedRef = ref(false)
const treeDataRef = ref<TreeOption[]>(props.treeData ?? [])
const selectedKeysRef = ref<(string | number)[]>([])
const originalNodeArr = computed(() => transformTreeArray(props.treeData))

// 所有权限
const allAuthLabels = computed(() => {
  originalNodeArr.value
    .map(item => item.id)
    .filter(item => String(item).includes('&'))
})

// 是否部分选中
const isIndeterminate = computed(() => {
  selectedKeysRef.value.length > 0
    && selectedKeysRef.value.length < allAuthLabels.value.length
})

// 过滤
function handleFilter(pattern: string, node: TreeOption) {
  return node.label?.includes(pattern) ?? false
}
/** 设置选中的值 */
function setCheckedKeys(v: Array<number | string>) {
  selectedKeysRef.value = v
}
/** 监听搜索框变化 */
watch(patternRef, (newV) => {
  const rootIds = getRootNodes(
    originalNodeArr.value,
    transformTreeArray(props.treeData).filter(item =>
      item.label.includes(newV),
    ),
  )
  treeDataRef.value
    = props.treeData.filter(item => rootIds.includes(item.key!)) ?? []
})
/** 监听全选框变化 */
watch(allCheckedRef, (newV) => {
  if (newV) {
    selectedKeysRef.value = allAuthLabels.value
  } else {
    selectedKeysRef.value = []
  }
})
/** 监听全选框随着下面多选框选中所发生的变化 */
watch(selectedKeysRef, (newV) => {
  if (!newV.length) {
    allCheckedRef.value = false
  } else if (newV.length === allAuthLabels.value.length) {
    allCheckedRef.value = true
  }
})
</script>
<template>
  <div>
    <n-input
      v-model:value="patternRef"
      style="margin-bottom: 10px"
      placeholder="搜索权限"
      clearable
    />
    <n-checkbox
      v-model:checked="allCheckedRef"
      style="margin-left: 24px; margin-bottom: 10px"
      :indeterminate="isIndeterminate"
    >
      <span class="inline-block pl-[2px]">全选</span>
    </n-checkbox>
    <n-scrollbar style="max-height: 120px">
      <n-tree
        checkable
        cascade
        block-line
        check-strategy="child"
        :checked-keys="selectedKeysRef"
        :pattern="patternRef"
        :filter="handleFilter"
        :data="treeDataRef"
        @update-checked-keys="setCheckedKeys"
      />
    </n-scrollbar>
  </div>
</template>
<style lang="scss" scoped></style> -->
<template>
  <div>...</div>
</template>

<script setup lang="ts"></script>

<style scoped lang="scss"></style>
