import { debounce } from 'lodash-es'

interface Data {
  name: string
  confirmFn: () => void
  titleAdd?: string
  titleModify?: string
  isNotTips?: boolean
}
export function useDrawerEditOrganize(params: Data) {
  const showEditRef = ref(false)
  const editTitle = ref(params.name)
  const editTypeRef = ref<'add' | 'addSubset' | 'modify' | 'view' | 'custom'>(
    'add',
  )
  const drawerTitle = computed(() =>
    editTypeRef.value === 'add' || editTypeRef.value === 'addSubset'
      ? `${params.titleAdd ?? `添加${editTitle.value}`}`
      : editTypeRef.value === 'modify'
        ? `${params.titleModify ?? `编辑${editTitle.value}`}`
        : editTypeRef.value === 'view'
          ? `${params.titleModify ?? `查看${editTitle.value}`}`
          : editTypeRef.value === 'custom'
            ? `${params.titleModify ?? `${editTitle.value}`}`
            : '',
  )
  function handleClickCancel() {
    if (params.isNotTips) {
      showEditRef.value = false
    }
    else {
      window.$dialog.warning({
        title: '提示',
        content: '关闭后本次编辑内容不保存，是否继续？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          showEditRef.value = false
        },
      })
    }
  }
  const debouncedConfirm = debounce(params.confirmFn, 800)
  function handleClickConfirm() {
    if (editTypeRef.value !== 'view') {
      debouncedConfirm()
    }
    else {
      showEditRef.value = false
    }
  }
  return {
    showEditRef,
    editTypeRef,
    drawerTitle,
    editTitle,

    handleClickConfirm,
    handleClickCancel,
  }
}
