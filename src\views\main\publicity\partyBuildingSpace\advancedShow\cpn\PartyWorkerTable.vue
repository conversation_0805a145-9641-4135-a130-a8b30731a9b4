<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    class="pt-[0px]"
    title=""
    :show-toolbar="false"
    custom-toolbar
    :table-columns="tableColumnsOfPartyMember"
    :table-data="tableData"
    :total="total"
    :loading="loading"
    :show-delete="false"
    :checked-row-keys="checkedRowKeys"
    @click-add="handleClickAdd"
    @click-delete="handleBatchDelete"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="handleClickAdd">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
      <n-button size="small" @click="handleBatchDelete">
        <template #icon>
          <n-icon>
            <delete-forever-round />
          </n-icon>
        </template>
        删除
      </n-button>
    </template>
    <template #filters>
      <n-date-picker
        v-model:formatted-value="filterReactive.year"
        size="small"
        value-format="yyyy"
        placeholder="请选择年份"
        type="year"
        clearable
      />
      <n-input
        v-model:value="filterReactive.orgName"
        size="small"
        placeholder="请输入组织名称"
        clearable
      />
    </template>
  </table-container>
  <!-- 新增优秀党员抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <AddPersonForm
        :id="idEditRef"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        :belong-tab-type="1"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { NButton, NSwitch } from 'naive-ui'
import { DeleteForeverRound, PlusRound } from '@vicons/material'
import { columnsOfPartyMember } from '../config'
import AddPersonForm from './AddPersonForm.vue'
import DeleteButton from '@/components/DeleteButton.vue'
import { useDrawerEdit, useMyTable } from '@/hooks'
import {
  delAdvancedPersonItem,
  getAdvancedPersonList,
  putUpdateAdvancedPersonItemRecommend,
  putUpdateAdvancedPersonItemTop,
} from '@/services/publicity/advancedShow'
const filterReactive = ref<{ type: string; orgName?: string; year?: string }>({
  orgName: '',
  type: '1',
})
// 有接口后添加：loading,tableData
const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getAdvancedPersonList, filterReactive, {
  batchDeleteTable: true,
  delApi: delAdvancedPersonItem,
})

watch(filterReactive.value, (newVal) => {
  loadData()
})

// 新增/编辑优秀党员抽屉
// const showEditRefOfOrganization
const idEditRef = ref()
const addNoticeFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('优秀党务工作者', handelConfirmEdit)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
}
/** 确定保存 */
function handelConfirmEdit() {
  addNoticeFormRef.value?.validateAndSave()
}
watch(showEditRef, (newV) => {
  if (!newV) {
    addNoticeFormRef.value?.resetForm()
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

// 修改和删除按钮渲染
const tableColumnsOfPartyMember = columnsOfPartyMember(
  (row) => {
    return [
      h(
        NButton,
        {
          onClick: () => {
            idEditRef.value = row.id
            editTypeRef.value = 'modify'
            showEditRef.value = true
          },
          type: 'primary',
          text: true,
          style: {
            marginRight: '10px',
          },
        },
        {
          default: () => '编辑',
        },
      ),
      h(DeleteButton, {
        handleConfirm: () => handleSingleDelete(String(row.id)),
      }),
    ]
  },
  (row) => {
    return h(NSwitch, {
      checkedValue: '1',
      uncheckedValue: '0',
      value: row.topStatus,
      loading: row.toploading,
      onUpdateValue(v) {
        row.toploading = true
        putUpdateAdvancedPersonItemTop(row.id)
          .then((res) => {
            row.topStatus = res ? '1' : '0'
          })
          .catch(() => {})
          .finally(() => {
            loadData()
            row.toploading = false
          })
      },
    })
  },
  (row) => {
    return h(NSwitch, {
      checkedValue: '1',
      uncheckedValue: '0',
      value: row.recommandStatus,
      loading: row.recommandloading,
      onUpdateValue(v) {
        row.recommandloading = true
        putUpdateAdvancedPersonItemRecommend(row.id)
          .then((res) => {
            row.recommandStatus = res ? '1' : '0'
          })
          .catch(() => {})
          .finally(() => {
            loadData()
            row.recommandloading = false
          })
      },
    })
  },
)
onMounted(loadData)
</script>

<style lang="scss" scoped>
:deep(.n-upload-file-list .n-upload-file.n-upload-file--image-card-type) {
  width: 136px;
  height: 191px;
}
</style>
