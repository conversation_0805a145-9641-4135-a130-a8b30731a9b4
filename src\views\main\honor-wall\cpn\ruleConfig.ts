import type { FormRules } from 'naive-ui'

export const honorSubFormRules: FormRules = {
  name: {
    required: true,
    message: '请输入分类名称',
    trigger: 'input',
    type: 'string',
  },
  parentName: {
    required: true,
    message: '请输入父级分类名称',
    trigger: 'input',
    type: 'string',
  },
  coverUrl: {
    required: true,
    message: '请上传专题栏目图片',
    trigger: 'change',
    type: 'string',
  },
}

export const honorDetailFormRules: FormRules = {
  title: {
    required: false,
    message: '请输入荣誉名称',
    trigger: 'input',
  },
  serviceId: {
    required: true,
    message: '请选择组织',
    trigger: 'change',
    type: 'string',
  },
  pictureUrl: {
    required: false,
    message: '请上传荣誉图',
    trigger: 'change',
    type: 'string',
  },
  sort: {
    required: true,
    message: '排序不能为空',
    trigger: 'input',
    type: 'number',
  },
  level: {
    required: false,
    message: '请选择荣誉级别',
    trigger: 'change',
    type: 'string',
  },
  grantTime: {
    required: true,
    message: '请选择授予时间',
    trigger: 'change',
    type: 'string',
  },
  multiPictureIds: {
    required: false,
    message: '请选择内容图片',
    trigger: 'change',
    type: 'string',
    // validator(rule: any, value: any) {
    //   if (!value || value.length === 0) {
    //     return new Error('请选择内容图片')
    //   }
    //   return true
    // },
    // trigger: 'change',
  },
}
