<script setup lang="ts">
import { newsDetailFormRules } from './config'
import { uploadImg } from '@/services'
import {
  getProjectSecretaryDetail,
  postProjectSecretary,
  putProjectSecretary,
} from '@/services/organize-garden'
import type { OrgStyleListItem } from '@/services/organize-garden/types'
import type { uploadFileItem } from '@/services/types'
interface Props {
  id: string
  orgId: string
  enterType: string
}
const props = defineProps<Props>()

const orgStyleFormRef = ref()

const formData = reactive<{ data: OrgStyleListItem }>({
  data: {
    id: '',
    orgId: props.orgId,
    title: '',
    summary: '',
    coverUrl: '',
    isTop: 0,
    isHidden: 0,
  },
})

const isViewModel = computed(() => {
  if (props.enterType === 'view') {
    return true
  }
  else {
    return false
  }
})

const getNewsDetailFn = async() => {
  const data = await getProjectSecretaryDetail(props.id)
  formData.data = data
}
onBeforeMount(() => {
  if (
    (props.enterType === 'modify' || props.enterType === 'view')
    && props.id
  ) {
    getNewsDetailFn()
  }
})

/**
 * 上传图片
 * @param {any} file:File
 */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formData.data.coverUrl === '' || formData.data.coverUrl === null) {
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formData.data.coverUrl = data.url
      }
    }
  }
  catch (error) {}
}
/**
 * 删除图片
 */
function handleCoverDelete() {
  formData.data.coverUrl = ''
}

const emits = defineEmits<{
  (e: 'saved'): void
}>()
async function validateAndSave() {
  orgStyleFormRef.value?.validate((errors: any) => {
    if (!errors) {
      const useApi = formData.data.id ? putProjectSecretary : postProjectSecretary
      useApi({ ...formData.data }).then((res) => {
        window.$message.success('保存成功')
        emits('saved')
      })
    }
  })
}
// 重置表单
function resetForm() {
  orgStyleFormRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <div>
    <n-form
      ref="orgStyleFormRef"
      :model="formData.data"
      :rules="newsDetailFormRules"
      require-mark-placement="left"
      label-placement="left"
      label-width="120px"
      :disabled="isViewModel"
    >
      <n-form-item label="书记项目标题：" path="title">
        <n-input
          v-model:value="formData.data.title"
          style="width: 580px"
          maxlength="50"
          show-count
          clearable
          placeholder="请输入书记项目标题"
        />
      </n-form-item>
      <n-form-item label="图片" path="coverUrl">
        <ImgUploader
          v-model:old-img-url="formData.data.coverUrl"
          :width="2024"
          :height="768"
          :is-readonly="isViewModel"
          :need-cropper="false"
          @done="handleCoverDone"
          @delete="handleCoverDelete"
        />
      </n-form-item>

      <n-form-item label="是否置顶：" path="isTop">
        <n-switch
          v-model:value="formData.data.isTop"
          :checked-value="1"
          :unchecked-value="0"
        />
      </n-form-item>
      <n-form-item label="是否隐藏：" path="isHidden">
        <n-switch
          v-model:value="formData.data.isHidden"
          :checked-value="1"
          :unchecked-value="0"
        />
      </n-form-item>

      <n-form-item label="书记项目简介：">
        <RichEditor
          v-model:value="formData.data.summary"
          style="width: 100%"
          :disabled="isViewModel"
        />
      </n-form-item>
    </n-form>
  </div>
</template>

<style scoped lang="scss"></style>
