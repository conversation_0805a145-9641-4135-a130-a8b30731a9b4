<!--
 * @Description: 题库表单
-->
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="100"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item label="题库名称：" path="name">
      <n-input
        v-model:value="formDataReactive.name"
        :maxlength="15"
        show-count
      />
    </n-form-item>
    <n-form-item label="题库说明：" path="description">
      <n-input
        v-model:value="formDataReactive.description"
        type="textarea"
        :maxlength="20"
        show-count
      />
    </n-form-item>
    <n-form-item label="适用答题任务：" path="types">
      <n-checkbox-group v-model:value="formDataReactive.types">
        <n-checkbox
          v-for="item in examinationLibraryTypes"
          :key="item.label"
          style="margin-right: 20px"
          :label="item.label"
          :value="item.value"
        />
      </n-checkbox-group>
    </n-form-item>
    <n-form-item label="被他人使用：" path="allowUse">
      <n-radio-group v-model:value="formDataReactive.allowUse">
        <div style="line-height: 30px">
          <n-radio :value="1">
            允许其他管理员应用
            <span style="color: #999">（其他管理员可以引用题库，但不能编辑和删除）</span>
          </n-radio>
        </div>
        <div style="line-height: 30px; margin-top: 5px">
          <n-radio :value="0">
            不允许
          </n-radio>
        </div>
      </n-radio-group>
    </n-form-item>
    <n-form-item label="题库封面：" path="file">
      <img-uploader
        :need-cropper="false"
        :width="220"
        :height="150"
        :old-img-url="oldImgUrlRef"
        @done="handleCoverDone"
        @delete="handleCoverDelete"
      />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { nextTick, onBeforeMount, reactive, ref } from 'vue'
import {
  NCheckbox,
  NCheckboxGroup,
  NForm,
  NFormItem,
  NInput,
  NRadio,
  NRadioGroup,
} from 'naive-ui'
import { formRules, getFormData } from './config'
import { examinationLibraryTypes } from '@/views/main/examination/library/config'
import type { IExaminationLibrary } from '@/services/examination/index'
import { postExaminationLibrary } from '@/services/examination/index'
import ImgUploader from '@/components/img-uploader'

const emits = defineEmits(['saved'])
const props = defineProps({
  originalData: {
    type: Object as PropType<IExaminationLibrary>,
    default: () => ({}),
  },
  categoryId: {
    type: String,
    default: '',
  },
  type: {
    type: String as PropType<'add' | 'modify' | 'view' | 'addSubset'>,
    default: 'add',
  },
})

const formRef = ref<InstanceType<typeof NForm>>()
const formDataReactive = reactive(getFormData())

// 题库封面相关
const oldImgUrlRef = ref('')

onBeforeMount(() => {
  if (props.originalData.libraryId && props.type === 'modify') {
    // 初始化表单
    const { libraryId, libraryName, type, description, allowUse, coverUrl }
      = props.originalData
    formDataReactive.id = libraryId
    formDataReactive.name = libraryName
    formDataReactive.description = description
    formDataReactive.types = type.split(',')
    formDataReactive.allowUse = allowUse
    nextTick(() => {
      oldImgUrlRef.value = coverUrl
    })
  }
})

function handleCoverDone(file: File) {
  formDataReactive.file = file
}
function handleCoverDelete() {
  formDataReactive.file = null
}

// 验证表单并保存
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // 验证 categoryId 是否为空
      if (!props.categoryId) {
        window.$message.error('分类ID不能为空，请重新选择分类后再试')
        return
      }

      formDataReactive.type = formDataReactive.types!.join(',')
      // 处理formData
      const formData = new FormData()
      formData.append('name', formDataReactive.name)
      formData.append('description', formDataReactive.description)
      formData.append('allowUse', String(formDataReactive.allowUse))
      formData.append('type', formDataReactive.type)
      formData.append('file', formDataReactive.file!)
      formData.append('categoryId', props.categoryId)
      if (props.originalData.libraryId && props.type === 'modify') {
        formData.append('id', String(props.originalData.libraryId))
      }
      postExaminationLibrary(formData).then(() => {
        window.$message.success('保存成功')
        emits('saved')
      })
    }
  })
}

defineExpose({
  validateAndSave,
})
</script>

<style lang="scss" scoped></style>
