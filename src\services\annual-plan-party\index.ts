import type { RequestTypes } from './types'
import { commonReq } from '@/services/request'

/** 获取年度工作计划 */
export function getAnnualPlanDetail(
  query: RequestTypes.QueryAnnualPlanDetailType,
) {
  return commonReq.get<RequestTypes.QueryAnnualPlanDetailResponseType>({
    url: '/party-affairs/work-record-book/party_branch_annual_work_plan/detail',
    params: query,
  })
}

/** 修改年度工作计划 */
export function updateAnnualPlan(data: RequestTypes.UpdateAnnualPlanType) {
  return commonReq.post({
    url: '/party-affairs/work-record-book/party_branch_annual_work_plan/edit',
    data,
  })
}
