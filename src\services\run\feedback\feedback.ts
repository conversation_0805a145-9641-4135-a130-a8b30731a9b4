import type { Feedback } from './types'
import { commonReq } from '@/services/request'
import type { PaginationRes } from '@/services/types'

/** 获取意见反馈列表 */
export function getFeedbackList(params: any) {
  return commonReq.get<PaginationRes<Feedback>>({
    url: '/sys-operation/backend/v1/feedback',
    params: {
      userName: params.userName,
      content: params.content,
      current: params.pageNum,
      size: params.pageSize,
    },
  })
}

/** 删除意见反馈 */
export function deleteFeedback(ids: string) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/feedback',
    params: {
      ids,
    },
  })
}
