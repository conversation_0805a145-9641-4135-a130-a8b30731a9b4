/**
 * @description: 党组织集中轮训分页查询
 * */
export interface QueryTrainingForPartySecretaryType {
  /**
   * 部门ID
   */
  deptId: number | string
  /**
   * 页码
   */
  pageNum?: number
  /**
   * 每页数量
   */
  pageSize?: number
  /**
   * 年份
   */
  year: number | string
  [property: string]: any
}

/**
 * @description: 党组织集中轮训分页查询返回类型
 */
export interface QueryTrainingForPartySecretaryResponseType {
  /**
   * countId
   */
  countId?: string
  /**
   * 当前页
   */
  current?: number
  /**
   * countId
   */
  maxLimit?: number
  /**
   * 自动优化 COUNT SQL
   */
  optimizeCountSql?: boolean
  /**
   * {@link #optimizeJoinOfCountSql()}
   */
  optimizeJoinOfCountSql?: boolean
  /**
   * 排序字段信息
   */
  orders?: OrderItem[]
  pages?: number
  /**
   * 查询数据列表
   */
  records?: RecordsItem[]
  /**
   * 是否进行 count 查询
   */
  searchCount?: boolean
  /**
   * 每页显示条数，默认 10
   */
  size?: number
  /**
   * 总数
   */
  total?: number
  [property: string]: any
}

/**
 * @description: 排序字段信息
 */
export interface OrderItem {
  /**
   * 是否正序排列，默认 true
   */
  asc?: boolean
  /**
   * 需要进行排序的字段
   */
  column?: string
  [property: string]: any
}

/**
 * @description: 查询数据列表
 */
export interface RecordsItem {
  /**
   * 地址
   */
  addr?: string
  /**
   * 0-正常，1-删除
   */
  delFlag?: string
  /**
   * 支部id
   */
  deptId?: number
  /**
   * 结束时间
   */
  endTime?: string
  /**
   * 主键id
   */
  id?: number
  /**
   * 培训项目内容
   */
  projectContent?: string
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 学时（小时）
   */
  studyHour?: number
  /**
   * 书记用户id
   */
  userId?: number | string | undefined
  /**
   * 书记姓名
   */
  userName?: string
  /**
   * 年份
   */
  year?: string
}

/**
 * @description: 查询党组织集中轮训详情
 * */
export interface QueryTrainingForPartySecretaryDetailType {
  /**
   * id
   */
  id: number | string
}

/**
 * @description: 查询党组织集中轮训详情返回类型
 */
export interface QueryTrainingForPartySecretaryDetailResponseType
  extends RecordsItem {
  /**
   * 时间范围的中间变量类型
   */
  minutesTime?: string[]
}

/**
 * @description: 新增党组织集中轮训记录类型
 * */
export type AddTrainingForPartySecretaryType = RecordsItem

/**
 * @description: 编辑党组织集中轮训记录类型
 * @extends RecordsItem id 必传
 * */
export type UpdateTrainingForPartySecretaryType = Required<
Pick<RecordsItem, 'id'>
> &
Omit<RecordsItem, 'id'>

/**
 * @description: 删除党组织集中轮训记录类型
 * */
export interface DeleteTrainingForPartySecretaryType {
  /**
   * id
   */
  id: number | string
}
