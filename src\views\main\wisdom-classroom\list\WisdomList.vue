<script lang="ts" setup>
import { NInputNumber, NSwitch } from 'naive-ui'
import { ArtTrackRound, DeleteForeverRound, PlusRound } from '@vicons/material'
import AddWisdomItemForm from '../cpn/AddWisdomItemForm.vue'
import AddWisdomCategoryForm from './AddWisdomCategoryForm.vue'
import CarouselPoolSetting from './CarouselPoolSetting.vue'
import { getTableColumns } from './config'
import { useDrawerEdit, useMyTable, useTreeMenu } from '@/hooks'
import {
  checkCategoryHaveNews,
  deleteNews,
  deleteNewsCategory,
  getNewsCategoryList,
  getNewsList,
  isJoinCarouselPool,
  moveNewsCategory,
  postNewsCategory,
  putNewsCategory,
  putNewsTop,
  sortZhiYunNewsList,
} from '@/services/wisdom'

import DeleteButton from '@/components/DeleteButton.vue'
import { judgePermission } from '@/directive/permission/ifHasPermi'

const { treeData, showModalType, moveNode, delNode, saveNode, init }
  = useTreeMenu({
    menuListApi: getNewsCategoryList,
    moveNodeApi: moveNewsCategory,
    moveChildNodeApi: moveNewsCategory,
    delNodeApi: deleteNewsCategory,
    delChildNodeApi: deleteNewsCategory,
    addNodeApi: postNewsCategory,
    modifyNodeApi: putNewsCategory,
    refreshTableApi: filterInput,
    checkDeleteApiFunc: checkCategoryHaveNews,
    maxLevel: 2,
    labelField: 'name',
    multiLevelKey: 'children',
    childLabelField: 'name',
    sessionId: 'wisdomCategoryId',
    sessionName: 'wisdomCategoryLabel',
  })

const showModal = ref(false)
const modalTitle = ref()
const addFirstCategoryRef = ref()

const currentType = ref()
const parentId = ref('')
const isShowCarousePoolDialog = ref(false)

// 操作栏目类型
const operateCategoryType = ref('add')

/** 新增子节点 */
async function handleAddChildNode(data: any) {
  if (
    data.type === 'sub'
    && data.model !== 'modify'
    && (!data.children || data.children.length === 0)
  ) {
    const response = await checkCategoryHaveNews(data.originData.id)
    if (!response) {
      // 表明该分类下存在资讯，添加子分类时需要给用户提示
      window.$dialog.warning({
        title: '提示',
        content:
          '该菜单下已有详细数据，若添加子菜单则无法展示已添加的详细数据，是否继续？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          showModal.value = true
        },
        onNegativeClick: () => {
          showModal.value = false
        },
      })
    }
    else {
      // 表明该分类下没有详细数据，添加子分类时无需提示
      showModal.value = true
    }
  }
  else {
    showModal.value = true
  }
  currentType.value = data.type
  parentId.value = data?.originData?.id
  // showModal.value = true
  operateCategoryType.value = 'add'
  // 专题还是栏目
  if (data.type === 'sub') {
    modalTitle.value = '新增栏目'
  }
  else {
    modalTitle.value = '新增专题'
  }
  // modalTitle.value = '新增'
  // addChildNode(data)
  if (data.model === 'modify') {
    if (data.type === 'sub') {
      modalTitle.value = '修改栏目'
    }
    else {
      modalTitle.value = '修改专题'
    }
    // modalTitle.value = '修改栏目'
    operateCategoryType.value = 'modify'
    nextTick(() => {
      addFirstCategoryRef.value.handleSetFormData(data)
    })
  }
}

/** 处理弹框需要保存的数据及校验弹框必填项 */
async function handleFormatterParams() {
  try {
    if (!(await addFirstCategoryRef.value.handleValidate())) {
      return
    }
    const data = addFirstCategoryRef.value.formData
    saveNode({ ...data, type: showModalType.value })
    showModal.value = false
  }
  catch (error) {
    console.error(error)
  }
}

function handleCancel() {
  showModal.value = false
  isShowCarousePoolDialog.value = false
}

// 选中菜单名称
const selectName = ref()

// 筛选项：类别id和资讯标题
const filterRef = ref({
  categoryId: '',
  title: null,
})
const {
  loading,
  currentPage,
  pageSize,
  total,
  checkedRowKeys,
  tableData,
  handleSingleDelete,
  handleBatchDelete,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(getNewsList, filterRef, {
  batchDeleteTable: true,
  delApi: deleteNews,
})

// 选中菜单触发的事件
function handleChangeTab(data: any) {
  // isChild
  const { label, clickExpand } = data
  if (!clickExpand) {
    filterRef.value.categoryId = data.originData.id
    window.sessionStorage.setItem('wisdomCategoryId', data.originData.id)
    window.sessionStorage.setItem('wisdomCategoryLabel', label)
    filterRef.value.title = null
    selectName.value = label
    currentPage.value = 1
  }
}

async function switchTop(value: boolean, id: string) {
  await putNewsTop(id)
  window.$message.success('修改置顶成功')
  loadData()
}

// 表格中是否加入轮播池 更新轮播状态
async function switchCarousePool(data: { id: string; type: string }) {
  await isJoinCarouselPool(data)
  window.$message.success('操作成功')
  loadData()
}

const idEditRef = ref()
const categoryIdRef = ref()
const addWisdomFormRef = ref()
const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEdit('智云课堂', handelConfirmEdit)

/** 点击添加按钮 */
function handleClickAdd() {
  editTypeRef.value = 'add'
  showEditRef.value = true
  categoryIdRef.value = filterRef.value.categoryId
}

function handelConfirmEdit() {
  addWisdomFormRef.value?.validateAndSave()
}

watch(showEditRef, (newV) => {
  if (!newV) {
    addWisdomFormRef.value?.resetForm()
  }
})

/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  loadData()
}

/** 列表操作 */
const tableColumns = getTableColumns(
  (row) => {
    return h(
      'div',
      {
        style: {
          color: '#AC241D',
          cursor: 'pointer',
          display: 'flex',
          gap: '15px',
        },
      },
      [
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'view'
              showEditRef.value = true
            },
          },
          { default: () => '查看' },
        ),
        h(
          'span',
          {
            onClick: () => {
              idEditRef.value = row.id
              editTypeRef.value = 'modify'
              showEditRef.value = true
            },
          },
          { default: () => '编辑' },
        ),
        h(DeleteButton, {
          handleConfirm: () => handleSingleDelete(String(row.id)),
        }),
      ],
    )
  },
  row =>
    h(NSwitch, {
      onUpdateValue: (value: any) => switchTop(value, row.id),
      value: Boolean(Number(row.isTop)),
    }),
  row =>
    h(
      NSwitch,
      {
        onUpdateValue: (value: any) =>
          switchCarousePool({ id: row.id, type: '1' }),
        value: Number(row.isCarouselPool),
        checkedValue: 1,
        uncheckedValue: 0,
      },
      {
        checked: '是',
        unchecked: '否',
      },
    ),
  (row) => {
    return h(
      NInputNumber,
      {
        value: row.sort,
        min: 0,
        max: 999999,
        step: 1,
        precision: 0,
        onUpdateValue: (val) => {
          row.sort = val as number
        },
        onBlur: () => {
          if (row.sort === null) {
            return
          }

          sortZhiYunNewsList({ id: row.id, sort: row.sort as number }).then(
            (res) => {
              window.$message.success('操作成功')
              loadData()
            },
          )
        },
      },
      {},
    )
  },
)

const defaultSelectedKeys = ref<string[]>([])

function filterInput(res: { id: string; name: string }) {
  // loadData()
  defaultSelectedKeys.value = [res.id]
  filterRef.value.categoryId = res.id
  selectName.value = res.name
}

// 打开轮播管理
function handleOpenCarouselPoolSetting() {
  isShowCarousePoolDialog.value = true
}

onMounted(() => {
  init().then((res: any) => {
    defaultSelectedKeys.value = [res.id]
    filterRef.value.categoryId = res.id
    selectName.value = res.name
  })
})
</script>

<template>
  <layout-container style="height: calc(100vh - 114px)">
    <template #side>
      <SideMenuNew
        v-model:show-modal="showModal"
        :default-selected-keys="defaultSelectedKeys"
        :modal-title="modalTitle"
        :tree-data="treeData"
        title="智云课堂"
        @move="moveNode"
        @del-node="delNode"
        @save-tree-node="handleFormatterParams"
        @add-child-node="handleAddChildNode"
        @select-node-key="handleChangeTab"
      />
    </template>

    <template #main>
      <table-container
        v-model:page="currentPage"
        v-model:page-size="pageSize"
        :checked-row-keys="checkedRowKeys"
        :loading="loading"
        :show-add="judgePermission('publicity_add_btn')"
        :show-delete="judgePermission('publicity_delete_btn')"
        :show-toolbar="false"
        :table-columns="tableColumns"
        :table-data="tableData"
        :title="selectName"
        :total="total"
        custom-toolbar
        @click-add="handleClickAdd"
        @click-delete="handleBatchDelete"
        @update-page="onUpdatePage"
        @update-page-size="onUpdatePageSize"
        @update-checked-row-keys="onUpdateCheckedRowKeys"
      >
        <template #btns>
          <n-button size="small" type="primary" @click="handleClickAdd">
            <template #icon>
              <n-icon>
                <plus-round />
              </n-icon>
            </template>
            添加
          </n-button>

          <!-- 轮播图管理 -->
          <n-button
            size="small"
            type="primary"
            @click="handleOpenCarouselPoolSetting"
          >
            <template #icon>
              <n-icon>
                <ArtTrackRound />
              </n-icon>
            </template>
            轮播图管理
          </n-button>

          <n-button size="small" @click="handleBatchDelete">
            <template #icon>
              <n-icon>
                <delete-forever-round />
              </n-icon>
            </template>
            删除
          </n-button>
        </template>

        <template #filters>
          <n-input
            v-model:value="filterRef.title"
            clearable
            placeholder="请输入标题"
            size="small"
            style="width: 200px"
          />
        </template>
      </table-container>
    </template>
  </layout-container>

  <n-drawer
    v-model:show="showEditRef"
    :mask-closable="false"
    :trap-focus="false"
    :width="700"
  >
    <n-drawer-content :title="drawerTitle" closable>
      <add-wisdom-item-form
        :id="idEditRef"
        ref="addWisdomFormRef"
        :category-id="categoryIdRef"
        :type="editTypeRef"
        @saved="handleListSaved"
      />

      <template v-if="['add', 'modify'].includes(editTypeRef)" #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            style="width: 80px"
            type="primary"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>

  <CustomDialog
    :show="showModal"
    :title="modalTitle"
    width="600px"
    @cancel="handleCancel"
    @confirm="handleFormatterParams"
    @update:show="(v: boolean) => (showModal = v)"
  >
    <AddWisdomCategoryForm
      v-show="showModalType === 'root'"
      ref="addFirstCategoryRef"
      :current-type="currentType"
      :parent-id="parentId"
      :tree-data="treeData"
      :type="operateCategoryType"
    />
  </CustomDialog>

  <!-- 轮播图管理 -->
  <CustomDialog
    :show="isShowCarousePoolDialog"
    title="轮播图管理"
    width="1200px"
    @cancel="handleCancel"
    @confirm="handleCancel"
    @update:show="(v: boolean) => (isShowCarousePoolDialog = v)"
  >
    <div class="p-[20px]">
      <CarouselPoolSetting @refresh-parent-category="init" />
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped></style>
