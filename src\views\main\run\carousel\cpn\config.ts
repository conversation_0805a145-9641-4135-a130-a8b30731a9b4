import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  title: [
    {
      required: true,
      message: '轮播图名称不能为空',
      trigger: 'input',
    },
  ],
  sort: [
    {
      required: true,
      message: '排序不能为空',
      trigger: 'input',
      type: 'number',
    },
  ],
  isMobile: [
    {
      required: true,
      message: '移动端是否展示不能为空',
      trigger: 'change',
      type: 'number',
    },
  ],
  coverUrl: [
    {
      required: true,
      message: '请上传轮播图图片',
      trigger: 'change',
      type: 'any',
    },
  ],
  type: [
    {
      required: true,
      message: '请选择跳转类型',
      trigger: 'change',
    },
  ],
  linkUrl: [
    {
      required: true,
      message: '请输入跳转地址',
      trigger: 'change',
    },
  ],
}

/** 轮播图尺寸 */
export const imgSizes = [
  {
    id: 1, // 首页顶部
    width: 708,
    height: 254,
  },
  {
    id: 2, // 首页中部
    width: 708,
    height: 180,
  },
  {
    id: 3, // 首页底部
    width: 708,
    height: 254,
  },
  {
    id: 4, // 学习页顶部
    width: 708,
    height: 254,
  },
  {
    id: 5, // 学习页中部
    width: 708,
    height: 180,
  },
  {
    id: 6, // 学习页底部
    width: 708,
    height: 254,
  },
  {
    id: 7, // 首页应用
    width: 708,
    height: 254,
  },
]
