import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { EditExamIndIcatorsIdsItem } from '@/services/affairs/discipline-inspection-list/exam-indicators/types'
import type { CompanySHowItem } from '@/services/publicity/companyShow/types'

export function getTableColumns(
  optionColumnRenderer: (row: EditExamIndIcatorsIdsItem) => VNodeChild,
  handleUpdateValueRender: (row: CompanySHowItem) => VNodeChild,
): DataTableColumns<EditExamIndIcatorsIdsItem> {
  return [
    { type: 'selection' },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'title',
      title: '封面图',
    },
    {
      key: 'matter',
      title: '公司名称',
    },
    {
      key: 'evaluationRequirements',
      title: '公司介绍',
      render: (row) => {
        return h('span', {
          innerHTML: row.evaluationRequirements,
        })
      },
    },
    {
      key: 'evaluationMode',
      title: '发布时间',
      render: (row) => {
        return h('span', {
          innerHTML: row.evaluationMode,
        })
      },
    },
    {
      key: 'fileList',
      title: '置顶',
      render: row => handleUpdateValueRender(row),
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
