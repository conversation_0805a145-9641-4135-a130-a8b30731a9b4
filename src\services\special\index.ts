/** 获取专题分类列表 */

import { commonReq } from '../request'
import type { PaginationReq } from '../types'
import type {
  AddSpecialActivityParams,
  AddSpecialCategoryParams,
  AddSpecialColumnParams,
  GetSpecialCategoryList,
  MoveSpecialCategoryParams,
  MoveSpecialColumnParams,
} from './types'

/** 获取专题分类列表 */
export function getSpecialCategoryList() {
  return commonReq.get<GetSpecialCategoryList[]>({
    url: '/sys-operation/theme/category',
  })
}

/** 新增专题分类 */
export function postSpecialCategory(data: AddSpecialCategoryParams) {
  return commonReq.post({
    url: '/sys-operation/theme/category',
    data,
  })
}

/** 修改专题分类 */
export function putSpecialCategory(data: AddSpecialCategoryParams) {
  return commonReq.put({
    url: '/sys-operation/theme/category',
    data,
  })
}

/** 移动专题分类 */
export function putMoveSpecialCategory(data: MoveSpecialCategoryParams) {
  return commonReq.put({
    url: '/sys-operation/theme/category/move',
    data,
  })
}

/** 删除专题分类 */
export function deleteSpecialCategory(ids: string) {
  return commonReq.delete({
    url: '/sys-operation/theme/category',
    params: {
      ids,
    },
  })
}

/** 新增专题栏目 */
export function postSpecialColumn(data: AddSpecialColumnParams) {
  return commonReq.post({
    url: '/sys-operation/backend/v1/themeColumn',
    data,
  })
}

/** 修改专题栏目 */
export function putSpecialColumn(data: AddSpecialColumnParams) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/themeColumn',
    data,
  })
}

/** 移动专题栏目 */
export function putMoveSpecialColumn(data: MoveSpecialColumnParams) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/themeColumn/move',
    data,
  })
}

/** 删除专题栏目 */
export function deleteSpecialColumn(ids: string) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/themeColumn',
    params: {
      ids,
    },
  })
}

/** 获取专题活动列表 */
export function getSpecialActivityList(
  params: { columnId: string; title: string } & PaginationReq,
) {
  return commonReq.get({
    url: '/sys-operation/theme/activity',
    params,
  })
}

/** 新增专题活动 */
export function postSpecialActivity(data: AddSpecialActivityParams) {
  return commonReq.post({
    url: '/sys-operation/theme/activity',
    data,
  })
}

/** 修改专题活动 */
export function putSpecialActivity(data: AddSpecialActivityParams) {
  return commonReq.put({
    url: '/sys-operation/theme/activity',
    data,
  })
}

/** 删除专题活动 */
export function deleteSpecialActivity(ids: string) {
  return commonReq.delete({
    url: '/sys-operation/theme/activity',
    params: {
      ids,
    },
  })
}

/** 查看专题活动详情 */
export function getSpecialActivityDetail(id: string) {
  return commonReq.get({
    url: `/sys-operation/theme/activity/${id}`,
  })
}

/** 置顶专题活动 */
export function postSpecialActivityTop(id: string) {
  return commonReq.post({
    url: `/sys-operation/theme/activity/top/${id}`,
  })
}

/** 推荐专题活动 */
export function postRecommendSpecialActivity(id: string) {
  return commonReq.post({
    url: `/sys-operation/theme/activity/recommend/${id}`,
  })
}

/** 隐藏专题活动 */
export function postHiddenSpecialActivity(id: string) {
  return commonReq.post({
    url: `/sys-operation/theme/activity/hidden/${id}`,
  })
}

/** 隐藏专题活动 */
export function updatedJoinRotationPool(id: string) {
  return commonReq.post({
    url: `/sys-operation/theme/activity/slider/${id}`,
  })
}
