<template>
  <div class="h-[50px] pt-[25px] pl-[30px]">
    <span class="text-[14px] font-[600] text-[#333] leading-[20px] mb-[26px]">先进展示</span>
  </div>
  <n-divider />
  <n-tabs type="line" animated class="pl-[30px]">
    <n-tab-pane name="Organization" tab="先进基层党组织">
      <PartyOrganizationTable />
    </n-tab-pane>
    <n-tab-pane name="PartyMember" tab="优秀党员">
      <PartyPersonTable />
    </n-tab-pane>
    <n-tab-pane name="PartyWorkers" tab="优秀党务工作者">
      <PartyWorkerTable />
    </n-tab-pane>
  </n-tabs>
</template>

<script setup lang="ts">
import PartyPersonTable from './cpn/PartyPersonTable.vue'
import PartyOrganizationTable from './cpn/PartyOrganizationTable.vue'
import PartyWorkerTable from './cpn/PartyWorkerTable.vue'
</script>

<style lang="scss" scoped></style>
