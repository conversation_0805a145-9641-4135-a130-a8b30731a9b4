<!--
 * @Description: 菜单编辑表单
-->
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="80"
    label-align="right"
    label-placement="left"
    :rules="formRules"
    :model="formDataReactive"
  >
    <n-grid>
      <n-form-item-gi
        :span="11"
        label="菜单类型："
        path="type"
        label-width="auto"
      >
        <n-select v-model:value="formDataReactive.type" :options="menuTypes" />
      </n-form-item-gi>
      <n-form-item-gi
        :offset="2"
        :span="11"
        label="菜单名称："
        path="name"
        label-width="auto"
      >
        <n-input v-model:value="formDataReactive.name" />
      </n-form-item-gi>
    </n-grid>
    <!-- 按钮不需要上传图标 -->
    <n-form-item
      v-if="formDataReactive.type !== MENU_DICT.BTN"
      label="选择图标："
      path="icon"
      label-width="auto"
    >
      <!-- <icon-select /> -->
      <n-popover
        trigger="click"
        placement="bottom"
        :show="iconVisible"
        width="trigger"
        @clickoutside="iconVisible = false"
      >
        <template #trigger>
          <n-input
            v-model:value="formDataReactive.icon"
            placeholder="请选择图标"
            clearable
            @click="showIcon"
          >
            <template #prefix>
              <Component
                :is="`icon-${formDataReactive.icon}`"
                :class="{ 'w-[20px] h-[20px] mr-[4px]': formDataReactive.icon }"
              />
            </template>
          </n-input>
        </template>
        <icon-select
          ref="iconSelectRef"
          style="width: 480px"
          :active-icon="formDataReactive.icon"
          @selected="selected"
        />
      </n-popover>
    </n-form-item>
    <!-- 按钮不需要路由地址 -->
    <n-form-item
      v-if="formDataReactive.type !== MENU_DICT.BTN"
      label="路由地址："
      path="path"
      label-width="auto"
    >
      <n-input v-model:value="formDataReactive.path" />
    </n-form-item>
    <!-- 仅菜单显示 -->
    <n-form-item
      v-if="formDataReactive.type === MENU_DICT.MENU"
      label="路由名称："
      path="routeName"
      label-width="auto"
    >
      <n-input v-model:value="formDataReactive.routeName" />
    </n-form-item>

    <!-- 菜单和按钮显示此选项 -->
    <n-grid
      v-if="
        formDataReactive.type === MENU_DICT.MENU ||
          formDataReactive.type === MENU_DICT.BTN
      "
    >
      <n-form-item-gi
        :span="11"
        label="权限字符："
        path="permission"
        label-width="auto"
      >
        <n-input v-model:value="formDataReactive.permission" />
      </n-form-item-gi>
      <n-form-item-gi
        :offset="2"
        :span="11"
        label="排序："
        path="sortOrder"
        label-width="84"
      >
        <n-input-number v-model:value="formDataReactive.sortOrder" :min="0" />
      </n-form-item-gi>
    </n-grid>

    <!-- 目录显示此选项 -->
    <n-grid v-if="formDataReactive.type === MENU_DICT.DIRECTORY">
      <n-form-item-gi
        :span="11"
        label="排序："
        path="sortOrder"
        label-width="84"
      >
        <n-input-number v-model:value="formDataReactive.sortOrder" :min="0" />
      </n-form-item-gi>
      <n-form-item-gi
        :offset="2"
        :span="11"
        label="显示隐藏："
        path="visible"
        label-width="auto"
      >
        <n-space>
          <n-radio
            :checked="formDataReactive.visible === VISIBLE_DICT.SHOW"
            :value="VISIBLE_DICT.SHOW"
            @change="handleVisibleChange"
          >
            显示
          </n-radio>
          <n-radio
            :checked="formDataReactive.visible === VISIBLE_DICT.NOT_SHOW"
            :value="VISIBLE_DICT.NOT_SHOW"
            @change="handleVisibleChange"
          >
            隐藏
          </n-radio>
        </n-space>
      </n-form-item-gi>
    </n-grid>
    <n-form-item label="上级菜单：" path="parentId" label-width="auto">
      <n-tree-select
        v-model:value="formDataReactive.parentId"
        :options="parentOptions"
      />
    </n-form-item>
    <!-- 仅菜单显示 -->
    <n-form-item
      v-if="formDataReactive.type === MENU_DICT.MENU"
      label="文件路径："
      path="filePath"
      label-width="auto"
    >
      <n-input v-model:value="formDataReactive.filePath" />
    </n-form-item>
    <!-- 按钮不显示 -->
    <n-form-item
      v-if="formDataReactive.type === MENU_DICT.MENU"
      label="显示隐藏："
      path="visible"
      label-width="auto"
    >
      <n-space>
        <n-radio
          :checked="formDataReactive.visible === VISIBLE_DICT.SHOW"
          :value="VISIBLE_DICT.SHOW"
          @change="handleVisibleChange"
        >
          显示
        </n-radio>
        <n-radio
          :checked="formDataReactive.visible === VISIBLE_DICT.NOT_SHOW"
          :value="VISIBLE_DICT.NOT_SHOW"
          @change="handleVisibleChange"
        >
          隐藏
        </n-radio>
      </n-space>
    </n-form-item>
    <!-- 按钮不需要重定向地址 -->
    <n-form-item
      v-if="formDataReactive.type !== MENU_DICT.BTN"
      label="重定向地址："
      path="redirect"
      label-width="auto"
    >
      <n-input v-model:value="formDataReactive.redirect" />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { onBeforeMount, reactive, ref } from 'vue'
import type { NForm } from 'naive-ui'
import {
  NFormItem,
  NFormItemGi,
  NGrid,
  NInput,
  NInputNumber,
  NSelect,
} from 'naive-ui'
import { MENU_DICT, VISIBLE_DICT, menuTypes } from '../../config'
import {
  getMenuDetail,
  postMenu,
  updateMenu,
} from '../../../../../../../services'
import { btnRules, directoryRules, getFormData, menuFormRules } from './config'
import IconSelect from '@/components/IconSelect/index.vue'

// 是否展示选择icon
const iconVisible = ref(false)

const props = defineProps({
  editType: { type: String as PropType<'add' | 'modify'>, required: true },
  id: { type: Number },
  parentId: { type: Number },
  parentList: { type: Array },
})
const emits = defineEmits(['saved'])

const parentOptions = computed(() => {
  if (props.parentList) {
    const list = props.parentList.slice(0)
    switchTree(list)
    list.unshift({
      label: '主菜单',
      key: '-1',
    })
    return list
  } else {
    return []
  }
})

const formRef = ref<InstanceType<typeof NForm>>()
const formDataReactive = reactive(getFormData())
const oldImgUrlRef = ref('')

const formRules = computed(() => {
  if (formDataReactive.type === MENU_DICT.MENU) {
    // 菜单规则
    return menuFormRules
  } else if (formDataReactive.type === MENU_DICT.BTN) {
    // 按钮规则
    return btnRules
  } else {
    // 目录规则
    return directoryRules
  }
})

onBeforeMount(() => {
  const { editType, id } = props

  if (editType === 'modify' && id) {
    getMenuDetail(id).then((res) => {
      formDataReactive.type = String(res.type)
      formDataReactive.name = res.name
      formDataReactive.path = res.path
      formDataReactive.sortOrder = res.sortOrder
      formDataReactive.parentId = res.parentId
      formDataReactive.permission = res.permission || ''
      formDataReactive.filePath = res.filePath || ''
      formDataReactive.routeName = res.routeName || ''
      formDataReactive.visible = res.visible
      formDataReactive.redirect = res.redirect
      formDataReactive.icon = res.icon
      oldImgUrlRef.value = res.icon
    })
  } else {
    // 初始化父级菜单
    formDataReactive.parentId = props.parentId
  }
})

// 转化table结构为Tree Select结构
function switchTree(list: any) {
  if (Array.isArray(list)) {
    list.forEach((item) => {
      item.label = item.name
      item.key = item.id
      if (item.children) {
        switchTree(item.children)
      }
    })
  }
}

function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // 处理formData
      const formData = new FormData()
      // 如果当前是编辑，添加menuId字段
      if (props.editType === 'modify') {
        formData.append('menuId', String(props.id))
      }
      formData.append(
        'parentId',
        String(formDataReactive.parentId ?? props.parentId ?? -1),
      )
      formData.append('type', String(formDataReactive.type))
      formData.append('name', formDataReactive.name)
      formData.append('sortOrder', String(formDataReactive.sortOrder))

      // 菜单独有字段
      if (formDataReactive.type === MENU_DICT.MENU) {
        formData.append('visible', String(formDataReactive.visible))
        formData.append('icon', formDataReactive.icon!)
        formData.append('path', formDataReactive.path!)
        formData.append('permission', formDataReactive.permission!)
        formData.append('filePath', formDataReactive.filePath!)
        formData.append('routeName', formDataReactive.routeName!)
        formData.append('redirect', formDataReactive.redirect!)
      }

      // 目录独有字段
      if (formDataReactive.type === MENU_DICT.DIRECTORY) {
        formData.append('visible', String(formDataReactive.visible))
        formData.append('icon', formDataReactive.icon!)
        formData.append('path', formDataReactive.path!)
        formData.append('redirect', formDataReactive.redirect!)
      }

      // 按钮独有字段
      if (formDataReactive.type === MENU_DICT.BTN) {
        formData.append('permission', formDataReactive.permission!)
      }

      if (props.editType === 'modify') {
        // 编辑菜单
        updateMenu(formData).then((res) => {
          window.$message.success('操作成功')
          emits('saved')
        })
      } else {
        // 新增菜单
        postMenu(formData).then((res) => {
          window.$message.success('操作成功')
          emits('saved')
        })
      }
    }
  })
}

function handleVisibleChange(e: Event) {
  formDataReactive.visible = (e.target as HTMLInputElement).value
}

function showIcon() {
  iconVisible.value = true
}

/** 选择图标 */
function selected(name: any) {
  // form.value.icon = name;
  iconVisible.value = false
  formDataReactive.icon = name
}

defineExpose({
  validateAndSave,
})
</script>
<style lang="scss" scoped>
:deep(.n-dialog__title) {
  padding-right: 0 !important;
}
</style>
