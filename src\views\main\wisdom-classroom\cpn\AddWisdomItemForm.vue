<script lang="ts" setup>
import { NForm } from 'naive-ui'
import { newsDetailFormRules } from './config'
import { uploadImg } from '@/services'
import {
  getNewsCategoryList,
  getNewsDetail,
  postNews,
  putNews,
} from '@/services/wisdom'
import type { uploadFileItem } from '@/services/types'

interface Props {
  type?: string
  id?: string
  categoryId: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formData = reactive<{ data: any }>({
  data: {
    title: '',
    shortTitle: '',
    author: '',
    publishTime: null,
    content: '',
    coverUrl: '',
    categoryId: props.categoryId,
    isTop: 0,
    isRecommend: 0,
    isHidden: 0,
    readNum: 0,
    commentNum: 0,
    likeNum: 0,
    sort: 0,
    reviewed: 0,
    reviewedUser: '',
    reviewedTime: '',
    isOutside: '0',
    linkUrl: '',
  },
})

const formRef = ref<InstanceType<typeof NForm>>()

const categoryList = ref<any>([])

onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getNewsDetail(props.id).then((res) => {
      formData.data = res

      // 从详情接口取出的附件需要设置它的百分比为100
      if (formData.data.fileList.length) {
        formData.data.fileList.forEach((item: any) => {
          item.percentage = 100
          item.url = item.fileName
          item.name = item.original
        })
      }
    })
  }
  getNewsCategoryList().then((res) => {
    categoryList.value = res.map((item) => {
      return {
        label: item.name,
        value: item.id,
        children: item.children
          ? item.children.map((item) => {
            return {
              label: item.name,
              value: item.id,
            }
          })
          : undefined,
      }
    })
  })
})

/**
 * 上传图片
 * @param {any} file:File
 */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formData.data.coverUrl === '' || formData.data.coverUrl === null) {
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formData.data.coverUrl = data.url
      }
    }
  }
  catch (error) {}
}

/**
 * 删除图片
 */
function handleCoverDelete() {
  formData.data.coverUrl = ''
}

// const viewImageSrc = computed(
//   () => import.meta.env.VITE_API_BASE + formData.data.coverUrl,
// )

function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      // 新增或编辑
      if (formData.data.id) {
        putNews({
          ...formData.data,
          fileIds: formData.data?.fileList
            ? formData.data.fileList.map((file: any) => file.id)
            : [],
        }).then((res) => {
          window.$message.success('编辑成功')
          emits('saved')
        })
      }
      else {
        postNews({
          ...formData.data,
          fileIds: formData.data?.fileList
            ? formData.data.fileList.map((file: any) => file.id)
            : [],
        }).then((res) => {
          window.$message.success('添加成功')
          emits('saved')
        })
      }
    }
  })
}

/** 获取党组织id */
// const handleUpdateValue = (v: string) => {
//   formData.data.categoryId = v
// }
// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

async function handleUpload(
  file: File,
  options: any,
  callBack: (data: any) => void,
  upDatedProgress: (progress: number) => void,
) {
  try {
    const fileData = new FormData()
    const fileItem = options.file.file as Blob
    fileData.append('file', fileItem)
    const data: uploadFileItem = await uploadImg(fileData, (num) => {
      upDatedProgress(num)
    })
    callBack(data)
  }
  catch (error) {
    window.$message.error('上传失败')
    // 在出错时停止执行后续代码
  }
}

defineExpose({
  validateAndSave,
  resetForm,
})
</script>

<template>
  <n-form
    ref="formRef"
    :disabled="props.type === 'view'"
    :model="formData.data"
    :rules="newsDetailFormRules"
    label-align="right"
    label-placement="left"
    label-width="90"
    require-mark-placement="left"
    size="small"
  >
    <n-form-item label="标题：" path="title">
      <n-input
        v-model:value="formData.data.title"
        clearable
        maxlength="100"
        placeholder="请输入标题"
        rows="2"
        show-count
        style="width: 580px"
        type="textarea"
      />
    </n-form-item>

    <n-form-item label="所属栏目：" path="categoryId">
      <n-tree-select
        v-model:value="formData.data.categoryId"
        :options="categoryList"
        check-strategy="all"
        children-field="children"
        clearable
        key-field="value"
        label-field="label"
        placeholder="请选择所属栏目"
        value-field="value"
        @update:value="(v:any) => (formData.data.categoryId = v)"
      />
    </n-form-item>

    <n-form-item label="图片" path="coverUrl">
      <ImgUploader
        v-model:oldImgUrl="formData.data.coverUrl"
        :height="150"
        :is-readonly="props.type === 'view'"
        :need-cropper="false"
        :width="220"
        @delete="handleCoverDelete"
        @done="handleCoverDone"
      />
    </n-form-item>

    <n-form-item label="排序：" path="sort">
      <n-input-number
        v-model:value="formData.data.sort"
        :max="999999"
        :min="0"
        :precision="0"
        :step="1"
        placeholder="请输入排序数值"
        style="width: 580px"
      />
    </n-form-item>
    <n-form-item label="   " path="">
      <span class="text-[12px] text-[#999999] mt-[-20px]">说明：数字越大排序越靠前，建议新增时查询当前序列排序的最大数值，并在此基础上增加避免创建后无法看到新建信息的情况。</span>
    </n-form-item>

    <n-form-item label="阅读量：">
      <n-input-number
        v-model:value="formData.data.readNum"
        :max="999999"
        :min="0"
        :precision="0"
        :step="1"
        placeholder="请输入基础阅读量"
        style="width: 580px"
      />
    </n-form-item>
    <n-form-item label="   " path="">
      <span class="text-[12px] text-[#999999] mt-[-20px]">说明：设置基础阅读量，实际阅读量为基础阅读量 + 实际阅读量。</span>
    </n-form-item>

    <n-form-item class="mt-[-20px]" label="点赞量：">
      <n-input-number
        v-model:value="formData.data.likeNum"
        :max="999999"
        :min="0"
        :precision="0"
        :step="1"
        placeholder="请输入基础点赞量"
        style="width: 580px"
      />
    </n-form-item>
    <n-form-item label="   " path="">
      <span class="text-[12px] text-[#999999] mt-[-20px]">说明：设置基础点赞量，实际点赞量为基础点赞量 + 实际点赞量。</span>
    </n-form-item>

    <n-form-item label="是否外链：" path="isTop">
      <n-switch
        v-model:value="formData.data.isOutside"
        checked-value="1"
        unchecked-value="0"
      />
    </n-form-item>
    <!--    上传附件-->
    <n-form-item
      v-if="formData.data.isOutside === '0'"
      label="附件："
      path="fileList"
    >
      <FileUploaderNew
        v-model:original-file-list="formData.data.fileList"
        :disabled="props.type === 'view'"
        :max="10"
        :size-limit="50"
        :upload-method="handleUpload"
        accept=".pdf"
        custom-tips-text="可上传多个文件，仅支持扩展名后缀是.pdf且大小50M以内的文件"
        need-progress
        show-tip
      />
    </n-form-item>
    <n-form-item
      v-if="formData.data.isOutside === '0'"
      label="内容详情："
      path="content"
    >
      <RichEditor
        v-model:value="formData.data.content"
        :disabled="props.type === 'view'"
        :rich-height="350"
        style="width: 100%"
      />
    </n-form-item>
    <n-form-item
      v-if="formData.data.isOutside === '1'"
      label="链接地址："
      path="linkUrl"
    >
      <n-input
        v-model:value="formData.data.linkUrl"
        clearable
        placeholder="请输入链接地址"
        style="width: 100%"
      />
    </n-form-item>
  </n-form>
</template>

<style lang="scss" scoped></style>
