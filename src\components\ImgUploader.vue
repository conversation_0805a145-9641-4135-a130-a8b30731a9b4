<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui'
import 'vue-cropper/dist/index.css'
import { VueCropper } from 'vue-cropper'
import { dataUrl2File, file2Base64, url2DataUrl } from '@/utils/file-utils'

interface Props {
  oldImgUrl?: string
  width: number
  height: number
  size?: number
  needCropper?: boolean
  accept?: string
  isReadonly?: boolean
  beforeUpload?: Function
}
const props = withDefaults(defineProps<Props>(), {
  oldImgUrl: '',
  needCropper: true,
  accept: '.jpg,.jpeg,.png',
  isReadonly: false,
})

const emits = defineEmits<{
  (e: 'done', value: File): void
  (e: 'delete'): void
  (e: 'error'): void
  (e: 'update:oldImgUrl', value: string): void
}>()
const showCropper = ref(false)
const fileList = ref<UploadFileInfo[]>()

// 裁剪组件可选项
const cropperOption = reactive({
  autoCrop: true,
  fixedBox: true,
  canMoveBox: false,
  centerBox: true,
  mode: 'cover',
  img: '',
  autoCropWidth: 0,
  autoCropHeight: 0,
  uid: '',
  fileName: '',
})

/** 图片选择完毕 */
function handleFileChange(options: {
  file: UploadFileInfo
  fileList: Array<UploadFileInfo>
  event?: Event
}) {
  const { fileList } = options
  if (!fileList.length) {
    emits('delete')
    return
  }
  const file = fileList[0]
  // 转换为 base64
  file2Base64(file.file).then((data) => {
    cropperOption.fileName = file.name
    if (props.needCropper) {
      cropperOption.img = data as string
      cropperOption.autoCropWidth = props.width
      cropperOption.autoCropHeight = props.height
      cropperOption.uid = file.id
      showCropper.value = true
    }
    else {
      setFileList(data as string)
    }
  })
}
/** 设置文件列表 */
function setFileList(data: string) {
  // 转换为 File 对象
  const file: File = dataUrl2File(data, cropperOption.fileName)
  fileList.value = [
    {
      id: cropperOption.uid,
      name: file.name,
      file,
      percentage: 100,
      status: 'finished',
    },
  ]
  // 重置 cropperOption
  cropperOption.img = ''
  cropperOption.autoCropWidth = 0
  cropperOption.autoCropHeight = 0
  cropperOption.uid = ''
  cropperOption.fileName = ''
  emits('done', file)
}

const cropperRef = ref()
/** 确定裁剪 */
function handleConfirmCrop() {
  cropperRef.value?.getCropData((data: string) => {
    setFileList(data)
  })
}
/** 取消裁剪 */
function handleCloseCrop() {
  setFileList(cropperOption.img)
}
/** 监听props的变化,进行dataUrl转换 */
watch(props, (newV) => {
  if (newV.oldImgUrl && newV.oldImgUrl !== '') {
    url2DataUrl(import.meta.env.VITE_API_BASE + newV.oldImgUrl)
      .then((res) => {
        cropperOption.fileName = 'original.png'
        setFileList(res)
      })
      .catch(() => {
        emits('error')
        emits('update:oldImgUrl', '')
      })
  }
  else {
    fileList.value = []
  }
})
</script>
<template>
  <div class="flex items-end">
    <n-upload
      v-model:file-list="fileList"
      :accept="accept"
      :max="1"
      list-type="image-card"
      show-remove-button
      :disabled="isReadonly"
      :on-before-upload="beforeUpload"
      @change="handleFileChange"
    />
    <span class="text-[12px] font-[400] text-[#999] flex flex-col w-[200px]">
      <span v-if="props.size">{{ '大小不超过' + props.size + 'M' }}</span>
      <span>推荐尺寸{{ props.width }}×{{ props.height }}</span>
      <span>支持{{ props.accept }}</span>
    </span>

    <n-modal
      v-if="showCropper"
      v-model:show="showCropper"
      style="width: auto"
      preset="dialog"
      title="图片裁剪"
      positive-text="确定"
      :show-icon="false"
      :mask-closable="false"
      @positive-click="handleConfirmCrop"
      @close="handleCloseCrop"
    >
      <div
        :style="{
          margin: '15px 0 20px 0',
          width: props.width + 150 + 'px',
          height: props.height + 150 + 'px',
        }"
      >
        <vue-cropper ref="cropperRef" v-bind="cropperOption" />
      </div>
    </n-modal>
  </div>
</template>
<style lang="scss" scoped>
:deep(.n-upload-file-list.n-upload-file-list--grid) {
  display: flex;
}
:deep(.n-upload-file-list .n-upload-file.n-upload-file--image-card-type) {
  width: 250px;
  height: 150px;
}

:deep(.n-upload-file--image-card-type) {
  border: none !important;
}
</style>
