import type { fileItem } from '@/services/affairs/discipline-inspection-list/exam-indicators/types'

/**  党员发展表格 */
export interface PartyDevelopmentListItem {
  id?: string
  /** 发展阶段 */
  developStage: string
  /** 姓名 */
  trueName: string
  /** 所属党组织 */
  organization: string
  /** 性别 */
  sex: string
  /** 学历 */
  edu: string
  /** 出生日期 */
  birthday: string
  /** 申请时间 */
  applyTime: string
  sort: number

  [key: string]: any
}

export interface DevMemberChoseList {
  userId: string
  username: string
  trueName: string
  password: string
  salt: any
  phone: string
  lockFlag: string
  avatar: any
  deptId: string
  identityId: string
  signImg: any
  birthday: string
  delFlag: string
  sex: string
  deptName: any
  identityName: any
  createTime: string
  updateTime: string
  partyProcessStatus?: string
}

export interface PartyDevelopmentMemberAdd {
  /** 入党流程状态 */
  partyProcessStatus: string | null
  /** 用户Id */
  userId: string | null
  /** 接收组织 */
  organizationId: string | null
  /** 申请时间 */
  applyTime: string | null
  /** 附件 */
  fileIds: string[] | null
  fileList: fileItem[]
}

export interface StageListAndBaseInfo {
  stageList: StageList[]
  partyMemberBaseInfo: PartyMemberBaseInfo
  sort: number
}

export interface StageList {
  id: string
  stageName: string
  sort: number
  trueName: string
  phone: string
  organizationId: string
  confirmTime: string
  contactId: string
  contacts: string[]
  fileList: FileList[]
}

export interface FileList {
  id: string
  fileName: string
  original: string
}

export interface PartyMemberBaseInfo {
  id: string
  userId?: string
  username: string
  trueName: string
  phone: string
  identityId: string
  sex: string
  nation: string
  nativePlace: string
  birthday: string
  edu: string
  jobPost: string
  homeAddr: string
  status: string
  avatar: string
}

export interface ContactListItem {
  userId: string
  username: string
  deptId: string
  trueName: string
  partyIdentity: string
}

/** 发展党员保存信息 */
export interface DevAndSaveInfo {
  id?: string
  memberId: string
  sort?: number | null
  organizationId?: string | null
  confirmTime?: string | null
  contactId?: string | null
  fileIds?: string[] | null
  trueName?: string | null
  phone?: string | null
  contacts: string[]
}
