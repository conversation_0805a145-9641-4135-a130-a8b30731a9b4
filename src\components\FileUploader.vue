<!-- 文件上传组件 -->
<template>
  <div class="file-uploader">
    <n-upload
      v-model:file-list="fileListRef"
      abstract
      :accept="accept"
      :max="props.max"
      :disabled="isReadonly"
      @change="handleChange"
    >
      <n-upload-trigger #="{ handleClick }" abstract>
        <div style="display: flex; align-items: center; margin-bottom: 15px">
          <n-button
            size="small"
            class="trigger-btn"
            :disabled="fileListRef.length >= props.max || disabled"
            @click="handleClick"
          >
            <template #icon>
              <n-icon size="16">
                <file-upload-outlined />
              </n-icon>
            </template>
            点击上传
          </n-button>

          <span
            v-if="showTip"
            style="
              font-size: 12px;
              font-weight: 400;
              color: #999999;
              margin-left: 20px;
            "
          >支持{{ accept }}文件</span>
        </div>
      </n-upload-trigger>
    </n-upload>

    <div v-for="(file, index) in fileListRef" :key="file.id" class="file-body">
      <div class="file-item">
        <span class="file-bg">
          <span>{{
            getCoverByExtension(getExtension(file.name || file.original))
          }}</span>
          <img v-if="file.url && needProgress" src="@/assets/image/done.png">
        </span>

        <span v-if="(file as any).editing">
          <n-input
            v-model:value="fileNameRef"
            size="small"
            @blur="() => handleSaveFileName(index)"
          />
          <span>{{ fileExtRef }}</span>
        </span>

        <n-ellipsis v-else class="file-name">
          {{ file.name || file.original }}
        </n-ellipsis>

        <span class="btns">
          <!-- <n-button size="large" text @click="handleClickEdit(index)">
            <template #icon>
              <n-icon>
                <mode-edit-outlined />
              </n-icon>
            </template>
          </n-button> -->
          <n-button
            v-show="!isReadonly"
            size="large"
            text
            @click="handleClickDelete(index)"
          >
            <template #icon>
              <n-icon>
                <delete-outlined />
              </n-icon>
            </template>
          </n-button>

          <n-button
            v-show="false"
            size="large"
            text
            @click="handleClickDown(index)"
          >
            <template #icon>
              <n-icon>
                <ArrowDownwardRound />
              </n-icon>
            </template>
          </n-button>
          <!-- <template v-if="fileListRef.length > 1">
            <n-button
              size="large"
              text
              :disabled="index === 0"
              @click="handleClickUp(index)"
            >
              <template #icon>
                <n-icon>
                  <arrow-upward-round />
                </n-icon>
              </template>
            </n-button>
            <n-button
              size="large"
              text
              :disabled="index === fileListRef.length - 1"
              @click="handleClickDown(index)"
            >
              <template #icon>
                <n-icon>
                  <arrow-downward-round />
                </n-icon>
              </template>
            </n-button>
          </template> -->
        </span>

        <n-progress
          v-if="needProgress && !file.url"
          :height="3"
          status="success"
          class="progress-bar"
          :show-indicator="false"
          :percentage="percentageRef"
        />
      </div>

      <n-checkbox
        v-if="showDownloadController"
        disabled
        label="允许下载"
        :value="1"
      />
    </div>

    <div v-if="accept !== '*/*'" class="tips-ctn">
      <slot name="tips" />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { ref, watch } from 'vue'
import type { UploadFileInfo } from 'naive-ui'
import {
  NButton,
  NCheckbox,
  NEllipsis,
  NIcon,
  NInput,
  NProgress,
  NUpload,
  NUploadTrigger,
} from 'naive-ui'
import {
  ArrowDownwardRound,
  DeleteOutlined,
  FileUploadOutlined,
  // ModeEditOutlined,
  // ArrowUpwardRound,
  // ArrowDownwardRound,
} from '@vicons/material'
import type { uploadFileItemNew } from '@/services/affairs/party-building-list/exam-indicators/types'

import { downloadFile } from '@/utils/downloader'

const props = defineProps({
  max: {
    type: Number,
    required: true,
  },
  showDownloadController: {
    type: Boolean,
    default: false,
  },
  accept: {
    type: String,
    default: '*/*',
  },
  sizeLimit: {
    type: Number,
    default: 0,
  },
  originalFileList: {
    type: Array as PropType<UploadFileInfo[]>,
    default: () => [],
  },
  showTip: {
    type: Boolean,
    default: false,
  },
  needProgress: {
    type: Boolean,
    default: false,
  },
  uploadMethod: {
    type: Function,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  isReadonly: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  'file-list-change',
  'delete-original-file',
  'video-done',
  'progress-done',
])

const playTimeRef = ref() // 视频时长
const fileListRef = ref<uploadFileItemNew[]>([]) // 文件列表
const fileNameRef = ref('') // 文件名
const fileExtRef = ref('') // 文件拓展名
const percentageRef = ref(0) // 进度条百分比

function handleChange(options: {
  file: UploadFileInfo
  fileList: Array<UploadFileInfo>
  event?: Event
}) {
  (options.file as any).editing = false

  // 判断文件拓展名
  const fileExt = options.file.file?.name.split('.').pop()?.toLowerCase()
  if (!fileExt || !props.accept.includes(fileExt)) {
    window.$message.warning(`请选择${props.accept}文件`, {
      duration: 3000,
    })
    options.fileList.pop()
    return false
  }

  if (
    props.sizeLimit > 0
    && (options.file.file?.size ?? 0) > props.sizeLimit * 1000 * 1000
  ) {
    options.fileList.pop()
    window.$message.warning('文件大小超出限制，请重新选择')
    return false
  }

  const isVideo = options.file.file?.type.includes('video')
  if (isVideo) {
    const fileUrl = URL.createObjectURL(options.file.file!)
    const audio = new Audio(fileUrl)
    audio.addEventListener('loadedmetadata', function() {
      playTimeRef.value = Math.round(this.duration * 1000)
      emits('video-done', playTimeRef.value)
    })
  }

  // 判断是否展示进度条，如果展示，直接调用上传接口
  if (props.needProgress) {
    props.uploadMethod?.(options.file.file, percentageRef, () => {
      emits('progress-done')
      window.$message.success('上传成功')
    })
  } else {
    emits('file-list-change', options.fileList)
  }
}

/** 点击编辑按钮 */
// function handleClickEdit(index: number) {
//   console.log(fileListRef.value[index].file)
//   const nameArr = fileListRef.value[index].name.split('.')
//   fileExtRef.value = nameArr.pop() ?? ''
//   fileNameRef.value = nameArr.join('.')
//   ;(fileListRef.value[index] as any).editing = true
// }

/** 保存文件名称 */
function handleSaveFileName(index: number) {
  const newName = `${fileNameRef.value}.${fileExtRef.value}`
  const oldFile = fileListRef.value[index].file
  fileListRef.value[index].name = newName
  if (oldFile) {
    fileListRef.value[index].file = new File([oldFile], newName, {
      type: oldFile?.type,
    })
  }
  (fileListRef.value[index] as any).editing = false
}

/** 点击删除按钮 */
function handleClickDelete(index: number) {
  if (!fileListRef.value[index].file) {
    // 如果file为undefined，说明是回显的文件，删除时发送事件
    emits('delete-original-file', fileListRef.value[index].id)
  }
  const beforeId = fileListRef.value[index].id || null
  fileListRef.value.splice(index, 1)
  emits('file-list-change', fileListRef.value, beforeId)
}

/** 点击上移按钮 */
// function handleClickUp(index: number) {
//   // 和上一个对换位置
//   let temp = fileListRef.value[index]
//   fileListRef.value[index] = fileListRef.value[index - 1]
//   fileListRef.value[index - 1] = temp
// }

/** 点击下移按钮 */
// function handleClickDown(index: number) {
//   // 和下一个对换位置
//   let temp = fileListRef.value[index]
//   fileListRef.value[index] = fileListRef.value[index + 1]
//   fileListRef.value[index + 1] = temp
// }

function getCoverByExtension(extension: string) {
  const low = extension.toLowerCase()
  switch (low) {
    case 'doc':
    case 'docx':
      return 'WORD'
    case 'ppt':
    case 'pptx':
      return 'PPT'
    default:
      return low.toUpperCase()
  }
}

function getExtension(fileName: string | undefined) {
  return fileName?.split('.').pop() ?? ''
}

function getFileList() {
  return fileListRef.value
}

watch(
  props,
  (newV) => {
    if (newV.originalFileList.length) {
      fileListRef.value = newV.originalFileList
    } else {
      fileListRef.value = []
    }
  },
  {
    deep: true,
    immediate: true,
  },
)

const handleClickDown = (index: number) => {
  const fileItem = fileListRef.value[index] || null
  // console.log('fileItem: ', fileItem)
  if (fileItem) {
    downloadFile(fileItem.fileName, fileItem.original)
    window.$message.success('下载成功')
  } else {
    window.$message.error('未找到下载的文件')
  }
}

defineExpose({
  getFileList,
})
</script>

<style lang="scss" scoped>
.file-uploader {
  width: 100%;

  .trigger-btn {
    width: 94px;
    height: 30px;
    background: linear-gradient(180deg, #ffffff 0%, #f5f5f5 100%);
  }

  .tips-ctn {
    margin-top: 14px;
    :deep(.tips) {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      line-height: 22px;
    }
  }

  .file-body {
    display: flex;

    &:nth-child(n + 3) > .file-item {
      border-top: none;
    }

    > .file-item {
      width: calc(100% - 99px);
      flex-grow: 1;
      height: 61px;
      background: #ffffff;
      border: 1px solid #dcddde;
      display: flex;
      align-items: center;
      padding-left: 11px;
      margin-right: 21px;
      position: relative;

      > .file-bg {
        width: 78px;
        height: 44px;
        background: #4c98fc;
        opacity: 0.7;
        font-size: 12px;
        font-family: ArialMT;
        color: #ffffff;
        line-height: 44px;
        text-align: center;
        position: relative;

        img {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 12px;
          height: 12px;
        }
      }

      :deep(.file-name) {
        max-width: 60%;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        margin-left: 10px;
      }

      > .btns {
        position: absolute;
        right: 20px;
        display: flex;
        align-items: center;

        .n-button:nth-child(n + 2) {
          margin-left: 10px;
        }
      }

      .progress-bar {
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
  }
}
</style>
