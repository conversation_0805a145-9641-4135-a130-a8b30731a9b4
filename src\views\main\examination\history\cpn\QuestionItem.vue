<!--
 * @Description: 题目展示
-->
<template>
  <div :class="{ bt: index > 0 }">
    <div class="top">
      <span class="type">
        {{ index + 1 }}、{{ getTypeLabel(question.type) }}
      </span>
      <span class="score">
        {{ question.achievePoint }}分 / {{ question.score }}分
      </span>
    </div>

    <div class="name">
      {{ question.name }}
    </div>

    <div v-if="question.type !== 4" class="options">
      <div
        v-for="item in question.optionList"
        :key="item.id"
        class="option-item"
      >
        {{ item.name }}、{{ item.content }}
      </div>
    </div>

    <div class="result">
      答题结果：<span>{{ question.selectedOptionId }}</span>
    </div>

    <div class="correct">
      正确答案：<span>{{ question.answer }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { questionTypes } from '@/views/main/examination/config'

defineProps({
  question: {
    type: Object,
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
})

// 根据type获取题型
function getTypeLabel(value: number) {
  return questionTypes.find(item => item.value === value)?.label
}
</script>

<style lang="scss" scoped>
.bt {
  border-top: 1px dashed #999;
  padding-top: 10px;
}

.top {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  display: flex;
  justify-content: space-between;
}

.name {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 26px;
  margin: 10px 0;
}

.options {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 34px;
  margin-bottom: 7px;
  padding-left: 15px;
}

.correct {
  margin: 10px 0;
}
</style>
