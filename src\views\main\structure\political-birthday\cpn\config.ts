import type { DataTableColumns, FormRules } from 'naive-ui'
import type { VNodeChild } from 'vue'

// 状态字典
// 状态 0-启用 1-禁用
export const USER_STATUS = {
  USE: '0',
  NOT: '1',
}

export const formRules: FormRules = {
  blessing: {
    required: true,
    message: '请输入祝福语',
    trigger: 'blur',
  },
  blessStatus: {
    required: false,
  },
  imgUrl: {
    required: true,
    message: '请上传祝福图片',
    trigger: 'blur',
  },
}

// 政治生日列表
export function getPoliticalBirthdayColumns(
  optionColumnRenderer: (row: any) => VNodeChild,
  topRender: (row: any) => VNodeChild,
  imgRender: (row: any) => VNodeChild,
): DataTableColumns {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      key: 'blessing',
      title: '祝福语',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '550px', 'word-break': 'break-all' },
        },
      },
    },
    {
      key: 'imgUrl',
      title: '图片',
      width: '15%',
      render: imgRender,
    },
    {
      key: 'updateTime',
      title: '更新时间',
      width: '20%',
    },
    {
      key: 'blessStatus',
      title: '状态（启用项随机）',
      width: '10%',
      render: topRender,
    },
    {
      key: 'action',
      title: '操作',
      width: '10%',
      render: row => optionColumnRenderer(row),
    },
  ]
}
