import type { PartyTeamListType, organizationPersonType } from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 获取班子管理列表 */
export function getPartyTeamList(
  params: {
    appointedName?: string
    orgId?: string
    teamTransitionStatus?: string
  } & PaginationReq,
) {
  return commonReq.get<PaginationRes<PartyTeamListType[]>>({
    url: '/org-construction/backend/v1/team_transition/page',
    params,
  })
}

/** 通过id查询班子管理--换届 */
export function getPartyTeam(id: string) {
  return commonReq.get({
    url: `/org-construction/backend/v1/team_transition/${id}`,
  })
}

/** 新增班子管理--换届 */
export function postPartyTeamSaveInfo(data: any) {
  return commonReq.post({
    url: '/org-construction/backend/v1/team_transition',
    data,
  })
}

/** 修改班子管理--换届 */
export function putPartyTeamSaveInfo(data: any) {
  return commonReq.put({
    url: '/org-construction/backend/v1/team_transition',
    data,
  })
}

/** 通过id删除班子管理 */
export function deletePartyTeam(ids: string) {
  return commonReq.delete({
    url: '/org-construction/backend/v1/team_transition',
    params: { ids },
  })
}

/** 班子管理--换届 */
export function postPartyTeamChange(teamTransitionId: string) {
  return commonReq.post({
    url: '/org-construction/backend/v1/team_transition/transition',
    params: { teamTransitionId },
  })
}

/**
 * 获取组织树结构
 */
export function getOrganizationTree() {
  return commonReq.get({
    url: '/org-construction/organization/list',
  })
}

/**
 * 根据部门id获取部门下的人
 */
export function getDeptPerson(params: { deptId: string }) {
  return commonReq.get<Array<organizationPersonType>>({
    url: '/upms/portal-user/list_user_show_by_dept_id',
    params,
  })
}
