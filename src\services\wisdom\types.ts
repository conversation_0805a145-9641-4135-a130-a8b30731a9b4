import type { ICustomCategoryTree } from '../types'
/** 智云课堂分类列表及其子类 */
export interface NewsCategoryList {
  children?: NewsCategoryList[] | null
  /** 评论得分 */
  configCommentScore?: number | null
  /** 学习得分 */
  configStudyScore?: number | null
  /** 主键id */
  id?: number | null
  /** 分类名称 */
  name?: null | string
  /** 父级id */
  parentId?: number | null
  /** 排序 */
  sort?: number | null
}

/** 智云课堂分类列表组装 */
export type NewsCategoryListVO = NewsCategoryList & ICustomCategoryTree

/** 新增 / 编辑 智云课堂分类 */
export interface AddOrEditNewsCategory {
  /** 评论得分 */
  configCommentScore?: number | null
  /** 学习得分 */
  configStudyScore?: number | null
  /** 分类名称 */
  name?: null | string
  /** 父级id */
  parentId?: number | null
  /** 排序 */
  sort?: number | null
  /** 主键Id */
  id?: string | null
}

/** 移动智云课堂分类 */
export interface MoveNewsCategory {
  /** 主键id */
  id: string
  /** 上移 -1、下移 +1, 默认值为0 */
  move: string
  /** 分类名称 */
  name?: null | string
  /** 父级id */
  parentId: number
  /** 排序 */
  sort?: number | null
}

/** 智云课堂列表 */
export interface NewsListItem {
  id: string
  title: string
  shortTitle: string
  author: string
  publishTime: string
  content: string
  coverUrl: string
  categoryId: string
  isTop: number
  isRecommend: number
  isHidden?: any
  readNum?: any
  commentNum?: any
  likeNum?: any
  collectNum?: any
  sort: number
  reviewed: number
  reviewedUser: string
  reviewedTime?: any
}

/** 新增 / 编辑智云课堂  */
export interface AddOrEditNewListItem {
  id?: number | string
  title: string
  shortTitle: string
  author: string
  publishTime: string | null
  content: string
  coverUrl: string
  categoryId: number | string
  isTop: number
  isRecommend: number
  isHidden: number
  readNum: number | string
  commentNum: number
  likeNum: number | string
  sort: number
  reviewed: number
  reviewedUser: string
  reviewedTime: string
}

/** 智云课堂详情 */
export interface NewsDetail {
  /** 作者 */
  author?: null | string
  /** 分类id */
  categoryId?: number | null
  /** 收藏次数 */
  collectNum?: number | null
  /** 评论次数 */
  commentNum?: number | null
  /** 内容 */
  content?: null | string
  /** 封面Url */
  coverUrl?: number | null
  /** 主键id */
  id?: number | null
  /** 是否隐藏 (0-否，1-是) */
  isHidden?: number | null
  /** 是否推荐 */
  isRecommand?: null | string
  /** 是否置顶 */
  isTop?: null | string
  /** 点赞次数 */
  likeNum?: number | null
  /** 发布时间 */
  publishTime?: null | string
  /** 阅读人次 */
  readNum?: number | null
  /** 阅读时间配置，单位（秒） */
  readTimeConfig?: number | null
  /** 审核是否通过 (0-否，1-是) */
  reviewed?: number | null
  /** 审核时间 */
  reviewedTime?: null | string
  /** 审核人 */
  reviewedUser?: null | string
  /** 短标题 */
  shortTitle?: null | string
  /** 排序 */
  sort?: number | null
  /** 标题 */
  title?: null | string
}

// 查询所有轮播池列表
export interface CarousePoolType {
  id: string
  serviceId?: any
  poolType: string
  name: string
  coverUrl: string
  sort: number
  isAppShow: number
  delFlag: string
}
