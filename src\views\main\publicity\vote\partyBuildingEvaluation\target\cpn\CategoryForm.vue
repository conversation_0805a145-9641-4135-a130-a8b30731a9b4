<script setup lang="ts">
import type { NForm } from 'naive-ui'
import { formRules } from './config'
import {
  addPartyBuildingEvaluationCategory,
  editorPartyBuildingEvaluationCategory,
} from '@/services/publicity/vote/evaluation'

interface Props {
  type?: string
  id?: string
  name?: string
  categoryId?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
  name: '',
  categoryId: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()
const categoryFormRef = ref<InstanceType<typeof NForm>>()

const formDataReactive = reactive({
  votingAssessId: '',
  name: '',
  id: '',
})

onMounted(async() => {
  if (props.type === 'modify') {
    formDataReactive.name = props.name
    formDataReactive.id = props.categoryId
  } else {
    formDataReactive.votingAssessId = props.id
    formDataReactive.name = props.name
  }
})

// 验证表单,调用接口
async function validateAndSave() {
  const errors = await new Promise((resolve) => {
    categoryFormRef.value?.validate((errors) => {
      resolve(errors)
    })
  })

  if (!errors) {
    try {
      const { type } = props
      const { name, id, votingAssessId } = formDataReactive
      let res: any = null
      if (type === 'modify') {
        res = await editorPartyBuildingEvaluationCategory({ id, name })
      } else {
        res = await addPartyBuildingEvaluationCategory({ votingAssessId, name })
      }
      if (res) {
        window.$message.success('保存成功')
        emits('saved')
      }
    } catch (error) {}
  }
}
// 重置表单
function resetForm() {
  categoryFormRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="categoryFormRef"
    size="small"
    require-mark-placement="left"
    label-width="80"
    label-align="right"
    label-placement="left"
    :rules="formRules"
    :model="formDataReactive"
    class="p-[22px] py-[25px]"
  >
    <n-form-item path="name">
      <n-input
        v-model:value="formDataReactive.name"
        placeholder="请输入考核类别"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
