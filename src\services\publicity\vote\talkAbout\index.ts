import type {
  ReviewTableItem,
  TalkAboutPartyBranchTableItem,
  TalkAboutPartyMemberTableItem,
  TalkAboutTableItem,
} from './type'
import type { Test } from '@/views/main/publicity/vote/reviewItems/type'
import type { PaginationReq, PaginationRes } from '@/services/types'
import { commonReq, downloadReq } from '@/services/request'

/** 获取明主评议列表 */
export function getTalkAboutTableList(
  params: { title: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<TalkAboutTableItem>>({
    url: '/party-affairs/backend/v1/democratic-review/page',
    params,
  })
}

/** 删除明主评议记录 */
export function delTalkAboutTableListItem(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/democratic-review',
    params: { ids },
  })
}

/** 获取明主评议详情 */
export function getTalkAboutInfo(id: string) {
  return commonReq.get({
    url: `/party-affairs/backend/v1/voting-review/${id}`,
  })
}

/** 添加明主评议详情 */
export function addTalkAboutInfoItem(data: TalkAboutTableItem) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/democratic-review',
    data,
  })
}

/** 更新明主评议详情 */
export function putTalkAboutInfo(data: TalkAboutTableItem) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/democratic-review',
    data,
  })
}

/** 获取民主评议党员列表 */
export function getTalkAboutPartyMemberList(
  params: {
    reviewId: string
    orgId: string
    commentatorName?: string
    orgName?: string
  } & PaginationReq,
) {
  return commonReq.get<PaginationRes<TalkAboutPartyMemberTableItem>>({
    url: '/party-affairs/backend/v1/democratic-review/getPartList',
    params,
  })
}

/** 获取民主评议党员详情 */
export function getTalkAboutPartyMemberDetail(params: {
  reviewId: string
  reviewItemId: string
  orgId: string
  commentatorId: string
}) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/democratic-review/getPartDetail',
    params,
  })
}

/** 获取民主评议党支部班子列表 */
export function getTalkAboutPartyBranchList(
  params: {
    reviewId: string
    orgId: string
  } & PaginationReq,
) {
  return commonReq.get<PaginationRes<TalkAboutPartyBranchTableItem>>({
    url: '/party-affairs/backend/v1/democratic-review/getBranchList',
    params,
  })
}

/** 获取民主评议党支部班子详情 */
export function getTalkAboutPartyBranchDetail(params: {
  reviewId: string
  reviewItemId: string
  orgId: string
}) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/democratic-review/getBranchDetail',
    params,
  })
}

/** 民主评议党员导出 */
export function postExportTalkAboutPartyMember(params: {
  reviewId: string
  orgId: string
  reviewItemId: string
}) {
  return downloadReq.get({
    url: '/party-affairs/backend/v1/democratic-review/part/export',
    params,
    responseType: 'blob',
  })
}

/** 民主评议党支部班子导出 */
export function postExportTalkAboutPartyBranch(params: {
  reviewId: string
  orgId: string
  reviewItemId: string
}) {
  return downloadReq.get({
    url: '/party-affairs/backend/v1/democratic-review/branch/detail/export',
    params,
    responseType: 'blob',
  })
}

/** 获取明主评议项党员列表 */
export function getReviewitemsPartList(
  params: { title: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<ReviewTableItem>>({
    url: '/party-affairs/backend/v1/democratic-review-item/pagePart',
    params,
  })
}

/** 获取明主评议项支部班子列表 */
export function getReviewitemsBranchList(
  params: { title: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<ReviewTableItem>>({
    url: '/party-affairs/backend/v1/democratic-review-item/pageBranch',
    params,
  })
}

/** 添加明主评议项党员详情 */
export function addReviewItemDetailPart(data: Test) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/democratic-review-item/savePart',
    data,
  })
}

/** 添加明主评议项支部班子详情 */
export function addReviewItemDetailBranch(data: Test) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/democratic-review-item/saveBranch',
    data,
  })
}

/** 删除明主评议项记录 */
export function delReviewItemListItem(ids: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/democratic-review-item',
    params: { ids },
  })
}

/** 获取明主评议项支部班子列表 */
export function getReviewTemplate() {
  return commonReq.get({
    url: '/party-affairs/backend/v1/democratic-review-template/getReviewTemplate',
  })
}

/** 更新明主评议项党员详情 */
export function putReviewItemDetailPart(data: Test) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/democratic-review-item/updatePart',
    data,
  })
}

/** 更新明主评议项支部班子详情 */
export function putReviewItemDetailBranch(data: Test) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/democratic-review-item/updateBranch',
    data,
  })
}

/** 获取民主评议项详情 */
export function getReviewItemDetail(params: { id: string }) {
  return commonReq.get({
    url: `/party-affairs/backend/v1/democratic-review-item/${params.id}`,
  })
}

/** 获取明主评议详情 */
export function getTalkAboutInfoNew(id: string) {
  return commonReq.get({
    url: `/party-affairs/backend/v1/democratic-review/${id}`,
  })
}

/** 获取民主评议项结果详情 */
export function getPartDetailEachDetail(params: { distributeId: string }) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/democratic-review/getPartDetailEachDetail',
    params,
  })
}

/** 民主评议下发 */
export function distributeReview(reviewId: string) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/democratic-review-distribute/distribute',
    params: { reviewId },
  })
}

/** 民主评议项是否可修改 */
export function reviewItemtoBeIssued(params: { reviewItemId: string }) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/democratic-review-item/reviewItemtoBeIssued',
    params,
  })
}
