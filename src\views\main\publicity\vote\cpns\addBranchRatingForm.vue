<script setup lang="ts">
import type { NForm } from 'naive-ui'
import dayjs from 'dayjs'
import { branchRatingInfoFormRules } from './config'
import {
  addBranchRatingInfoItem,
  getBranchRatingDetailInfo,
  modifyBranchRatingInfoItem,
} from '@/services/publicity/vote/branchRating'
import type { AddBranchRatingType } from '@/services/publicity/vote/branchRating/type'

interface Props {
  type?: string
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<AddBranchRatingType>({
  id: '',
  title: '',
  year: null,
  selfStartTime: null,
  selfEndTime: null,
  status: '',
})
const formRef = ref<InstanceType<typeof NForm>>()

// 时间检验判断
const selfStartTimeVal = ref()
const selfEndTimeVal = ref()
const validateSelfStartTime = (time: number) => {
  selfStartTimeVal.value = time
}
const validateSelfEndTime = (time: number) => {
  selfEndTimeVal.value = time
}

onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getBranchRatingDetailInfo(props.id).then((res) => {
      formDataReactive.id = res.id || ''
      formDataReactive.title = res.title || ''
      formDataReactive.year = res.year || ''
      formDataReactive.selfStartTime = res.selfStartTime || ''
      formDataReactive.selfEndTime = res.selfEndTime || ''
      formDataReactive.status = res.status || ''
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      // 更新或添加
      if (formDataReactive.id) {
        // 更新
        modifyBranchRatingInfoItem(formDataReactive).then(() => {
          window.$message.success('更新成功')
          emits('saved')
        })
      } else {
        // 新增
        const data: AddBranchRatingType = {
          title: formDataReactive.title,
          year: formDataReactive.year,
          selfStartTime: formDataReactive.selfStartTime,
          selfEndTime: formDataReactive.selfEndTime,
        }
        addBranchRatingInfoItem(data).then(() => {
          window.$message.success('添加成功')
          emits('saved')
        })
      }
    }
  })
}

// 屏蔽年份
const yearDisable = (time: number) => {
  // 1、当前之前的年份不能选；2、当前年份后5年之外不能选
  return (
    time < dayjs(`${dayjs().year()}-01-01`).valueOf()
    || time
      > dayjs(
        `${dayjs().year() + 5}-${dayjs().month()}-${dayjs().date()}`,
      ).valueOf()
  )
}

// 不能选择当前之前的日期
const formatterStartTime = (time: number) => {
  return time < Date.now() - 86400000
}

const formatterEndTime = (time: number) => {
  if (selfStartTimeVal.value) {
    return time < selfStartTimeVal.value - 86400000
  } else {
    return time < Date.now() - 86400000
  }
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="120"
    label-align="right"
    label-placement="left"
    :model="formDataReactive"
    :rules="branchRatingInfoFormRules"
  >
    <n-form-item span="24" label="评定方案名称：" path="title">
      <n-input
        v-model:value="formDataReactive.title"
        rows="5"
        maxlength="20"
        show-count
      />
    </n-form-item>
    <n-form-item span="24" label="评议年度：" path="year">
      <n-date-picker
        v-model:formatted-value="formDataReactive.year"
        style="width: 100%"
        placeholder="请选择评议年度"
        clearable
        type="year"
        :is-date-disabled="yearDisable"
        @update:formatted-value="
          (v:any) => (formDataReactive.year = v)
        "
      />
    </n-form-item>
    <n-form-item span="24" label="自评开始时间：" path="selfStartTime">
      <n-date-picker
        v-model:formatted-value="formDataReactive.selfStartTime"
        :disabled="props.type === 'modify'"
        style="width: 100%"
        placeholder="请选择自评开始时间"
        clearable
        type="datetime"
        :is-date-disabled="formatterStartTime"
        @update:value="validateSelfStartTime"
        @update:formatted-value="
          (v:any) => (formDataReactive.selfStartTime = v)
        "
      />
    </n-form-item>
    <n-form-item span="24" label="自评结束时间：" path="selfEndTime">
      <n-date-picker
        v-model:formatted-value="formDataReactive.selfEndTime"
        style="width: 100%"
        placeholder="请选择自评结束时间"
        clearable
        type="datetime"
        :is-date-disabled="formatterEndTime"
        @update:value="validateSelfEndTime"
        @update:formatted-value="
          (v:any) => (formDataReactive.selfEndTime = v)
        "
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
