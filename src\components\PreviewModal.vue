<script setup lang="ts">
defineProps<{ showModal: boolean; richText: string }>()
</script>
<template>
  <div>
    <Transition name="modal">
      <div
        v-show="showModal"
        class="absolute left-[50%] top-[50%] transform translate-x-[-50%] translate-y-[-40%] w-[500px] h-[800px] z-20"
      >
        <div class="w-[360px] h-[661px] bg-[white] rounded-[28px] p-[11px]">
          <div class="pt-[10px]">
            <p class="text-[18px] font-semibold">
              江苏交控党委召开全系统党史学习教育动员部署会
            </p>
            <div class="mt-[16px]">
              <span>江苏交通控股党建</span>
              <span class="mx-[10px]">661阅读</span>
              <span>2021-03-08</span>
            </div>
            <div class="mt-[16px]" v-html="richText" />
          </div>
        </div>
      </div>
    </Transition>
    <slot />
  </div>
</template>

<style scoped lang="scss">
.modal-enter-active,
.modal-leave-active {
  transition: all 0.5s ease;
}

.modal-enter-from,
.modal-leave-to {
  width: 0;
  height: 0;
  opacity: 0;
}
</style>
