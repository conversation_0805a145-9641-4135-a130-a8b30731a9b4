/*
 * @Description: 权限管理相关数据
 */
import { defineStore } from 'pinia'
import type { SelectOption } from 'naive-ui'
import type {
  IDeptTreeNode,
  IMenu,
  IRole,
  IRoleQuery,
  IUser,
  IUserQueryByDept,
  IVerifyUser,
  IVerifyUserQuery,
} from '../../../services'
import {
  getAllCompanys,
  getAllMenus,
  getDeptTree,
  getDeptsByCompany,
  getOtherOptions,
  getRoleList,
  getUserList,
  getUserListByDept,
  getVerifyUserList,
} from '../../../services'

const useAuthorityStore = defineStore('authority', {
  state: () => ({
    userList: [] as IUser[], // 用户列表
    userSelectOptions: [] as SelectOption[], // 用户选择项列表

    deptList: [] as IDeptTreeNode[], // 部门树

    allMenus: [] as IMenu[], // 所有菜单

    roleList: [] as IRole[], // 角色列表

    otherOptions: {} as Record<string, SelectOption[]>, // 公司、职务、岗位、角色的选项
    deptOptions: [] as SelectOption[], // 部门选项

    verifyUserList: [] as IVerifyUser[], // 审核用户列表
  }),

  getters: {
    usernameById: (state) => {
      return (id: number) =>
        state.userSelectOptions.find(item => item.value === id)?.label
    },
  },

  actions: {
    async getUserListAction(deptId: number, params: IUserQueryByDept) {
      const res = await getUserListByDept(deptId, params)
      this.userList = res.list
      return Promise.resolve(res.total)
    },

    async getUserSelectOptions() {
      const res = await getUserList({})
      this.userSelectOptions = res.records.map<SelectOption>(item => ({
        label: item.username,
        value: item.userId,
      }))
    },

    async getDeptTreeAction() {
      const res = await getDeptTree()
      this.deptList = res
    },

    async getAllMenusAction() {
      this.allMenus = await getAllMenus()
    },

    async getRoleListAction(params: IRoleQuery) {
      const res = await getRoleList(params)
      this.roleList = res.list
      return Promise.resolve(res.total)
    },

    async getOptionsAction() {
      const res = await getOtherOptions()
      this.otherOptions = res

      const allCompanys = await getAllCompanys()
      this.otherOptions.companyList = allCompanys.map(item => ({
        label: item.name,
        value: item.deptId,
      }))
    },

    async getDeptsByCompanyAction(corpId: number) {
      const res = await getDeptsByCompany(corpId)
      this.deptOptions = res
    },

    async getVerifyUserListAction(params: IVerifyUserQuery) {
      const res = await getVerifyUserList(params)
      this.verifyUserList = res.list
      return Promise.resolve(res.total)
    },
  },
})

export default useAuthorityStore
