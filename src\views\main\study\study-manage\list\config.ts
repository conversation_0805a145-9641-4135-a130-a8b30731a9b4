import type { VNodeChild } from 'vue'
import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
// import type { informationType } from '@/services/run/types'
import { NImage } from 'naive-ui'
import type { StudyContentItem } from '@/services/study/study-manage/types'
export function createColumns(
  operationRender: (row: StudyContentItem) => VNodeChild,
  topRender: (row: StudyContentItem) => VNodeChild,
): TableColumns<StudyContentItem> {
  return [
    {
      type: 'selection',
      align: 'center',
    },
    {
      key: 'index',
      title: '序号',
      width: '5%',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      title: '课程标题',
      key: 'title',
      width: '20%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      title: '封面图',
      key: 'img',
      width: '15%',
      render: row =>
        row.cover
        && h(NImage, {
          src: import.meta.env.VITE_API_BASE + row.cover.url,
          width: '62',
          style: { height: '40px' },
        }),
    },
    {
      title: '学习人数',
      width: '15%',
      key: 'studyNum',
      align: 'center',
    },

    {
      title: '更新时间',
      key: 'updateTime',
      width: '15%',
      render: row => row.updateTime ?? '-',
    },
    {
      title: '状态',
      key: 'publishStatus',
      align: 'center',
      width: '10%',
      render: row => (row.publishStatus === '1' ? '未发布' : '已发布'),
    },
    {
      title: '是否置顶',
      key: 'top',
      width: '8%',
      render: topRender,
    },
    {
      title: '操作',
      width: '12%',
      key: 'operation',
      render: operationRender,
    },
  ]
}
