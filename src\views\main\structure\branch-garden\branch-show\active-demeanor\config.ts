import type { VNodeChild } from 'vue'
import { type DataTableColumns, NImage } from 'naive-ui'
// import { NImage } from 'naive-ui'
import type { ActiveDemeanorItem } from '@/services/structure/branch-garden/types'

export function getTableColumns(
  optionColumnRenderer: (row: ActiveDemeanorItem) => VNodeChild,
): DataTableColumns<ActiveDemeanorItem> {
  return [
    { type: 'selection' },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      key: 'imgUrl',
      title: '活动图片',
      align: 'center',
      width: '10%',
      render: (row) => {
        const url = row.imgUrl || ''
        return h(NImage, {
          src: import.meta.env.VITE_API_BASE + url,
          style: { width: '100px' },
        })
      },
    },

    {
      key: 'title',
      title: '活动名称',
      width: '25%',
    },
    {
      title: '移动端是否展示',
      key: 'isMobile',
      align: 'center',
      width: '15%',
    },
    {
      title: '移动端排序',
      key: 'sort',
      align: 'center',
      width: '15%',
    },
    {
      key: 'updateTime',
      title: '更新时间',
      width: '20%',
      render: (row) => {
        return h('span', {
          innerHTML: row.updateTime,
        })
      },
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '10%',
      render: row => optionColumnRenderer(row),
    },
  ]
}
