import type { VNodeChild } from 'vue'
import type { DataTableColumns } from 'naive-ui'
import type { UserItem } from '@/services/members/historical/types'
import { POLITICAL_STATUS, getDictLabelByValue } from '@/store/dict'

export function getTableColumns(
  optionColumnRenderer: (row: UserItem) => VNodeChild,
): DataTableColumns<UserItem> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      render: (_, i) => i + 1,
    },
    {
      key: 'trueName',
      title: '姓名',
    },
    {
      key: 'phone',
      title: '手机号',
    },
    {
      key: 'political',
      title: '政治面貌',
      render: (row) => {
        return getDictLabelByValue(POLITICAL_STATUS, row.political)
      },
    },
    // {
    //   key: 'partyIdentity',
    //   title: '党内职务',
    // },
    // {
    //   key: 'partyIdentity',
    //   title: '部门',
    // },
    {
      key: 'deptName',
      title: '所属党组织',
      render: row => row.deptName ?? '--',
    },
    {
      key: 'updateTime',
      title: '更新时间',
      render: row => row.updateTime ?? '--',
    },
    {
      key: 'reasonType',
      title: '状态',
    },
    {
      key: 'action',
      title: '操作',
      align: 'left',
      width: '180',
      render: row => optionColumnRenderer(row),
    },
  ]
}
