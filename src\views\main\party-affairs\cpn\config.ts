import type { FormRules } from 'naive-ui'
import { isHttpOrHttpsLink } from '@/utils/utils'

export const formRules: FormRules = {
  themeName: [
    {
      required: true,
      message: '主题不能为空',
      trigger: 'input',
    },
  ],
  content: [
    {
      required: true,
      message: '内容不能为空',
      trigger: 'input',
    },
  ],

  isTop: [
    {
      required: true,
      message: '请选择是否置顶',
      trigger: 'change',
    },
  ],
  pictureUrl: {
    required: true,
    message: '请上传图片',
    trigger: 'change',
    type: 'string',
  },
  fileList: {
    required: true,
    validator(rule: any, value: any) {
      if (value === null) {
        return new Error('请选择文件')
      }
      return true
    },
    trigger: 'change',
  },
  linkUrl: {
    required: true,
    validator(rule: any, value: any) {
      if (value === null || !isHttpOrHttpsLink(value)) {
        return new Error('请输入合法的链接地址')
      }
      return true
    },
    trigger: 'input',
    type: 'string',
  },
}
