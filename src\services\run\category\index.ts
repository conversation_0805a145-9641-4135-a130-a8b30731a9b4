import type { categoryDetailType, categoryListType } from './types'
import { commonReq } from '@/services/request'

// 获取分类列表
export function fetchCategoryClassification() {
  return commonReq.get({
    url: '/sys-operation/backend/v1/news/category',
  })
}

// 添加分类
export function addCategoryClassification(data: {
  id?: string
  sort?: number
}) {
  return commonReq.post({
    url: '/sys-operation/backend/v1/news/category',
    data,
  })
}

// 修改分类
export function operateCategoryClassification(data: {
  id?: string
  name?: string
  sort?: number
  move?: string
}) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/news/category',
    data,
  })
}

// 删除分类
export function deleteCategoryClassification(data: { ids: string }) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/news/category',
    params: data,
  })
}

// 获取资讯列表
export function loadCategoryTable(params: {
  size?: number
  current?: number
  categoryId?: string
  title?: string
}) {
  return commonReq.get<{ total: number; records: categoryListType[] }>({
    url: '/sys-operation/backend/v1/news',
    params,
  })
}

// 修改推荐
export function modifyRecommend(id: string) {
  return commonReq.put({
    url: `/sys-operation/backend/v1/news/recommend/${id}`,
  })
}
// 修改置顶
export function modifyTop(id: string) {
  return commonReq.put({
    url: `/sys-operation/backend/v1/news/top/${id}`,
  })
}

// 删除资讯
export function delCategory(data: { ids: string[] }) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/news',
    data,
  })
}

// 添加资讯
export function addCategory(data: categoryDetailType) {
  return commonReq.post({
    url: '/sys-operation/backend/v1/news',
    data,
  })
}

// 获取资讯详情
export function getNewsDetail(id: string | null) {
  return commonReq.get({
    url: `/sys-operation/backend/v1/news/${id}`,
  })
}

// 编辑资讯
export function editorCategory(data: any) {
  return commonReq.put({
    url: '/sys-operation/backend/v1/news',
    data,
  })
}

// 删除单条资讯
export function deleteNewsItem(data: { ids: string }) {
  return commonReq.delete({
    url: '/sys-operation/backend/v1/news',
    params: data,
  })
}
