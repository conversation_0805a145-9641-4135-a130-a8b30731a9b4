import type { ToHistoryUserDetailItem, UserDetailItem, UserItem } from './types'
import { commonReq } from '@/services/request'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 获取用户列表 */
export function getUserList(
  params: { trueName: string; deptId: string } & PaginationReq,
) {
  return commonReq.get<PaginationRes<UserItem>>({
    url: '/upms/portal-user/page',
    params,
  })
}

/** 禁用或启用用户 */
export function putDisabledUser(id: string) {
  return commonReq.put({
    url: `/upms/portal-user/lock/${id}`,
  })
}

/** 获取用户详细信息 */
export function getUserInfo(id: string) {
  return commonReq.get({
    url: `/upms/portal-user/users/${id}`,
  })
}

/** 添加用户 */
export function addUserItem(data: UserDetailItem) {
  return commonReq.post({
    url: '/upms/portal-user/users',
    data,
  })
}

/** 编辑用户 */
export function editorUserItem(data: UserDetailItem) {
  return commonReq.put({
    url: '/upms/portal-user/users',
    data,
  })
}

/** 删除用户 */
export function deleteUsers(ids: string) {
  return commonReq.delete({
    url: '/upms/portal-user/users',
    params: { ids },
  })
}

/** 导入用户文件 */
export function importUserFile(data: FormData) {
  return commonReq.post({
    url: '/upms/portal-user/exports',
    data,
  })
}

/** 重置用户密码 */
export function resetPwd(id: string) {
  return commonReq.put({
    url: `/upms/portal-user/password/${id}`,
  })
}

/** 转至历史 */
export function toHistoryUserItem(data: ToHistoryUserDetailItem) {
  return commonReq.post({
    url: '/upms/portal-user-history',
    data: {
      userId: data.userId,
      reasonType: data.reasonType,
      reasonDescription: data.reasonDescription,
    },
  })
}

/** 排序 */
export function sortUserItem(data: {
  id: string
  direction: string
  deptId: string
}) {
  return commonReq.post({
    url: '/upms/portal-user/sort-swap',
    data,
  })
}
