<!-- 党费基数设定 -->
<template>
  <table-container
    v-model:page="currentPage"
    v-model:page-size="pageSize"
    title="党费基数设定"
    :show-toolbar="false"
    custom-toolbar
    :page-sizes="[10, 15, 20, 30, 50, 100, 10000]"
    :table-columns="tableColumns"
    :table-data="tableData"
    :total="total"
    :checked-row-keys="checkedRowKeys"
    :loading="loading"
    @update-page="onUpdatePage"
    @update-page-size="onUpdatePageSize"
    @update-checked-row-keys="onUpdateCheckedRowKeys"
  >
    <template #btns>
      <button class="operate-btn import-btn" @click="handleExport">
        <n-icon>
          <DocumentImport class="mt-[1px] import-icon" />
        </n-icon>
        数据导入
      </button>
      <button class="operate-btn ml-[8px]" @click="handelExam">
        <n-icon>
          <LibraryAddCheckFilled class="mt-[1px]" />
        </n-icon>
        审核/重审
      </button>
      <button class="operate-btn ml-[8px]" @click="handleCopy">
        <n-icon>
          <ContentCopyFilled class="mt-[1px]" />
        </n-icon>
        复制新建
      </button>
      <button class="operate-btn ml-[8px]" @click="handleExportExcel">
        <n-icon>
          <Export class="mt-[1px]" />
        </n-icon>
        导出
      </button>
    </template>
    <template #filters>
      <div class="filter-operate">
        <n-date-picker
          v-model:value="monthDate"
          class="custom-date"
          type="month"
          clearable
          placeholder="请选择月份"
          @change="handleMonthChange"
        />
        <n-tree-select
          v-model:value="filterReactive.unitId"
          :options="transformTreeData(organizeData)"
          placeholder="请选择所在组织"
          value-field="deptId"
          key-field="deptId"
          clearable
        />
        <n-input
          v-model:value="filterReactive.userName"
          class="custom-input"
          size="small"
          placeholder="请输入姓名"
          clearable
        />
        <n-select
          v-model:value="filterReactive.reviewStatus"
          class="custom-select"
          size="small"
          placeholder="请选择审核状态"
          :options="REVIEWSTATUSOPTIONS"
          clearable
        />
      </div>
    </template>
  </table-container>

  <!-- 导入党费 -->
  <n-modal
    v-model:show="importVisible"
    width="795px"
    :mask-closable="false"
    @cancel="handleCancelExport"
  >
    <div class="import-card">
      <div class="title">
        导入党费
      </div>

      <div class="import-close-icon" @click="handleImportCancel">
        <n-icon>
          <CloseRound />
        </n-icon>
      </div>

      <div class="import-tips">
        <div class="import-label ml-[13px]">
          <div class="ml-[4px] mt-[2px]">
            i
          </div>
        </div>
        <div class="ml-[6px]">
          提示：根据固定模板，直接将党员每月的党费导入，选择当前方式，不按比例核算党费。
        </div>
      </div>

      <div class="import-detail import-base">
        <n-form
          ref="importFormRef"
          inline
          :label-width="80"
          label-placement="left"
          :model="importFormValue"
          :rules="importRules"
        >
          <n-form-item label="选择月份:" path="month">
            <n-date-picker
              v-model:value="importFormValue.month"
              type="month"
              clearable
              placeholder="请选择月份"
            />
          </n-form-item>
        </n-form>

        <!-- 步骤 -->
        <div class="step-one mt-[-10px]">
          <span class="step-num">1</span>
          <span>直接导入固定党费，下载
            <span class="text-[#006FFF] cursor-pointer" @click="handleTemplate">人员模板</span>
          </span>
        </div>
        <div class="step-line" />
        <div class="step-two">
          <span class="step-num">2</span>
          <span>上传填写好的应缴党费模板并导入</span>
        </div>
        <n-form
          ref="importFileFormRef"
          inline
          :label-width="80"
          label-placement="left"
          :model="importFileFormValue"
          :rules="importFileFormRules"
          class="import-file-form mt-[22px]"
        >
          <n-form-item label="选择文件:" path="file">
            <n-upload
              accept=".xls,.xlsx"
              :show-file-list="true"
              @change="handleClickImportExcel"
            >
              <!-- <n-input placeholder="请选择文件" class="w-[250px]" /> -->
              <n-button size="small">
                <template #icon>
                  <n-icon>
                    <document-import />
                  </n-icon>
                </template>
                导入
              </n-button>
            </n-upload>
          </n-form-item>
        </n-form>
      </div>

      <div class="import-btn-contain">
        <button class="submit" @click="handleImportConfirm">
          提交
        </button>
        <button class="cancel" @click="handleImportCancel">
          取消
        </button>
      </div>
    </div>
  </n-modal>

  <!-- 导入失败原因 -->
  <n-modal
    v-model:show="importFailDetailVisible"
    width="477px"
    @cancel="handleCancelExportFail"
  >
    <div class="import-fail-card">
      <div class="tip-icon">
        i
      </div>

      <div class="fail-title">
        导入失败
      </div>

      <div class="import-close-icon" @click="handleCancelExportFail">
        <n-icon>
          <CloseRound />
        </n-icon>
      </div>

      <div class="detail-reason">
        <div v-for="item in importReason" :key="item" class="reason-item">
          失败原因：{{ item }}
        </div>
      </div>
    </div>
  </n-modal>

  <!-- 审核/重审 -->
  <n-modal
    v-model:show="examVisible"
    width="795px"
    :mask-closable="false"
    @cancel="handleCancelExam"
  >
    <div class="import-card">
      <div class="title">
        审核/重审
      </div>

      <div class="import-close-icon" @click="handleCancelExam">
        <n-icon>
          <CloseRound />
        </n-icon>
      </div>

      <div class="w-[96%]">
        <n-data-table
          class="mt-[20px]"
          :max-height="260"
          virtual-scroll
          :scroll-x="1100"
          :columns="examColumns"
          :data="examData"
          :pagination="false"
          :bordered="false"
        />
      </div>

      <div class="import-btn-contain exam-btn-contain">
        <button class="cancel" @click="handleCancelExam">
          返回
        </button>
        <button class="submit" @click="handleExamPass">
          通过
        </button>
        <button class="submit" @click="handleExamNotPass">
          未通过
        </button>
      </div>
    </div>
  </n-modal>

  <!-- 复制新建 -->
  <n-modal
    v-model:show="copyVisible"
    width="795px"
    :mask-closable="false"
    @cancel="handleCancelCopy"
  >
    <div class="import-card">
      <div class="title">
        复制新建
      </div>

      <div class="import-close-icon" @click="handleCancelCopy">
        <n-icon>
          <CloseRound />
        </n-icon>
      </div>

      <div class="import-detail copy-form">
        <n-form
          ref="copyFormRef"
          :label-width="100"
          require-mark-placement="left"
          label-placement="left"
          :model="copyFormValue"
          :rules="copyRules"
        >
          <n-form-item label="数据源月份:" path="from">
            <n-date-picker
              v-model:value="copyFormValue.from"
              type="month"
              clearable
              placeholder="请选择月份"
            />
          </n-form-item>
          <n-form-item label="被赋值月份:" path="to">
            <n-date-picker
              v-model:value="copyFormValue.to"
              type="month"
              clearable
              placeholder="请选择月份"
            />
          </n-form-item>
        </n-form>
        <div class="ml-[14px] mt-[50px]">
          说明：请确保被赋值月份的数据被清空，否则无法执行复制操作。
        </div>
      </div>

      <div class="import-btn-contain">
        <button class="submit" @click="handleSubmitCopy">
          复制
        </button>
        <button class="cancel" @click="handleCancelCopy">
          取消
        </button>
      </div>
    </div>
  </n-modal>

  <!-- 编辑 -->
  <n-modal
    v-model:show="editVisible"
    :mask-closable="false"
    @cancel="handleCancelEdit"
  >
    <div class="import-card edit-card">
      <div class="title">
        编辑
      </div>

      <div class="import-close-icon" @click="handleCancelEdit">
        <n-icon>
          <CloseRound />
        </n-icon>
      </div>
      <n-form
        ref="editFormRef"
        label-width="auto"
        :model="editFormValue"
        :rules="editFormRules"
        label-placement="left"
        require-mark-placement="left"
        class="mt-[30px] w-[80%]"
      >
        <n-form-item label="姓名：" path="userName">
          <n-input
            v-model:value="editFormValue.userName"
            placeholder="请输入姓名"
            disabled
          />
        </n-form-item>
        <n-form-item label="手机号码：" path="userPhone">
          <n-input
            v-model:value="editFormValue.userPhone"
            placeholder="请输入手机号码"
          />
        </n-form-item>
        <n-form-item label="所在组织：" path="unitId">
          <n-tree-select
            v-model:value="editFormValue.unitId"
            :options="transformTreeData(organizeData)"
            placeholder="请选择所在组织"
            value-field="deptId"
            key-field="deptId"
            clearable
            disabled
          />
        </n-form-item>
        <n-form-item label="职务：" path="partyIdentityName">
          <n-input
            v-model:value="editFormValue.partyIdentityName"
            placeholder="请选择职务"
            disabled
          />
        </n-form-item>
        <n-form-item label="月份：" path="returnTime" class="edit-date">
          <n-date-picker
            v-model:value="editMonth"
            type="month"
            clearable
            placeholder="请选择月份"
            @change="handleEditMonth"
          />
        </n-form-item>
        <n-form-item label="应缴金额(元)：" path="totalAmount">
          <n-input
            v-model:value="editFormValue.totalAmount"
            placeholder="请输入应缴金额(元)"
          />
        </n-form-item>
      </n-form>

      <div class="import-btn-contain exam-btn-contain edit-btn-contain">
        <button class="cancel" @click="handleCancelEdit">
          返回
        </button>
        <button class="submit" @click="handleSureEdit">
          确定
        </button>
      </div>
    </div>
  </n-modal>

  <!-- 备注 -->
  <n-modal
    v-model:show="remarkVisible"
    :mask-closable="false"
    @cancel="handleCancelRemark"
  >
    <div class="import-card edit-card">
      <div class="title">
        备注
      </div>

      <div class="import-close-icon" @click="handleCancelRemark">
        <n-icon>
          <CloseRound />
        </n-icon>
      </div>

      <div class="w-[80%] ml-[20px] mt-[60px] remark-form">
        <n-input
          v-model:value="remarkValue"
          :disabled="true"
          type="textarea"
          placeholder="请输入备注"
          size="large"
        />
      </div>

      <div class="import-btn-contain exam-btn-contain">
        <button class="cancel" @click="handleCancelRemark">
          返回
        </button>
        <button class="submit" @click="handleSureRemark">
          确定
        </button>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { h, onMounted, ref } from 'vue'
import { NButton, NIcon, NInput } from 'naive-ui'
import { DocumentImport, Export } from '@vicons/carbon'
import {
  CloseRound,
  ContentCopyFilled,
  LibraryAddCheckFilled,
} from '@vicons/material'
import {
  PAYSTATUS,
  REVIEWSTATUS,
  REVIEWSTATUSOPTIONS,
  getPayBaseColumns,
} from '../config'
import { getCurrentPartyOrganizationByUser } from '@/services/data-permission'
import {
  copyBaseItem,
  downBaseTemplate,
  editBaseItem,
  examBaseItem,
  exportBaseList,
  getBaseList,
  importRecordList,
  remarkBaseItem,
} from '@/services/pay-manage/index'
import { useMyTable } from '@/hooks'
import { downloadArrayBuffer } from '@/utils/downloader'

interface filterItem {
  monthDate: null | Date | string
  unitId: string
  userName: string
  reviewStatus: null | string
}

interface editFormInterface {
  userName: null | string
  userPhone: null | string
  unitId: null | string
  partyIdentityName: null | string
  returnTime: null | string | Date
  totalAmount: null | string
}

interface currentRemarkRowInterface {
  id: number | string
}

interface copyFormValueInterface {
  from: number | null
  to: number | null
}

interface importFileFormInterface {
  file: null | FormData
}

const monthDate = ref(null)

// 查询条件
const filterReactive = ref<filterItem>({
  monthDate: null,
  unitId: '',
  userName: '',
  reviewStatus: null,
})

const {
  loading,
  currentPage,
  pageSize,
  total,
  tableData,
  checkedRowKeys,
  onUpdatePage,
  onUpdatePageSize,
  onUpdateCheckedRowKeys,
  loadData,
} = useMyTable(
  getBaseList,
  filterReactive,
  {
    // batchDeleteTable: true,
    // delApi: delTalkAboutTableListItem,
  },
  false,
  ref(false),
  ref(''),
  undefined,
  100,
)

// 导入弹框
const importVisible = ref(false)

// 导入错误弹框
const importFailDetailVisible = ref(false)

// 审核/重审弹框
const examVisible = ref(false)

// 复制新建弹框
const copyVisible = ref(false)

// 编辑弹框
const editVisible = ref(false)

// 备注弹框
const remarkVisible = ref(false)

const importFormRef = ref()

const copyFormRef = ref()

const importFileFormRef = ref()

const editFormRef = ref<any>(null)

const importFormValue = ref({
  month: new Date(),
  zuzi: '',
})

const copyFormValue = ref<copyFormValueInterface>({
  from: null,
  to: null,
})

const importFileFormValue = ref<importFileFormInterface>({
  file: null,
})

const editFormValue = ref<editFormInterface>({
  userName: null,
  userPhone: null,
  unitId: null,
  partyIdentityName: null,
  returnTime: null,
  totalAmount: null,
})

const remarkValue = ref('')

const importRules = ref({})

const copyRules = ref({
  from: {
    required: true,
    trigger: ['blur', 'change'],
    validator(rule: any, value: string) {
      if (!value) {
        return new Error('请选择数据源月份')
      }
      return true
    },
  },
  to: {
    required: true,
    trigger: ['blur', 'change'],
    validator(rule: any, value: string) {
      if (!value) {
        return new Error('请选择被赋值月份')
      }
      return true
    },
  },
})

const importFileFormRules = ref({
  file: {
    required: true,
    trigger: ['blur', 'change'],
    validator(rule: any, value: any) {
      if (value === null) {
        return new Error('请选择文件')
      }
      return true
    },
  },
})

// 编辑的月份
const editMonth = ref()

const editFormRules = ref({
  userPhone: {
    required: true,
    trigger: ['blur'],
    validator(rule: any, value: string) {
      if (!value) {
        return new Error('请输入手机号码')
      } else if (!/^1[3456789]\d{9}$/.test(value)) {
        return new Error('请输入正确手机号码')
      }
      return true
    },
  },
  returnTime: {
    required: true,
    trigger: ['blur', 'change'],
    validator(rule: any, value: string) {
      if (!value) {
        return new Error('请选择月份')
      }
      return true
    },
  },
  totalAmount: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请输入应缴金额(元)',
  },
})

// 修改和删除按钮渲染
const tableColumns = getPayBaseColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          handelSingleExam(row)
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
          display:
            row.reviewStatus === REVIEWSTATUS.WAIT
            || row.reviewStatus === REVIEWSTATUS.NOT
            || (row.reviewStatus === REVIEWSTATUS.PASS
              && row.payStatus === PAYSTATUS.NOT)
              ? 'inline'
              : 'none',
        },
      },
      {
        default: () => '审核',
      },
    ),
    h(
      NButton,
      {
        onClick: () => {
          handleEdit(row)
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
          display:
            row.reviewStatus === REVIEWSTATUS.WAIT
            || row.reviewStatus === REVIEWSTATUS.NOT
              ? 'inline'
              : 'none',
        },
      },
      {
        default: () => '编辑',
      },
    ),
    h(
      NButton,
      {
        onClick: () => {
          handleRemark(row)
        },
        type: 'primary',
        text: true,
        style: {
          display: row.reviewRemark ? 'inline' : 'none',
          marginRight: '10px',
        },
      },
      {
        default: () => '备注',
      },
    ),
  ]
})

// 组织数据
const organizeData = ref([])

/** 获取列表 */
const getOrganizeList = () => {
  getCurrentPartyOrganizationByUser().then((res: any) => {
    organizeData.value = res || []
  })
}

// 审核列表
const examData = ref<any>([])

const examColumns = ref([
  {
    key: 'index',
    title: '序号',
    align: 'center',
    render: (_: any, i: number) => i + 1,
  },
  {
    key: 'userName',
    title: '姓名',
  },
  {
    key: 'userPhone',
    title: '手机号码',
  },
  {
    key: 'unitName',
    title: '所在组织',
  },
  {
    key: 'partyIdentityName',
    title: '职务',
  },
  {
    key: 'returnTime',
    title: '月份',
  },
  {
    key: 'totalAmount',
    title: '应缴金额（元）',
    width: 150,
  },
  {
    key: 'reviewStatus',
    title: '审核状态',
    render: (row: any) => {
      if (row.reviewStatus === REVIEWSTATUS.NOT) {
        return '未通过'
      } else if (row.reviewStatus === REVIEWSTATUS.PASS) {
        return '通过'
      } else if (row.reviewStatus === REVIEWSTATUS.WAIT) {
        return '待审核'
      } else {
        return ''
      }
    },
  },
  {
    key: 'reviewRemark',
    title: '备注',
    width: 200,
    fixed: 'right',
    render: (row: any) => {
      return h(NInput, {
        type: 'textarea',
        value: row.reviewRemark,
        onUpdateValue: (value: any) => {
          handleSingRemark(row, value)
        },
        style: {
          height: '40px',
        },
      })
    },
  },
])

// 导入取消
function handleCancelExport() {
  importVisible.value = false
}

// 导入失败理由取消
function handleCancelExportFail() {
  importFailDetailVisible.value = false
}

// 导入弹框打开
function handleExport() {
  importVisible.value = true
}

// 编辑弹框打开
function handleEdit(row: any) {
  editFormValue.value = JSON.parse(JSON.stringify(row))
  editFormValue.value.totalAmount = String(row.totalAmount)
  editFormValue.value.returnTime = row.returnTime
    ? new Date(row.returnTime)
    : null
  editMonth.value = row.returnTime ? new Date(row.returnTime) : null
  editVisible.value = true
}

const currentRemarkRow = ref<currentRemarkRowInterface>({ id: '' })

// 备注弹框打开
function handleRemark(row: any) {
  currentRemarkRow.value = row
  remarkValue.value = row.reviewRemark
  remarkVisible.value = true
}

// 审核弹框备注修改
function handleSingRemark(row: any, value: any) {
  row.reviewRemark = value
}

// 审核取消
function handleCancelExam() {
  examVisible.value = false
}

// 备注取消
function handleCancelRemark() {
  remarkVisible.value = false
}

// 审核
function handelExam() {
  if (checkedRowKeys.value.length === 0) {
    window.$message.warning('请先选择数据')
    return
  }
  examVisible.value = true
  // 过滤出选中数据
  const examTableData: any = []
  tableData.value.forEach((data: any) => {
    if (checkedRowKeys.value.includes(data.id)) {
      examTableData.push(data)
    }
  })
  examData.value = examTableData
}

// 单个审核
function handelSingleExam(row: any) {
  examData.value = [row]
  examVisible.value = true
}

// 复制新建
function handleCopy() {
  copyVisible.value = true
}

// 复制新建取消
function handleCancelCopy() {
  copyVisible.value = false
}

// 复制新建提交
function handleSubmitCopy() {
  copyFormRef.value?.validate((errors: any) => {
    if (!errors) {
      const data = {
        from: transformDateString(new Date(copyFormValue.value.from || '')),
        to: transformDateString(new Date(copyFormValue.value.to || '')),
      }

      copyBaseItem(data).then((res) => {
        window.$message.success('操作成功')
        loadData()
      })
      copyVisible.value = false
      copyFormValue.value.from = null
      copyFormValue.value.to = null
    }
  })
}

// 取消编辑
function handleCancelEdit() {
  editVisible.value = false
}

function handleMonthChange(value: string | number) {
  if (value === null) {
    filterReactive.value.monthDate = null
    return
  }
  const date = new Date(value)
  const year = date.getFullYear()
  const month = `0${date.getMonth() + 1}`.slice(-2) // 月份需要加1，且保证两位数显示
  const day = `0${date.getDate()}`.slice(-2) // 保证日期显示两位数
  filterReactive.value.monthDate = `${year}-${month}-${day} 00:00:00`
}

// 保存编辑
function handleSureEdit() {
  editFormRef.value?.validate((errors: any) => {
    if (!errors) {
      if (typeof editFormValue.value.returnTime === 'object') {
        handleEditMonth(editFormValue.value.returnTime)
      }
      editBaseItem(editFormValue.value).then((res) => {
        loadData()
      })
      editVisible.value = false
    }
  })
}

// 保存备注
function handleSureRemark() {
  const data = {
    id: currentRemarkRow.value.id,
    remark: remarkValue.value,
  }
  remarkBaseItem(data).then((res) => {
    window.$message.success('操作成功')
    loadData()
  })
  remarkVisible.value = false
}

// 将时间转为字符串格式
function transformDateString(date: Date): string {
  const year = date.getFullYear()
  const month = `0${date.getMonth() + 1}`.slice(-2) // 月份需要加1，且保证两位数显示
  const day = `0${date.getDate()}`.slice(-2) // 保证日期显示两位数
  const str = `${year}-${month}-${day} 00:00:00`
  return str
}

function handleEditMonth(value: any) {
  if (value === null) {
    editFormValue.value.returnTime = null
    return
  }
  const date = new Date(value)
  const year = date.getFullYear()
  const month = `0${date.getMonth() + 1}`.slice(-2) // 月份需要加1，且保证两位数显示
  const day = `0${date.getDate()}`.slice(-2) // 保证日期显示两位数
  editFormValue.value.returnTime = `${year}-${month}-${day} 00:00:00`
}

// 导入失败原因
const importReason = ref<string[]>([])

// 导入确认
function handleImportConfirm() {
  importFormRef.value?.validate((errors: any) => {
    if (!errors) {
      importFileFormRef.value?.validate((errors2: any) => {
        if (!errors2) {
          const file = importFileFormValue.value.file
          let monthDate: string | Date = importFormValue.value.month
          if (typeof monthDate !== 'object') {
            monthDate = new Date(monthDate)
          }
          const year = monthDate.getFullYear()
          const month = `0${monthDate.getMonth() + 1}`.slice(-2) // 月份需要加1，且保证两位数显示
          const day = `0${monthDate.getDate()}`.slice(-2) // 保证日期显示两位数
          monthDate = `${year}-${month}-${day} 00:00:00`
          importRecordList(file, monthDate).then((res) => {
            if (Array.isArray(res)) {
              if (res.length === 0) {
                // 导入成功
                window.$message.success('导入成功')
                loadData()
              } else {
                // 导入失败
                importFailDetailVisible.value = true
                importReason.value = res.slice(0)
                loadData()
              }
            }
          })
          importFileFormValue.value.file = null
          importFileFormRef.value?.restoreValidation()
          importVisible.value = false
        }
      })
    }
  })
}

// 导入取消
function handleImportCancel() {
  importFileFormValue.value.file = null
  importFileFormRef.value?.restoreValidation()
  importVisible.value = false
}

// 审核通过
function handleExamPass() {
  examBaseItem(REVIEWSTATUS.PASS, examData.value).then((res) => {
    window.$message.success('操作成功')
    loadData()
  })
  examVisible.value = false
}

// 审核未通过
function handleExamNotPass() {
  examBaseItem(REVIEWSTATUS.NOT, examData.value).then((res) => {
    window.$message.success('操作成功')
    loadData()
  })
  examVisible.value = false
}

// 转化data为树状结构
function transformTreeData(data: any) {
  if (Array.isArray(data)) {
    data.forEach((item) => {
      item.key = item.id
      item.label = item.name
      item.value = item.id

      if (item.children) {
        item.children.forEach((child: any) => {
          transformTreeData(child)
        })
      }
    })
  } else {
    data.key = data.id
    data.label = data.name
    data.value = data.id

    if (data.children) {
      data.children.forEach((child: any) => {
        transformTreeData(child)
      })
    }
  }
  return data
}

// 下载数据
function handleExportExcel() {
  loading.value = true
  exportBaseList({ ...filterReactive.value })
    .then((res: any) => {
      downloadArrayBuffer(
        res,
        '党费基数导出.xlsx',
        'application/octet-stream; charset=UTF-8',
      )
    })
    .finally(() => {
      loading.value = false
    })
}

/** 导入 */
const handleClickImportExcel = (options: {
  file: any
  fileList: any
  event?: Event
}) => {
  const formData = new FormData()
  formData.append('file', options.file.file)
  importFileFormValue.value.file = formData
}

// 下载导出模板
function handleTemplate() {
  downBaseTemplate().then((res: any) => {
    downloadArrayBuffer(
      res,
      '基数模板.xlsx',
      'application/octet-stream; charset=UTF-8',
    )
  })
}

watch(filterReactive.value, () => {
  loadData()
})

onMounted(() => {
  loadData()
  getOrganizeList()
})
</script>

<style lang="scss" scoped>
.operate-btn {
  width: 85px;
  height: 28px;
  background: #ffffff;
  border-radius: 3px;
  border: 1px solid #d8d9da;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
}

.import-btn {
  background: #cb0000;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
}

.import-icon {
  transform: rotate(180deg);
}

:deep(.copy-form .n-date-picker .n-input) {
  width: 166px !important;
  height: 28px;
}

.filter-operate {
  @apply flex gap-x-1;
  width: 720px;
}

:deep(.n-input) {
  height: 28px;
}

:deep(.dialog-header) {
  background: #ffffff;
}

.import-card {
  width: 795px;
  height: 471px;
  background: #ffffff;
  border-radius: 5px;
  padding-left: 24px;

  .title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    line-height: 20px;
    margin: 15px 0 0 10px;
  }

  .import-tips {
    width: 747px;
    height: 36px;
    background: #fffae1;
    border-radius: 3px;
    border: 1px solid #f5dd8c;
    font-size: 12px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #7c5125;
    display: flex;
    align-items: center;
    margin-top: 34px;
  }

  .import-detail {
    width: 747px;
    height: 260px;
    background: #f5f6f8;
    border: 1px solid #dcdfe6;
    margin-top: 16px;
    padding: 29px 0 0 23px;

    .step-one,
    .step-two {
      font-size: 12px;
      font-weight: 500;
      color: #333333;
      line-height: 17px;
      margin-left: 30px;
    }

    .step-num {
      font-size: 14px;
      font-family: Arial, Arial;
      font-weight: 900;
      color: #ee0000;
      margin-right: 20px;
    }

    .step-line {
      width: 0px;
      height: 38px;
      border-right: 1px solid #d0d5d8;
      margin-top: 10px;
      margin-bottom: 10px;
      margin-left: 35px;
    }
  }

  .import-label {
    width: 12px;
    height: 12px;
    background: #fdae35;
    border-radius: 50%;
    color: #fff;
    display: flex;
    align-items: center;
    font-style: oblique;
  }
}

.import-close-icon {
  position: absolute;
  right: 16px;
  top: 16px;
  cursor: pointer;
}

.import-btn-contain {
  position: absolute;
  right: 26px;
  bottom: 30px;

  .submit {
    width: 80px;
    height: 30px;
    background: #cb0000;
    border-radius: 3px;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #ffffff;
  }

  .cancel {
    width: 80px;
    height: 30px;
    background: #ffffff;
    border-radius: 3px;
    border: 1px solid #d8d9da;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #333333;
    margin-left: 12px;
  }
}

.exam-btn-contain {
  .cancel {
    margin-right: 10px;
  }

  .submit {
    margin-right: 10px;
  }
}

.edit-btn-contain {
  bottom: 20px !important;
}

:deep(.import-detail .n-date-picker .n-input) {
  width: 130px;
  height: 32px;
}

.import-base {
  height: 270px !important;
}

:deep(.import-base .n-date-picker .n-input) {
  width: 166px !important;
  height: 32px;
}

// :deep(.import-base .n-date-picker) {
//   width: 250px !important;
// }

// :deep(.import-base .n-date-panel-month-calendar) {
//   width: 250px !important;
// }

// :deep(.import-base .n-date-panel-actions) {
//   width: 250px !important;
// }

:deep(.import-base .n-date-panel .n-date-panel--month .n-date-panel--shadow) {
  width: 250px !important;
}

:deep(.import-detail .n-input) {
  width: 130px;
  height: 32px;
}

:deep(.import-detail
    .n-base-selection
    .n-base-selection__border, .n-base-selection
    .n-base-selection__state-border) {
  width: 210px;
  height: 32px !important;
  box-sizing: border-box !important;
}

:deep(.import-detail .n-base-selection .n-base-selection-label) {
  width: 210px;
  height: 32px !important;
  box-sizing: border-box !important;
}

:deep(.import-file-form .n-input) {
  width: 250px !important;
}

.import-fail-card {
  width: 477px;
  height: 262px;
  background: #ffffff;
  border-radius: 4px;

  .tip-icon {
    width: 23px;
    height: 23px;
    border-radius: 50%;
    border: 2px solid #faad14;
    color: #faad14;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 16px;
    margin: 34px 0 0 34px;
    transform: rotate(180deg);
  }

  .fail-title {
    font-size: 16px;
    font-weight: 400;
    color: #000000;
    margin-left: 80px;
    margin-top: -23px;
  }

  .detail-reason {
    width: 409px;
    height: 149px;
    background: #f5f6f8;
    border: 1px solid #dcdfe6;
    margin-left: 34px;
    margin-top: 19px;
    padding: 14px 0 14px 22px;
    overflow-y: auto;

    .reason-item {
      font-size: 12px;
      font-weight: 400;
      color: #666666;
      line-height: 26px;
    }
  }
}

.edit-card {
  width: 500px !important;
  height: 500px !important;
}

:deep(.edit-date .n-date-picker) {
  width: 100% !important;
}

:deep(.remark-form .n-input) {
  height: 100px !important;
}

:deep(.n-upload-trigger + .n-upload-file-list) {
  margin-top: -2px;
  margin-left: -10px;
}
</style>
