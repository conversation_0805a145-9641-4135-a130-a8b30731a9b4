import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import type { VNodeChild } from 'vue'
import type { ICarousel } from '@/services/run/carousel/types'

export function getTableColumns(
  optionColumnRenderer: (row: ICarousel) => VNodeChild,
  IndexRenderer: (row: ICarousel, index: number) => VNodeChild,
  phoneRenderer: (row: ICarousel) => VNodeChild,
): TableColumns<ICarousel> {
  return [
    {
      title: '序号',
      key: 'pictureUrl',
      render: IndexRenderer,
    },
    {
      title: '姓名',
      key: 'createBy',
    },
    {
      title: '手机号',
      key: 'phone',
      render: phoneRenderer,
    },
    {
      title: '组织名称',
      key: 'orgName',
    },
    {
      title: '标题',
      key: 'title',
      width: 280,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '内容',
      key: 'content',
      width: 280,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '发布时间',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      align: 'left',
      render: optionColumnRenderer,
    },
  ]
}
