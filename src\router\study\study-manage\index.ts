import type { RouteRecordRaw } from 'vue-router'

const studyManageRoute: RouteRecordRaw = {
  path: 'study-manage',
  name: 'studyManage',
  meta: { title: '学习管理', icon: 'course', category: 'study', order: 1 },
  component: () => import('@/views/main/study/study-manage/Index.vue'),
  redirect: { name: 'studyManageList' },
  children: [
    {
      path: 'study-manage-list',
      name: 'studyManageList',
      component: () =>
        import('@/views/main/study/study-manage/list/StudyManageList.vue'),
    },
    {
      path: 'study-manage-add',
      name: 'studyManageAdd',
      component: () =>
        import('@/views/main/study/study-manage/edit/StudyManageAdd.vue'),
    },
  ],
}
export default studyManageRoute
