<script setup lang="ts">
import { NForm } from 'naive-ui'
import { getFormRules } from './config'

import type {
  AddHandleItem,
  AddRewardItem,
  BranchBasicOrganizationConditionUserType,
} from '@/services/recordBookMaintenance/types'
import {
  addHandleStatus,
  addRewardStatus,
  editHandleStatus,
  editRewardStatus,
  getHandleStatusDetail,
  getRewardStatusDetail,
  getUserByDepartmentId,
} from '@/services/recordBookMaintenance'

type AddRewardOrHandleItem = AddRewardItem | AddHandleItem
interface Props {
  type?: string
  id?: string
  belong?: 'reward' | 'handle'
  deptId: string
  year: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
  belong: 'reward',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const userList = ref<BranchBasicOrganizationConditionUserType[]>([])
function fetchUserList() {
  getUserByDepartmentId({ deptId: props.deptId }).then((res) => {
    userList.value = res || []
  })
}
const userOptions = computed(() => {
  return userList.value.map((item) => {
    return {
      label: item.trueName,
      value: item.userId,
    }
  })
})

interface FormModel {
  id?: string
  deptId: string
  year: string
  userId?: string | null
  userName: string
  date: string | null
  type: string
  remark: string
}

const formDataReactive = reactive<FormModel>({
  id: '',
  deptId: props.deptId,
  year: props.year,
  userId: null,
  userName: '',
  date: null,
  type: '',
  remark: '',
})

const formRef = ref<InstanceType<typeof NForm>>()
const formRules = getFormRules(props.belong as 'reward' | 'handle')

onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.id) {
    getDetailApi().then((res) => {
      formDataReactive.id = res.id
      formDataReactive.userId = res.userId!
      formDataReactive.userName = res.userName!

      if (props.belong === 'reward') {
        formDataReactive.date = res.awardDate
        formDataReactive.type = res.awardName
        formDataReactive.remark = res.awardStory
      }
      else {
        formDataReactive.date = res.decisionDate
        formDataReactive.type = res.disciplineType
        formDataReactive.remark = res.disciplineReason
      }
    })
  }
})

// ✅ 动态获取详情接口
function getDetailApi() {
  if (props.belong === 'reward') {
    return getRewardStatusDetail(props.id!)
  }
  else {
    return getHandleStatusDetail(props.id!)
  }
}

// ✅ 动态选择新增接口
function postApi(data: AddRewardOrHandleItem) {
  if (props.belong === 'reward') {
    return addRewardStatus(data as AddRewardItem)
  }
  else {
    return addHandleStatus(data as AddHandleItem)
  }
}

// ✅ 动态选择编辑接口
function putApi(data: AddRewardOrHandleItem) {
  if (props.belong === 'reward') {
    return editRewardStatus(data as AddRewardItem)
  }
  else {
    return editHandleStatus(data as AddHandleItem)
  }
}

function isDateDisabled(ts: number): boolean {
  const date = new Date(ts)
  const selectedYear = Number(props.year)

  // 限制只能选指定年份
  if (date.getFullYear() !== selectedYear) {
    return true
  }

  return false
}

// ✅ 验证表单并调用正确的新增或编辑接口
const validateAndSave = () => {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      const isEdit = !!formDataReactive.id
      const userName = userList.value.find(
        item => item.userId === formDataReactive.userId,
      )?.trueName
      const baseData = {
        deptId: props.deptId,
        year: props.year,
        userId: formDataReactive.userId,
        userName,
      }

      if (isEdit) {
        // 编辑时加上 id
        Object.assign(baseData, { id: formDataReactive.id })
      }

      const payload
        = props.belong === 'reward'
          ? {
            ...baseData,
            awardDate: formDataReactive.date,
            awardName: formDataReactive.type,
            awardStory: formDataReactive.remark,
          }
          : {
            ...baseData,
            decisionDate: formDataReactive.date,
            disciplineType: formDataReactive.type,
            disciplineReason: formDataReactive.remark,
          }

      const apiCall = isEdit ? putApi : postApi
      apiCall(payload as AddRewardOrHandleItem).then((res) => {
        if (res) {
          window.$message.success('保存成功')
          emits('saved')
        }
      })
    }
  })
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}
defineExpose({
  validateAndSave,
  resetForm,
})

onMounted(() => {
  fetchUserList()
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    :label-width="props.belong === 'reward' ? 100 : 130"
    label-align="right"
    label-placement="left"
    :disabled="props.type === 'view'"
    :model="formDataReactive"
    :rules="formRules"
  >
    <n-form-item label="姓名" path="userId">
      <n-select
        v-model:value="formDataReactive.userId"
        :options="userOptions"
        clearable
        placeholder="请选择姓名"
      />
    </n-form-item>
    <n-form-item label="时间：" path="date">
      <n-date-picker
        v-model:formatted-value="formDataReactive.date"
        clearable
        placeholder="请选择时间"
        style="width: 100%"
        type="date"
        value-format="yyyy-MM-dd"
        :is-date-disabled="isDateDisabled"
      />
    </n-form-item>
    <n-form-item
      :label="props.belong === 'reward' ? '奖励名称：' : '处分类别：'"
      path="type"
    >
      <n-input
        v-model:value="formDataReactive.type"
        :placeholder="
          props.belong === 'reward' ? '请输入奖励名称' : '请输入处分类别'
        "
        maxlength="30"
        show-count
        clearable
      />
    </n-form-item>

    <n-form-item
      :label="props.belong === 'reward' ? '简要事迹：' : '处分（处置原因）：'"
      path="remark"
    >
      <n-input
        v-model:value="formDataReactive.remark"
        maxlength="500"
        :placeholder="
          props.belong === 'reward'
            ? '请输入简要事迹'
            : '请输入处分（处置原因）'
        "
        show-count
        type="textarea"
        :autosize="{ minRows: 12, maxRows: 20 }"
      />
    </n-form-item>
  </n-form>
</template>
<style lang="scss" scoped></style>
