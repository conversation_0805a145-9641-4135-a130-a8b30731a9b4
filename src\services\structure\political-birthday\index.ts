import { commonReq } from '@/services/request'

/** 获取政治生日列表 */
export function getBirthdayList(params: any) {
  return commonReq.get({
    url: '/party-affairs/backend/v1/political_birthday_blessing/page',
    params,
  })
}

/** 通过id查询政治生日祝福语
 */
export function getBirthdayById(id: string) {
  return commonReq.get({
    url: `/party-affairs/backend/v1/political_birthday_blessing/${id}`,
  })
}

/** 新增政治生日祝福语
 */
export function addBirthdayById(data: any) {
  return commonReq.post({
    url: '/party-affairs/backend/v1/political_birthday_blessing',
    data,
  })
}

/** 修改政治生日祝福语
 */
export function updateBirthday(data: any) {
  return commonReq.put({
    url: '/party-affairs/backend/v1/political_birthday_blessing',
    data,
  })
}

/** 删除政治生日祝福语
 */
export function delBirthday(ids: any) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/political_birthday_blessing',
    data: [ids],
  })
}
