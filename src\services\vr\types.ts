export interface VRListItem{
  /** 封面图url */
  coverUrl?: null | string
  /** 创建时间 */
  createTime?: null | string
  /** 主键ID */
  id: string
  /** 是否隐藏 (0-否，1-是) */
  isHidden?: null | number
  /** 是否跳转到外部网页 (0-否，1-是) */
  isOutside?: null | number
  /** 链接 */
  linkUrl?: null | string
  /** 说明 */
  remark?: null | string
  /** 标题 */
  title?: null | string
  [property: string]: any
}

export interface AddOrEditVRListItem{
  /** 封面图url */
  coverUrl?: null | string
  /** 主键ID */
  id?: string
  /** 是否隐藏 (0-否，1-是) */
  isHidden?: null | number
  /** 是否跳转到外部网页 (0-否，1-是) */
  isOutside?: null | number
  /** 链接 */
  linkUrl?: null | string
  /** 说明 */
  remark?: null | string
  /** 资讯标题 */
  title?: null | string
  [property: string]: any
}
