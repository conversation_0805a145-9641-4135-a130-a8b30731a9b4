<script setup lang="ts">
import { NForm } from 'naive-ui'
import type { SelectOption, UploadFileInfo } from 'naive-ui'
import { PersonAddAltOutlined } from '@vicons/material'
import { honorDetailFormRules } from './ruleConfig'
import type { HonorWallDetailItem } from '@/services/honor-wall/types'
import { addHonorWall, putHonorWall } from '@/services/honor-wall'
import type { uploadFileItem } from '@/services/types'
import { uploadImg } from '@/services'
import { useCurrentOrganizationListOptions } from '@/hooks/use-select-options'
// import { getUnitTreeNode } from '@/utils/utils'
import { HONORTYPE } from '@/constant'
import { getUserByDepartmentId } from '@/services/organization'
import { HONOR_LEVEL } from '@/store/dict'

const { organizationCurrentListTree } = useCurrentOrganizationListOptions() // 获取组织列表
interface Props {
  type?: string
  item?: HonorWallDetailItem
  categoryId: string
  honorType: string
}
const props = withDefaults(defineProps<Props>(), {
  type: 'add',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = reactive<{ value: HonorWallDetailItem }>({
  value: {
    categoryId: props.categoryId,
    honorType: props.honorType,
    fileList: [],
    title: '',
    sort: null,
    level: null,
  },
})

const cacheUploadFileList = ref<uploadFileItem[]>([])

// 选中组织的用户列表
const departMentUser = ref([])

const formRef = ref<InstanceType<typeof NForm>>()

onBeforeMount(() => {
  if ((props.type === 'modify' || props.type === 'view') && props.item) {
    nextTick(() => {
      formDataReactive.value.categoryId = props!.item!.categoryId
      formDataReactive.value.title = props!.item!.title
      formDataReactive.value.deptId = props!.item!.deptId
      formDataReactive.value.deptName = props!.item!.deptName
      formDataReactive.value.fileList = props!.item!.fileList || []
      formDataReactive.value.grantTime = props!.item!.grantTime
      formDataReactive.value.honorType = props!.item!.honorType
      formDataReactive.value.id = props!.item!.id
      formDataReactive.value.multiPictureIds = props!.item!.multiPictureIds
      formDataReactive.value.pictureUrl = props!.item!.pictureUrl
      formDataReactive.value.serviceId = props!.item!.serviceId
      formDataReactive.value.serviceName = props!.item!.serviceName
      formDataReactive.value.updateTime = props!.item!.updateTime
      formDataReactive.value.sort = props!.item!.sort
      formDataReactive.value.level = props!.item!.level

      // 处理上传的荣誉内容图片回显
      if (
        formDataReactive.value.fileList
        && formDataReactive.value.fileList.length
      ) {
        formDataReactive.value.fileList.forEach((item) => {
          item.url = import.meta.env.VITE_API_BASE + item.fileName
          item.status = 'finished'
        })

        // 同步cacheUploadFileList.value
        cacheUploadFileList.value = formDataReactive.value.fileList.map(
          (file) => {
            return {
              fileId: file.id,
              bucketName: '',
              url: file.url,
              fileName: '',
              status: file.status,
            }
          },
        )
      }
    })

    if (props.honorType === HONORTYPE.PERSONHONOR && props.item.deptId) {
      choosePeople(props.item.deptId)
    }
  }
})

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (formDataReactive.value.id) {
        putHonorWall(formDataReactive.value).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
      else {
        addHonorWall(formDataReactive.value).then((res) => {
          if (res) {
            window.$message.success('保存成功')
            emits('saved')
          }
        })
      }
    }
  })
}

function getFormRules() {
  const rules = JSON.parse(JSON.stringify(honorDetailFormRules))
  if (props.honorType === HONORTYPE.PERSONHONOR) {
    rules.serviceId.message = '请选择党员'
    rules.deptId = {
      required: true,
      message: '请选择组织',
      trigger: 'change',
      type: 'string',
    }
  }
  return rules
}

/**
 * 上传图片
 * @param {any} file:File
 */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (
      !formDataReactive.value.pictureUrl
      || formDataReactive.value.pictureUrl === ''
    ) {
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formDataReactive.value.pictureUrl = data.url
      }
    }
  }
  catch (error) {}
}
/**
 * 删除图片
 */
function handleCoverDelete() {
  formDataReactive.value.pictureUrl = ''
}

// 上传荣誉内容图
function handleContentCoverDone(options: {
  file: UploadFileInfo & { fileId: string }
  fileList: Array<UploadFileInfo>
  event?: Event
}) {
  const { file } = options
  formDataReactive.value.fileList = options.fileList
  if (file.status !== 'removed') {
    const imgFileData = new FormData()
    imgFileData.append('file', file.file as File)
    uploadImg(imgFileData).then((res) => {
      if (res) {
        cacheUploadFileList.value.push({
          ...res,
          status: 'finished',
          id: file.id,
        })
      }
    })
  }
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

function updateCascaderData(v: any, option: SelectOption) {
  formDataReactive.value.serviceId = v
  // const selectedNode = getUnitTreeNode(organizationCurrentListTree.value, v)
  formDataReactive.value.serviceName = option.name as string
}

function choosePeople(deptId: string | undefined) {
  if (!deptId) {
    window.$message.error('请先选择组织')
    departMentUser.value = []
  }
  else {
    // 获取部门下的用户列表
    getUserByDepartmentId(deptId).then((res) => {
      departMentUser.value = res.map((item: any) => {
        item.value = item.userId
        item.label = item.trueName
        return item
      })
    })
  }
}

function handleUpdateValue(value: string, option: SelectOption) {
  formDataReactive.value.serviceName = option.trueName as string
}

function handleRemove(options: {
  file: UploadFileInfo
  fileList: Array<UploadFileInfo>
  index: number
}) {
  if (cacheUploadFileList.value.length) {
    cacheUploadFileList.value = cacheUploadFileList.value.filter(
      item => (item.id || item.fileId) !== options.file.id,
    )
  }
}

watch(
  () => cacheUploadFileList.value,
  (newVal) => {
    if (cacheUploadFileList.value.length) {
      formDataReactive.value.multiPictureIds = newVal
        .map(item => item.fileId)
        .join(',')
    }
    else {
      formDataReactive.value.multiPictureIds = ''
    }
  },
  { deep: true, immediate: true },
)

defineExpose({
  validateAndSave,
  resetForm,
})
</script>
<template>
  <n-form
    ref="formRef"
    size="small"
    require-mark-placement="left"
    label-width="100"
    label-align="right"
    label-placement="left"
    :disabled="props.type === 'view'"
    :model="formDataReactive.value"
    :rules="getFormRules()"
  >
    <n-form-item label="荣誉名称：" path="title">
      <n-input
        v-model:value="formDataReactive.value.title"
        placeholder="请输入荣誉名称"
        maxlength="60"
        show-count
        clearable
      />
    </n-form-item>
    <n-form-item
      v-if="props.honorType === HONORTYPE.ORGHONOR"
      label="组织名称："
      path="serviceId"
    >
      <!-- <n-cascader
        v-model:value="formDataReactive.serviceId"
        placeholder="请选择组织"
        :options="(organizationCurrentListTree as any)"
        value-field="id"
        label-field="name"
        children-field="children"
        check-strategy="child"
        :show-path="false"
        clearable
        filterable
        style="width: 260px"
        @update:value="updateCascaderData"
      /> -->
      <n-tree-select
        v-model:value="formDataReactive.value.serviceId"
        :options="(organizationCurrentListTree as any)"
        value-field="id"
        label-field="name"
        key-field="id"
        children-field="children"
        check-strategy="all"
        placeholder="请选择组织"
        :show-path="false"
        clearable
        filterable
        style="width: 580px"
        @update:value="updateCascaderData"
      />
    </n-form-item>

    <n-form-item
      v-if="props.honorType === HONORTYPE.PERSONHONOR"
      label="组织名称："
      path="deptId"
    >
      <!-- <n-cascader
        v-model:value="formDataReactive.deptId"
        placeholder="请选择组织"
        :options="(organizationCurrentListTree as any)"
        value-field="id"
        label-field="name"
        children-field="children"
        check-strategy="child"
        :show-path="false"
        clearable
        filterable
        style="width: 260px"
      /> -->
      <n-tree-select
        v-model:value="formDataReactive.value.deptId"
        :options="(organizationCurrentListTree as any)"
        value-field="id"
        label-field="name"
        key-field="id"
        children-field="children"
        check-strategy="all"
        placeholder="请选择组织"
        :show-path="false"
        clearable
        filterable
        style="width: 580px"
      />
    </n-form-item>

    <n-form-item
      v-if="props.honorType === HONORTYPE.PERSONHONOR"
      path="serviceId"
      label="党员名称："
      @click="choosePeople(formDataReactive.value.deptId)"
    >
      <n-select
        v-model:value="formDataReactive.value.serviceId"
        :options="departMentUser"
        placeholder="请选择组织人员"
        @update:value="handleUpdateValue"
      >
        <template #arrow>
          <PersonAddAltOutlined />
        </template>
      </n-select>
    </n-form-item>
    <n-form-item span="24" label="排序：" path="sort">
      <n-input-number
        v-model:value="formDataReactive.value.sort"
        :min="1"
        placeholder="请输入排序权重"
        clearable
      />
    </n-form-item>

    <n-form-item :span="12" label="荣誉级别：" path="level">
      <n-select
        v-model:value="formDataReactive.value.level"
        :options="HONOR_LEVEL"
        placeholder="请选择荣誉级别"
        clearable
      />
    </n-form-item>

    <n-form-item label="荣誉封面图：" path="pictureUrl">
      <ImgUploader
        v-model:oldImgUrl="formDataReactive.value.pictureUrl"
        :width="640"
        :height="445"
        :need-cropper="false"
        :is-readonly="props.type === 'view'"
        @done="handleCoverDone"
        @delete="handleCoverDelete"
      />
    </n-form-item>

    <n-form-item label="荣誉内容图：" path="multiPictureIds">
      <div class="w-full">
        <n-upload
          v-model:file-list="formDataReactive.value.fileList"
          list-type="image-card"
          accept=".jpg,.jpeg,.png"
          @change="handleContentCoverDone"
          @remove="handleRemove"
        />
        <p class="text-[12px] font-[400] text-[#999] mt-[20px]">
          推荐尺寸 649×445，支持 .jpg，.jpeg,.png
        </p>
      </div>
    </n-form-item>

    <n-form-item label="授予时间：" path="grantTime">
      <n-date-picker
        v-model:formatted-value="formDataReactive.value.grantTime"
        value-format="yyyy-MM"
        type="month"
        style="width: 580px"
        clearable
        placeholder="请选择授予时间"
        @clear="() => (formDataReactive.value.grantTime = null)"
      />
    </n-form-item>
  </n-form>
</template>

<style lang="scss" scoped></style>
