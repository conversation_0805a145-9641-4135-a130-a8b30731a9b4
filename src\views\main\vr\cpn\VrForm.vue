<script lang="ts" setup>
import { NForm } from 'naive-ui'
import { HelpOutlineRound } from '@vicons/material'
import { formRules } from './config'
import { getVRDetail, postVR, putVR } from '@/services/vr'
import type { AddOrEditVRListItem } from '@/services/vr/types'
import { uploadImg } from '@/services'
import type { uploadFileItem } from '@/services/types'

const emits = defineEmits<{
  (e: 'saved'): void
}>()

interface Props {
  type?: string
  id?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  id: '0',
})

const oriFormDataReactive = reactive<AddOrEditVRListItem>({
  title: '',
  isOutside: 0,
  isHidden: 0,
  remark: '', // 通知内容
  coverUrl: '',
  linkUrl: '',
  id: '',
})
const formDataReactive = reactive<AddOrEditVRListItem>({
  title: '',
  isOutside: 0,
  isHidden: 0,
  remark: '',
  coverUrl: '',
  linkUrl: '',
  id: '',
})

const formRef = ref<InstanceType<typeof NForm>>()

/**
 * 上传图片
 * @param {any} file:File
 */
async function handleCoverDone(file: File) {
  const imgFileData = new FormData()
  imgFileData.append('file', file)
  try {
    if (formDataReactive.coverUrl === '') {
      const data: uploadFileItem = await uploadImg(imgFileData)
      if (data) {
        formDataReactive.coverUrl = data.url
      }
    }
  }
  catch (error) {}
}

/**
 * 删除图片
 */
function handleCoverDelete() {
  formDataReactive.coverUrl = ''
}

// 重置表单
function resetForm() {
  formRef.value?.restoreValidation()
}

// 关闭时判断是否存在改变了的数据
function getChangedFlag() {
  return deepEqual(oriFormDataReactive, formDataReactive)
}

function deepEqual(obj1: any, obj2: any) {
  if (Object.keys(obj1).length !== Object.keys(obj2).length) {
    return false
  }

  for (const key in obj1) {
    if (!Object.prototype.hasOwnProperty.call(obj2, key)) {
      return false
    }
    if (typeof obj1[key] === 'object') {
      if (JSON.stringify(obj1[key]) !== JSON.stringify(obj2[key])) {
        return false
      }
    }
    else {
      if (obj1[key] !== obj2[key]) {
        return false
      }
    }
  }

  return true
}

// 验证表单,调用接口
function validateAndSave() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (props.id === '0') {
        postVR(formDataReactive).then((res) => {
          window.$message.success('保存成功')
          emits('saved')
        })
      }
      else {
        putVR(formDataReactive).then((res) => {
          window.$message.success('保存成功')
          emits('saved')
        })
      }
    }
  })
}

async function getDetail() {
  if (props.id !== '0') {
    const data = await getVRDetail(props.id)
    for (const key in data) {
      if (formDataReactive[key] !== undefined) {
        formDataReactive[key] = data[key]
      }
    }
  }
}

onMounted(() => {
  getDetail()
})
defineExpose({
  validateAndSave,
  resetForm,
  getChangedFlag,
})
</script>
<template>
  <n-form
    ref="formRef"
    :model="formDataReactive"
    :rules="formRules"
    label-align="right"
    label-placement="left"
    label-width="140"
    require-mark-placement="left"
    size="small"
  >
    <n-grid>
      <n-form-item-gi label="标题：" path="title" span="24">
        <n-input
          v-model:value="formDataReactive.title"
          :disabled="type == 'view' ? true : false"
          clearable
          maxlength="50"
          placeholder="请输入线上展馆标题"
          show-count
        />
      </n-form-item-gi>
      <n-form-item-gi label="链接：" path="linkUrl" span="24">
        <n-input
          v-model:value="formDataReactive.linkUrl"
          :disabled="type == 'view' ? true : false"
          clearable
          placeholder="请输入线上展馆链接"
        />
      </n-form-item-gi>
      <n-form-item-gi label="图片：" path="coverUrl" span="24">
        <ImgUploader
          v-model:oldImgUrl="formDataReactive.coverUrl"
          :height="150"
          :is-readonly="type == 'view' ? true : false"
          :need-cropper="false"
          :width="220"
          @delete="handleCoverDelete"
          @done="handleCoverDone"
        />
      </n-form-item-gi>
      <n-form-item-gi path="isOutside" span="24">
        <template #label>
          <div class="flex items-center gap-[4px]">
            <span>跳转到外部链接</span>
            <n-tooltip placement="bottom" trigger="hover">
              <template #trigger>
                <n-icon size="16">
                  <HelpOutlineRound />
                </n-icon>
              </template>
              <div class="flex flex-col">
                <span>推荐否。</span>
                <span>选择否，则本次点击请求跳转在软件内完成，用户点击后退可返回到列表页。</span>
                <span>选择是，跳转到外部链接，若需要切回软件，则需要使用系统的软件列表进行切换，操作复杂度增加。</span>
              </div>
            </n-tooltip>
          </div>
        </template>
        <n-switch
          v-model:value="formDataReactive.isOutside"
          :checked-value="1"
          :disabled="type == 'view' ? true : false"
          :unchecked-value="0"
        />
      </n-form-item-gi>

      <n-form-item-gi label="是否隐藏：" path="isHidden" span="24">
        <n-switch
          v-model:value="formDataReactive.isHidden"
          :checked-value="1"
          :disabled="type == 'view' ? true : false"
          :unchecked-value="0"
        />
      </n-form-item-gi>

      <n-form-item-gi label="说明：" span="24">
        <n-input
          v-model:value="formDataReactive.remark"
          :autosize="{ minRows: 5 }"
          :disabled="type == 'view' ? true : false"
          :round="true"
          clearable
          maxlength="1000"
          placeholder="请输入线上展馆说明，不超过1000字"
          show-count
          type="textarea"
        />
      </n-form-item-gi>
    </n-grid>
  </n-form>
</template>
<style lang="scss" scoped></style>
