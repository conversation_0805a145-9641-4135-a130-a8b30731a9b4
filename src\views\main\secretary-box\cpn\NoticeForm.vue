<!-- 查看详情 -->

<script setup lang="ts">
import { getSecretaryDetail } from '@/services/secretary-box/index'

interface Props {
  id?: string
}
const props = withDefaults(defineProps<Props>(), {
  id: '0',
})

const emits = defineEmits<{
  (e: 'saved'): void
}>()

const formDataReactive = ref<any>({})

onBeforeMount(() => {
  if (props.id) {
    getSecretaryDetail(props.id).then((res) => {
      formDataReactive.value = res
    })
  }
})

// 验证表单,调用接口
function validateAndSave() {}

defineExpose({
  validateAndSave,
})
</script>

<template>
  <div class="container">
    <div class="top">
      <div class="row">
        <div class="label">
          所属组织：
        </div>
        <div class="content">
          {{ formDataReactive?.deptName }}
        </div>
      </div>
      <div class="row">
        <div class="label">
          姓名：
        </div>
        <div class="content">
          {{ formDataReactive?.suggesterName }}
        </div>
      </div>
      <div class="row">
        <div class="label">
          提问时间：
        </div>
        <div class="content">
          {{ formDataReactive?.suggestTime }}
        </div>
      </div>
      <div class="row center-content">
        <div class="label">
          提问内容：
        </div>
        <n-tooltip trigger="hover">
          <template #trigger>
            <div class="content mt-[10px]">
              {{ formDataReactive?.suggest }}
            </div>
          </template>
          {{ formDataReactive?.suggest }}
        </n-tooltip>
      </div>
    </div>

    <div class="bottom">
      <div class="title">
        回复
      </div>
      <div class="row mt-[20px]">
        <div class="label">
          书记：
        </div>
        <div class="content">
          {{ formDataReactive?.replierName }}
        </div>
      </div>
      <div class="row">
        <div class="label">
          回复时间：
        </div>
        <div class="content">
          {{ formDataReactive?.replyTime }}
        </div>
      </div>
      <div class="row center-content">
        <div class="label">
          回复内容：
        </div>
        <n-tooltip trigger="hover">
          <template #trigger>
            <div class="text-content-container mt-[10px]">
              <div class="text-content">
                {{ formDataReactive?.reply }}
              </div>
            </div>
          </template>
          {{ formDataReactive?.reply }}
        </n-tooltip>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  background: rgb(245, 246, 248);
  padding: 0 8px;
}

.top {
  width: 100%;
  background: #ffffff;
  border-radius: 5px;
  padding: 13px 29px 32px 22px;
  margin-top: 10px;
}

.row {
  display: flex;
  align-items: center;
}

.label {
  width: 100px;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 40px;
}

.content {
  width: 414px;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 26px;
  margin-left: 13px;
  overflow: hidden;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
  /** 对象作为伸缩盒子模型显示 **/
  display: -webkit-box;
  /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  -webkit-box-orient: vertical;
  /** 显示的行数 **/
  -webkit-line-clamp: 3;
}

.bottom {
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 5px;
  padding: 13px 29px 32px 22px;
  margin-top: 10px;
}

.title {
  font-weight: 500;
  font-size: 18px;
  color: #333333;
  line-height: 20px;
}

.text-content-container {
  width: 414px;
  height: 184px;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  background: #f5f6f8;
  border-radius: 2px;
  padding: 15px 23px 20px 23px;
}

.text-content {
  line-height: 26px;
  margin-left: 13px;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
}

.center-content {
  align-items: flex-start;
}
</style>
