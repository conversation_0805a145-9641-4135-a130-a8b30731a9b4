/** 获取学习分类列表 */
export interface CategoryItem {
  id: string
  name: string
  sort: string
  categoryFlag: string
}

/** 获取学习内容列表 */
export interface StudyContentItem {
  id: string
  title: string
  cover: Cover
  studyNum: string
  updateTime: string
  isTop: string
  publishStatus: string
}

/** 学习内容 - 添加、编辑、详情使用 */
export interface StudyContentDetail {
  id: string
  title: string
  content: string
  files: File[]
  cover: Cover
  categoryId: string
  isTop: string
  studyTimeConfig?: number
  type: string
}

/** 文件 */
export interface File {
  id: string
  url: string
  original: string
}
/** 封面图 */
export interface Cover {
  id: string
  url: string
  original: string
}
