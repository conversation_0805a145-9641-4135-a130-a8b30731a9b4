import type {
  ManageLeaderDetailTypes,
  OrganizationDetailItem,
  OrganizationItemTableItem,
  OrganizationUsersTypes,
} from './types'
import { commonReq } from '@/services/request'
import type { PaginationRes } from '@/services/types'

/** 获取组织架构列表 */
export function getOrganizationTableList() {
  return commonReq.get<PaginationRes<OrganizationItemTableItem>>({
    url: '/org-construction/organization/list',
  })
}

/** 获取当前用户管辖范围内的组织列表 */
export function getCurrentUserOrganizationList() {
  return commonReq.get<PaginationRes<OrganizationItemTableItem>>({
    url: '/org-construction/orgconstruction_change/cur_user_manage_depts',
  })
}

/** 在当前用户管辖范围内模糊查询组织列表 */
export function getCurrentUserVagueOrganizationList(params: {
  orgName: string
}) {
  return commonReq.get<PaginationRes<OrganizationItemTableItem>>({
    url: '/org-construction/orgconstruction_change/cur_user_manage_depts/orgName',
    params,
  })
}

/** 模糊查询获取组织架构列表 */
export function getVagueOrganizationTableList(params: { orgName: string }) {
  return commonReq.get<PaginationRes<OrganizationItemTableItem>>({
    url: '/org-construction/organization/list/orgName',
    params,
  })
}

/** 获取组织架构详情 */
export function getOrganizationDetail(id: string) {
  return commonReq.get<OrganizationDetailItem>({
    url: `/org-construction/organization/${id}`,
  })
}

/** 添加组织架构 */
export function postOrganizationItem(data: OrganizationDetailItem) {
  return commonReq.post({
    url: '/org-construction/orgconstruction_change',
    data,
  })
}

/** 编辑组织架构 */
export function putOrganizationItem(data: OrganizationDetailItem) {
  return commonReq.put({
    url: '/org-construction/orgconstruction_change',
    data,
  })
}

/** 删除组织架构 */
export function deleteOrganizationItem(id: string) {
  return commonReq.delete({
    url: `/org-construction/orgconstruction_change/${id}`,
  })
}

/** 升降级 */
export function upDownOrganizationItem(data: any) {
  return commonReq.post({
    url: '/org-construction/orgconstruction_change/relagation',
    data,
  })
}

/** 合并 */
export function mergeOrganizationItem(data: any) {
  return commonReq.post({
    url: '/org-construction/orgconstruction_change/merge',
    data,
  })
}

/** 拆分 */
export function splitOrganizationItem(data: any) {
  return commonReq.post({
    url: '/org-construction/orgconstruction_change/split',
    data,
  })
}

/** 根据部门id查询app用户列表 */
export function getUserByDepartmentId(deptId: any) {
  return commonReq.get({
    url: `/upms/portal-user/list_user_show_by_dept_id?deptId=${deptId}`,
  })
}

// 获取领导班子列表
export function getLeaderList(deptId: string) {
  return commonReq.get<ManageLeaderDetailTypes[]>({
    url: '/org-construction/orgconstruction_change/leadership_team_list',
    params: { deptId },
  })
}

// 获取部门id和支部内身份查询用户
export function getLeaderUserList(params: {
  deptId: string
  trueName: string
}) {
  return commonReq.get<OrganizationUsersTypes[]>({
    url: '/upms/portal-user/list_user_by_dept_id_and_party_identity',
    params,
  })
}

// 添加或编辑领导班子
export function addOrEditLeader(data: any) {
  return commonReq.post({
    url: '/org-construction/orgconstruction_change/handle_leadership_team',
    data,
  })
}

// 删除领导班子成员
export function deleteLeader(id: string) {
  return commonReq.delete({
    url: '/org-construction/orgconstruction_change/delete_leadership_team_user',
    params: { id },
  })
}

// 组织结构树-同级别组织-实现上移或下移
export function moveOrganizationItem(
  data: Array<{ id: string; weight: number }>,
) {
  return commonReq.post({
    url: '/org-construction/orgconstruction_change/swap-sort',
    data,
  })
}
