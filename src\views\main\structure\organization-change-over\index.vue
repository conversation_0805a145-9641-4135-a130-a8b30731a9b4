<script setup lang="ts">
import { NButton } from 'naive-ui'
import { PlusRound } from '@vicons/material'
import { getTableColumns, statusOptions, transferType } from './config'
import NoticeForm from './cpn/NoticeForm.vue'
import CheckForm from './cpn/CheckForm.vue'
import { useDrawerEditOrganize } from '@/hooks'
import {
  getOrganizationChangeOverTableList,
  putUpdateOrganizationChangeOver,
} from '@/services/structure/organization-change-over'
const userNameRef = ref('')
const transferTypeRef = ref<null | string>(null)
const phaseStatusRef = ref<null | string>(null)
const pageNum = ref(1)
const pageSize = ref(10)
const loading = ref(false)

const currentFormStatus = ref('add')

const total = ref(0)
const tableData = ref([])

/** 获取组织列表 */
const getTableList = () => {
  loading.value = true
  const params = {
    userName: userNameRef.value,
    transferType: transferTypeRef.value,
    phaseStatus: phaseStatusRef.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }
  getOrganizationChangeOverTableList(params)
    .then((res: any) => {
      total.value = res.total || 0
      tableData.value = res.records || []
    })
    .finally(() => {
      loading.value = false
    })
}

// 新增/查看党组织转接抽屉
const idEditRef = ref()
const idEditParentID = ref()
const idEditParentName = ref()
const idEditLevel = ref()
const addNoticeFormRef = ref()
const checkFormRef = ref()

/**
 * 保存操作
 */
const handelConfirmEdit = () => {
  if (['check'].includes(currentFormStatus.value)) {
    checkFormRef.value?.validateAndSave()
  }
  else {
    addNoticeFormRef.value?.validateAndSave()
  }
}

const {
  drawerTitle,
  showEditRef,
  editTypeRef,
  editTitle,
  handleClickConfirm,
  handleClickCancel,
} = useDrawerEditOrganize({
  name: '组织关系转接',
  confirmFn: handelConfirmEdit,
  isNotTips: true,
})

/**
 * @description 添加组织关系转接
 */
const addOrganizationChangeOver = () => {
  editTitle.value = '组织关系转接'
  editTypeRef.value = 'add'
  currentFormStatus.value = 'add'
  showEditRef.value = true
}

watch(showEditRef, (newV) => {
  if (!newV) {
    if (['check'].includes(currentFormStatus.value)) {
      checkFormRef.value?.resetForm()
    }
    else {
      addNoticeFormRef.value?.resetForm()
    }
  }
})
/** 保存成功 */
function handleListSaved() {
  showEditRef.value = false
  getTableList()
}

// 修改和删除按钮渲染
const tableColumns = getTableColumns((row) => {
  return [
    h(
      NButton,
      {
        onClick: () => {
          window.$dialog.warning({
            title: '提示',
            content: '撤销后无法恢复，确认撤销？',
            positiveText: '确认',
            negativeText: '取消',
            onPositiveClick: () => {
              const data = {
                id: row.id,
                phaseStatus: 2,
                remark: '',
              }
              putUpdateOrganizationChangeOver(data).then((res) => {
                window.$message.success('撤销成功')
                getTableList()
              })
            },
          })
        },
        type: 'primary',
        disabled: ['已驳回', '已撤销', '已完成'].includes(row.phaseStatus),
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '撤销',
      },
    ),
    h(
      NButton,
      {
        onClick: () => {
          editTitle.value = '审核组织关系转接'
          editTypeRef.value = 'custom'
          currentFormStatus.value = 'check'
          showEditRef.value = true
          idEditRef.value = row.id
        },
        disabled: ['已驳回', '已撤销', '已完成'].includes(row.phaseStatus),
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '审核',
      },
    ),
    h(
      NButton,
      {
        onClick: () => {
          editTitle.value = '组织关系转接'
          editTypeRef.value = 'view'
          currentFormStatus.value = 'view'
          showEditRef.value = true
          idEditRef.value = row.id
        },
        type: 'primary',
        text: true,
        style: {
          marginRight: '10px',
        },
      },
      {
        default: () => '查看',
      },
    ),
  ]
})

watch(pageNum, () => {
  getTableList()
})
watch(pageSize, () => {
  getTableList()
})

onMounted(() => {
  getTableList()
})
</script>
<template>
  <table-container
    v-model:page="pageNum"
    v-model:page-size="pageSize"
    title="组织关系转接"
    :show-toolbar="false"
    custom-toolbar
    :loading="loading"
    :total="total"
    :table-columns="tableColumns"
    :table-data="tableData"
    :show-delete="false"
    :show-pagination="true"
    default-expand-all
    @click-add="addOrganizationChangeOver"
  >
    <template #btns>
      <n-button size="small" type="primary" @click="addOrganizationChangeOver">
        <template #icon>
          <n-icon>
            <plus-round />
          </n-icon>
        </template>
        添加
      </n-button>
    </template>
    <template #filters>
      <div
        class="flex flex-row items-center justify-between gap-[10px] w-[600px]"
      >
        <n-select
          v-model:value="transferTypeRef"
          clearable
          size="medium"
          :options="transferType"
          placeholder="请选择调动类型"
          @update:value="getTableList"
        />
        <n-select
          v-model:value="phaseStatusRef"
          clearable
          size="medium"
          :options="statusOptions"
          placeholder="请选择状态"
          @update:value="getTableList"
        />
        <n-input
          v-model:value="userNameRef"
          size="medium"
          placeholder="请输入党员姓名"
          clearable
          @input="getTableList"
        />
      </div>
    </template>
  </table-container>

  <!-- 新增通知抽屉 -->
  <n-drawer v-model:show="showEditRef" :width="700" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <notice-form
        v-show="['add', 'view'].includes(currentFormStatus)"
        :id="idEditRef"
        ref="addNoticeFormRef"
        :type="editTypeRef"
        :transfer-type-prop="transferType"
        :parent-id="idEditParentID"
        :parent-name="idEditParentName"
        :level="idEditLevel"
        @saved="handleListSaved"
      />
      <check-form
        v-show="['check'].includes(currentFormStatus)"
        :id="idEditRef"
        ref="checkFormRef"
        @saved="handleListSaved"
      />
      <template #footer>
        <div class="flex justify-center w-full gap-[12px]">
          <n-button
            type="primary"
            style="width: 80px"
            @click="handleClickConfirm"
          >
            确定
          </n-button>
          <n-button style="width: 80px" @click="handleClickCancel">
            取消
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<style lang="scss" scoped></style>
