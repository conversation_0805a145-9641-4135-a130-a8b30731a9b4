import type { VNodeChild } from 'vue'
import type { TableColumns } from 'naive-ui/es/data-table/src/interface'
import { NImage } from 'naive-ui'
import type { NewsListItem } from '@/services/news/types'
import type { CarousePoolType } from '@/services/wisdom/types'

export function getTableColumns(
  operationRender: (row: NewsListItem) => VNodeChild,
  topRender: (row: NewsListItem) => VNodeChild,
  carouselPoolRender: (row: NewsListItem) => VNodeChild,
  sortRender: (row: NewsListItem) => VNodeChild,
): TableColumns<NewsListItem> {
  return [
    {
      type: 'selection',
      align: 'center',
    },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      title: '标题',
      key: 'title',
      width: '16%',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      title: '图片',
      key: 'img',
      width: '8%',
      render: row =>
        h(NImage, {
          src: import.meta.env.VITE_API_BASE + row.coverUrl,
          width: '62',
          style: { height: '40px' },
        }),
    },
    {
      title: '是否外链',
      key: 'isOutside',
      width: '6%',
      render: row => (row.isOutside === '1' ? '是' : '否'),
    },
    {
      title: '外链地址',
      key: 'linkUrl',
      width: '15%',
      render: (row) => {
        return row.linkUrl
          ? h(
            'a',
            {
              href: row.linkUrl,
              target: '_blank',
              style: {
                color: '#3f7ee8',
              },
            },
            row.linkUrl,
          )
          : '--'
      },
    },
    {
      title: '阅读量',
      key: 'readNum',
      align: 'center',
      width: '5%',
    },
    {
      title: '点赞量',
      key: 'likeNum',
      align: 'center',
      width: '5%',
    },
    // {
    //   title: '评论数',
    //   key: 'comment',
    //   render: commentRender,
    // },
    {
      title: '发表时间',
      key: 'publishTime',
      width: '12%',
      render: row => row.publishTime ?? '-',
    },
    {
      title: '是否置顶',
      key: 'isTop',
      width: '8%',
      render: topRender,
    },
    {
      title: '是否加入轮播池',
      key: 'isTop',
      width: '10%',
      render: carouselPoolRender,
    },
    {
      title: '排序',
      key: 'sort',
      width: '8%',
      align: 'center',
      render: sortRender,
    },
    {
      title: '操作',
      key: 'operation',
      width: '10%',
      render: operationRender,
    },
  ]
}

// 轮播图管理的表格
export function getTableColumnsOfCarouselPool(
  sortRender: (row: CarousePoolType) => VNodeChild,
  showMobileRender: (row: CarousePoolType) => VNodeChild,
  delRender: (row: CarousePoolType) => VNodeChild,
): TableColumns<CarousePoolType> {
  return [
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: '5%',
      render: (_, i) => i + 1,
    },
    {
      title: '类型',
      align: 'center',
      key: 'poolType',
      render: row => h('span', {}, row.poolType === '1' ? '课程' : '专栏'),
    },
    {
      title: '名称',
      key: 'name',
      ellipsis: {
        tooltip: {
          contentStyle: { width: '400px', 'word-break': 'break-all' },
        },
      },
    },
    {
      title: '图片',
      key: 'coverUrl',
      width: '8%',
      align: 'center',
      render: row =>
        h(NImage, {
          src: import.meta.env.VITE_API_BASE + row.coverUrl,
          width: '62',
          style: { height: '40px' },
        }),
    },
    {
      title: '排序',
      key: 'sort',
      align: 'center',
      width: '10%',
      render: sortRender,
    },
    {
      title: '移动端展示',
      key: 'isAppShow',
      width: '15%',
      align: 'center',
      render: showMobileRender,
    },
    {
      title: '移除轮播池',
      key: 'delFlag',
      width: '10%',
      align: 'center',
      render: delRender,
    },
  ]
}
