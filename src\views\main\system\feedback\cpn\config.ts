import type { FormRules } from 'naive-ui'

export const formRules: FormRules = {
  resourceName: [
    {
      required: true,
      message: '图片名称不能为空',
      trigger: 'input',
    },
  ],
  layout: [
    {
      required: true,
      message: '图片位置不能为空',
      type: 'number',
      trigger: 'change',
    },
  ],
  sortOrder: [
    {
      required: true,
      message: '排序不能为空',
      trigger: 'input',
      type: 'number',
    },
  ],
  jumpType: [
    {
      required: true,
      message: '跳转类型不能为空',
      trigger: 'change',
      type: 'number',
    },
  ],
  file: [
    {
      required: true,
      message: '请上传图片',
      trigger: 'change',
      type: 'any',
    },
  ],
}

/** 轮播图尺寸 */
export const imgSizes = [
  {
    id: 1, // 首页顶部
    width: 708,
    height: 254,
  },
  {
    id: 2, // 首页中部
    width: 708,
    height: 180,
  },
  {
    id: 3, // 首页底部
    width: 708,
    height: 254,
  },
  {
    id: 4, // 学习页顶部
    width: 708,
    height: 254,
  },
  {
    id: 5, // 学习页中部
    width: 708,
    height: 180,
  },
  {
    id: 6, // 学习页底部
    width: 708,
    height: 254,
  },
  {
    id: 7, // 首页应用
    width: 708,
    height: 254,
  },
]
