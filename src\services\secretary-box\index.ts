// 书记信箱
import { commonReq } from '../request'
import type { SecretaryList } from './types'
import type { PaginationReq, PaginationRes } from '@/services/types'

/** 书记信箱分页查询 */
export function getSecretaryList(params: any & PaginationReq) {
  return commonReq.get<PaginationRes<SecretaryList>>({
    url: '/party-affairs/backend/v1/suggestion/list',
    params,
  })
}

/** 书记信箱详情 */
export function getSecretaryDetail(id: string) {
  return commonReq.get<PaginationRes<SecretaryList>>({
    url: '/party-affairs/backend/v1/suggestion',
    params: {
      id,
    },
  })
}

/** 删除书记信箱数据 */
export function deleteSecretary(id: string) {
  return commonReq.delete({
    url: '/party-affairs/backend/v1/suggestion',
    params: {
      id,
    },
  })
}
