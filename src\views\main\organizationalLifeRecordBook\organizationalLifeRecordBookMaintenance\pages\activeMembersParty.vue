<script lang="ts" setup>
import { NButton, NIcon, type TreeSelectOption } from 'naive-ui'
import { CachedSharp, DownloadRound } from '@vicons/material'
import dayjs from 'dayjs'
import { useRecordBookStore } from '@/store/recordBook'
import { useFetchEnumerationOptions } from '@/hooks/use-select-options'
import { getActivePartyMemberColumns } from '@/views/main/organizationalLifeRecordBook/organizationalLifeRecordBookMaintenance/pages/config'
import type { ActivistsMemberRosterType } from '@/services/recordBookMaintenance/types'
import {
  deletePartyMemberActiveRoster,
  getPartyMemberActiveRosterShowSyncDialog,
  getPartyMemberRosterList,
  getSyncTime,
  syncPartyMemberActiveRoster,
} from '@/services/recordBookMaintenance'

const sexType = ref(useFetchEnumerationOptions('sex'))
const eduType = ref(useFetchEnumerationOptions('edu'))

const recordBookStore = useRecordBookStore()
const currentYearOptions = computed(() => {
  return recordBookStore.getYearOptions
})

const router = useRouter()
const pageNum = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const total = ref(0)
const selectedDeptId = ref<string | null>(null)
const selectedYear = ref<string | null>(null)
const showSyncDialog = ref<boolean>(false)
const tableData = ref<ActivistsMemberRosterType[]>([])

const currentDownloadFileType = ref<'word' | 'pdf'>('word')
const showPopConfirm = ref<boolean>(false)
const syncRecordTime = ref<string>('')
const isNeedShowSyncDialog = ref<boolean>(false)

const calcOrganizationListTree = computed(() => {
  return recordBookStore.getOrganizationListTreeList
})

const currentSelectedDept = computed(() => {
  return recordBookStore.getCurrentSelectedDept
})
// 获取当前年份
const currentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear
})

// 判断是不是当年
const isCurrentYear = computed(() => {
  return recordBookStore.getCurrentSelectedYear !== dayjs().year().toString()
})

const tableColumns = getActivePartyMemberColumns(
  (row) => {
    return h(
      'span',
      {},
      {
        default: () => {
          const options = toRaw(sexType.value.enumerationList)
          const findData = options.find(item => item.value === row.sex)
          return findData?.label || ''
        },
      },
    )
  },
  (row) => {
    return h(
      'span',
      {},
      {
        default: () => {
          const options = toRaw(eduType.value.enumerationList)
          const findData = options.find(item => item.value === row.edu)
          return findData?.label || ''
        },
      },
    )
  },
  (row, index) => {
    return [
      h(
        'div',
        {
          class: 'flex flex-row gap-x-[10px]',
        },
        {
          default: () => [
            h(
              NButton,
              {
                text: true,
                color: '#AC241D',
                onClick: () => {
                  router.push({
                    name: 'party-member-development',
                    query: {
                      id: row.memberId,
                      developId: row.developId,
                    },
                  })
                },
              },
              {
                default: () => '查看',
              },
            ),
            h(
              NButton,
              {
                text: true,
                color: '#AC241D',
                onClick: () => {
                  window.$dialog.create({
                    type: 'default',
                    closable: false,
                    content: '确认删除该条记录？',
                    showIcon: false,
                    positiveText: '确认',
                    negativeText: '取消',
                    onPositiveClick: () => {
                      deletePartyMemberActiveRoster(row.id).then(() => {
                        window.$message.success('删除成功！')
                        loadData()
                      })
                    },
                  })
                },
              },
              {
                default: () => '删除',
              },
            ),
          ],
        },
      ),
    ]
  },
)

function handleUpdateTreeSelectedValue(v: string, option: TreeSelectOption) {
  if (!v) {
    return
  }
  selectedDeptId.value = v
  loadData()
  recordBookStore.setCurrentSelectedDept({
    deptId: v || '',
    deptName: (option?.name as string) || '',
  })
}

function handleUpdateYearSelectedValue(v: string) {
  if (!v) {
    return
  }
  selectedYear.value = v
  loadData()
  recordBookStore.setCurrentSelectedYear(v || '')
}

function handleDownLoadFile() {
  recordBookStore.fetchDownLoadFile({
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
    type: currentDownloadFileType.value,
  })

  nextTick(() => {
    showPopConfirm.value = false
  })
}

function handleCancelShowPopConfirm() {
  nextTick(() => {
    showPopConfirm.value = false
  })
}

watch(
  () => pageNum.value,
  () => {
    loadData()
  },
)

watch(
  () => pageSize.value,
  () => {
    pageNum.value = 1
    loadData()
  },
)

function loadData() {
  if (!selectedDeptId.value || !selectedYear.value) {
    return
  }
  loading.value = true
  fetchShowSyncDialog()
  fetchSyncTime()
  getPartyMemberRosterList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
  })
    .then((res) => {
      total.value = res.total || 0
      tableData.value = (res.records as any) || []
    })
    .finally(() => {
      loading.value = false
    })
}

function handelSyncMemberData(syncType: '0' | '1') {
  syncPartyMemberActiveRoster({ deptId: selectedDeptId.value!, syncType })
    .then(() => {
      window.$message.success('同步成功！')
      fetchSyncTime()
      loadData()
    })
    .finally(() => {
      showSyncDialog.value = false
    })
}

function fetchSyncTime() {
  getSyncTime({
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
    fieldType: '1', // 1 入党积极分子
  }).then((res) => {
    syncRecordTime.value = (res as any) || ''
  })
}

function handelShowSyncDialog() {
  if (isNeedShowSyncDialog.value) {
    showSyncDialog.value = true
  }
  else {
    handelSyncMemberData('1') // 覆盖同步
  }
}

function fetchShowSyncDialog() {
  getPartyMemberActiveRosterShowSyncDialog({
    deptId: selectedDeptId.value!,
    year: selectedYear.value!,
  }).then((res) => {
    isNeedShowSyncDialog.value = res as boolean
  })
}

onMounted(() => {
  selectedDeptId.value = currentSelectedDept.value.deptId
  selectedYear.value = currentYear.value
  loadData()
  fetchSyncTime()
})
</script>

<template>
  <div>
    <table-container
      v-model:page="pageNum"
      v-model:page-size="pageSize"
      :loading="loading"
      :show-delete="false"
      :show-pagination="true"
      :show-toolbar="false"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total="total"
      default-expand-all
      title="入党积极分子名册"
    >
      <template #row>
        <div
          class="w-[100%] flex flex-row items-start justify-between gap-[10px] pb-[10px]"
        >
          <div class="flex flex-row gap-x-[10px] justify-start items-start">
            <n-tree-select
              v-model:value="selectedDeptId"
              :options="calcOrganizationListTree"
              :show-path="false"
              check-strategy="all"
              children-field="children"
              filterable
              key-field="id"
              label-field="name"
              placeholder="请选择所属党组织"
              style="width: 400px"
              value-field="id"
              @update:value="handleUpdateTreeSelectedValue"
            />
            <n-select
              v-model:value="selectedYear"
              :options="currentYearOptions"
              placeholder="请选择年份"
              size="medium"
              style="width: 200px"
              @update:value="handleUpdateYearSelectedValue"
            />
          </div>

          <div class="flex flex-row gap-x-[10px] justify-start items-start">
            <n-popconfirm
              :show="showPopConfirm"
              :show-icon="false"
              placement="bottom"
            >
              <template #trigger>
                <n-button @click="showPopConfirm = !showPopConfirm">
                  <template #icon>
                    <n-icon size="16">
                      <DownloadRound />
                    </n-icon>
                  </template>
                  下载
                </n-button>
              </template>
              <div class="py-[10px]">
                <n-radio-group v-model:value="currentDownloadFileType">
                  <n-radio
                    v-for="(fileType, fileTypeIndex) in [
                      { label: 'word', value: 'word' },
                      { label: 'pdf', value: 'pdf' },
                    ]"
                    :key="fileTypeIndex"
                    :value="fileType.value"
                  >
                    {{ fileType.label }}
                  </n-radio>
                </n-radio-group>
              </div>

              <template #action>
                <div class="w-[100%] flex flex-row justify-center items-center">
                  <n-button
                    size="small"
                    type="primary"
                    @click="handleDownLoadFile(item)"
                  >
                    确定
                  </n-button>
                  <n-button size="small" @click="handleCancelShowPopConfirm">
                    取消
                  </n-button>
                </div>
              </template>
            </n-popconfirm>
            <div class="flex flex-col gap-y-[10px]">
              <n-button :disabled="isCurrentYear" @click="handelShowSyncDialog">
                <template #icon>
                  <n-icon size="16">
                    <CachedSharp />
                  </n-icon>
                </template>
                同步数据
              </n-button>
              <span
                v-if="syncRecordTime"
                class="text-[#333333] text-[12px] leading-[17px] flex flex-row items-center gap-x-[6px]"
              ><n-icon color="#AC241D" size="16"><CachedSharp /></n-icon>{{ syncRecordTime }}</span>
            </div>
          </div>
        </div>
      </template>
    </table-container>

    <n-modal v-model:show="showSyncDialog" preset="dialog" title="同步数据">
      <div class="py-[10px]">
        <span class="text-[16px] leading-[32px]">是否保留上次修改内容？</span>
      </div>
      <template #action>
        <div
          class="w-[100%] flex flex-row justify-center items-center gap-x-[20px]"
        >
          <n-button @click="handelSyncMemberData('0')">
            确认保留
          </n-button>
          <n-button @click="handelSyncMemberData('1')">
            覆盖所有
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<style lang="scss" scoped></style>
